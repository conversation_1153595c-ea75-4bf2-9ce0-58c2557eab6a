################ ANTpy###########
import os
import re
from pathlib import Path
from typing import List, Dict, Optional
from tqdm import tqdm

import cupy as cp
import numpy as np
import tifffile
import ants
import matplotlib.pyplot as plt


def normalize_image(img: cp.ndarray) -> cp.ndarray:
    img_min = img.min()
    img_max = img.max()
    return (img - img_min) / (img_max - img_min + 1e-12)


def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:
    max_h = max(img.shape[0] for img in images)
    max_w = max(img.shape[1] for img in images)
    padded = []
    for img in images:
        pad_h = max_h - img.shape[0]
        pad_w = max_w - img.shape[1]
        pad_top = pad_h // 2
        pad_bottom = pad_h - pad_top
        pad_left = pad_w // 2
        pad_right = pad_w - pad_left
        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),
                            mode='constant', constant_values=0)
        padded.append(img_padded)
    return padded


def ants_from_cp(img: cp.ndarray) -> ants.ANTsImage:
    return ants.from_numpy(cp.asnumpy(img))


def ants_to_cp(img: ants.ANTsImage) -> cp.ndarray:
    return cp.asarray(img.numpy())


def ants_registration(fixed: cp.ndarray, moving: cp.ndarray,
                      transform_type: str) -> (cp.ndarray, List[str]):
    print(f"Starting {transform_type} registration...")
    fixed_ants = ants_from_cp(normalize_image(fixed))
    moving_ants = ants_from_cp(normalize_image(moving))
    reg = ants.registration(fixed=fixed_ants, moving=moving_ants, type_of_transform=transform_type)

    # Apply transforms to original (non-normalized) moving image
    fixed_ants_orig = ants_from_cp(fixed)
    moving_ants_orig = ants_from_cp(moving)
    warped = ants.apply_transforms(fixed=fixed_ants_orig,
                                  moving=moving_ants_orig,
                                  transformlist=reg['fwdtransforms'],
                                  interpolator='linear')
    warped_cp = ants_to_cp(warped)
    print(f"Completed {transform_type} registration. Transform files: {reg['fwdtransforms']}")
    return warped_cp, reg['fwdtransforms']


def apply_transforms_to_channel(channel: cp.ndarray, fixed: cp.ndarray,
                                transformlist: List[str]) -> cp.ndarray:
    print(f"Applying transforms to channel...")
    fixed_ants = ants_from_cp(fixed)
    moving_ants = ants_from_cp(channel)
    warped = ants.apply_transforms(fixed=fixed_ants,
                                  moving=moving_ants,
                                  transformlist=transformlist,
                                  interpolator='linear')
    result = ants_to_cp(warped)
    print(f"Transform application complete")
    return result


def save_image(img: cp.ndarray, output_dir: str, filename: str):
    out_path = os.path.join(output_dir, filename)
    np_img = cp.asnumpy(img).astype(np.float32)
    tifffile.imwrite(out_path, np_img)
    print(f"Saved image: {out_path}")


def plot_images(fixed: cp.ndarray, moving: cp.ndarray, title: str):
    # Quick visualization (can be removed if running headless)
    fig, axes = plt.subplots(1, 2, figsize=(10, 5))
    axes[0].imshow(cp.asnumpy(fixed), cmap='gray')
    axes[0].set_title("Reference (Fixed)")
    axes[0].axis('off')
    axes[1].imshow(cp.asnumpy(moving), cmap='gray')
    axes[1].set_title(title)
    axes[1].axis('off')
    plt.show()


def get_basename_key(filename: str) -> str:
    match = re.match(r"(.+)_ch\d{2}\.tif$", filename)
    if match:
        return match.group(1)
    raise ValueError(f"Filename {filename} does not match expected pattern *_chXX.tif")


def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:
    files = [f for f in os.listdir(folder) if f.endswith('.tif')]
    groups = {}
    for f in files:
        base = get_basename_key(f)
        ch_match = re.search(r"_ch(\d{2})\.tif$", f)
        if not ch_match:
            continue
        ch = f"ch{ch_match.group(1)}"
        groups.setdefault(base, {})[ch] = os.path.join(folder, f)
    return groups


def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:
    channels = {}
    for ch, p in paths.items():
        print(f"Loading channel {ch} from {p}")
        img = tifffile.imread(p)
        if img.ndim > 2:
            img = img[0]
        channels[ch] = cp.asarray(img)
    return channels


def save_channels_as_tiff(channels: Dict[str, cp.ndarray], out_path: str):
    imgs = [cp.asnumpy(channels[ch]) for ch in sorted(channels.keys())]
    stacked = np.stack(imgs, axis=0)
    tifffile.imwrite(out_path, stacked)
    print(f"Saved multi-channel TIFF: {out_path}")


def batch_align_to_reference(
    reference_img_path: str,
    input_folder: str,
    output_folder: str,
    affine_transform_type: str = "Affine",
    use_non_rigid: bool = True,
    use_micro_registration: bool = False,
    save_intermediate: bool = False,
):
    print(f"Loading reference ch00 image from {reference_img_path}...")
    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))

    groups = group_images_by_basename(input_folder)
    print(f"Found {len(groups)} groups to align.")

    Path(output_folder).mkdir(parents=True, exist_ok=True)

    for base, channel_paths in tqdm(groups.items(), desc="Processing image groups"):
        if 'ch00' not in channel_paths:
            print(f"Skipping '{base}': no ch00 channel found.")
            continue

        print(f"\n{'='*50}")
        print(f"Processing group: {base}")
        print(f"{'='*50}")
        
        channels = load_channels(channel_paths)

        # Pad fixed + moving images to largest size
        print("Padding images to largest size...")
        all_imgs = [fixed_ch00] + list(channels.values())
        all_padded = pad_to_largest(all_imgs)

        fixed_ch00_padded = all_padded[0]
        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))

        transforms = {}

        # Affine registration
        print(f"\n--- Starting Affine Registration ---")
        affine_img, affine_transforms = ants_registration(
            fixed_ch00_padded, channels_padded['ch00'], transform_type=affine_transform_type
        )
        if save_intermediate:
            save_image(affine_img, output_folder, f"{base}_affine.tif")
        print(f"--- Affine Registration Complete ---")

        # Non-rigid registration (SyN)
        if use_non_rigid:
            print(f"\n--- Starting Non-Rigid Registration ---")
            non_rigid_img, non_rigid_transforms = ants_registration(
                fixed_ch00_padded, affine_img, transform_type="SyN"
            )
            if save_intermediate:
                save_image(non_rigid_img, output_folder, f"{base}_non_rigid.tif")
            transforms[channel_paths['ch00']] = affine_transforms + non_rigid_transforms
            aligned_img = non_rigid_img
            print(f"--- Non-Rigid Registration Complete ---")
        else:
            transforms[channel_paths['ch00']] = affine_transforms
            aligned_img = affine_img

        # Micro-registration (optional)
        if use_micro_registration:
            print(f"\n--- Starting Micro-Registration ---")
            micro_img, micro_transforms = ants_registration(
                fixed_ch00_padded, aligned_img, transform_type="SyN"
            )
            if save_intermediate:
                save_image(micro_img, output_folder, f"{base}_micro.tif")
            transforms[channel_paths['ch00']] += micro_transforms
            aligned_img = micro_img
            print(f"--- Micro-Registration Complete ---")

        # Store aligned ch00
        aligned_channels = {'ch00': aligned_img}

        # Apply combined transforms to other channels
        print(f"\n--- Applying Transforms to Other Channels ---")
        for ch, img in tqdm(channels_padded.items(), desc="Processing channels"):
            if ch == 'ch00':
                continue
            print(f"Processing channel: {ch}")
            aligned_channels[ch] = apply_transforms_to_channel(img, fixed_ch00_padded, transforms[channel_paths['ch00']])

        # Save final multi-channel aligned TIFF
        out_path = os.path.join(output_folder, f"{base}_aligned.tif")
        save_channels_as_tiff(aligned_channels, out_path)
        print(f"Saved aligned image: {out_path}")

        # Optional visualization
        plot_images(fixed_ch00_padded, aligned_img, f"Aligned: {base}")