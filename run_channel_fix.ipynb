{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fix Channel Order in VALIS Registration\n", "\n", "This notebook uses the `fix_channel_order_solution.py` script to properly organize channels in the final merged image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pathlib\n", "from tqdm import tqdm\n", "from valis import registration, valtils, preprocessing, slide_io\n", "\n", "# Import the solution\n", "from fix_channel_order_solution import process_images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define your directories and reference slide\n", "src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/aligned_images/\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/aligned_images/LB06 BP 4i p21 green p16 red 25mar25.lif - R 6_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.3  # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the processing function\n", "merged_slide_path = process_images(\n", "    src_dir=src_dir,\n", "    dst_dir=dst_dir,\n", "    reference_slide_path=reference_slide,\n", "    micro_reg_fraction=micro_reg_fraction\n", ")\n", "\n", "print(f\"\\nProcessing complete! Merged slide saved to: {merged_slide_path}\")"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}