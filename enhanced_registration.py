"""
Enhanced registration functions with CLAHE preprocessing and optimized non-rigid registration.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union

import cupy as cp
import numpy as np
import tifffile
import ants
import matplotlib.pyplot as plt
from skimage.transform import resize
from skimage.registration import phase_cross_correlation
from skimage.exposure import equalize_adapthist  # CLAHE implementation
from tqdm import tqdm

def normalize_image(img: Union[cp.ndarray, np.ndarray]) -> Union[cp.ndarray, np.ndarray]:
    """Normalize image to [0,1] range."""
    if isinstance(img, np.ndarray):
        img_min = img.min()
        img_max = img.max()
        return (img - img_min) / (img_max - img_min + 1e-12)
    else:  # CuPy array
        img_min = img.min()
        img_max = img.max()
        return (img - img_min) / (img_max - img_min + 1e-12)

def apply_clahe(img: Union[cp.ndarray, np.ndarray], kernel_size=None, clip_limit=0.01) -> np.ndarray:
    """
    Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to enhance image contrast.
    
    Args:
        img: Input image
        kernel_size: Size of contextual regions (if None, defaults to 1/8 of image height/width)
        clip_limit: Clipping limit to prevent amplification of noise
        
    Returns:
        CLAHE-enhanced image
    """
    # Convert to numpy if it's a CuPy array
    if isinstance(img, cp.ndarray):
        img_np = cp.asnumpy(img)
    else:
        img_np = img
    
    # Normalize to [0,1] for CLAHE
    img_norm = normalize_image(img_np)
    
    # Set default kernel size if not provided
    if kernel_size is None:
        kernel_size = max(8, min(img_norm.shape) // 8)
    
    # Apply CLAHE
    enhanced = equalize_adapthist(
        img_norm, 
        kernel_size=kernel_size,
        clip_limit=clip_limit
    )
    
    return enhanced

def ants_from_cp(img: cp.ndarray) -> ants.ANTsImage:
    """Convert CuPy array to ANTs image."""
    return ants.from_numpy(cp.asnumpy(img))

def ants_to_cp(img: ants.ANTsImage) -> cp.ndarray:
    """Convert ANTs image to CuPy array."""
    return cp.asarray(img.numpy())

def optimized_ants_registration(
    fixed: cp.ndarray, 
    moving: cp.ndarray,
    transform_type: str,
    initializer: Optional[ants.ANTsTransform] = None,
    interpolator: str = 'linear',
    downsample_factor: float = 0.5,  # Default to 50% downsampling for SyN
    use_clahe: bool = True,
    memory_efficient: bool = True
) -> Tuple[cp.ndarray, List[str], float]:
    """
    Optimized ANTs registration function with CLAHE preprocessing and downsampling.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        transform_type: Type of transform ('Rigid', 'Affine', 'SyN', etc.)
        initializer: Optional initial transform
        interpolator: Interpolation method
        downsample_factor: Factor to downsample images (1.0 = no downsampling, 0.5 = half size)
        use_clahe: Whether to apply CLAHE preprocessing
        memory_efficient: Whether to free memory aggressively during processing
    
    Returns:
        Tuple of (warped image, transform paths, similarity metric)
    """
    # Free memory before starting
    if memory_efficient:
        cp.get_default_memory_pool().free_all_blocks()
    
    # Store original images for final transformation
    fixed_orig = fixed.copy()
    moving_orig = moving.copy()
    
    # Normalize images
    fixed_norm = normalize_image(fixed)
    moving_norm = normalize_image(moving)
    
    # Apply CLAHE if requested (for registration only)
    if use_clahe:
        fixed_np = cp.asnumpy(fixed_norm)
        moving_np = cp.asnumpy(moving_norm)
        
        fixed_enhanced = apply_clahe(fixed_np)
        moving_enhanced = apply_clahe(moving_np)
        
        print("Applied CLAHE preprocessing for better feature detection")
    else:
        fixed_enhanced = cp.asnumpy(fixed_norm)
        moving_enhanced = cp.asnumpy(moving_norm)
    
    # Downsample for faster processing if requested
    if downsample_factor < 1.0 and transform_type == "SyN":
        # Calculate new dimensions
        new_shape = (int(fixed_enhanced.shape[0] * downsample_factor), 
                     int(fixed_enhanced.shape[1] * downsample_factor))
        
        # Resize images
        fixed_small = resize(fixed_enhanced, new_shape, preserve_range=True, anti_aliasing=True)
        moving_small = resize(moving_enhanced, new_shape, preserve_range=True, anti_aliasing=True)
        
        # Convert to ANTs format
        fixed_ants = ants.from_numpy(fixed_small)
        moving_ants = ants.from_numpy(moving_small)
        
        print(f"Downsampled images from {fixed_enhanced.shape} to {new_shape} for faster registration")
        
        # Free memory
        if memory_efficient:
            del fixed_np, moving_np, fixed_enhanced, moving_enhanced, fixed_small, moving_small
            cp.get_default_memory_pool().free_all_blocks()
    else:
        # Use original resolution
        fixed_ants = ants.from_numpy(fixed_enhanced)
        moving_ants = ants.from_numpy(moving_enhanced)
    
    # Optimize parameters based on transform type
    if transform_type == "SyN":
        # Optimized SyN parameters for faster convergence
        reg = ants.registration(
            fixed=fixed_ants, 
            moving=moving_ants, 
            type_of_transform=transform_type,
            grad_step=0.2,  # Increased for faster convergence
            flow_sigma=3,
            total_sigma=0,
            syn_metric='CC',
            syn_sampling=64,  # Increased for faster processing
            reg_iterations=(50,30,10)  # Reduced iterations
        )
    else:
        reg = ants.registration(
            fixed=fixed_ants,
            moving=moving_ants,
            type_of_transform=transform_type,
            initial_transform=initializer
        )
    
    # Get transforms
    fwd_transforms = reg.get('fwdtransforms', [])
    if isinstance(fwd_transforms, str):
        fwd_transforms = [fwd_transforms]

    # Apply transforms to original images to preserve intensity
    fixed_ants_orig = ants_from_cp(fixed_orig)
    moving_ants_orig = ants_from_cp(moving_orig)
    
    warped = ants.apply_transforms(
        fixed=fixed_ants_orig,
        moving=moving_ants_orig,
        transformlist=fwd_transforms,
        interpolator=interpolator
    )
    
    # Calculate similarity
    similarity = ants.image_similarity(fixed_ants, warped, 'MattesMutualInformation')
    
    # Free memory after processing
    if memory_efficient:
        cp.get_default_memory_pool().free_all_blocks()
    
    return ants_to_cp(warped), fwd_transforms, similarity

def fast_non_rigid_registration(fixed: cp.ndarray, moving: cp.ndarray, use_clahe: bool = True):
    """
    Perform optimized non-rigid registration using SyN with downsampling and CLAHE.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        use_clahe: Whether to apply CLAHE preprocessing
        
    Returns:
        Tuple of (warped image, transform paths)
    """
    warped, transform_paths, _ = optimized_ants_registration(
        fixed, 
        moving, 
        transform_type="SyN",
        downsample_factor=0.25,  # Use 25% resolution for faster processing
        use_clahe=use_clahe
    )
    print(f"Non-rigid registration completed. Transform paths: {transform_paths}")
    return warped, transform_paths

def optimized_apply_transforms(
    channel: cp.ndarray, 
    fixed: cp.ndarray,
    transformlist: List[str], 
    interpolator: str = 'bSpline'
) -> cp.ndarray:
    """
    Apply transforms with memory optimization.
    
    Args:
        channel: Image to transform
        fixed: Reference image
        transformlist: List of transform paths
        interpolator: Interpolation method
        
    Returns:
        Transformed image
    """
    # Free memory before starting
    cp.get_default_memory_pool().free_all_blocks()
    
    fixed_ants = ants_from_cp(fixed)
    moving_ants = ants_from_cp(channel)
    
    warped = ants.apply_transforms(
        fixed=fixed_ants,
        moving=moving_ants,
        transformlist=transformlist,
        interpolator=interpolator
    )
    
    # Free memory after processing
    cp.get_default_memory_pool().free_all_blocks()
    
    return ants_to_cp(warped)

def cupy_fourier_shift(img: cp.ndarray, shift: tuple) -> cp.ndarray:
    """
    Optimized spatial domain shift for large images.
    
    Args:
        img: Image to shift
        shift: (y, x) shift values
        
    Returns:
        Shifted image
    """
    # Free memory explicitly
    cp.get_default_memory_pool().free_all_blocks()
    
    # Use spatial domain shift instead of Fourier for large images
    shift_y, shift_x = shift
    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)
