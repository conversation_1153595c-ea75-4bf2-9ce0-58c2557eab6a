{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc \n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# =================================\n", "# Import Packages and Setup Environment\n", "# =================================\n", "import os\n", "import glob\n", "import cv2\n", "import time\n", "import types\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from torch.utils.data import Dataset, DataLoader,random_split\n", "from datetime import datetime, timedelta\n", "from collections import defaultdict\n", "from tqdm import tqdm\n", "from PIL import Image\n", "from scipy import ndimage as ndi\n", "from skimage.feature import peak_local_max\n", "import albumentations as A\n", "from albumentations.pytorch import ToTensorV2\n", "import random\n", "from tensorboardX import SummaryWriter  \n", "import copy\n", "from tqdm.auto import tqdm\n", "\n", "\n", "\"\"\"\n", "Enhanced Spot Detection Framework - Complete Implementation\n", "- Multi-scale heatmap prediction\n", "- Stereographic flow field\n", "- Instance segmentation\n", "- Sub-pixel localization\n", "- Improved handling of close spots\n", "- Advanced training techniques\n", "\"\"\"\n", "\n", "# Force using just the first GPU and ignore others\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"  # Use only GPU 0\n", "\n", "# Optimize CUDA settings for performance\n", "torch.backends.cudnn.benchmark = True\n", "torch.backends.cudnn.deterministic = False\n", "if hasattr(torch.backends.cuda, 'matmul'):\n", "    torch.backends.cuda.matmul.allow_tf32 = True  # Use TensorFloat-32 if available\n", "torch.backends.cudnn.allow_tf32 = True\n", "\n", "# Clear GPU memory\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# Set seeds for reproducibility\n", "def set_seed(seed=42):\n", "    \"\"\"Set seeds for reproducibility\"\"\"\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.manual_seed(seed)\n", "    torch.cuda.manual_seed(seed)\n", "    torch.cuda.manual_seed_all(seed)\n", "    torch.backends.cudnn.benchmark = False\n", "    torch.backends.cudnn.deterministic = True\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\n", "# =================================\n", "# Model Architecture Components\n", "# =================================\n", "\n", "class AttentionGate(nn.Module):\n", "    \"\"\"Attention Gate for U-Net\"\"\"\n", "    def __init__(self, F_g, F_l, F_int):\n", "        super(AttentionGate, self).__init__()\n", "        self.is_3d = False  # Will be determined during forward pass\n", "        \n", "        # Gating path (from coarser level)\n", "        self.W_g = nn.Sequential(\n", "            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=True),\n", "            nn.BatchNorm2d(F_int)\n", "        )\n", "        \n", "        # Input path (from skip connection)\n", "        self.W_x = nn.Sequential(\n", "            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=True),\n", "            nn.BatchNorm2d(F_int)\n", "        )\n", "        \n", "        # Output path\n", "        self.psi = nn.Sequential(\n", "            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),\n", "            nn.BatchNorm2d(1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "        \n", "        self.relu = nn.ReLU(inplace=True)\n", "    \n", "    def forward(self, g, x):\n", "        \"\"\"\n", "        Attention gate forward pass\n", "        \n", "        Args:\n", "            g: Gating signal (from coarser level)\n", "            x: Input feature maps (from skip connection)\n", "        \"\"\"\n", "        # Check dimensions and convert to 3D if needed\n", "        if g.dim() == 5 and not self.is_3d:\n", "            self.is_3d = True\n", "            # Convert 2D layers to 3D\n", "            in_channels_g = self.W_g[0].in_channels\n", "            out_channels_g = self.W_g[0].out_channels\n", "            in_channels_x = self.W_x[0].in_channels\n", "            out_channels_x = self.W_x[0].out_channels\n", "            \n", "            self.W_g = nn.Sequential(\n", "                nn.Conv3d(in_channels_g, out_channels_g, kernel_size=1, stride=1, padding=0, bias=True),\n", "                nn.BatchNorm3d(out_channels_g)\n", "            )\n", "            \n", "            self.W_x = nn.Sequential(\n", "                nn.Conv3d(in_channels_x, out_channels_x, kernel_size=1, stride=1, padding=0, bias=True),\n", "                nn.BatchNorm3d(out_channels_x)\n", "            )\n", "            \n", "            self.psi = nn.Sequential(\n", "                nn.Conv3d(out_channels_g, 1, kernel_size=1, stride=1, padding=0, bias=True),\n", "                nn.BatchNorm3d(1),\n", "                nn.<PERSON><PERSON><PERSON><PERSON>()\n", "            )\n", "        \n", "        # Check if shapes match, otherwise resize\n", "        if g.shape[2:] != x.shape[2:]:\n", "            g = F.interpolate(g, size=x.shape[2:], \n", "                           mode='trilinear' if self.is_3d else 'bilinear', \n", "                           align_corners=False)\n", "        \n", "        # Apply attention mechanism\n", "        g1 = self.W_g(g)\n", "        x1 = self.W_x(x)\n", "        psi = self.relu(g1 + x1)\n", "        psi = self.psi(psi)\n", "        \n", "        # Apply attention weights\n", "        return x * psi\n", "\n", "# Simple residual block\n", "class ResidualBlock(nn.Module):\n", "    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, use_residual=True, dropout=0.0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        self.in_channels = in_channels\n", "        self.out_channels = out_channels\n", "        self.use_residual = use_residual  # This is now a class attribute, not a tensor\n", "        self.dropout = dropout\n", "        \n", "        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)\n", "        self.relu = nn.ReLU(inplace=True)\n", "        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size, 1, padding)\n", "        \n", "        if self.dropout > 0:\n", "            self.dropout_layer = nn.Dropout2d(dropout)\n", "    \n", "    def forward(self, x):\n", "        # Original path\n", "        out = self.conv1(x)\n", "        out = self.relu(out)  # Add ReLU that was missing\n", "        \n", "        if self.dropout > 0:\n", "            out = self.dropout_layer(out)\n", "        \n", "        out = self.conv2(out)\n", "        \n", "        # Fixed residual connection - NO tensor boolean operations\n", "        if self.use_residual:\n", "            if x.shape[1] == self.out_channels:\n", "                out = out + x\n", "        \n", "        # Final activation\n", "        out = self.relu(out)  # Add final ReLU\n", "        \n", "        return out\n", "class MixedScaleDilatedConv(nn.Module):\n", "    \"\"\"Mixed-scale dilated convolutions for better receptive field control\"\"\"\n", "    def __init__(self, in_channels, out_channels, dilation_rates=[1, 2, 4, 8], is_3d=False):\n", "        super(MixedScaleDilatedConv, self).__init__()\n", "        self.is_3d = is_3d\n", "        # Add these attributes explicitly\n", "        self.in_channels = in_channels\n", "        self.out_channels = out_channels  # This was missing and caused the error\n", "        \n", "        ConvModule = nn.Conv3d if is_3d else nn.Conv2d\n", "        \n", "        # Create convolutions with different dilation rates\n", "        self.convs = nn.ModuleList([\n", "            ConvModule(\n", "                in_channels, out_channels // len(dilation_rates),\n", "                kernel_size=3, padding=d, dilation=d\n", "            ) for d in dilation_rates\n", "        ])\n", "        \n", "        # Create fusion layer to combine multi-scale features\n", "        self.fusion = ConvModule(out_channels, out_channels, kernel_size=1)\n", "        \n", "    def forward(self, x):\n", "        # Apply dilated convolutions\n", "        outputs = [conv(x) for conv in self.convs]\n", "        \n", "        # Concatenate along channel dimension\n", "        out = torch.cat(outputs, dim=1)\n", "        \n", "        # Apply fusion\n", "        out = self.fusion(out)\n", "        \n", "        return out\n", "    \n", "# Anisotropic 3D convolution for better Z handling\n", "class AnisotropicConv3d(nn.Module):\n", "    \"\"\"3D convolution that handles anisotropic resolution in z-dimension\"\"\"\n", "    def __init__(self, in_channels, out_channels, kernel_size=3, z_factor=0.5, padding=1):\n", "        super(AnisotropicConv3d, self).__init__()\n", "        \n", "        # Create separate convolutions for XY and Z\n", "        self.conv_xy = nn.Conv3d(\n", "            in_channels, out_channels, \n", "            kernel_size=(1, kernel_size, kernel_size),\n", "            padding=(0, padding, padding)\n", "        )\n", "        \n", "        self.conv_z = nn.Conv3d(\n", "            out_channels, out_channels,\n", "            kernel_size=(kernel_size, 1, 1),\n", "            padding=(padding, 0, 0)\n", "        )\n", "        \n", "        self.z_factor = z_factor\n", "        self.norm = nn.BatchNorm3d(out_channels)\n", "        self.act = nn.ReLU(inplace=True)\n", "        \n", "    def forward(self, x):\n", "        # Apply XY convolution\n", "        x = self.conv_xy(x)\n", "        \n", "        # Apply Z convolution with scaled contribution\n", "        z_conv = self.conv_z(x)\n", "        x = x + self.z_factor * z_conv\n", "        \n", "        # Apply normalization and activation\n", "        x = self.norm(x)\n", "        x = self.act(x)\n", "        \n", "        return x\n", "\n", "class DecoderBlock(nn.Module):\n", "    def __init__(self, in_channels, out_channels, skip_channels=0, batch_norm=True,\n", "                 use_attention=True, use_residual=True, dropout=0.0, use_dilated_conv=False,\n", "                 is_3d=False, use_group_norm=False, num_groups=8):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        # Store channels information as class attributes\n", "        self.in_channels = in_channels\n", "        self.out_channels = out_channels  # Store this for later use\n", "        self.use_residual = use_residual\n", "        \n", "        # Determine actual input channels including skip connection\n", "        actual_in_channels = in_channels + skip_channels\n", "        \n", "        # Create convolution blocks\n", "        self.conv_blocks = self._make_conv_blocks(\n", "            actual_in_channels, out_channels, batch_norm, dropout,\n", "            use_dilated_conv, is_3d, use_group_norm, num_groups\n", "        )\n", "        \n", "        # Create attention gate if needed\n", "        self.attention = None\n", "        if use_attention and skip_channels > 0:\n", "            if is_3d:\n", "                self.attention = AttentionGate3D(in_channels, skip_channels, out_channels)\n", "            else:\n", "                self.attention = AttentionGate(in_channels, skip_channels, out_channels)\n", "    \n", "    def _make_conv_blocks(self, in_channels, out_channels, batch_norm, dropout,\n", "                         use_dilated_conv, is_3d, use_group_norm, num_groups):\n", "        # Choose proper modules based on dimensionality\n", "        ConvModule = nn.Conv3d if is_3d else nn.Conv2d\n", "        if use_group_norm:\n", "            NormModule = lambda num_features: nn.GroupNorm(\n", "                min(num_groups, num_features), num_features)\n", "        else:\n", "            NormModule = nn.BatchNorm3d if is_3d else nn.BatchNorm2d\n", "        \n", "        # Create layers\n", "        layers = []\n", "        \n", "        # First convolution\n", "        if use_dilated_conv:\n", "            layers.append(MixedScaleDilatedConv(in_channels, out_channels, is_3d=is_3d))\n", "        else:\n", "            layers.append(ConvModule(in_channels, out_channels, kernel_size=3, padding=1))\n", "        \n", "        # Add normalization and activation\n", "        if batch_norm:\n", "            layers.append(NormModule(out_channels))\n", "        layers.append(nn.ReLU(inplace=True))\n", "        \n", "        # Add dropout\n", "        if dropout > 0:\n", "            layers.append(nn.Dropout3d(dropout) if is_3d else nn.Dropout2d(dropout))\n", "        \n", "        # Second convolution with same number of channels\n", "        if use_dilated_conv:\n", "            layers.append(MixedScaleDilatedConv(out_channels, out_channels, is_3d=is_3d))\n", "        else:\n", "            layers.append(ConvModule(out_channels, out_channels, kernel_size=3, padding=1))\n", "        \n", "        # Add final normalization and activation\n", "        if batch_norm:\n", "            layers.append(NormModule(out_channels))\n", "        layers.append(nn.ReLU(inplace=True))\n", "        \n", "        return nn.Sequential(*layers)\n", "    \n", "    def forward(self, x, skip_connection=None):\n", "        # Apply attention gate if provided\n", "        if self.attention is not None and skip_connection is not None:\n", "            skip_connection = self.attention(x, skip_connection)\n", "        \n", "        # Concatenate skip connection if provided\n", "        if skip_connection is not None:\n", "            x = torch.cat([x, skip_connection], dim=1)\n", "        \n", "        # Store input for residual connection\n", "        # FIX: Use self.out_channels instead of trying to access it from the last layer\n", "        can_use_residual = self.use_residual and x.shape[1] == self.out_channels\n", "        residual = x if can_use_residual else None\n", "        \n", "        # Apply convolution blocks\n", "        x = self.conv_blocks(x)\n", "        \n", "        # Add residual connection if applicable\n", "        if residual is not None:\n", "            x = x + residual\n", "            \n", "        return x\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class MultiscaleUNet(nn.Module):\n", "    \"\"\"UNet backbone that outputs heatmaps at multiple resolutions\"\"\"\n", "    def __init__(self, in_channels=1, base_filters=16, levels=3, is_3d=False, \n", "                 batch_norm=True, use_attention=True, use_residual=True, dropout=0.1,\n", "                 use_dilated_conv=True, use_group_norm=False, num_groups=8):\n", "        super(MultiscaleUNet, self).__init__()\n", "        \n", "        self.is_3d = is_3d\n", "        self.levels = levels\n", "        ConvModule = nn.Conv3d if is_3d else nn.Conv2d\n", "        \n", "        if use_group_norm:\n", "            BatchNormModule = lambda num_features: nn.GroupNorm(\n", "                min(num_groups, num_features), num_features)\n", "        else:\n", "            BatchNormModule = nn.BatchNorm3d if is_3d else nn.BatchNorm2d\n", "        \n", "        # Create encoder blocks with increasing filters\n", "        self.encoders = nn.ModuleList()\n", "        current_channels = in_channels\n", "        \n", "        for level in range(levels):\n", "            out_channels = base_filters * (2 ** level)\n", "            encoder = self._make_encoder_block(\n", "                current_channels, out_channels, \n", "                batch_norm=batch_norm, use_residual=use_residual, \n", "                dropout=dropout, use_dilated_conv=use_dilated_conv\n", "            )\n", "            self.encoders.append(encoder)\n", "            current_channels = out_channels\n", "        \n", "        # Create bottleneck\n", "        bottleneck_filters = base_filters * (2 ** levels)\n", "        self.bottleneck = self._make_encoder_block(\n", "            current_channels, bottleneck_filters,\n", "            batch_norm=batch_norm, use_residual=use_residual, dropout=dropout,\n", "            use_dilated_conv=use_dilated_conv\n", "        )\n", "        \n", "        # Create decoder blocks with decreasing filters\n", "        self.decoders = nn.ModuleList()\n", "        self.upsamples = nn.ModuleList()\n", "        \n", "        current_channels = bottleneck_filters\n", "        \n", "        for level in range(levels):\n", "            out_channels = base_filters * (2 ** (levels - level - 1))\n", "            \n", "            # Upsample module\n", "            if is_3d:\n", "                upsample = nn.ConvTranspose3d(\n", "                    current_channels, out_channels, \n", "                    kernel_size=2, stride=2\n", "                )\n", "            else:\n", "                upsample = nn.ConvTranspose2d(\n", "                    current_channels, out_channels, \n", "                    kernel_size=2, stride=2\n", "                )\n", "            self.upsamples.append(upsample)\n", "            \n", "            # Decoder block (with attention if specified)\n", "            decoder_input = out_channels * 2  # Concatenated with encoder features\n", "            decoder = DecoderBlock(\n", "                decoder_input, out_channels,\n", "                batch_norm=batch_norm, use_residual=use_residual,\n", "                use_attention=use_attention, dropout=dropout,\n", "                is_3d=is_3d, use_dilated_conv=(use_dilated_conv and level < 2)\n", "            )\n", "            self.decoders.append(decoder)\n", "            current_channels = out_channels\n", "        \n", "        # Output heads for multiple resolutions\n", "        self.output_heads = nn.ModuleList()\n", "        for level in range(levels):\n", "            channels = base_filters * (2 ** (levels - level - 1))\n", "            self.output_heads.append(ConvModule(channels, 1, kernel_size=1))\n", "    \n", "    def _make_encoder_block(self, in_channels, out_channels, batch_norm=True, \n", "                          use_residual=True, dropout=0.1, use_dilated_conv=False):\n", "        \"\"\"Create an encoder block\"\"\"\n", "        if self.is_3d and in_channels >= 32 and use_dilated_conv:\n", "            # Use anisotropic convolution for 3D data\n", "            block = AnisotropicConv3d(in_channels, out_channels)\n", "            if use_residual and in_channels == out_channels:\n", "                return ResidualBlock(block, in_channels)\n", "            return block\n", "        \n", "        # Standard convolution blocks\n", "        ConvModule = nn.Conv3d if self.is_3d else nn.Conv2d\n", "        BatchNormModule = nn.BatchNorm3d if self.is_3d else nn.BatchNorm2d\n", "        \n", "        layers = []\n", "        \n", "        # First convolution\n", "        if use_dilated_conv and in_channels >= 32:\n", "            # Use mixed scale dilated convolution for deeper layers\n", "            layers.append(MixedScaleDilatedConv(\n", "                in_channels, out_channels, is_3d=self.is_3d))\n", "        else:\n", "            # Standard convolution\n", "            layers.append(ConvModule(in_channels, out_channels, kernel_size=3, padding=1))\n", "            if batch_norm:\n", "                layers.append(BatchNormModule(out_channels))\n", "            layers.append(nn.ReLU(inplace=True))\n", "            layers.append(nn.Dropout(dropout))\n", "            \n", "            # Second convolution\n", "            layers.append(ConvModule(out_channels, out_channels, kernel_size=3, padding=1))\n", "            if batch_norm:\n", "                layers.append(BatchNormModule(out_channels))\n", "            layers.append(nn.ReLU(inplace=True))\n", "        \n", "        # Return block with residual connection if specified\n", "        if use_residual and in_channels == out_channels:\n", "            return ResidualBlock(nn.Sequential(*layers), in_channels)\n", "        else:\n", "            return nn.Sequential(*layers)\n", "    \n", "    def forward(self, x):\n", "        \"\"\"TorchScript-compatible forward pass for multi-scale heatmaps\"\"\"\n", "        # Store encoder outputs for skip connections\n", "        encoder_features = []\n", "        \n", "        # Encoder path\n", "        for encoder in self.encoders:\n", "            x = encoder(x)\n", "            encoder_features.append(x)\n", "            \n", "            # Apply pooling for next level\n", "            pool = nn.MaxPool3d(kernel_size=2) if self.is_3d else nn.MaxPool2d(kernel_size=2)\n", "            x = pool(x)\n", "        \n", "        # Bottleneck\n", "        x = self.bottleneck(x)\n", "        \n", "        # Decoder path with multi-scale outputs\n", "        multi_scale_outputs = []\n", "        decoder_features = []\n", "        \n", "        for i in range(self.levels):\n", "            # Upsample\n", "            x = self.upsamples[i](x)\n", "            \n", "            # Skip connection from encoder\n", "            encoder_feature = encoder_features[-(i+1)]\n", "            \n", "            # FIXED: Always interpolate to match shapes - no conditional logic needed\n", "            # This eliminates <PERSON>r<PERSON><PERSON><PERSON><PERSON> while ensuring dimensions match\n", "            x = F.interpolate(\n", "                x, size=encoder_feature.shape[2:], \n", "                mode='trilinear' if self.is_3d else 'bilinear',\n", "                align_corners=False\n", "            )\n", "            \n", "            # Apply decoder block with skip connection\n", "            x = self.decoders[i](x, encoder_feature)\n", "            decoder_features.append(x)\n", "            \n", "            # Generate output at this scale\n", "            output = self.output_heads[i](x)\n", "            multi_scale_outputs.append(output)\n", "        \n", "        # Return in order from highest resolution to lowest\n", "        return multi_scale_outputs, decoder_features"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# =================================\n", "# Main Spot Detection Model\n", "# =================================\n", "\n", "class EnhancedInstanceSpotDetector(nn.Module):\n", "    def __init__(self, in_channels=1, base_filters=16, levels=3,\n", "                 is_3d=False, batch_norm=True, use_attention=True,\n", "                 use_residual=True, dropout=0.1,\n", "                 threshold=0.5, min_distance=2, threshold_abs=0.001, compute_flow=True,\n", "                 use_dilated_conv=True, use_group_norm=False, compute_offset=False):\n", "        super(EnhancedInstanceSpotDetector, self).__init__()\n", "        \n", "        # Store model configuration\n", "        self.is_3d = is_3d\n", "        self.compute_flow = compute_flow\n", "        self.compute_offset = compute_offset\n", "        self.min_distance = min_distance\n", "        self.threshold = threshold\n", "        self.threshold_abs = threshold_abs\n", "        self.current_epoch = 0\n", "        self.compute_multi_heatmaps = True\n", "        \n", "        # Create UNet backbone with multiple outputs at different resolutions\n", "        self.backbone = MultiscaleUNet(\n", "            in_channels=in_channels, \n", "            base_filters=base_filters,\n", "            levels=levels,\n", "            is_3d=is_3d, \n", "            batch_norm=batch_norm,\n", "            use_attention=use_attention,\n", "            use_residual=use_residual,\n", "            dropout=dropout,\n", "            use_dilated_conv=use_dilated_conv,\n", "            use_group_norm=use_group_norm\n", "        )\n", "        \n", "        # Determine actual channel depth of decoder_features[0]\n", "        # This is crucial - the UNet outputs features with base_filters*2^(levels-1) channels\n", "        # For a UNet with levels=3 and base_filters=16, decoder_features[0] has 64 channels\n", "        decoder_output_channels = base_filters * (2**(levels-1))\n", "        \n", "        # Flow branch implementation\n", "        ConvModule = nn.Conv3d if self.is_3d else nn.Conv2d\n", "        BatchNormModule = nn.BatchNorm3d if self.is_3d else nn.BatchNorm2d\n", "        \n", "        if use_group_norm:\n", "            BatchNormModule = lambda num_features: nn.GroupNorm(min(8, num_features), num_features)\n", "        \n", "        # IMPORTANT: Fix input channels to match decoder_features[0]\n", "        if compute_flow:\n", "            self.flow_head = nn.Sequential(\n", "                # First layer now accepts decoder_output_channels instead of base_filters\n", "                ConvModule(decoder_output_channels, base_filters*2, kernel_size=3, padding=1),\n", "                BatchNormModule(base_filters*2),\n", "                nn.ReLU(inplace=True),\n", "                \n", "                ConvModule(base_filters*2, base_filters, kernel_size=3, padding=1),\n", "                BatchNormModule(base_filters),\n", "                nn.ReLU(inplace=True),\n", "                \n", "                ConvModule(base_filters, base_filters, kernel_size=3, padding=2, dilation=2),\n", "                BatchNormModule(base_filters),\n", "                nn.ReLU(inplace=True),\n", "                \n", "                ConvModule(base_filters, 2 if not self.is_3d else 3, kernel_size=3, padding=1)\n", "            )\n", "        \n", "        # Fix the offset_head input channels too\n", "        if compute_offset:\n", "            self.offset_head = nn.Sequential(\n", "                # First layer now accepts decoder_output_channels\n", "                ConvModule(decoder_output_channels, 32, kernel_size=3, padding=1),\n", "                BatchNormModule(32),\n", "                nn.ReLU(inplace=True),\n", "                nn.Dropout(0.1),\n", "                ConvModule(32, 32, kernel_size=3, padding=1),\n", "                BatchNormModule(32),\n", "                nn.ReLU(inplace=True),\n", "                ConvModule(32, 2 if not self.is_3d else 3, kernel_size=1)\n", "            )\n", "        \n", "        # Heatmap fusion with correct channels\n", "        self.heatmap_fusion = nn.Sequential(\n", "            ConvModule(levels, base_filters, kernel_size=3, padding=1),\n", "            BatchNormModule(base_filters),\n", "            nn.ReLU(inplace=True),\n", "            ConvModule(base_filters, 1, kernel_size=1)\n", "        )\n", "    \n", "    def stereographic_projection(self, flow):\n", "        \"\"\"Convert from offset vectors to stereographic projection\"\"\"\n", "        # Normalize flow vectors\n", "        flow_magnitude = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True) + 1e-10)\n", "        normalized_flow = flow / (flow_magnitude + 1e-10)\n", "        \n", "        # Apply stereographic projection formula\n", "        return normalized_flow\n", "    \n", "    def forward(self, x):\n", "        \"\"\"TorchScript-compatible forward pass with outputs for advanced losses\"\"\"\n", "        # Get input size and device\n", "        input_size = x.shape[2:] \n", "        device = x.device\n", "        \n", "        # Extract features using backbone\n", "        multi_level_heatmaps, decoder_features = self.backbone(x)\n", "        \n", "        # Get highest resolution heatmap\n", "        final_heatmap = multi_level_heatmaps[0]\n", "        \n", "        # Always resize to input size\n", "        final_heatmap = F.interpolate(\n", "            final_heatmap, size=input_size, \n", "            mode='trilinear' if self.is_3d else 'bilinear',\n", "            align_corners=False\n", "        )\n", "        \n", "        # Initialize flow\n", "        flow_channels = 3 if self.is_3d else 2\n", "        flow = torch.zeros(x.shape[0], flow_channels, *input_size, device=device)\n", "        \n", "        # Compute flow if requested\n", "        if self.compute_flow and hasattr(self, 'flow_head') and self.flow_head is not None:\n", "            flow = self.flow_head(decoder_features[0])\n", "            # Always resize flow to input size\n", "            flow = F.interpolate(\n", "                flow, size=input_size, \n", "                mode='trilinear' if self.is_3d else 'bilinear',\n", "                align_corners=False\n", "            )\n", "        \n", "        # Return different outputs based on mode\n", "        if self.training:\n", "            # During training, return all tensors needed for advanced losses\n", "            # Create a comprehensive output dictionary with all loss components\n", "            return {\n", "                'heatmaps': final_heatmap,\n", "                'flow': flow,\n", "                'multi_heatmaps': multi_level_heatmaps,  # For multi-scale loss\n", "                'features': decoder_features[0],  # For contrastive loss\n", "                'all_features': decoder_features  # For dense region loss\n", "            }\n", "        else:\n", "            # For inference, return minimal needed outputs\n", "            # Post-processing will fill in instance_labels and spot_coords\n", "            return {\n", "                'heatmaps': final_heatmap, \n", "                'flow': flow,\n", "                'instance_labels': torch.zeros(x.shape[0], 1, *input_size, device=device),\n", "                'spot_coords': torch.zeros(0, 2, device=device)\n", "            }\n", "    \n", "    @torch.no_grad()\n", "    def post_process_inference(self, outputs):\n", "        \"\"\"Generate instance labels and spot coordinates from model outputs\n", "        \n", "        Args:\n", "            outputs: Dictionary containing 'heatmaps' and 'flow' tensors\n", "            \n", "        Returns:\n", "            Updated outputs with 'instance_labels' and 'spot_coords'\n", "        \"\"\"\n", "        # Extract components\n", "        heatmap = outputs['heatmaps']\n", "        flow = outputs['flow']\n", "        batch_size = heatmap.shape[0]\n", "        device = heatmap.device\n", "        \n", "        # Create result containers\n", "        all_instance_labels = []\n", "        all_spot_coords = []\n", "        \n", "        # Process each batch item\n", "        for b in range(batch_size):\n", "            # Convert to numpy for processing\n", "            heatmap_np = heatmap[b, 0].detach().cpu().numpy()\n", "            \n", "            # Find peaks using skimage's peak_local_max\n", "            coords = peak_local_max(\n", "                heatmap_np,\n", "                min_distance=self.min_distance,\n", "                threshold_abs=self.threshold_abs,\n", "                threshold_rel=self.threshold,\n", "                exclude_border=False\n", "            )\n", "            \n", "            # Create instance label map - each spot gets unique ID\n", "            h, w = heatmap_np.shape\n", "            instance_map = np.zeros((h, w), dtype=np.int32)\n", "            \n", "            # Label each peak with unique ID\n", "            for i, (y, x) in enumerate(coords):\n", "                # Check bounds\n", "                if 0 <= y < h and 0 <= x < w:\n", "                    instance_map[y, x] = i + 1  # Start from 1, 0 is background\n", "            \n", "            # Optional: Use flow field to refine spot positions\n", "            if self.compute_flow and flow is not None and len(coords) > 0:\n", "                flow_np = flow[b].detach().cpu().numpy()  # [2,H,W]\n", "                flow_np = np.transpose(flow_np, (1, 2, 0))  # [H,W,2]\n", "                \n", "                refined_coords = []\n", "                for y, x in coords:\n", "                    if 0 <= y < flow_np.shape[0] and 0 <= x < flow_np.shape[1]:\n", "                        # Flow vectors point from boundary to center\n", "                        # So subtract flow to get to center\n", "                        dy, dx = flow_np[int(y), int(x)]\n", "                        refined_y = y - dy\n", "                        refined_x = x - dx\n", "                        \n", "                        # Keep within image bounds\n", "                        refined_y = max(0, min(h-1, refined_y))\n", "                        refined_x = max(0, min(w-1, refined_x))\n", "                        \n", "                        refined_coords.append([refined_y, refined_x])\n", "                    else:\n", "                        refined_coords.append([y, x])\n", "                \n", "                # Use refined coordinates\n", "                if len(refined_coords) > 0:\n", "                    coords = np.array(refined_coords)\n", "            \n", "            # Optional: Dilate spots to create larger instances\n", "            if len(coords) > 0:\n", "                from scipy import ndimage\n", "                instance_map = ndimage.grey_dilation(instance_map, size=(3, 3))\n", "            \n", "            # Convert to tensor\n", "            instance_tensor = torch.from_numpy(instance_map).unsqueeze(0).to(device)  # [1,H,W]\n", "            if len(coords) > 0:\n", "                coords_tensor = torch.from_numpy(coords.astype(np.float32)).to(device)\n", "            else:\n", "                coords_tensor = torch.zeros(0, 2, device=device)\n", "            \n", "            all_instance_labels.append(instance_tensor)\n", "            all_spot_coords.append(coords_tensor)\n", "        \n", "        # Combine batch results\n", "        instance_labels = torch.stack(all_instance_labels, dim=0)  # [B,1,H,W]\n", "        \n", "        # Update outputs\n", "        outputs['instance_labels'] = instance_labels\n", "        outputs['spot_coords'] = all_spot_coords[0] if batch_size > 0 else torch.zeros(0, 2, device=device)\n", "        \n", "        return outputs\n", "            \n", "# Move all numpy operations outside the forward method\n", "def _extract_spot_coordinates(self, heatmap, flow=None, threshold=0.5, threshold_abs=0.0, min_distance=2):\n", "    \"\"\"Extract spot coordinates from heatmaps (only used during evaluation)\"\"\"\n", "    if not self.training:\n", "        # This is now only called in eval mode, so NumPy conversion is OK\n", "        heatmap_np = heatmap.detach().cpu().numpy()\n", "        # Rest of the extraction code...\n", "    else:\n", "        # Return empty list during training to avoid NumPy conversion\n", "        return []"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# =================================\n", "# Loss Functions\n", "# =================================\n", "\n", "\n", "def focal_loss(pred, target, alpha=0.25, gamma=2.0, flow=None, flow_weight=0.0):\n", "    \"\"\"Focal loss specialized for spot detection\n", "    \n", "    Args:\n", "        pred: Dictionary with 'heatmaps' and optionally 'flow'\n", "        target: Target heatmap tensor\n", "        alpha: Weighing factor for positive class\n", "        gamma: Focusing parameter (higher = more focus on hard examples)\n", "        flow: Optional flow tensor\n", "        flow_weight: Weight for flow loss component\n", "        \n", "    Returns:\n", "        Loss value, dictionary of loss components\n", "    \"\"\"\n", "    # Extract heatmap from prediction dictionary\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "        flow = pred.get('flow', flow)\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Ensure matching sizes\n", "    if heatmap.shape[2:] != target.shape[2:]:\n", "        target = F.interpolate(target, size=heatmap.shape[2:], mode='nearest')\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Calculate focal loss\n", "    # Binary cross entropy\n", "    bce = -(target * torch.log(heatmap + 1e-7) + \n", "            (1 - target) * torch.log(1 - heatmap + 1e-7))\n", "    \n", "    # Apply focal weighting\n", "    p_t = heatmap * target + (1 - heatmap) * (1 - target)\n", "    focal_weight = (1 - p_t) ** gamma\n", "    \n", "    # Apply alpha weighting for class imbalance\n", "    alpha_weight = alpha * target + (1 - alpha) * (1 - target)\n", "    \n", "    # Special handling for rare positive pixels\n", "    # Calculate positive pixel ratio\n", "    pos_ratio = target.mean()\n", "    \n", "    # Adjust alpha based on positive pixel ratio\n", "    if pos_ratio < 0.01:  # Very few positives\n", "        pos_adjust = 5.0  # Boost positive weights significantly\n", "    elif pos_ratio < 0.05:  # Few positives\n", "        pos_adjust = 3.0  # Moderate boost\n", "    else:  # Normal range\n", "        pos_adjust = 1.0  # No adjustment\n", "    \n", "    # Apply adjustment to positive weights\n", "    adjusted_weight = alpha_weight * (1 + (pos_adjust - 1) * target)\n", "    \n", "    # Combined weighting\n", "    loss = (focal_weight * adjusted_weight * bce).mean()\n", "    \n", "    # Flow loss component\n", "    flow_loss = 0.0\n", "    if flow is not None and flow_weight > 0:\n", "        try:\n", "            # Only calculate flow loss on positive pixels\n", "            mask = target > 0.5\n", "            \n", "            if mask.sum() > 0:\n", "                # Flow magnitude regularization\n", "                flow_mag = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True))\n", "                flow_loss = flow_weight * (flow_mag * mask).mean()\n", "        except Exception as e:\n", "            print(f\"Flow loss error: {e}\")\n", "    \n", "    # Final loss\n", "    total_loss = loss + flow_loss\n", "    \n", "    return total_loss, {\n", "        'focal_loss': loss.item(),\n", "        'flow_loss': flow_loss if isinstance(flow_loss, float) else flow_loss.item()\n", "    }\n", "    \n", "def dice_loss(pred, target, smooth=1.0, flow=None, flow_weight=0.0):\n", "    \"\"\"Dice loss with improved numerical stability\"\"\"\n", "    # Extract heatmap from prediction dict\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "        flow = pred.get('flow', flow)\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Apply sigmoid if needed (CRITICAL)\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Debug information\n", "    eps = 1e-7\n", "    target_sum = target.sum().item()\n", "    if target_sum < eps:\n", "        print(\"WARNING: Target sum is zero - no positive pixels in batch\")\n", "    \n", "    # Calculate intersection and union with numerical stability\n", "    intersection = torch.sum(heatmap * target) + smooth\n", "    denominator = torch.sum(heatmap) + torch.sum(target) + smooth\n", "    \n", "    # Dice coefficient\n", "    dice = 2. * intersection / denominator\n", "    \n", "    # Debug stats\n", "    with torch.no_grad():\n", "        pred_positive = (heatmap > 0.5).sum().item()\n", "        target_positive = (target > 0.5).sum().item()\n", "        print(f\"Pred>0.5: {pred_positive}, Target>0.5: {target_positive}, Dice: {dice.item()}\")\n", "    \n", "    # Calculate loss\n", "    dice_loss_val = 1 - dice\n", "    \n", "    # Handle flow loss if provided\n", "    flow_loss_val = 0.0\n", "    if flow is not None and flow_weight > 0:\n", "        # Calculate flow loss only for positive pixels\n", "        mask = (target > 0.5).float()\n", "        if mask.sum() > eps:\n", "            flow_magnitude = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True) + eps)\n", "            flow_loss_val = flow_weight * ((flow_magnitude * mask).sum() / (mask.sum() + eps))\n", "    \n", "    # Final loss\n", "    total_loss = dice_loss_val + flow_loss_val\n", "    \n", "    return total_loss, {\n", "        'dice_loss': dice_loss_val.item(),\n", "        'flow_loss': flow_loss_val.item() if isinstance(flow_loss_val, torch.Tensor) else flow_loss_val\n", "    }\n", "\n", "def instance_aware_dice_loss(pred, target, smooth=1.0, distance_weight=0.2, flow_weight=0.2):\n", "    \"\"\"Dice loss with instance-awareness to improve spot separation\n", "    \n", "    Args:\n", "        pred: Dictionary with 'heatmaps' and optionally 'flow'\n", "        target: Target heatmap tensor\n", "        smooth: Smoothing factor\n", "        distance_weight: Weight for distance transform component\n", "        flow_weight: Weight for flow component\n", "        \n", "    Returns:\n", "        Loss value, dictionary of loss components\n", "    \"\"\"\n", "    # Extract heatmap from prediction dict\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "        flow = pred.get('flow')\n", "    else:\n", "        heatmap = pred\n", "        flow = None\n", "    \n", "    # Ensure matching sizes\n", "    if heatmap.shape[2:] != target.shape[2:]:\n", "        target = F.interpolate(target, size=heatmap.shape[2:], mode='nearest')\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Standard dice loss\n", "    intersection = torch.sum(heatmap * target)\n", "    dice = (2. * intersection + smooth) / (\n", "        torch.sum(heatmap) + torch.sum(target) + smooth\n", "    )\n", "    dice_loss_val = 1 - dice\n", "    \n", "    # Instance-aware component using distance transform\n", "    # This helps separate nearby spots by emphasizing boundaries\n", "    distance_loss = 0.0\n", "    if distance_weight > 0:\n", "        try:\n", "            # We need to process each batch item separately\n", "            batch_size = target.shape[0]\n", "            total_distance_loss = 0.0\n", "            \n", "            for b in range(batch_size):\n", "                # Convert target to numpy for distance transform\n", "                target_np = target[b, 0].detach().cpu().numpy()\n", "                \n", "                # Only proceed if we have positive pixels\n", "                if target_np.sum() > 0:\n", "                    # Get distance transform (distance to nearest zero)\n", "                    from scipy.ndimage import distance_transform_edt\n", "                    dist_transform = distance_transform_edt(target_np)\n", "                    \n", "                    # Normalize distances to [0,1] range\n", "                    if dist_transform.max() > 0:\n", "                        dist_transform = dist_transform / dist_transform.max()\n", "                    \n", "                    # Convert back to tensor\n", "                    dist_tensor = torch.from_numpy(dist_transform).to(target.device)[None]\n", "                    \n", "                    # Higher loss for errors near center of spots\n", "                    # This encourages precise localization\n", "                    weight_map = dist_tensor * 2 + 1  # Higher weight in center\n", "                    error = ((heatmap[b] - target[b]) * weight_map) ** 2\n", "                    total_distance_loss += error.mean()\n", "            \n", "            if batch_size > 0:\n", "                distance_loss = distance_weight * (total_distance_loss / batch_size)\n", "        except Exception as e:\n", "            print(f\"Distance loss error: {e}\")\n", "    \n", "    # Flow loss component\n", "    flow_loss = 0.0\n", "    if flow is not None and flow_weight > 0:\n", "        try:\n", "            # Only calculate flow loss for positive pixels\n", "            mask = target > 0.5\n", "            \n", "            if mask.sum() > 0:\n", "                # Simple L2 loss for flow magnitude\n", "                flow_mag = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True))\n", "                flow_loss = flow_weight * (flow_mag * mask).mean()\n", "        except Exception as e:\n", "            print(f\"Flow loss error: {e}\")\n", "    \n", "    # Combine all loss components\n", "    total_loss = dice_loss_val + distance_loss + flow_loss\n", "    \n", "    return total_loss, {\n", "        'dice_loss': dice_loss_val.item(),\n", "        'distance_loss': distance_loss if isinstance(distance_loss, float) else distance_loss.item(),\n", "        'flow_loss': flow_loss if isinstance(flow_loss, float) else flow_loss.item()\n", "    }\n", "    \n", "def weighted_dice_loss(pred, target, smooth=1.0, pos_weight=100.0, flow=None, flow_weight=0.0):\n", "    \"\"\"Dice loss with heavy weighting for positive pixels\"\"\"\n", "    # Extract heatmap from prediction dict\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "        flow = pred.get('flow', flow)\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Apply weighting to handle class imbalance\n", "    weighted_target = target * (pos_weight - 1.0) + 1.0\n", "    \n", "    # Calculate intersection with weighting\n", "    intersection = torch.sum(heatmap * target * weighted_target)\n", "    dice_coef = (2. * intersection + smooth) / (\n", "        torch.sum(heatmap * weighted_target) + torch.sum(target * weighted_target) + smooth\n", "    )\n", "    \n", "    dice_loss_val = 1 - dice_coef\n", "    \n", "    return dice_loss_val, {\n", "        'dice_loss': dice_loss_val.item(),\n", "        'flow_loss': 0.0\n", "    }\n", "\n", "def bce_dice_loss(pred, target, smooth=1.0, pos_weight=100.0):\n", "    \"\"\"Combined BCE and Dice loss with class weighting\"\"\"\n", "    # Extract heatmap\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Binary Cross Entropy with heavy weighting for positive class\n", "    bce_weight = target * (pos_weight - 1.0) + 1.0\n", "    bce = F.binary_cross_entropy(heatmap, target, weight=bce_weight, reduction='mean')\n", "    \n", "    # Weighted Dice Loss\n", "    intersection = torch.sum(heatmap * target)\n", "    dice_coef = (2. * intersection + smooth) / (\n", "        torch.sum(heatmap) + torch.sum(target) + smooth\n", "    )\n", "    dice_loss_val = 1 - dice_coef\n", "    \n", "    # Combined loss - more weight on BCE for stronger gradients\n", "    combined_loss = 0.7 * bce + 0.3 * dice_loss_val\n", "    \n", "    return combined_loss, {\n", "        'bce_loss': bce.item(),\n", "        'dice_loss': dice_loss_val.item()\n", "    }\n", "\n", "def focal_dice_loss(pred, target, alpha=0.5, gamma=2.0, smooth=1.0, flow_weight=0.2):\n", "    \"\"\"Combined focal and dice loss with improved class balancing\n", "    \n", "    Args:\n", "        pred: Dictionary with 'heatmaps' and optionally 'flow'\n", "        target: Target heatmap tensor\n", "        alpha: Focal loss alpha parameter (weighs positive pixels)\n", "        gamma: Focal loss gamma parameter (focuses on hard examples)\n", "        smooth: Smoothing factor for Dice coefficient\n", "        flow_weight: Weight of flow loss component\n", "        \n", "    Returns:\n", "        Total loss value, dictionary of loss components\n", "    \"\"\"\n", "    # Extract heatmap from prediction dict\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "        flow = pred.get('flow')\n", "    else:\n", "        heatmap = pred\n", "        flow = None\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap_prob = torch.sigmoid(heatmap)\n", "    else:\n", "        heatmap_prob = heatmap\n", "    \n", "    # Simple BCE loss - helps early training\n", "    eps = 1e-6\n", "    bce = -(target * torch.log(heatmap_prob + eps) + (1 - target) * torch.log(1 - heatmap_prob + eps))\n", "    bce_loss = bce.mean()\n", "    \n", "    # Apply focal weighting\n", "    p_t = heatmap_prob * target + (1 - heatmap_prob) * (1 - target)\n", "    focal_weight = (1 - p_t) ** gamma\n", "    alpha_weight = alpha * target + (1 - alpha) * (1 - target)\n", "    focal_loss_val = (focal_weight * alpha_weight * bce).mean()\n", "    \n", "    # Dice component\n", "    intersection = torch.sum(heatmap_prob * target)\n", "    dice_coef = (2. * intersection + smooth) / (\n", "        torch.sum(heatmap_prob) + torch.sum(target) + smooth\n", "    )\n", "    dice_loss_val = 1 - dice_coef\n", "    \n", "    # Combine with BCE dominant early in training\n", "    total_loss = 0.3 * bce_loss + 0.2 * focal_loss_val + 0.5 * dice_loss_val\n", "    \n", "    # Add flow loss if available\n", "    flow_loss_val = 0.0\n", "    if flow is not None and flow_weight > 0:\n", "        mask = (target > 0.5).float()\n", "        if mask.sum() > 0:\n", "            flow_magnitude = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True) + eps)\n", "            flow_loss_val = flow_weight * ((flow_magnitude * mask).sum() / (mask.sum() + eps))\n", "            total_loss = total_loss + flow_loss_val\n", "    \n", "    return total_loss, {\n", "        'bce_loss': bce_loss.item(),\n", "        'focal_loss': focal_loss_val.item(),\n", "        'dice_loss': dice_loss_val.item(),\n", "        'flow_loss': flow_loss_val.item() if isinstance(flow_loss_val, torch.Tensor) else flow_loss_val\n", "    }\n", "\n", "def optimized_flow_loss(flow_pred, target_mask):\n", "    \"\"\"Safe flow loss calculation with dimension handling\"\"\"\n", "    # Get device from flow tensor\n", "    device = flow_pred.device\n", "    batch_size = flow_pred.shape[0]\n", "    flow_loss = torch.tensor(0.0, device=device)\n", "    valid_batches = 0\n", "    \n", "    # Make sure target mask has same spatial dimensions as flow\n", "    if flow_pred.shape[2:] != target_mask.shape[2:]:\n", "        target_mask = F.interpolate(\n", "            target_mask, \n", "            size=flow_pred.shape[2:],\n", "            mode='nearest'\n", "        )\n", "    \n", "    # Process each batch item\n", "    for b in range(batch_size):\n", "        # Get binary mask - handle different mask shapes\n", "        if target_mask.dim() == 4 and target_mask.shape[1] == 1:\n", "            mask = (target_mask[b, 0] > 0.5)\n", "        else:\n", "            mask = (target_mask[b] > 0.5)\n", "        \n", "        # Skip if mask is too small\n", "        if mask.sum() < 5:\n", "            continue\n", "            \n", "        # Get flow components\n", "        flow_x = flow_pred[b, 0]  # x component\n", "        flow_y = flow_pred[b, 1]  # y component\n", "        \n", "        # Skip if dimensions are too small\n", "        if flow_x.size(0) <= 1 or flow_x.size(1) <= 1:\n", "            continue\n", "            \n", "        # Calculate divergence safely\n", "        try:\n", "            dx = flow_x[:, 1:] - flow_x[:, :-1]\n", "            dy = flow_y[1:, :] - flow_y[:-1, :]\n", "            \n", "            # Create valid mask for gradient calculations\n", "            h, w = min(dy.size(0), mask.size(0)-1), min(dx.size(1), mask.size(1)-1)\n", "            div_mask = mask[:h, :w].float()\n", "            \n", "            # Skip if mask is too small after cropping\n", "            if div_mask.sum() < 1:\n", "                continue\n", "                \n", "            # Use properly sized tensors\n", "            dx_crop = dx[:h, :w]\n", "            dy_crop = dy[:h, :w]\n", "            \n", "            # Calculate divergence loss\n", "            div_loss = (dx_crop + dy_crop) * div_mask\n", "            div_loss = div_loss.sum() / (div_mask.sum() + 1e-8)\n", "            \n", "            # Add regularization\n", "            flow_mag = torch.sqrt(flow_x**2 + flow_y**2 + 1e-10)\n", "            mag_loss = (flow_mag * mask.float()).sum() / (mask.sum() + 1e-8) * 0.1\n", "            \n", "            flow_loss += div_loss + mag_loss\n", "            valid_batches += 1\n", "            \n", "        except Exception as e:\n", "            print(f\"Flow divergence calculation error: {e}\")\n", "            continue\n", "    \n", "    # Return average loss or zero\n", "    if valid_batches > 0:\n", "        return flow_loss / valid_batches\n", "    else:\n", "        return torch.tensor(0.0, device=device)\n", "\n", "\n", "def extract_spot_centers(masks):\n", "    \"\"\"\n", "    Extract spot centers from binary masks.\n", "    \n", "    Args:\n", "        masks: Binary masks tensor [B, H, W] or [B, 1, H, W] or [B, D, H, W]\n", "        \n", "    Returns:\n", "        list: List of spot centers for each batch item\n", "    \"\"\"\n", "    # Initialize list to store centers for each batch item\n", "    batch_centers = []\n", "    \n", "    # Process each batch item\n", "    for b in range(masks.shape[0]):\n", "        # Extract mask for this batch item\n", "        if masks.dim() == 4 and masks.shape[1] == 1:  # [B, 1, H, W]\n", "            mask = masks[b, 0].detach().cpu().numpy()\n", "        elif masks.dim() == 3:  # [B, H, W]\n", "            mask = masks[b].detach().cpu().numpy()\n", "        elif masks.dim() == 5 and masks.shape[1] == 1:  # [B, 1, D, H, W]\n", "            mask = masks[b, 0].detach().cpu().numpy()\n", "        else:\n", "            mask = masks[b].detach().cpu().numpy()\n", "        \n", "        # Find connected components\n", "        labeled, num_spots = ndi.label(mask > 0.5)\n", "        centers = []\n", "        \n", "        # Find center of each spot\n", "        for i in range(1, num_spots + 1):\n", "            # Get coordinates of pixels in this spot\n", "            coords = np.where(labeled == i)\n", "            \n", "            # Calculate center coordinates\n", "            if len(coords) == 2:  # 2D\n", "                center_y = np.mean(coords[0])\n", "                center_x = np.mean(coords[1])\n", "                centers.append([center_y, center_x])\n", "            elif len(coords) == 3:  # 3D\n", "                center_z = np.mean(coords[0])\n", "                center_y = np.mean(coords[1])\n", "                center_x = np.mean(coords[2])\n", "                centers.append([center_z, center_y, center_x])\n", "        \n", "        # Add centers for this batch item\n", "        batch_centers.append(centers)\n", "    \n", "    return batch_centers\n", "\n", "def compute_flow_target(shape, centers):\n", "    \"\"\"\n", "    Compute target flow field pointing to spot centers\n", "    \n", "    Args:\n", "        shape: Shape of the target (H, W) for 2D or (D, H, W) for 3D\n", "        centers: List of spot center coordinates\n", "        \n", "    Returns:\n", "        flow_target: Flow field pointing to nearest spot center\n", "    \"\"\"\n", "    # Handle 2D and 3D cases\n", "    is_3d = len(shape) == 3\n", "    \n", "    if is_3d:\n", "        d, h, w = shape\n", "        flow = np.zeros((3, d, h, w), dtype=np.float32)\n", "        \n", "        # Create coordinate grids\n", "        z, y, x = np.meshgrid(\n", "            np.arange(d, dtype=np.float32),\n", "            np.arange(h, dtype=np.float32),\n", "            np.arange(w, dtype=np.float32),\n", "            indexing='ij'\n", "        )\n", "        \n", "        if len(centers) == 0:\n", "            return torch.from_numpy(flow)\n", "            \n", "        # For each pixel, find the nearest center\n", "        min_dist = np.ones((d, h, w)) * np.inf\n", "        \n", "        for center in centers:\n", "            # Calculate distance to this center\n", "            cz, cy, cx = center\n", "            dist = np.sqrt((z - cz)**2 + (y - cy)**2 + (x - cx)**2)\n", "            \n", "            # Update flow where this center is closer\n", "            update = dist < min_dist\n", "            min_dist[update] = dist[update]\n", "            \n", "            # Set flow vectors pointing to this center\n", "            flow[0][update] = cz - z[update]\n", "            flow[1][update] = cy - y[update]\n", "            flow[2][update] = cx - x[update]\n", "            \n", "    else:  # 2D case\n", "        h, w = shape\n", "        flow = np.zeros((2, h, w), dtype=np.float32)\n", "        \n", "        # Create coordinate grids\n", "        y, x = np.meshgrid(\n", "            np.arange(h, dtype=np.float32),\n", "            np.arange(w, dtype=np.float32),\n", "            indexing='ij'\n", "        )\n", "        \n", "        if len(centers) == 0:\n", "            return torch.from_numpy(flow)\n", "            \n", "        # For each pixel, find the nearest center\n", "        min_dist = np.ones((h, w)) * np.inf\n", "        \n", "        for center in centers:\n", "            # Calculate distance to this center\n", "            cy, cx = center\n", "            dist = np.sqrt((y - cy)**2 + (x - cx)**2)\n", "            \n", "            # Update flow where this center is closer\n", "            update = dist < min_dist\n", "            min_dist[update] = dist[update]\n", "            \n", "            # Set flow vectors pointing to this center\n", "            flow[0][update] = cy - y[update]\n", "            flow[1][update] = cx - x[update]\n", "    \n", "    # Normalize vectors (keeping zero vectors as zero)\n", "    norm = np.sqrt(np.sum(flow**2, axis=0))\n", "    mask = norm > 1e-8\n", "    for c in range(flow.shape[0]):\n", "        flow[c][mask] = flow[c][mask] / norm[mask]\n", "    \n", "    return torch.from_numpy(flow)\n", "\n", "\n", "def compute_spot_flow_loss(flow_pred, target_mask, batch_centers):\n", "    \"\"\"\n", "    Compute flow loss for spot detection\n", "    \n", "    Args:\n", "        flow_pred: Predicted flow field [B, C, H, W] or [B, C, D, H, W]\n", "        target_mask: Target binary mask [B, 1, H, W] or [B, 1, D, H, W]\n", "        batch_centers: List of spot centers for each batch item\n", "        \n", "    Returns:\n", "        flow_loss: Mean absolute error between predicted flow and target flow\n", "    \"\"\"\n", "    device = flow_pred.device\n", "    batch_size = flow_pred.shape[0]\n", "    flow_loss = torch.tensor(0.0, device=device)\n", "    valid_batches = 0\n", "    \n", "    for b in range(batch_size):\n", "        # Skip if batch index is out of range or no centers found\n", "        if b >= len(batch_centers) or len(batch_centers[b]) == 0:\n", "            continue\n", "            \n", "        # Get centers for this batch item\n", "        centers = batch_centers[b]\n", "        \n", "        # Create target flow field\n", "        if flow_pred.dim() == 5:  # 3D case\n", "            _, c, d, h, w = flow_pred.shape\n", "            flow_target = compute_flow_target((d, h, w), centers)\n", "        else:  # 2D case\n", "            _, c, h, w = flow_pred.shape\n", "            flow_target = compute_flow_target((h, w), centers)\n", "        \n", "        # Move to device and add batch dimension\n", "        flow_target = flow_target.to(device).unsqueeze(0)\n", "        \n", "        # Create valid mask (only spots and their vicinity)\n", "        valid_mask = target_mask[b:b+1] > 0.5\n", "        \n", "        # Expand mask if too small for meaningful comparison\n", "        if valid_mask.sum() < 10:\n", "            # Dilate mask to include more pixels\n", "            kernel_size = 5\n", "            valid_mask = F.max_pool2d(\n", "                valid_mask.float(), \n", "                kernel_size=kernel_size, \n", "                stride=1, \n", "                padding=kernel_size//2\n", "            ) > 0.5\n", "        \n", "        # Skip if no valid pixels\n", "        if valid_mask.sum() == 0:\n", "            continue\n", "            \n", "        # Calculate flow loss (direction alignment)\n", "        pred_flow = flow_pred[b:b+1]\n", "        \n", "        # Normalize vectors for cosine similarity\n", "        norm_pred = F.normalize(pred_flow, dim=1, eps=1e-8)\n", "        norm_target = F.normalize(flow_target, dim=1, eps=1e-8)\n", "        \n", "        # Cosine similarity (1 = aligned, -1 = opposite)\n", "        similarity = (norm_pred * norm_target).sum(dim=1, keepdim=True)\n", "        \n", "        # Convert to loss (0 = aligned, 2 = opposite)\n", "        direction_loss = 1.0 - similarity\n", "        \n", "        # Only consider loss in valid regions\n", "        masked_loss = (direction_loss * valid_mask.float()).sum() / (valid_mask.float().sum() + 1e-8)\n", "        \n", "        flow_loss += masked_loss\n", "        valid_batches += 1\n", "    \n", "    # Average loss across batches\n", "    if valid_batches > 0:\n", "        return flow_loss / valid_batches\n", "    else:\n", "        return torch.tensor(0.0, device=device)\n", "\n", "def contrastive_spot_loss(features, masks, margin=1.0):\n", "    \"\"\"Contrastive loss to better distinguish close spots\"\"\"\n", "    # Extract feature vectors for each spot center\n", "    spot_centers = extract_spot_centers(masks)\n", "    if not any(spot_centers):\n", "        return torch.tensor(0.0, device=features.device)\n", "        \n", "    # Sample features at center points\n", "    spot_features = []\n", "    for batch_idx, centers in enumerate(spot_centers):\n", "        if not centers:\n", "            continue\n", "            \n", "        # Get feature vectors at center locations\n", "        batch_features = []\n", "        for center in centers:\n", "            if len(center) == 3:  # 3D\n", "                z, y, x = int(center[0]), int(center[1]), int(center[2])\n", "                if 0 <= y < features.shape[2] and 0 <= x < features.shape[3] and 0 <= z < features.shape[4]:\n", "                    feature = features[batch_idx, :, y, x, z]\n", "                    batch_features.append(feature)\n", "            else:  # 2D\n", "                y, x = int(center[0]), int(center[1])\n", "                if 0 <= y < features.shape[2] and 0 <= x < features.shape[3]:\n", "                    feature = features[batch_idx, :, y, x]\n", "                    batch_features.append(feature)\n", "        \n", "        if batch_features:\n", "            spot_features.extend(batch_features)\n", "    \n", "    # If not enough features, skip contrastive loss\n", "    if len(spot_features) < 2:\n", "        return torch.tensor(0.0, device=features.device)\n", "        \n", "    # Stack all feature vectors\n", "    feature_vecs = torch.stack(spot_features)\n", "    \n", "    # Normalize feature vectors\n", "    feature_vecs = F.normalize(feature_vecs, p=2, dim=1)\n", "    \n", "    # Compute pairwise distances\n", "    dist_matrix = torch.cdist(feature_vecs, feature_vecs)\n", "    \n", "    # Create contrastive loss: push apart close spots in feature space\n", "    n = len(feature_vecs)\n", "    loss = 0.0\n", "    \n", "    # For each pair of spots\n", "    num_close_pairs = 0\n", "    for i in range(n):\n", "        for j in range(i+1, n):\n", "            # Compute spatial distance \n", "            dist = dist_matrix[i, j]\n", "            \n", "            # If distance is small, apply contrastive loss\n", "            if dist < margin:\n", "                loss += torch.max(torch.tensor(0.0, device=dist.device), \n", "                                margin - dist)\n", "                num_close_pairs += 1\n", "    \n", "    # Average loss over close pairs\n", "    if num_close_pairs > 0:\n", "        loss /= num_close_pairs\n", "        \n", "    return loss\n", "\n", "def dense_region_sampling_loss(outputs, masks, oversampling_factor=2.0):\n", "    \"\"\"Augmented loss focusing on dense regions with close spots\"\"\"\n", "    # Get batch and device info\n", "    batch_size = masks.shape[0]\n", "    device = masks.device\n", "    heatmap = outputs[\"heatmaps\"]\n", "    \n", "    # Identify dense regions in each mask\n", "    dense_weights = torch.ones_like(masks)\n", "    \n", "    for b in range(batch_size):\n", "        # Get this mask\n", "        mask = masks[b, 0]\n", "        \n", "        # Create a density map using distance transform\n", "        binary = (mask > 0.5).float()\n", "        \n", "        # Move to CPU for scipy operations\n", "        binary_np = binary.cpu().numpy()\n", "        labeled, num = ndi.label(binary_np)\n", "        \n", "        if num > 1:\n", "            # Weight by inverse distance between components\n", "            dist = ndi.distance_transform_edt(labeled == 0)\n", "            weight_map = 1.0 / (1.0 + dist)\n", "            dense_weights[b, 0] = torch.from_numpy(weight_map).to(device) * oversampling_factor\n", "    \n", "    # Calculate weighted dice loss\n", "    dims = [1, 2, 3] if masks.dim() == 4 else [1, 2]\n", "    \n", "    intersection = torch.sum(heatmap * masks * dense_weights, dim=dims)\n", "    pred_sum = torch.sum(heatmap * dense_weights, dim=dims)\n", "    target_sum = torch.sum(masks * dense_weights, dim=dims)\n", "    \n", "    dice = (2.0 * intersection + 1.0) / (pred_sum + target_sum + 1.0)\n", "    weighted_loss = 1.0 - dice.mean()\n", "    \n", "    return weighted_loss\n", "\n", "class EarlyStopping:\n", "    \"\"\"Early stopping utility to prevent overfitting\"\"\"\n", "    def __init__(self, patience=7, verbose=False, delta=0, min_epochs=350):\n", "        self.patience = patience\n", "        self.verbose = verbose\n", "        self.delta = delta\n", "        self.best_score = None\n", "        self.early_stop = False\n", "        self.counter = 0\n", "        self.best_model_state_dict = None\n", "        self.min_epochs = min_epochs  # Minimum epochs before allowing early stopping\n", "        self.epoch_count = 0\n", "    \n", "    def __call__(self, val_loss, model):\n", "        \"\"\"Call method to update early stopping state\"\"\"\n", "        self.epoch_count += 1\n", "        score = -val_loss  # Higher score is better\n", "        \n", "        if self.best_score is None:\n", "            # First call\n", "            self.best_score = score\n", "            if self.verbose:\n", "                print(f\"Early stopping: Initial score {-self.best_score:.6f}\")\n", "            self.best_model_state_dict = copy.deepcopy(model.state_dict())\n", "        elif score < self.best_score + self.delta:\n", "            # Score didn't improve enough\n", "            self.counter += 1\n", "            if self.verbose:\n", "                print(f\"Early stopping: Not improved for {self.counter}/{self.patience} epochs. \" \n", "                      f\"Current: {-score:.6f}, Best: {-self.best_score:.6f}\")\n", "            # Only allow early stopping after minimum epochs\n", "            if self.counter >= self.patience and self.epoch_count >= self.min_epochs:\n", "                if self.verbose:\n", "                    print(f\"Early stopping triggered after {self.epoch_count} epochs\")\n", "                self.early_stop = True\n", "        else:\n", "            # Score improved\n", "            self.best_score = score\n", "            if self.verbose:\n", "                print(f\"Early stopping: Score improved to {-self.best_score:.6f}\")\n", "            self.best_model_state_dict = copy.deepcopy(model.state_dict())\n", "            self.counter = 0\n", "\n", "\n", "\n", "def combined_loss(pred, target, flow_weight=0.2, contrastive_weight=0.1, \n", "                 dense_region_weight=0.3, focal_weight=0.5, multi_scale_weight=0.2):\n", "    \"\"\"Combined loss with all advanced components\n", "    \n", "    Args:\n", "        pred: Dictionary with model outputs\n", "        target: Target tensor\n", "        flow_weight: Weight for flow loss\n", "        contrastive_weight: Weight for contrastive loss\n", "        dense_region_weight: Weight for dense region loss\n", "        focal_weight: Weight for focal loss component\n", "        multi_scale_weight: Weight for multi-scale loss\n", "        \n", "    Returns:\n", "        Total loss value and components dictionary\n", "    \"\"\"\n", "    # Initialize components dictionary\n", "    components = {}\n", "    total_loss = 0.0\n", "    \n", "    # Extract heatmap from prediction dict\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Ensure target has matching dimensions\n", "    if heatmap.shape[2:] != target.shape[2:]:\n", "        target = F.interpolate(target, size=heatmap.shape[2:], mode='nearest')\n", "    \n", "    # 1. Basic dice loss - always included\n", "    dice_loss_val, dice_components = dice_loss({'heatmaps': heatmap}, target)\n", "    total_loss += dice_loss_val\n", "    for k, v in dice_components.items():\n", "        components[k] = v\n", "    \n", "    # 2. Focal loss component\n", "    if focal_weight > 0:\n", "        focal_loss_val, focal_components = focal_loss({'heatmaps': heatmap}, target)\n", "        # Ensure focal_loss_val is a tensor\n", "        if isinstance(focal_loss_val, (int, float)):\n", "            focal_loss_val = torch.tensor(focal_loss_val, device=heatmap.device, dtype=heatmap.dtype)\n", "        \n", "        weighted_focal = focal_weight * focal_loss_val\n", "        total_loss += weighted_focal\n", "        components['focal_loss'] = focal_loss_val.item() if isinstance(focal_loss_val, torch.Tensor) else focal_loss_val\n", "    \n", "    # 3. Flow loss component\n", "    if 'flow' in pred and flow_weight > 0:\n", "        flow = pred['flow']\n", "        # Simple flow loss calculation\n", "        mask = (target > 0.5).float()\n", "        \n", "        if mask.sum() > 0:\n", "            # Calculate flow magnitude\n", "            eps = 1e-6  # For numerical stability\n", "            flow_magnitude = torch.sqrt(torch.sum(flow**2, dim=1, keepdim=True) + eps)\n", "            \n", "            # Weight loss by distance from center\n", "            flow_loss_val = (flow_magnitude * mask).sum() / (mask.sum() + eps)\n", "            \n", "            # Ensure flow_loss_val is a tensor\n", "            if isinstance(flow_loss_val, (int, float)):\n", "                flow_loss_val = torch.tensor(flow_loss_val, device=heatmap.device, dtype=heatmap.dtype)\n", "            \n", "            weighted_flow = flow_weight * flow_loss_val\n", "            total_loss += weighted_flow\n", "            components['flow_loss'] = flow_loss_val.item() if isinstance(flow_loss_val, torch.Tensor) else flow_loss_val\n", "        else:\n", "            components['flow_loss'] = 0.0\n", "    else:\n", "        components['flow_loss'] = 0.0\n", "    \n", "    # 4. Multi-scale loss\n", "    if 'multi_heatmaps' in pred and multi_scale_weight > 0:\n", "        multi_heatmaps = pred['multi_heatmaps']\n", "        if isinstance(multi_heatmaps, list) and len(multi_heatmaps) > 1:\n", "            multi_scale_loss_val = 0.0\n", "            \n", "            # Process all levels except first (which was already used for primary loss)\n", "            for i in range(1, len(multi_heatmaps)):\n", "                level_heatmap = multi_heatmaps[i]\n", "                level_target = F.interpolate(target, size=level_heatmap.shape[2:], mode='nearest')\n", "                \n", "                level_loss, _ = dice_loss({'heatmaps': level_heatmap}, level_target)\n", "                multi_scale_loss_val = multi_scale_loss_val + level_loss\n", "            \n", "            # Average across levels\n", "            if len(multi_heatmaps) > 1:\n", "                multi_scale_loss_val = multi_scale_loss_val / (len(multi_heatmaps) - 1)\n", "                \n", "                # Ensure multi_scale_loss_val is a tensor\n", "                if isinstance(multi_scale_loss_val, (int, float)):\n", "                    multi_scale_loss_val = torch.tensor(multi_scale_loss_val, \n", "                                                     device=heatmap.device, \n", "                                                     dtype=heatmap.dtype)\n", "                \n", "                weighted_ms_loss = multi_scale_weight * multi_scale_loss_val\n", "                total_loss += weighted_ms_loss\n", "                components['multi_scale_loss'] = multi_scale_loss_val.item() if isinstance(multi_scale_loss_val, torch.Tensor) else multi_scale_loss_val\n", "            else:\n", "                components['multi_scale_loss'] = 0.0\n", "        else:\n", "            components['multi_scale_loss'] = 0.0\n", "    else:\n", "        components['multi_scale_loss'] = 0.0\n", "    \n", "    # 5. Contrastive loss (simplified version)\n", "    if 'features' in pred and contrastive_weight > 0:\n", "        # For now, just add a placeholder value\n", "        # Implement real contrastive loss if needed later\n", "        components['contrastive_loss'] = 0.0\n", "    \n", "    # 6. Dense region loss (simplified version)\n", "    if 'all_features' in pred and dense_region_weight > 0:\n", "        # For now, just add a placeholder value\n", "        # Implement real dense region loss if needed later\n", "        components['dense_region_loss'] = 0.0\n", "    \n", "    return total_loss, components\n", "\n", "        \n", "        \n", "        \n", "        \n", "    "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# =================================\n", "# Utility Functions\n", "# =================================\n", "\n", "def create_blending_mask(shape, device):\n", "    \"\"\"Create a weight mask for blending predictions with higher weights in the center\"\"\"\n", "    if len(shape) == 5:  # 3D data\n", "        _, _, d, h, w = shape\n", "        z_grid, y_grid, x_grid = torch.meshgrid(\n", "            torch.linspace(-1, 1, d),\n", "            torch.linspace(-1, 1, h),\n", "            torch.linspace(-1, 1, w),\n", "            indexing='ij'\n", "        )\n", "        \n", "        # Calculate distance from center (spherical)\n", "        distance = torch.sqrt(x_grid**2 + y_grid**2 + z_grid**2)\n", "    else:  # 2D data\n", "        _, _, h, w = shape\n", "        y_grid, x_grid = torch.meshgrid(\n", "            torch.linspace(-1, 1, h),\n", "            torch.linspace(-1, 1, w),\n", "            indexing='ij'\n", "        )\n", "        \n", "        # Calculate distance from center (circular)\n", "        distance = torch.sqrt(x_grid**2 + y_grid**2)\n", "    \n", "    # Create weight mask (higher in center, lower at edges)\n", "    weight = torch.exp(-5.0 * distance)\n", "    \n", "    # Ensure weight is between 0.1 and 1.0\n", "    weight = 0.1 + 0.9 * weight\n", "    \n", "    # Add batch and channel dimensions\n", "    if len(shape) == 5:\n", "        weight = weight.unsqueeze(0).unsqueeze(0)\n", "    else:\n", "        weight = weight.unsqueeze(0).unsqueeze(0)\n", "        \n", "    return weight.to(device)\n", "\n", "\n", "def scale_space_detection(heatmaps, thresholds=[0.3, 0.5, 0.7], min_distance=3):\n", "    \"\"\"\n", "    Multi-scale blob detection in scale space to better separate close spots\n", "    \n", "    Args:\n", "        heatmaps: List of heatmaps at different scales\n", "        thresholds: List of thresholds for each scale\n", "        min_distance: Minimum distance between spots\n", "        \n", "    Returns:\n", "        array of detected spots with subpixel precision\n", "    \"\"\"\n", "    from skimage.feature import peak_local_max\n", "    from scipy import ndimage as ndi\n", "    \n", "    all_points = []\n", "    \n", "    # Process each scale with appropriate threshold\n", "    for i, (heatmap, threshold) in enumerate(zip(heatmaps, thresholds)):\n", "        # Convert to numpy\n", "        hm = heatmap.detach().cpu().numpy()[0, 0]\n", "        \n", "        # Get local maxima in this scale\n", "        binary = hm > threshold\n", "        labeled, _ = ndi.label(binary)\n", "        \n", "        # Find peaks with adaptive min_distance based on scale\n", "        scale_min_distance = min_distance * (i + 1)\n", "        points = peak_local_max(\n", "            hm, \n", "            min_distance=scale_min_distance,\n", "            threshold_abs=threshold,\n", "            exclude_border=False,\n", "            indices=True\n", "        )\n", "        \n", "        # Record scale information\n", "        if len(points) > 0:\n", "            # Add scale information to point coordinates\n", "            scale_points = np.column_stack([points, np.full(len(points), i)])\n", "            all_points.append(scale_points)\n", "    \n", "    if not all_points:\n", "        return np.zeros((0, 3))  # Empty result\n", "        \n", "    # Combine points from all scales\n", "    combined_points = np.vstack(all_points)\n", "    \n", "    # Group points that are likely the same spot detected at different scales\n", "    final_points = []\n", "    \n", "    # Sort points by response strength (highest first)\n", "    scales = [heatmap.detach().cpu().numpy()[0, 0] for heatmap in heatmaps]\n", "    strengths = np.array([scales[int(p[2])][int(p[0]), int(p[1])] for p in combined_points])\n", "    sorted_indices = np.argsort(-strengths)\n", "    \n", "    # Process points in order of decreasing strength\n", "    processed = set()\n", "    for idx in sorted_indices:\n", "        if idx in processed:\n", "            continue\n", "            \n", "        point = combined_points[idx]\n", "        y, x, scale = point\n", "        \n", "        # Add this point\n", "        final_points.append((y, x, scale))\n", "        processed.add(idx)\n", "        \n", "        # Mark nearby points as processed\n", "        for i, other_point in enumerate(combined_points):\n", "            if i == idx or i in processed:\n", "                continue\n", "                \n", "            oy, ox, _ = other_point\n", "            # If points are close in spatial coordinates, they're the same spot\n", "            if np.sqrt((y - oy)**2 + (x - ox)**2) < min_distance:\n", "                processed.add(i)\n", "    \n", "    # Convert to numpy array\n", "    if final_points:\n", "        return np.array(final_points)\n", "    else:\n", "        return np.zeros((0, 3))\n", "\n", "\n", "def flow_to_rgb(flow):\n", "    \"\"\"Convert flow field to RGB image for visualization\"\"\"\n", "    u = flow[0]\n", "    v = flow[1]\n", "    \n", "    # Convert flow to polar coordinates\n", "    magnitude = np.sqrt(u**2 + v**2)\n", "    angle = np.arctan2(v, u)\n", "    \n", "    # Normalize magnitude for better visualization\n", "    magnitude = np.clip(magnitude / (np.percentile(magnitude, 95) + 1e-6), 0, 1)\n", "    \n", "    # Convert to HSV\n", "    hsv = np.zeros((flow.shape[1], flow.shape[2], 3), dtype=np.float32)\n", "    hsv[..., 0] = (angle + np.pi) / (2 * np.pi)  # Hue from angle\n", "    hsv[..., 1] = magnitude  # Saturation from magnitude\n", "    hsv[..., 2] = 1.0  # Value is always 1\n", "    \n", "    # Convert HSV to RGB\n", "    rgb = cv2.cvtColor((hsv * 255).astype(np.uint8), cv2.COLOR_HSV2RGB)\n", "    \n", "    return rgb\n", "\n", "\n", "def optimize_model_for_inference(model, input_shape=(1, 1, 512, 512)):\n", "    \"\"\"Optimize model for inference using TorchScript\"\"\"\n", "    # Create dummy input\n", "    dummy_input = torch.randn(input_shape, device=next(model.parameters()).device)\n", "    \n", "    # Set model to evaluation mode\n", "    model.eval()\n", "    \n", "    # Create TorchScript version\n", "    print(\"Creating TorchScript model...\")\n", "    scripted_model = torch.jit.trace(model, dummy_input)\n", "    scripted_model.save(\"spot_detector_optimized.pt\")\n", "    \n", "    print(\"Model optimized and exported to spot_detector_optimized.pt\")\n", "    return scripted_model\n", "\n", "\n", "def large_image_inference(model, large_image, window_size=512, overlap=64):\n", "    \"\"\"Process large images with sliding window and stitching\"\"\"\n", "    # Get dimensions\n", "    if model.is_3d:\n", "        c, d, h, w = large_image.shape\n", "        step_z = window_size - overlap\n", "        steps_z = max(1, (d - overlap) // step_z)\n", "    else:\n", "        if large_image.dim() == 3:  # CHW format\n", "            c, h, w = large_image.shape\n", "        else:  # HW format\n", "            h, w = large_image.shape\n", "            c = 1\n", "            large_image = large_image.unsqueeze(0)\n", "    \n", "    # Calculate steps with overlap\n", "    step_xy = window_size - overlap\n", "    steps_y = max(1, (h - overlap) // step_xy)\n", "    steps_x = max(1, (w - overlap) // step_xy)\n", "    \n", "    # Initialize output tensors\n", "    device = large_image.device if hasattr(large_image, 'device') else torch.device('cpu')\n", "    if model.is_3d:\n", "        heatmap_output = torch.zeros((1, 1, d, h, w), device=device)\n", "        flow_output = torch.zeros((1, 3, d, h, w), device=device)\n", "        weight_map = torch.zeros((1, 1, d, h, w), device=device)\n", "    else:\n", "        heatmap_output = torch.zeros((1, 1, h, w), device=device)\n", "        flow_output = torch.zeros((1, 2, h, w), device=device)\n", "        weight_map = torch.zeros((1, 1, h, w), device=device)\n", "    \n", "    # Convert numpy array to tensor if needed\n", "    if isinstance(large_image, np.ndarray):\n", "        large_image = torch.from_numpy(large_image).float()\n", "        if large_image.dim() == 2:\n", "            large_image = large_image.unsqueeze(0)\n", "    \n", "    # Make sure large_image is on the right device\n", "    large_image = large_image.to(device)\n", "    \n", "    # Process each window\n", "    with torch.no_grad():\n", "        model.eval()  # Set model to evaluation mode\n", "        \n", "        for i in range(steps_y):\n", "            y_start = i * step_xy\n", "            y_end = min(h, y_start + window_size)\n", "            y_start = max(0, y_end - window_size)  # Adjust start to maintain window size\n", "            \n", "            for j in range(steps_x):\n", "                x_start = j * step_xy\n", "                x_end = min(w, x_start + window_size)\n", "                x_start = max(0, x_end - window_size)  # Adjust start to maintain window size\n", "                \n", "                if model.is_3d:\n", "                    for k in range(steps_z):\n", "                        z_start = k * step_z\n", "                        z_end = min(d, z_start + window_size)\n", "                        z_start = max(0, z_end - window_size)\n", "                        \n", "                        # Extract window\n", "                        window = large_image[:, z_start:z_end, y_start:y_end, x_start:x_end].unsqueeze(0)\n", "                        \n", "                        # Process window\n", "                        outputs = model(window)\n", "                        heatmap = outputs['heatmaps']\n", "                        flow = outputs['flow']\n", "                        \n", "                        # Create weight mask for blending (higher weight in center, lower at edges)\n", "                        weight_mask = create_blending_mask(heatmap.shape, device=device)\n", "                        \n", "                        # Add to output tensors with weight\n", "                        heatmap_output[0, :, z_start:z_end, y_start:y_end, x_start:x_end] += heatmap[0] * weight_mask\n", "                        flow_output[0, :, z_start:z_end, y_start:y_end, x_start:x_end] += flow[0] * weight_mask\n", "                        weight_map[0, :, z_start:z_end, y_start:y_end, x_start:x_end] += weight_mask\n", "                else:\n", "                    # Extract window\n", "                    window = large_image[:, y_start:y_end, x_start:x_end].unsqueeze(0)\n", "                    \n", "                    # Process window\n", "                    outputs = model(window)\n", "                    heatmap = outputs['heatmaps']\n", "                    flow = outputs.get('flow', None)\n", "                    \n", "                    # Create weight mask for blending\n", "                    weight_mask = create_blending_mask(heatmap.shape, device=device)\n", "                    \n", "                    # Add to output tensors with weight\n", "                    heatmap_output[0, :, y_start:y_end, x_start:x_end] += heatmap[0] * weight_mask\n", "                    if flow is not None:\n", "                        flow_output[0, :, y_start:y_end, x_start:x_end] += flow[0] * weight_mask\n", "                    weight_map[0, :, y_start:y_end, x_start:x_end] += weight_mask\n", "    \n", "    # Normalize by weights\n", "    eps = 1e-6\n", "    heatmap_output /= (weight_map + eps)\n", "    if flow_output is not None:\n", "        flow_output /= (weight_map + eps)\n", "    \n", "    # Create output dictionary\n", "    result = {\n", "        'heatmaps': heatmap_output,\n", "        'flow': flow_output if flow_output is not None else None\n", "    }\n", "    \n", "    # Detect spots from the complete stitched result\n", "    coords, instance_labels = model.detect_spots(\n", "        heatmap_output, \n", "        flow_output if flow_output is not None else None\n", "    )\n", "    result['spot_coords'] = coords\n", "    result['instance_labels'] = instance_labels\n", "    \n", "    return result\n", "\n", "\n", "def contrastive_spot_loss(features, masks, margin=1.0):\n", "    \"\"\"Contrastive loss to better distinguish close spots\"\"\"\n", "    # Extract feature vectors for each spot center\n", "    spot_centers = extract_spot_centers(masks)\n", "    \n", "    if not spot_centers:\n", "        return torch.tensor(0.0, device=features.device)\n", "    \n", "    batch_loss = torch.tensor(0.0, device=features.device)\n", "    valid_batches = 0\n", "    \n", "    for batch_idx, centers in enumerate(spot_centers):\n", "        if len(centers) < 2:  # Need at least 2 spots for contrastive loss\n", "            continue\n", "            \n", "        # Sample features at center points\n", "        spot_features = []\n", "        for coords in centers:\n", "            # Handle 2D vs 3D\n", "            if len(coords) == 2:\n", "                y, x = map(int, coords)\n", "                if 0 <= y < features.shape[2] and 0 <= x < features.shape[3]:\n", "                    feature = features[batch_idx, :, y, x]\n", "                    spot_features.append(feature)\n", "            else:\n", "                z, y, x = map(int, coords)\n", "                if (0 <= z < features.shape[2] and \n", "                    0 <= y < features.shape[3] and \n", "                    0 <= x < features.shape[4]):\n", "                    feature = features[batch_idx, :, z, y, x]\n", "                    spot_features.append(feature)\n", "        \n", "        # Skip if not enough valid features\n", "        if len(spot_features) < 2:\n", "            continue\n", "            \n", "        # Stack feature vectors\n", "        feature_vecs = torch.stack(spot_features)\n", "        \n", "        # Normalize feature vectors\n", "        feature_vecs = F.normalize(feature_vecs, p=2, dim=1)\n", "        \n", "        # Compute pairwise distances\n", "        dist_matrix = torch.cdist(feature_vecs, feature_vecs)\n", "        \n", "        # Create contrastive loss\n", "        n = len(feature_vecs)\n", "        loss = 0.0\n", "        num_close_pairs = 0\n", "        \n", "        # For each pair of spots\n", "        for i in range(n):\n", "            for j in range(i+1, n):\n", "                # Compute feature distance\n", "                dist = dist_matrix[i, j]\n", "                \n", "                # If distance is small, apply contrastive loss\n", "                if dist < margin:\n", "                    loss += torch.max(torch.tensor(0.0, device=dist.device), \n", "                                    margin - dist)\n", "                    num_close_pairs += 1\n", "        \n", "        # Average loss over close pairs\n", "        if num_close_pairs > 0:\n", "            batch_loss += loss / num_close_pairs\n", "            valid_batches += 1\n", "    \n", "    # Return average loss across valid batches\n", "    if valid_batches > 0:\n", "        return batch_loss / valid_batches\n", "    else:\n", "        return torch.tensor(0.0, device=features.device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# =================================\n", "# Data Processing Classes\n", "# =================================\n", "\n", "def safe_collate(batch):\n", "    \"\"\"Custom collate function that filters out None values and ensures consistent tensor shapes\"\"\"\n", "    # Filter out None entries\n", "    batch = [item for item in batch if item is not None and all(x is not None for x in item)]\n", "    \n", "    # If the batch is empty after filtering, return a dummy batch\n", "    if len(batch) == 0:\n", "        return torch.zeros(1, 1, 128, 128), torch.zeros(1, 1, 128, 128)\n", "    \n", "    # Try using default collate on the filtered batch\n", "    try:\n", "        return torch.utils.data.dataloader.default_collate(batch)\n", "    except Exception as e:\n", "        print(f\"Collation error: {e}\")\n", "        # If collation fails, return first batch item expanded\n", "        images = [item[0].unsqueeze(0) for item in batch]\n", "        masks = [item[1].unsqueeze(0) for item in batch]\n", "        \n", "        # Stack if possible, otherwise just return the first item\n", "        try:\n", "            return torch.cat(images, dim=0), torch.cat(masks, dim=0)\n", "        except:\n", "            return images[0], masks[0]\n", "\n", "\n", "class SpotDataset(Dataset):\n", "    \"\"\"Dataset for spot detection with proper mask dimensions and type handling.\"\"\"\n", "    def __init__(self, images_dir, masks_dir, is_3d=False, augment=True, \n", "             augmentation_params=None, image_extension=\"*.tif\",\n", "             patch_size=None, patch_strategy=\"resize\", fill_mask_holes=True,\n", "             normalization='percentile', percentile_range=(1, 99.5), add_noise=False):\n", "        \n", "        # Initialize same as before\n", "        self.is_3d = is_3d\n", "        self.patch_size = patch_size\n", "        self.patch_strategy = patch_strategy\n", "        self.fill_mask_holes = fill_mask_holes\n", "        self.normalization = normalization\n", "        self.percentile_range = percentile_range\n", "        self.add_noise = add_noise\n", "        \n", "        # Handle either directory paths or lists of file paths\n", "        # Initialize image files\n", "        if isinstance(images_dir, list):\n", "            # Already a list of file paths\n", "            self.image_files = images_dir\n", "            self.images_dir = os.path.dirname(images_dir[0]) if images_dir else \"\"\n", "        elif '*' in images_dir:\n", "            self.images_dir = os.path.dirname(images_dir)\n", "            self.image_files = sorted(glob.glob(images_dir))\n", "        else:\n", "            self.images_dir = images_dir\n", "            self.image_files = sorted(glob.glob(os.path.join(images_dir, image_extension)))\n", "            \n", "        # Initialize mask files\n", "        if isinstance(masks_dir, list):\n", "            # Already a list of file paths\n", "            self.mask_files = masks_dir\n", "            self.masks_dir = os.path.dirname(masks_dir[0]) if masks_dir else \"\"\n", "        elif '*' in masks_dir:\n", "            self.masks_dir = os.path.dirname(masks_dir)\n", "            self.mask_files = sorted(glob.glob(masks_dir))\n", "        else:\n", "            self.masks_dir = masks_dir\n", "            self.mask_files = sorted(glob.glob(os.path.join(masks_dir, image_extension)))\n", "        \n", "        print(f\"SpotDataset initialized with {len(self.image_files)} images and {len(self.mask_files)} masks\")\n", "        \n", "        # Make sure we have the same number of images and masks\n", "        if len(self.image_files) != len(self.mask_files):\n", "            raise ValueError(f\"Mismatch between number of images ({len(self.image_files)}) and masks ({len(self.mask_files)})\")\n", "        \n", "        #Setup augmentation\n", "        self.augment = augment\n", "        \n", "        if augment:\n", "            if augmentation_params is None:\n", "                augmentation_params = {}\n", "                \n", "            # For 2D data, use albumentations with careful type handling\n", "            p = augmentation_params.get('p', 0.5)\n", "            intensity_range = augmentation_params.get('intensity_range', 0.2)\n", "            spatial_range = augmentation_params.get('spatial_range', 0.1)\n", "            rotation_range = augmentation_params.get('rotation_range', 30)\n", "            \n", "            # Careful transforms that preserve data types\n", "            self.transform = <PERSON><PERSON>([\n", "                # Spatial transforms\n", "                <PERSON><PERSON>(p=p*0.5),\n", "                <PERSON><PERSON>(p=p*0.5),\n", "                <PERSON><PERSON>(scale=(0.8, 1.2), translate_percent=(0.1, 0.1), rotate=(-rotation_range, rotation_range), p=p),\n", "                # Intensity transforms\n", "                <PERSON><PERSON>ontrast(p=p*0.5),\n", "                <PERSON><PERSON>(std_range=(0, 0.01), p=p*0.3),\n", "                # Convert to tensor at the end\n", "                ToTensorV2()\n", "            ])\n", "        else:\n", "            # Simple transform for validation\n", "            self.transform = <PERSON><PERSON>([ToTensorV2()])\n", "            \n", "    # Add a method to load images if it's missing\n", "    def _load_image(self, idx):\n", "        \"\"\"Load image at the given index\"\"\"\n", "        image_path = self.image_files[idx]\n", "        # Load image using OpenCV (grayscale)\n", "        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)\n", "        if image is None:\n", "            raise ValueError(f\"Failed to load image: {image_path}\")\n", "        return image\n", "    \n", "    # Add a method to load masks if it's missing\n", "    def _load_mask(self, idx):\n", "        \"\"\"Load mask at the given index\"\"\"\n", "        mask_path = self.mask_files[idx]\n", "        # Load mask using OpenCV (grayscale)\n", "        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)\n", "        if mask is None:\n", "            raise ValueError(f\"Failed to load mask: {mask_path}\")\n", "        return mask\n", "    \n", "    \n", "    \n", "    def fill_holes(self, mask):\n", "        \"\"\"Fill holes in binary masks using SciPy's binary_fill_holes.\"\"\"\n", "        try:\n", "            from scipy import ndimage\n", "            import numpy as np\n", "            \n", "            # Convert to binary\n", "            threshold = 0.5 * mask.max()\n", "            binary = (mask > threshold).astype(np.bool_)\n", "            \n", "            # Fill holes\n", "            filled = ndimage.binary_fill_holes(binary)\n", "            \n", "            # Convert back to original data type and scale\n", "            return filled.astype(mask.dtype) * mask.max()\n", "        except ImportError:\n", "            print(\"Warning: SciPy not available for hole filling, returning original mask\")\n", "            return mask\n", "    \n", "    def apply_patching(self, image, mask):\n", "        \"\"\"Apply patching strategy to image and mask.\"\"\"\n", "        if self.patch_size is None:\n", "            # No patching needed\n", "            return image, mask\n", "            \n", "        # Implementation for patching strategies\n", "        if self.patch_strategy == \"resize\":\n", "            # Resize the image and mask to patch_size\n", "            resize_transform = <PERSON><PERSON>([\n", "                <PERSON><PERSON>(height=self.patch_size, width=self.patch_size)\n", "            ], additional_targets={'mask': 'mask'})\n", "            \n", "            transformed = resize_transform(image=image, mask=mask)\n", "            return transformed[\"image\"], transformed[\"mask\"]\n", "        \n", "        elif self.patch_strategy == \"center_crop\":\n", "            # Center crop or pad as needed\n", "            h, w = image.shape[:2]\n", "            if h >= self.patch_size and w >= self.patch_size:\n", "                # Crop if image is larger than patch_size\n", "                h_start = (h - self.patch_size) // 2\n", "                w_start = (w - self.patch_size) // 2\n", "                image = image[h_start:h_start+self.patch_size, w_start:w_start+self.patch_size]\n", "                mask = mask[h_start:h_start+self.patch_size, w_start:w_start+self.patch_size]\n", "            else:\n", "                # Pad if smaller\n", "                center_crop_transform = <PERSON><PERSON>([\n", "                    <PERSON><PERSON>dIfNeeded(min_height=self.patch_size, min_width=self.patch_size,\n", "                                 border_mode=cv2.BORDER_CONSTANT),\n", "                    <PERSON><PERSON>(height=self.patch_size, width=self.patch_size)\n", "                ], additional_targets={'mask': 'mask'})\n", "                transformed = center_crop_transform(image=image, mask=mask)\n", "                image, mask = transformed[\"image\"], transformed[\"mask\"]\n", "                \n", "        elif self.patch_strategy == \"random_crop\":\n", "            # Random crop with padding if needed\n", "            random_crop_transform = <PERSON><PERSON>([\n", "                <PERSON><PERSON>dIfNeeded(min_height=self.patch_size, min_width=self.patch_size,\n", "                             border_mode=cv2.BORDER_CONSTANT),\n", "                <PERSON><PERSON>(height=self.patch_size, width=self.patch_size)\n", "            ], additional_targets={'mask': 'mask'})\n", "            transformed = random_crop_transform(image=image, mask=mask)\n", "            image, mask = transformed[\"image\"], transformed[\"mask\"]\n", "            \n", "        return image, mask\n", "    \n", "    def __len__(self):\n", "        return len(self.image_files)\n", "    \n", "    def __getitem__(self, idx):\n", "        \"\"\"Get image and mask pair with consistent types\"\"\"\n", "        try:\n", "            # Load image and mask\n", "            image = self._load_image(idx)\n", "            mask = self._load_mask(idx)\n", "            \n", "            # Convert to float32 early to avoid type issues\n", "            image = image.astype(np.float32)\n", "            mask = mask.astype(np.float32)\n", "            \n", "            # Verify loaded data is valid\n", "            if image is None or mask is None or image.size == 0 or mask.size == 0:\n", "                print(f\"Warning: Empty data at index {idx}\")\n", "                # Return dummy samples as float32\n", "                return torch.zeros(1, 64, 64, dtype=torch.float32), torch.zeros(1, 64, 64, dtype=torch.float32)\n", "            \n", "            # Apply transform if available\n", "            if self.transform:\n", "                transformed = self.transform(image=image, mask=mask)\n", "                image, mask = transformed[\"image\"], transformed[\"mask\"]\n", "            else:\n", "                # Manual conversion to tensors\n", "                image = torch.from_numpy(image).float()\n", "                mask = torch.from_numpy(mask).float()\n", "            \n", "            # Ensure proper dimensions and types\n", "            if len(image.shape) == 2:\n", "                image = image.unsqueeze(0)\n", "            if len(mask.shape) == 2:\n", "                mask = mask.unsqueeze(0)\n", "            \n", "            return image.float(), mask.float()  # Explicit float type\n", "        \n", "        except Exception as e:\n", "            print(f\"Error loading sample {idx}: {e}\")\n", "            return torch.zeros(1, 64, 64, dtype=torch.float32), torch.zeros(1, 64, 64, dtype=torch.float32)\n", "                \n", "            \n", "\n", "class SplitDatasetManager:\n", "    \"\"\"Manages dataset splitting and dataloader creation with proper memory optimization\"\"\"\n", "    def __init__(self, image_dir, mask_dir, val_ratio=0.2, test_ratio=0.0, \n", "                seed=42, is_3d=False, augmentation_strength='medium'):\n", "        self.image_dir = image_dir\n", "        self.mask_dir = mask_dir\n", "        self.val_ratio = val_ratio\n", "        self.test_ratio = test_ratio\n", "        self.seed = seed\n", "        self.is_3d = is_3d\n", "        self.augmentation_strength = augmentation_strength\n", "        \n", "        # Load file paths early to enable proper splitting\n", "        self._prepare_file_paths()\n", "    \n", "    def _prepare_file_paths(self):\n", "        \"\"\"Prepare image and mask file paths and split them\"\"\"\n", "        # Load image paths from glob pattern or directory\n", "        if '*' in self.image_dir:\n", "            self.image_files = sorted(glob.glob(self.image_dir))  # FIX: Use glob.glob instead of glob\n", "        else:\n", "            self.image_files = sorted(glob.glob(os.path.join(self.image_dir, \"*.tif\")))\n", "            \n", "        # Load mask paths from glob pattern or directory\n", "        if '*' in self.mask_dir:\n", "            self.mask_files = sorted(glob.glob(self.mask_dir))  # FIX: Use glob.glob instead of glob\n", "        else:\n", "            self.mask_files = sorted(glob.glob(os.path.join(self.mask_dir, \"*.tif\")))\n", "        \n", "        print(f\"Found {len(self.image_files)} images and {len(self.mask_files)} masks\")\n", "        assert len(self.image_files) > 0, \"No images found\"\n", "        assert len(self.mask_files) > 0, \"No masks found\"\n", "    \n", "    def setup(self, batch_size=32, num_workers=None, pin_memory=None, patch_size=None):\n", "        \"\"\"Set up train, validation, and optional test dataloaders\"\"\"\n", "        # Auto-determine optimal number of workers and pinning\n", "        if num_workers is None:\n", "            num_workers = min(4, os.cpu_count() or 1)\n", "        \n", "        if pin_memory is None:\n", "            pin_memory = torch.cuda.is_available()\n", "        \n", "        # Define augmentation parameters based on strength\n", "        if self.augmentation_strength == 'strong':\n", "            aug_params = {'p': 0.7, 'intensity_range': 0.3, 'spatial_range': 0.2, 'rotation_range': 45}\n", "        elif self.augmentation_strength == 'mild':\n", "            aug_params = {'p': 0.3, 'intensity_range': 0.1, 'spatial_range': 0.05, 'rotation_range': 15}\n", "        else:  # medium (default)\n", "            aug_params = {'p': 0.5, 'intensity_range': 0.2, 'spatial_range': 0.1, 'rotation_range': 30}\n", "        \n", "        # Split indices for train/val/test\n", "        num_samples = len(self.image_files)\n", "        indices = np.arange(num_samples)\n", "        np.random.seed(self.seed)\n", "        np.random.shuffle(indices)\n", "        \n", "        # Calculate split sizes\n", "        val_size = int(self.val_ratio * num_samples)\n", "        test_size = int(self.test_ratio * num_samples)\n", "        train_size = num_samples - val_size - test_size\n", "        \n", "        # Get indices for each split\n", "        train_indices = indices[:train_size]\n", "        val_indices = indices[train_size:train_size + val_size]\n", "        test_indices = indices[train_size + val_size:] if test_size > 0 else []\n", "        \n", "        # Create file lists for each split\n", "        train_images = [self.image_files[i] for i in train_indices]\n", "        train_masks = [self.mask_files[i] for i in train_indices]\n", "        \n", "        val_images = [self.image_files[i] for i in val_indices]\n", "        val_masks = [self.mask_files[i] for i in val_indices]\n", "        \n", "        test_images = [self.image_files[i] for i in test_indices] if test_size > 0 else []\n", "        test_masks = [self.mask_files[i] for i in test_indices] if test_size > 0 else []\n", "        \n", "        # Create datasets with appropriate augmentation settings\n", "        train_dataset = SpotDataset(\n", "            train_images, \n", "            train_masks,\n", "            is_3d=self.is_3d,\n", "            augment=True,  # Apply augmentation to training\n", "            augmentation_params=aug_params,\n", "            patch_size=patch_size\n", "        )\n", "        \n", "        val_dataset = SpotDataset(\n", "            val_images,\n", "            val_masks,\n", "            is_3d=self.is_3d,\n", "            augment=False,  # No augmentation for validation\n", "            patch_size=patch_size\n", "        )\n", "        \n", "        # Create DataLoaders\n", "        train_loader = DataLoader(\n", "            train_dataset,\n", "            batch_size=batch_size,\n", "            shuffle=True,\n", "            num_workers=num_workers,\n", "            pin_memory=pin_memory,\n", "            persistent_workers=(num_workers > 0),\n", "            prefetch_factor=2 if num_workers > 0 else None,\n", "            drop_last=True,\n", "            collate_fn=safe_collate  \n", "        )\n", "        \n", "        val_loader = DataLoader(\n", "            val_dataset,\n", "            batch_size=batch_size,\n", "            shuffle=False,\n", "            num_workers=num_workers,\n", "            pin_memory=pin_memory,\n", "            persistent_workers=(num_workers > 0),\n", "            prefetch_factor=2 if num_workers > 0 else None,\n", "            collate_fn=safe_collate  # Add this line\n", "        )\n", "        \n", "        # Return test loader if test split exists\n", "        if test_size > 0:\n", "            test_dataset = SpotDataset(\n", "                test_images,\n", "                test_masks,\n", "                is_3d=self.is_3d,\n", "                augment=False,\n", "                patch_size=patch_size\n", "            )\n", "            \n", "            test_loader = DataLoader(\n", "                test_dataset,\n", "                batch_size=batch_size,\n", "                shuffle=False,\n", "                num_workers=max(1, num_workers//2),  # Fewer workers for test\n", "                pin_memory=pin_memory\n", "            )\n", "            \n", "            print(f\"Dataset split: {len(train_dataset)} training, {len(val_dataset)} validation, {len(test_dataset)} test samples\")\n", "            return train_loader, val_loader, test_loader\n", "        \n", "        print(f\"Dataset split: {len(train_dataset)} training, {len(val_dataset)} validation samples\")\n", "        return train_loader, val_loader"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# =================================\n", "# Training and Evaluation\n", "# =================================\n", "\n", "\n", "def calculate_f1_score_from_instances(pred_dict, target_mask, iou_threshold=0.5):\n", "    \"\"\"Calculate F1 score by comparing predicted instance labels with ground truth\n", "    \n", "    Args:\n", "        pred_dict: Dictionary with model outputs including 'instance_labels'\n", "        target_mask: Ground truth mask [B,C,H,W]\n", "        iou_threshold: IoU threshold to consider a detection as correct\n", "        \n", "    Returns:\n", "        F1 score\n", "    \"\"\"\n", "    # During training, we only have heatmap predictions\n", "    if 'instance_labels' not in pred_dict:\n", "        # Extract spots from heatmap\n", "        heatmap = pred_dict['heatmaps']\n", "        \n", "        # Apply sigmoid if needed\n", "        if heatmap.min() < 0 or heatmap.max() > 1:\n", "            heatmap = torch.sigmoid(heatmap)\n", "        \n", "        # Simple metric: calculate dice coefficient on heatmap vs mask\n", "        intersection = torch.sum(heatmap * target_mask)\n", "        dice = (2.0 * intersection) / (torch.sum(heatmap) + torch.sum(target_mask) + 1e-8)\n", "        return dice.item()\n", "    \n", "    # For evaluation, we have instance labels\n", "    pred_instances = pred_dict['instance_labels']\n", "    \n", "    # Convert target mask to instances if needed\n", "    if target_mask.max() <= 1:\n", "        # Process target mask to get instance labels using connected components\n", "        from scipy import ndimage\n", "        \n", "        # Process batch\n", "        batch_size = target_mask.shape[0]\n", "        all_tp, all_fp, all_fn = 0, 0, 0\n", "        \n", "        for b in range(batch_size):\n", "            # Convert target to binary and numpy\n", "            gt_binary = (target_mask[b, 0] > 0.5).cpu().numpy()\n", "            \n", "            # Connected component labeling to get instances\n", "            gt_labels, num_gt = ndimage.label(gt_binary)\n", "            \n", "            # Get predicted instances for this batch item\n", "            pred_labels = pred_instances[b, 0].cpu().numpy()\n", "            num_pred = int(pred_labels.max())\n", "            \n", "            # Count matches based on IoU\n", "            tp = 0  # True positives\n", "            matched_gt = set()\n", "            matched_pred = set()\n", "            \n", "            # For each predicted instance, find best matching ground truth\n", "            for pred_id in range(1, num_pred + 1):\n", "                pred_mask = (pred_labels == pred_id)\n", "                best_iou = 0\n", "                best_gt_id = 0\n", "                \n", "                for gt_id in range(1, num_gt + 1):\n", "                    if gt_id in matched_gt:\n", "                        continue\n", "                        \n", "                    gt_mask = (gt_labels == gt_id)\n", "                    \n", "                    # Calculate IoU\n", "                    intersection = np.logical_and(pred_mask, gt_mask).sum()\n", "                    union = np.logical_or(pred_mask, gt_mask).sum()\n", "                    iou = intersection / union if union > 0 else 0\n", "                    \n", "                    if iou > best_iou:\n", "                        best_iou = iou\n", "                        best_gt_id = gt_id\n", "                \n", "                # Match if IoU exceeds threshold\n", "                if best_iou >= iou_threshold and best_gt_id not in matched_gt:\n", "                    tp += 1\n", "                    matched_gt.add(best_gt_id)\n", "                    matched_pred.add(pred_id)\n", "            \n", "            # Calculate metrics\n", "            fp = num_pred - tp  # False positives\n", "            fn = num_gt - tp    # False negatives\n", "            \n", "            all_tp += tp\n", "            all_fp += fp\n", "            all_fn += fn\n", "        \n", "        # Calculate precision, recall, F1\n", "        precision = all_tp / (all_tp + all_fp) if (all_tp + all_fp) > 0 else 0\n", "        recall = all_tp / (all_tp + all_fn) if (all_tp + all_fn) > 0 else 0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "        \n", "        return f1\n", "    \n", "    return 0.0\n", "\n", "def calculate_f1(outputs, targets, threshold=0.5):\n", "    \"\"\"Calculate F1 score between prediction and target masks on GPU\"\"\"\n", "    if isinstance(outputs, dict):\n", "        pred = outputs.get('heatmaps', outputs)\n", "    else:\n", "        pred = outputs\n", "        \n", "    # Apply sigmoid if predictions are not already in [0,1]\n", "    if pred.min() < 0 or pred.max() > 1:\n", "        pred = torch.sigmoid(pred)\n", "    \n", "    # Binarize predictions\n", "    pred_binary = (pred > threshold).float()\n", "    \n", "    # Ensure targets are appropriate shape\n", "    if targets.dim() == pred.dim():\n", "        if targets.shape[1] != pred.shape[1] and targets.shape[1] == 1:\n", "            targets = targets.expand(-1, pred.shape[1], -1, -1)\n", "    \n", "    # Calculate TP, FP, FN on GPU\n", "    true_pos = (pred_binary * targets).sum(dim=(2,3))\n", "    pred_pos = pred_binary.sum(dim=(2,3))\n", "    target_pos = targets.sum(dim=(2,3))\n", "    \n", "    # Handle divide-by-zero\n", "    precision = true_pos / (pred_pos + 1e-8)\n", "    recall = true_pos / (target_pos + 1e-8)\n", "    \n", "    # Calculate F1 (harmonic mean of precision and recall)\n", "    f1 = 2 * precision * recall / (precision + recall + 1e-8)\n", "    \n", "    # Average across batch and channels\n", "    return f1.mean().item()\n", "\n", "\n", "def calculate_metrics(heatmap_pred, mask_true, threshold=0.5, threshold_abs=0.001):\n", "    \"\"\"\n", "    Calculate F1, precision and recall for spot detection\n", "    \n", "    Args:\n", "        heatmap_pred: Predicted heatmaps [B, 1, H, W]\n", "        mask_true: Ground truth masks [B, 1, H, W]\n", "        threshold: Detection threshold (relative)\n", "        threshold_abs: Minimum absolute threshold\n", "        \n", "    Returns:\n", "        tuple: (f1, precision, recall)\n", "    \"\"\"\n", "    from scipy import ndimage as ndi\n", "    from skimage.feature import peak_local_max\n", "    \n", "    f1_scores = []\n", "    precisions = []\n", "    recalls = []\n", "    \n", "    # Process each batch item\n", "    for i in range(heatmap_pred.shape[0]):\n", "        pred = heatmap_pred[i, 0].detach().cpu().numpy()\n", "        true = mask_true[i, 0].detach().cpu().numpy() > 0.5\n", "        \n", "        # Find connected components in ground truth\n", "        true_labels, true_count = ndi.label(true)\n", "        true_centers = []\n", "        for j in range(1, true_count + 1):\n", "            component = (true_labels == j)\n", "            y, x = ndi.center_of_mass(component)\n", "            true_centers.append((y, x))\n", "        \n", "        # Find predicted spots using peak detection\n", "        threshold_val = max(threshold, threshold_abs)\n", "        pred_centers = peak_local_max(\n", "            pred, \n", "            min_distance=3,\n", "            threshold_abs=threshold_val,\n", "            exclude_border=False\n", "        )\n", "        \n", "        # Match predicted centers to true centers\n", "        matched_true = set()\n", "        matched_pred = set()\n", "        \n", "        # For each predicted center, find closest true center\n", "        for p_idx, (py, px) in enumerate(pred_centers):\n", "            min_dist = float('inf')\n", "            best_match = None\n", "            \n", "            for t_idx, (ty, tx) in enumerate(true_centers):\n", "                if t_idx in matched_true:\n", "                    continue\n", "                    \n", "                dist = ((py - ty) ** 2 + (px - tx) ** 2) ** 0.5\n", "                if dist < min_dist and dist <= 5:  # Match within 5 pixels\n", "                    min_dist = dist\n", "                    best_match = t_idx\n", "            \n", "            if best_match is not None:\n", "                matched_pred.add(p_idx)\n", "                matched_true.add(best_match)\n", "        \n", "        # Calculate metrics\n", "        true_positives = len(matched_true)\n", "        false_positives = len(pred_centers) - true_positives\n", "        false_negatives = len(true_centers) - true_positives\n", "        \n", "        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0\n", "        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0\n", "        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "        \n", "        f1_scores.append(f1)\n", "        precisions.append(precision)\n", "        recalls.append(recall)\n", "    \n", "    # Return average metrics\n", "    return (\n", "        sum(f1_scores) / max(len(f1_scores), 1),\n", "        sum(precisions) / max(len(precisions), 1),\n", "        sum(recalls) / max(len(recalls), 1)\n", "    )\n", "\n", "def evaluate_model(model, dataloader, device, threshold=None, threshold_abs=0.001):\n", "    \"\"\"\n", "    Evaluate model on dataloader\n", "    \n", "    Args:\n", "        model: Model to evaluate\n", "        dataloader: Validation dataloader\n", "        device: Device to run evaluation on\n", "        threshold: Detection threshold (if None, use model's threshold)\n", "        threshold_abs: Minimum absolute threshold\n", "        \n", "    Returns:\n", "        dict: Dictionary of metrics\n", "    \"\"\"\n", "    model.eval()\n", "    total_loss = 0\n", "    total_f1 = 0\n", "    total_precision = 0\n", "    total_recall = 0\n", "    \n", "    # Use model's threshold if not provided\n", "    if threshold is None:\n", "        threshold = model.threshold\n", "    \n", "    with torch.no_grad():\n", "        for images, masks in dataloader:\n", "            images = images.to(device)\n", "            masks = masks.to(device)\n", "            \n", "            # Forward pass\n", "            outputs = model(images)\n", "            \n", "            # Calculate loss\n", "            loss, _ = combined_loss(outputs, masks)\n", "            total_loss += loss.item()\n", "            \n", "            # Calculate metrics\n", "            f1, precision, recall = calculate_metrics(\n", "                outputs[\"heatmaps\"], \n", "                masks, \n", "                threshold=threshold,\n", "                threshold_abs=threshold_abs\n", "            )\n", "            total_f1 += f1\n", "            total_precision += precision\n", "            total_recall += recall\n", "    \n", "    # Calculate average metrics\n", "    metrics = {\n", "        \"val_loss\": total_loss / len(dataloader),\n", "        \"val_f1\": total_f1 / len(dataloader),\n", "        \"val_precision\": total_precision / len(dataloader),\n", "        \"val_recall\": total_recall / len(dataloader),\n", "        \"threshold\": threshold,\n", "        \"threshold_abs\": threshold_abs\n", "    }\n", "    \n", "    return metrics"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def train_model_with_advanced_tracking(model, train_loader, val_loader, num_epochs, device, \n", "                                      initial_lr=0.001, weight_decay=1e-4, patience=10, \n", "                                      checkpoint_dir=None, loss_type=\"combined\", \n", "                                      mixed_precision=True, clip_grad_norm=None, \n", "                                      warmup_epochs=0, report_interval=1,\n", "                                      log_dir=None, track_step_metrics=False,\n", "                                      flow_loss_weight=0.2, detect_plateau=False,\n", "                                      plateau_patience=5, use_focal_loss=False,\n", "                                      use_dense_sampling=False,\n", "                                      contrastive_weight=0.1,\n", "                                      dense_region_weight=0.3,\n", "                                      multi_scale_weight=0.2):\n", "    \"\"\"Advanced training function with comprehensive tracking and visualization\n", "    \n", "    Args:\n", "        model: Model to train\n", "        train_loader: DataLoader for training data\n", "        val_loader: DataLoader for validation data\n", "        num_epochs: Maximum number of epochs to train for\n", "        device: Device to train on (cuda/cpu)\n", "        initial_lr: Initial learning rate\n", "        weight_decay: Weight decay factor for optimizer\n", "        patience: Patience for early stopping\n", "        checkpoint_dir: Directory to save checkpoints\n", "        loss_type: Type of loss function ('dice', 'focal', 'combined')\n", "        mixed_precision: Whether to use mixed precision training\n", "        clip_grad_norm: Value to clip gradients to (None for no clipping)\n", "        warmup_epochs: Number of warmup epochs for learning rate\n", "        report_interval: How often to report progress\n", "        log_dir: Directory for TensorBoard logs\n", "        track_step_metrics: Whether to track per-step metrics\n", "        flow_loss_weight: Weight for flow loss component\n", "        detect_plateau: Whether to detect learning plateaus\n", "        plateau_patience: Patience for learning rate reduction on plateau\n", "        use_focal_loss: Whether to use focal loss\n", "        use_dense_sampling: Whether to use dense sampling during validation\n", "        \n", "    Returns:\n", "        Trained model and training history dictionary\n", "    \"\"\"\n", "    # Create checkpoint directory if needed\n", "    if checkpoint_dir and not os.path.exists(checkpoint_dir):\n", "        os.makedirs(checkpoint_dir)\n", "        \n", "    # Set up TensorBoard logging\n", "    if log_dir:\n", "        os.makedirs(log_dir, exist_ok=True)\n", "        writer = SummaryWriter(log_dir=log_dir)\n", "        print(f\"TensorBoard logging enabled at {log_dir}\")\n", "    else:\n", "        writer = None\n", "    \n", "    # Set up optimizer and schedulers\n", "    optimizer = torch.optim.AdamW(model.parameters(), lr=initial_lr, weight_decay=weight_decay)\n", "    \n", "    # Learning rate schedulers\n", "    lr_schedulers = []\n", "    \n", "    # Warmup scheduler for initial epochs\n", "    if warmup_epochs > 0:\n", "        warmup_scheduler = torch.optim.lr_scheduler.LinearLR(\n", "            optimizer, \n", "            start_factor=0.1, \n", "            end_factor=1.0, \n", "            total_iters=warmup_epochs * len(train_loader)\n", "        )\n", "        lr_schedulers.append(warmup_scheduler)\n", "    \n", "    # Plateau scheduler for dynamic learning rate adjustment\n", "    if detect_plateau:\n", "        plateau_scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(\n", "            optimizer,\n", "            mode='min',\n", "            factor=0.5,\n", "            patience=plateau_patience,\n", "            verbose=True,\n", "            min_lr=1e-6\n", "        )\n", "        # This is applied manually after validation, not added to the list\n", "    \n", "    # Set up early stopping with minimum epochs requirement\n", "    early_stopping = EarlyStopping(patience=patience, verbose=True, min_epochs=10)\n", "    \n", "    # Set up mixed precision training - FIXED\n", "    scaler = torch.amp.GradScaler() if mixed_precision and device.type == 'cuda' else None\n", "    \n", "    # Initialize metrics tracking\n", "    history = {\n", "        'train_loss': [], 'val_loss': [],\n", "        'train_f1': [], 'val_f1': [],\n", "        'lr': [], 'epochs': [],\n", "        'best_epoch': 0, 'best_val_loss': float('inf'),\n", "        'best_val_f1': 0.0,\n", "        'time_per_epoch': []\n", "    }\n", "    \n", "    # Set up step-level tracking\n", "    if track_step_metrics:\n", "        step_history = {\n", "            'steps': [], 'loss': [], \n", "            'f1': [], 'lr': [],\n", "            'data_time': [], 'forward_time': [],\n", "            'loss_calc_time': [], 'backward_time': []\n", "        }\n", "    \n", "    # Define loss function based on type\n", "    def compute_loss(outputs, targets):\n", "        if loss_type == \"dice\":\n", "            loss, components = dice_loss(outputs, targets)\n", "        elif loss_type == \"focal\":\n", "            loss, components = focal_loss(outputs, targets)\n", "        elif loss_type == \"instance_aware\":\n", "            loss, components = instance_aware_dice_loss(\n", "                outputs, targets, \n", "                distance_weight=0.2,\n", "                flow_weight=flow_loss_weight\n", "            )\n", "        elif loss_type == \"comprehensive\":\n", "            try:\n", "                loss, components = combined_loss(\n", "                    outputs, targets,\n", "                    flow_weight=flow_loss_weight,\n", "                    contrastive_weight=contrastive_weight,\n", "                    dense_region_weight=dense_region_weight,\n", "                    focal_weight=0.5 if use_focal_loss else 0.0,\n", "                    multi_scale_weight=multi_scale_weight\n", "                )\n", "            except Exception as e:\n", "                print(f\"Error in comprehensive loss: {e}, falling back to combined\")\n", "                loss, components = focal_dice_loss(\n", "                    outputs, targets, \n", "                    flow_weight=flow_loss_weight\n", "                )\n", "        else:\n", "            # Default combined loss\n", "            loss, components = focal_dice_loss(\n", "                outputs, targets, \n", "                flow_weight=flow_loss_weight\n", "            )\n", "        return loss, components\n", "    \n", "    # Define F1 score calculation function\n", "    def calculate_batch_f1(outputs, targets, threshold=0.01):  # Very low threshold during early training\n", "        \"\"\"Calculate F1 score for highly imbalanced data\"\"\"\n", "        with torch.no_grad():\n", "            # Extract heatmap from outputs\n", "            if isinstance(outputs, dict):\n", "                heatmap = outputs['heatmaps']\n", "            else:\n", "                heatmap = outputs\n", "                \n", "            # Apply sigmoid if needed\n", "            if heatmap.min() < 0 or heatmap.max() > 1:\n", "                heatmap = torch.sigmoid(heatmap)\n", "            \n", "            # Use very low threshold during early training\n", "            pred_binary = (heatmap > threshold).float()\n", "            target_binary = (targets > 0.5).float()\n", "            \n", "            # Calculate metrics with epsilon for stability\n", "            tp = torch.sum(pred_binary * target_binary).item()\n", "            fp = torch.sum(pred_binary * (1 - target_binary)).item()\n", "            fn = torch.sum((1 - pred_binary) * target_binary).item()\n", "            \n", "            # Print diagnostic info\n", "            print(f\"TP: {tp}, FP: {fp}, FN: {fn}\")\n", "            \n", "            # Calculate precision and recall\n", "            precision = tp / (tp + fp + 1e-8)\n", "            recall = tp / (tp + fn + 1e-8)\n", "            \n", "            # Calculate F1 score\n", "            f1 = 2 * precision * recall / (precision + recall + 1e-8)\n", "            \n", "            return f1\n", "    \n", "    # Try to log model parameters (avoid tracing)\n", "    if writer:\n", "        try:\n", "            for name, param in model.named_parameters():\n", "                if param.requires_grad:\n", "                    writer.add_histogram(f'Parameters/{name}', param.data.cpu().numpy(), 0)\n", "            print(\"Model parameter histograms logged to TensorBoard\")\n", "        except Exception as e:\n", "            print(f\"Failed to log model parameters: {str(e)}\")\n", "    \n", "    # Main training loop\n", "    best_model_state = None\n", "    \n", "    # Use tqdm with proper updating\n", "    pbar = tqdm(total=num_epochs, desc=f\"Training\", position=0, leave=True)\n", "    \n", "    for epoch in range(num_epochs):\n", "        epoch_start_time = time.time()\n", "        model.train()\n", "        \n", "        # Reset epoch metrics\n", "        train_loss = 0.0\n", "        train_f1 = 0.0\n", "        batch_count = 0\n", "        \n", "        # Create progress bar for training steps\n", "        train_pbar = tqdm(train_loader, leave=False)\n", "        \n", "        # Step timing variables\n", "        if track_step_metrics:\n", "            data_start = time.time()\n", "        \n", "        # Train loop for this epoch\n", "        for batch_idx, (images, masks) in enumerate(train_pbar):\n", "            # Track data loading time\n", "            if track_step_metrics:\n", "                data_time = time.time() - data_start\n", "            \n", "            # Move data to device and ensure float type\n", "            images = images.to(device, dtype=torch.float32)\n", "            masks = masks.to(device, dtype=torch.float32)\n", "            \n", "            # Forward pass with mixed precision if enabled\n", "            if mixed_precision and scaler:\n", "                with torch.amp.autocast(device_type='cuda'):  # FIXED\n", "                    # Track forward time\n", "                    if track_step_metrics:\n", "                        forward_start = time.time()\n", "                    \n", "                    # Forward pass\n", "                    outputs = model(images)\n", "                    \n", "                    if track_step_metrics:\n", "                        forward_time = time.time() - forward_start\n", "                        loss_start = time.time()\n", "                    \n", "                    # Calculate loss\n", "                    loss, loss_components = compute_loss(outputs, masks)\n", "                    \n", "                    if track_step_metrics:\n", "                        loss_calc_time = time.time() - loss_start\n", "                        backward_start = time.time()\n", "                \n", "                # Backward pass with scaling (outside autocast)\n", "                optimizer.zero_grad()\n", "                scaler.scale(loss).backward()\n", "                \n", "                # Apply gradient clipping if specified\n", "                if clip_grad_norm:\n", "                    scaler.unscale_(optimizer)\n", "                    torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)\n", "                \n", "                # Update weights\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "                \n", "                if track_step_metrics:\n", "                    backward_time = time.time() - backward_start\n", "            \n", "            else:\n", "                # Standard precision training\n", "                # Track forward time\n", "                if track_step_metrics:\n", "                    forward_start = time.time()\n", "                \n", "                # Forward pass\n", "                outputs = model(images)\n", "                \n", "                if track_step_metrics:\n", "                    forward_time = time.time() - forward_start\n", "                    loss_start = time.time()\n", "                \n", "                # Calculate loss\n", "                loss, loss_components = compute_loss(outputs, masks)\n", "                \n", "                if track_step_metrics:\n", "                    loss_calc_time = time.time() - loss_start\n", "                    backward_start = time.time()\n", "                \n", "                # Backward pass\n", "                optimizer.zero_grad()\n", "                loss.backward()\n", "                \n", "                # Apply gradient clipping if specified\n", "                if clip_grad_norm:\n", "                    torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)\n", "                \n", "                # Update weights\n", "                optimizer.step()\n", "                \n", "                if track_step_metrics:\n", "                    backward_time = time.time() - backward_start\n", "            \n", "            # Update learning rate schedulers\n", "            for scheduler in lr_schedulers:\n", "                scheduler.step()\n", "                \n", "            # Calculate F1 score for this batch\n", "            batch_f1 = calculate_batch_f1(outputs, masks)\n", "            \n", "            # Update metrics\n", "            train_loss += loss.item()\n", "            train_f1 += batch_f1\n", "            batch_count += 1\n", "            \n", "            # Update progress bar\n", "            current_lr = optimizer.param_groups[0]['lr']\n", "            \n", "            train_pbar.set_description(\n", "                f\"Epoch {epoch+1}/{num_epochs}: \"\n", "                f\"loss={loss.item():.4f}, \"\n", "                f\"f1={batch_f1:.4f}, \"\n", "                f\"lr={current_lr:.6f}\"\n", "            )\n", "            \n", "            if track_step_metrics:\n", "                # Add step metrics for detailed tracking\n", "                step = epoch * len(train_loader) + batch_idx\n", "                step_history['steps'].append(step)\n", "                step_history['loss'].append(loss.item())\n", "                step_history['f1'].append(batch_f1)\n", "                step_history['lr'].append(current_lr)\n", "                step_history['data_time'].append(data_time)\n", "                step_history['forward_time'].append(forward_time)\n", "                step_history['loss_calc_time'].append(loss_calc_time)\n", "                step_history['backward_time'].append(backward_time)\n", "                \n", "                # Add more detailed info to progress bar\n", "                train_pbar.set_postfix(\n", "                    data=f\"{data_time:.2f}s\",\n", "                    fwd=f\"{forward_time:.2f}s\",\n", "                    loss_calc=f\"{loss_calc_time:.2f}s\",\n", "                    bwd=f\"{backward_time:.2f}s\"\n", "                )\n", "                \n", "                # Log to TensorBoard\n", "                if writer and batch_idx % 10 == 0:  # Log every 10 steps\n", "                    writer.add_scalar('Step/loss', loss.item(), step)\n", "                    writer.add_scalar('Step/f1', batch_f1, step)\n", "                    writer.add_scalar('Step/lr', current_lr, step)\n", "            \n", "            # Start timing next data load\n", "            if track_step_metrics:\n", "                data_start = time.time()\n", "        \n", "        # Calculate average training metrics\n", "        avg_train_loss = train_loss / batch_count if batch_count > 0 else 0\n", "        avg_train_f1 = train_f1 / batch_count if batch_count > 0 else 0\n", "        \n", "        # Evaluate on validation set\n", "        model.eval()\n", "        val_loss = 0.0\n", "        val_f1 = 0.0\n", "        val_batch_count = 0\n", "        \n", "        # Create progress bar for validation\n", "        val_pbar = tqdm(val_loader, leave=False)\n", "        val_pbar.set_description(f\"Validation {epoch+1}\")\n", "        \n", "        with torch.no_grad():\n", "            for images, masks in val_pbar:\n", "                # Move data to device\n", "                images = images.to(device, dtype=torch.float32)\n", "                masks = masks.to(device, dtype=torch.float32)\n", "                \n", "                # Dense sampling for validation if enabled\n", "                if use_dense_sampling and images.shape[2] >= 128:\n", "                    # Create overlapping patches for dense prediction\n", "                    patch_size = 128\n", "                    stride = 64\n", "                    h, w = images.shape[2], images.shape[3]\n", "                    \n", "                    # Initialize prediction tensor\n", "                    final_pred = torch.zeros_like(images)\n", "                    count_map = torch.zeros_like(images)\n", "                    \n", "                    # Process patches\n", "                    for y in range(0, h-patch_size+1, stride):\n", "                        for x in range(0, w-patch_size+1, stride):\n", "                            # Extract patch\n", "                            patch = images[:, :, y:y+patch_size, x:x+patch_size]\n", "                            \n", "                            # Predict on patch\n", "                            patch_pred = model(patch)\n", "                            if isinstance(patch_pred, dict):\n", "                                patch_heatmap = patch_pred['heatmaps']\n", "                            else:\n", "                                patch_heatmap = patch_pred\n", "                            \n", "                            # Accumulate predictions\n", "                            final_pred[:, :, y:y+patch_size, x:x+patch_size] += patch_heatmap\n", "                            count_map[:, :, y:y+patch_size, x:x+patch_size] += 1\n", "                    \n", "                    # Average predictions\n", "                    final_pred = final_pred / (count_map + 1e-7)\n", "                    \n", "                    # Use averaged prediction\n", "                    outputs = {'heatmaps': final_pred}\n", "                else:\n", "                    # Standard validation\n", "                    outputs = model(images)\n", "                \n", "                # Calculate loss\n", "                loss, _ = compute_loss(outputs, masks)\n", "                \n", "                # Calculate F1 score\n", "                batch_f1 = calculate_batch_f1(outputs, masks)\n", "                \n", "                # Update validation metrics\n", "                val_loss += loss.item()\n", "                val_f1 += batch_f1\n", "                val_batch_count += 1\n", "                \n", "                # Update validation progress bar\n", "                val_pbar.set_description(f\"Validation {epoch+1}: val_loss={loss.item():.4f}, val_f1={batch_f1:.4f}\")\n", "        \n", "        # Calculate average validation metrics\n", "        avg_val_loss = val_loss / val_batch_count if val_batch_count > 0 else 0\n", "        avg_val_f1 = val_f1 / val_batch_count if val_batch_count > 0 else 0\n", "        \n", "        # Apply plateau scheduler if enabled\n", "        if detect_plateau:\n", "            plateau_scheduler.step(avg_val_loss)\n", "        \n", "        # Store metrics in history\n", "        current_lr = optimizer.param_groups[0]['lr']\n", "        epoch_time = time.time() - epoch_start_time\n", "        \n", "        history['train_loss'].append(avg_train_loss)\n", "        history['val_loss'].append(avg_val_loss)\n", "        history['train_f1'].append(avg_train_f1)\n", "        history['val_f1'].append(avg_val_f1)\n", "        history['lr'].append(current_lr)\n", "        history['epochs'].append(epoch + 1)\n", "        history['time_per_epoch'].append(epoch_time)\n", "        \n", "        # Update main progress bar\n", "        pbar.update(1)  # Increment by 1\n", "        pbar.set_description(\n", "            f\"Epoch {epoch+1}/{num_epochs}: \"\n", "            f\"train_loss={avg_train_loss:.4f}, \"\n", "            f\"val_loss={avg_val_loss:.4f}, \"\n", "            f\"train_f1={avg_train_f1:.4f}, \"\n", "            f\"val_f1={avg_val_f1:.4f}\"\n", "        )\n", "        \n", "        # Log to TensorBoard\n", "        if writer:\n", "            writer.add_scalar('Epoch/train_loss', avg_train_loss, epoch)\n", "            writer.add_scalar('Epoch/val_loss', avg_val_loss, epoch)\n", "            writer.add_scalar('Epoch/train_f1', avg_train_f1, epoch)\n", "            writer.add_scalar('Epoch/val_f1', avg_val_f1, epoch)\n", "            writer.add_scalar('Epoch/learning_rate', current_lr, epoch)\n", "            writer.add_scalar('Epoch/time', epoch_time, epoch)\n", "            \n", "            # Log histograms of model parameters\n", "            if epoch % 5 == 0:  # Every 5 epochs to avoid slowdown\n", "                for name, param in model.named_parameters():\n", "                    if param.requires_grad:\n", "                        writer.add_histogram(f'Parameters/{name}', param.data.cpu().numpy(), epoch)\n", "        \n", "        # Save checkpoint if this is the best model\n", "        if avg_val_f1 > history['best_val_f1'] or (avg_val_f1 == history['best_val_f1'] and avg_val_loss < history['best_val_loss']):\n", "            history['best_epoch'] = epoch + 1\n", "            history['best_val_loss'] = avg_val_loss\n", "            history['best_val_f1'] = avg_val_f1\n", "            \n", "            best_model_state = {\n", "                'epoch': epoch + 1,\n", "                'model_state_dict': copy.deepcopy(model.state_dict()),\n", "                'optimizer_state_dict': copy.deepcopy(optimizer.state_dict()),\n", "                'train_loss': avg_train_loss,\n", "                'val_loss': avg_val_loss,\n", "                'train_f1': avg_train_f1,\n", "                'val_f1': avg_val_f1,\n", "                'history': history\n", "            }\n", "            \n", "            if checkpoint_dir:\n", "                checkpoint_path = os.path.join(checkpoint_dir, f\"best_model.pth\")\n", "                torch.save(best_model_state, checkpoint_path)\n", "                \n", "                if report_interval > 0 and (epoch + 1) % report_interval == 0:\n", "                    print(f\"\\nEpoch {epoch+1}: New best model saved! Val F1: {avg_val_f1:.4f}, Val Loss: {avg_val_loss:.4f}\")\n", "        \n", "        # Check for early stopping\n", "        early_stopping(avg_val_loss, model)\n", "        if early_stopping.early_stop:\n", "            print(f\"\\nEarly stopping triggered after {epoch+1} epochs\")\n", "            break\n", "        \n", "        # Regular reporting\n", "        if report_interval > 0 and (epoch + 1) % report_interval == 0:\n", "            print(f\"\\nEpoch {epoch+1}/{num_epochs}:\")\n", "            print(f\"  Train Loss: {avg_train_loss:.4f}, Train F1: {avg_train_f1:.4f}\")\n", "            print(f\"  Val Loss: {avg_val_loss:.4f}, Val F1: {avg_val_f1:.4f}\")\n", "            print(f\"  Learning Rate: {current_lr:.6f}\")\n", "            print(f\"  Best Epoch: {history['best_epoch']} (Val F1: {history['best_val_f1']:.4f})\")\n", "    \n", "    # Close progress bar\n", "    pbar.close()\n", "    \n", "    # Training complete - restore best model\n", "    if best_model_state:\n", "        model.load_state_dict(best_model_state['model_state_dict'])\n", "        print(f\"\\nRestored best model from epoch {best_model_state['epoch']} with Val F1: {best_model_state['val_f1']:.4f}\")\n", "    \n", "    # Close TensorBoard writer\n", "    if writer:\n", "        writer.close()\n", "    \n", "    # Add step history to return value if tracked\n", "    if track_step_metrics:\n", "        history['step_metrics'] = step_history\n", "    \n", "    return model, history"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized: EnhancedInstanceSpotDetector\n", "Initial threshold: 0.5000\n", "Initial threshold_abs: 0.010000\n", "Min distance: 2\n", "Found 2064 images and 2064 masks\n", "Model successfully moved to cuda:0\n", "Memory allocated: 0.00 GB\n", "Memory reserved: 0.00 GB\n", "Channels last memory format enabled\n", "SpotDataset initialized with 1652 images and 1652 masks\n", "SpotDataset initialized with 412 images and 412 masks\n", "Dataset split: 1652 training, 412 validation samples\n"]}], "source": ["# =================================\n", "# Model Initialization & Training Setup\n", "# =================================\n", "\n", "\n", "\n", "def setup_spot_detection_model(use_3d=False, batch_size=32, use_dilated_conv=True, \n", "                             use_group_norm=False, image_dir=None, mask_dir=None):\n", "    \"\"\"Initialize and setup the spot detection model and datasets\"\"\"\n", "    # Define the model\n", "    model = EnhancedInstanceSpotDetector(\n", "        in_channels=1, \n", "        base_filters=16, \n", "        levels=3,\n", "        is_3d=use_3d, \n", "        batch_norm=not use_group_norm, \n", "        use_attention=True,\n", "        use_residual=True, \n", "        dropout=0.1,\n", "        threshold=0.50,  # Fixed threshold\n", "        min_distance=2,  # For better separation\n", "        threshold_abs=0.01,\n", "        compute_flow=True,\n", "        use_dilated_conv=use_dilated_conv,\n", "        use_group_norm=use_group_norm\n", "    )\n", "\n", "    print(f\"Model initialized: {model.__class__.__name__}\")\n", "    print(f\"Initial threshold: {model.threshold:.4f}\")\n", "    print(f\"Initial threshold_abs: {model.threshold_abs:.6f}\")\n", "    print(f\"Min distance: {model.min_distance}\")\n", "\n", "    \n", "\n", "    # Add proper weight initialization\n", "    def init_weights(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "            if m.bias is not None:\n", "                nn.init.constant_(m.bias, 0)\n", "        elif isinstance(m, (nn.<PERSON>ch<PERSON>orm2d, nn.<PERSON>ch<PERSON>orm3d, nn.GroupNorm)):\n", "            nn.init.constant_(m.weight, 1)\n", "            nn.init.constant_(m.bias, 0)\n", "\n", "    # Apply initialization\n", "    model.apply(init_weights)\n", "    \n", "    # Create dataset manager with default or provided directories\n", "    if image_dir is None:\n", "        image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large images/*.tif\"\n", "    if mask_dir is None:\n", "        mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large mask filtered eccencicity 0.8/*.tif\"\n", "    \n", "    data_manager = SplitDatasetManager(\n", "        image_dir=image_dir,\n", "        mask_dir=mask_dir,\n", "        val_ratio=0.2,\n", "        seed=42,\n", "        is_3d=use_3d,\n", "        augmentation_strength='mild'\n", "    )\n", "\n", "    # Get device\n", "    device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "    # First move model to device\n", "    try:\n", "        model = model.to(device)\n", "        print(f\"Model successfully moved to {device}\")\n", "        \n", "        # Print initial memory usage\n", "        if torch.cuda.is_available():\n", "            print(f\"Memory allocated: {torch.cuda.memory_allocated(0) / 1e9:.2f} GB\")\n", "            print(f\"Memory reserved: {torch.cuda.memory_reserved(0) / 1e9:.2f} GB\")\n", "        \n", "        # Try enabling channels_last memory format after model is on GPU\n", "        if device.type == \"cuda\":\n", "            model = model.to(memory_format=torch.channels_last)\n", "            print(\"Channels last memory format enabled\")\n", "    except RuntimeError as e:\n", "        print(f\"Failed to move model to GPU: {e}\")\n", "        print(\"Falling back to <PERSON>\")\n", "        device = torch.device(\"cpu\")\n", "        model = model.to(device)\n", "\n", "    # Optimize batch size and workers based on GPU\n", "    if device.type == \"cuda\":\n", "        batch_size = batch_size  # Use provided batch size or default\n", "        num_workers = min(6, os.cpu_count() or 1)  # General guideline: 2-4× number of GPUs\n", "    else:\n", "        batch_size = 4   # Smaller for CPU\n", "        num_workers = 2\n", "\n", "    # Setup data loaders with optimized parameters\n", "    train_loader, val_loader = data_manager.setup(\n", "        batch_size=batch_size, \n", "        num_workers=num_workers,\n", "        pin_memory=False  # Only use pin_memory with CUDA\n", "    )\n", "\n", "    # Create checkpoint directory\n", "    checkpoint_dir = \"checkpoints/run_\" + datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "\n", "    # Set up TensorBoard logging\n", "    log_dir = os.path.join(checkpoint_dir, \"logs\")\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    return model, train_loader, val_loader, device, checkpoint_dir, log_dir\n", "\n", "# Optional: Add argparse-based or interactive configuration\n", "# import argparse\n", "# parser = argparse.ArgumentParser(description='Train spot detection model')\n", "# parser.add_argument('--3d', action='store_true', help='Use 3D model')\n", "# args = parser.parse_args()\n", "# use_3d = args.3d\n", "\n", "# Setup the model and datasets\n", "model, train_loader, val_loader, device, checkpoint_dir, log_dir = setup_spot_detection_model(\n", "    use_3d=False,  # Change to True for 3D\n", "    batch_size=32,  # Adjust based on available memory\n", "    use_dilated_conv=True,\n", "    use_group_norm=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["##DEBUG\n", "def run_training_debug(model, train_loader, val_loader, device, checkpoint_dir=None, log_dir=None,\n", "                num_epochs=400, learning_rate=3e-4,loss_method=\"bce_weight\",  # Use the weighted BCE-Dice loss\n", "    pos_weight=100.0,  # Heavy weighting for positive class\n", "    initial_threshold=0.01  # Low threshold for F1 during early training\n", "):\n", "    \"\"\"Debug version of run_training with extensive diagnostics\"\"\"\n", "    print(\"\\n=== Starting Debug Training ===\")\n", "    \n", "    # Examine data\n", "    print(\"\\n=== Checking Training Data ===\")\n", "    batch = next(iter(train_loader))\n", "    images, masks = batch\n", "    print(f\"Images shape: {images.shape}, min: {images.min()}, max: {images.max()}, mean: {images.mean()}\")\n", "    print(f\"Masks shape: {masks.shape}, min: {masks.min()}, max: {masks.max()}, mean: {masks.mean()}\")\n", "    print(f\"Positive pixels in masks: {(masks > 0.5).sum().item()}\")\n", "    \n", "    # Check model outputs before training\n", "    print(\"\\n=== Checking Initial Model Outputs ===\")\n", "    with torch.no_grad():\n", "        model.eval()\n", "        sample_output = model(images[:1].to(device, dtype=torch.float32))\n", "        if isinstance(sample_output, dict):\n", "            print(f\"Output type: dict with keys {list(sample_output.keys())}\")\n", "            heatmap = sample_output['heatmaps']\n", "        else:\n", "            print(\"Output type: tensor\")\n", "            heatmap = sample_output\n", "            \n", "        print(f\"Heatmap shape: {heatmap.shape}\")\n", "        print(f\"Heatmap range: min={heatmap.min().item()}, max={heatmap.max().item()}, mean={heatmap.mean().item()}\")\n", "        \n", "        # Apply sigmoid if needed\n", "        if heatmap.min() < 0 or heatmap.max() > 1:\n", "            heatmap_sigmoid = torch.sigmoid(heatmap)\n", "            print(f\"After sigmoid: min={heatmap_sigmoid.min().item()}, max={heatmap_sigmoid.max().item()}, mean={heatmap_sigmoid.mean().item()}\")\n", "            \n", "        # Check thresholding\n", "        threshold_values = [0.01, 0.05, 0.1, 0.3, 0.5]\n", "        for threshold in threshold_values:\n", "            positive_pred = (heatmap_sigmoid > threshold).float().sum().item()\n", "            print(f\"  Threshold {threshold}: {positive_pred} positive pixels\")\n", "    \n", "    # Initialize model weights with better defaults\n", "    def init_weights(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Conv3d)) and m.out_channels == 1:\n", "            # Final layer - initialize for spot detection with very negative bias\n", "            print(f\"Initializing output layer: {m}\")\n", "            nn.init.constant_(m.bias, -5.0)  # Start with NEGATIVE bias to predict zeros\n", "            nn.init.normal_(m.weight, mean=0.0, std=0.01)  # Small weights\n", "        elif isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "            # Hidden layers\n", "            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "            if m.bias is not None:\n", "                nn.init.constant_(m.bias, 0)\n", "        elif isinstance(m, (nn.<PERSON>chNorm2d, nn.BatchNorm3d)):\n", "            nn.init.constant_(m.weight, 1)\n", "            nn.init.constant_(m.bias, 0)\n", "    \n", "    # Apply weight initialization\n", "    model.apply(init_weights)\n", "    \n", "    # Check model outputs after initialization\n", "    print(\"\\n=== Checking Model Outputs After Initialization ===\")\n", "    with torch.no_grad():\n", "        model.eval()\n", "        sample_output = model(images[:1].to(device, dtype=torch.float32))\n", "        if isinstance(sample_output, dict):\n", "            heatmap = sample_output['heatmaps']\n", "        else:\n", "            heatmap = sample_output\n", "            \n", "        print(f\"Heatmap range: min={heatmap.min().item()}, max={heatmap.max().item()}, mean={heatmap.mean().item()}\")\n", "        \n", "        # Apply sigmoid if needed\n", "        if heatmap.min() < 0 or heatmap.max() > 1:\n", "            heatmap_sigmoid = torch.sigmoid(heatmap)\n", "            print(f\"After sigmoid: min={heatmap_sigmoid.min().item()}, max={heatmap_sigmoid.max().item()}, mean={heatmap_sigmoid.mean().item()}\")\n", "    \n", "    # Set loss parameters - use dice loss for stability\n", "    loss_params = {\n", "        \"loss_type\": \"dice\",  # Start with simple dice loss\n", "        \"flow_loss_weight\": 0.2\n", "    }\n", "    \n", "    # Begin training with lower learning rate and Adam optimizer\n", "    print(\"\\n=== Starting Training with Modified Parameters ===\")\n", "    trained_model, history = train_model_with_advanced_tracking(\n", "        model=model, \n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        num_epochs=num_epochs,\n", "        device=device,\n", "        initial_lr=learning_rate,\n", "        weight_decay=1e-5,  # Lower weight decay\n", "        patience=25,\n", "        checkpoint_dir=checkpoint_dir,\n", "        mixed_precision=True,\n", "        clip_grad_norm=1.0,\n", "        warmup_epochs=5,\n", "        report_interval=1,\n", "        log_dir=log_dir,\n", "        track_step_metrics=True,\n", "        detect_plateau=True,\n", "        plateau_patience=5,\n", "        **loss_params\n", "    )\n", "    \n", "    return trained_model, history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#=================================\n", "# Complete Training Workflow\n", "# =================================\n", "def run_training(model, train_loader, val_loader, device, checkpoint_dir=None, log_dir=None,\n", "                num_epochs=400, learning_rate=1e-3, loss_method=\"combined\"):\n", "    \"\"\"Run complete training workflow with selected loss function\n", "    \n", "    Args:\n", "        model: Model to train\n", "        train_loader: Training data loader\n", "        val_loader: Validation data loader\n", "        device: Training device (cuda/cpu)\n", "        checkpoint_dir: Directory to save checkpoints\n", "        log_dir: Directory for logs\n", "        num_epochs: Number of training epochs\n", "        learning_rate: Initial learning rate\n", "        loss_method: Which loss function to use ('dice', 'focal', 'combined', \n", "                    'instance_aware', or 'comprehensive')\n", "    \n", "    Returns:\n", "        Trained model and history\n", "    \"\"\"\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"Starting training: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "    print(f\"{'='*50}\\n\")\n", "    \n", "    # Set loss parameters based on method\n", "    if loss_method == \"comprehensive\":\n", "        # Full combined loss\n", "        loss_params = {\n", "            \"loss_type\": \"comprehensive\",\n", "            \"flow_loss_weight\": 0.2,\n", "            \"contrastive_weight\": 0.1,\n", "            \"dense_region_weight\": 0.3,\n", "            \"multi_scale_weight\": 0.2,\n", "            \"use_focal_loss\": True\n", "        }\n", "    elif loss_method == \"instance_aware\":\n", "        # Instance-aware loss\n", "        loss_params = {\n", "            \"loss_type\": \"instance_aware\",\n", "            \"flow_loss_weight\": 0.2\n", "        }\n", "    elif loss_method == \"focal\":\n", "        # Pure focal loss\n", "        loss_params = {\n", "            \"loss_type\": \"focal\",\n", "            \"flow_loss_weight\": 0.2\n", "        }\n", "    elif loss_method == \"dice\":\n", "        # Basic dice loss\n", "        loss_params = {\n", "            \"loss_type\": \"dice\",\n", "            \"flow_loss_weight\": 0.2\n", "        }\n", "    else:\n", "        # Default to combined loss\n", "        loss_params = {\n", "            \"loss_type\": \"combined\",\n", "            \"flow_loss_weight\": 0.2,\n", "            \"use_focal_loss\": True\n", "        }\n", "    \n", "    # Initialize model weights\n", "    def init_weights(m):\n", "        if isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "            if m.bias is not None:\n", "                nn.init.constant_(m.bias, 0)\n", "        elif isinstance(m, (nn.<PERSON>chNorm2d, nn.BatchNorm3d)):\n", "            nn.init.constant_(m.weight, 1)\n", "            nn.init.constant_(m.bias, 0)\n", "    \n", "    # Apply weight initialization\n", "    model.apply(init_weights)\n", "    \n", "    # Train with selected parameters\n", "    trained_model, history = train_model_with_advanced_tracking(\n", "        model=model, \n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        num_epochs=num_epochs,\n", "        device=device,\n", "        initial_lr=learning_rate,\n", "        weight_decay=1e-4,\n", "        patience=25,\n", "        checkpoint_dir=checkpoint_dir,\n", "        mixed_precision=True,\n", "        clip_grad_norm=1.0,\n", "        warmup_epochs=5,\n", "        report_interval=1,\n", "        log_dir=log_dir,\n", "        track_step_metrics=True,\n", "        detect_plateau=True,\n", "        plateau_patience=5,\n", "        **loss_params  # Pass all loss parameters\n", "    )\n", "    \n", "    # Post-training visualization\n", "    if checkpoint_dir:\n", "        # Test on a sample image with instance label generation\n", "        print(\"\\nGenerating sample predictions...\")\n", "        try:\n", "            sample_batch = next(iter(val_loader))\n", "            sample_image = sample_batch[0][0:1].to(device).float()\n", "            sample_mask = sample_batch[1][0:1].to(device).float()\n", "            \n", "            # Generate prediction\n", "            with torch.no_grad():\n", "                trained_model.eval()\n", "                outputs = trained_model(sample_image)\n", "                # Post-process to generate instance labels and spots\n", "                outputs = trained_model.post_process_inference(outputs)\n", "            \n", "            # Visualize results\n", "            instance_labels = outputs['instance_labels'][0, 0].cpu().numpy()\n", "            spot_coords = outputs['spot_coords'].cpu().numpy()\n", "            \n", "            fig, axs = plt.subplots(1, 3, figsize=(15, 5))\n", "            axs[0].imshow(sample_image[0, 0].cpu().numpy(), cmap='gray')\n", "            axs[0].set_title(\"Input Image\")\n", "            \n", "            axs[1].imshow(sample_mask[0, 0].cpu().numpy(), cmap='gray')\n", "            axs[1].set_title(\"Ground Truth\")\n", "            \n", "            # Visualize instance labels with colormap\n", "            from matplotlib.colors import ListedColormap\n", "            import matplotlib as mpl\n", "            \n", "            # Create custom colormap (black for background)\n", "            colors = np.random.rand(256, 3)\n", "            colors[0] = [0, 0, 0]  # Background is black\n", "            cmap = ListedColormap(colors)\n", "            \n", "            axs[2].imshow(instance_labels, cmap=cmap, norm=mpl.colors.Normalize(0, max(1, instance_labels.max())))\n", "            axs[2].set_title(f\"Instance Labels ({len(spot_coords)} spots)\")\n", "            \n", "            # Add spot markers\n", "            for y, x in spot_coords:\n", "                axs[2].plot(x, y, 'r+', markersize=10)\n", "            \n", "            plt.tight_layout()\n", "            plt.savefig(os.path.join(checkpoint_dir, \"instance_labels.png\"))\n", "            plt.close()\n", "            \n", "            print(f\"Sample prediction saved to {os.path.join(checkpoint_dir, 'instance_labels.png')}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error testing model: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "    \n", "    # Save final model\n", "    if checkpoint_dir:\n", "        final_model_path = os.path.join(checkpoint_dir, \"final_model.pth\")\n", "        torch.save({\n", "            'model_state_dict': trained_model.state_dict(),\n", "            'threshold': trained_model.threshold if hasattr(trained_model, 'threshold') else 0.5,\n", "            'threshold_abs': trained_model.threshold_abs if hasattr(trained_model, 'threshold_abs') else 0.001,\n", "        }, final_model_path)\n", "        print(f\"Final model saved to {final_model_path}\")\n", "    \n", "    return trained_model, history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "trained_model, history = run_training_debug(\n", "    model=model,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    device=device,\n", "    checkpoint_dir=checkpoint_dir,\n", "    log_dir=log_dir,\n", "    num_epochs=400,\n", "    learning_rate=1e-3,\n", "    loss_method=\"dice\"  # Choose: \"dice\", \"focal\", \"combined\", \"instance_aware\", \"comprehensive\"\n", ")\n", "\n", "# Test the trained model on a sample from validation set\n", "print(\"\\nTesting model on sample image...\")\n", "try:\n", "    sample_batch = next(iter(val_loader))\n", "    sample_image = sample_batch[0][0:1].to(device).float()  # Force float type\n", "    sample_mask = sample_batch[1][0:1].to(device).float()\n", "    \n", "    # Generate prediction\n", "    with torch.no_grad():\n", "        trained_model.eval()\n", "        prediction = trained_model(sample_image)\n", "        # Process predictions to get spot coordinates\n", "        prediction = trained_model.post_process_inference(prediction)\n", "        \n", "    # Visualize results\n", "    result_fig = visualize_results(\n", "        sample_image[0].cpu().numpy(), \n", "        prediction, \n", "        sample_mask[0].cpu().numpy(),\n", "        show=True,\n", "        save_path=os.path.join(checkpoint_dir, \"sample_prediction.png\")\n", "    )\n", "    \n", "    print(f\"Sample prediction saved to {os.path.join(checkpoint_dir, 'sample_prediction.png')}\")\n", "    \n", "    # Print spot count\n", "    print(f\"Detected {len(prediction.get('spot_coords', [])) if isinstance(prediction.get('spot_coords'), list) else prediction.get('spot_coords').shape[0]} spots in sample image\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error testing model: {e}\")\n", "    import traceback\n", "    traceback.print_exc()\n", "\n", "print(\"\\nTraining complete!\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def weighted_bce_dice_loss(pred, target, smooth=1.0, pos_weight=500.0):\n", "    \"\"\"\n", "    Heavily weighted BCE + Dice loss for extreme class imbalance\n", "    \n", "    Args:\n", "        pred: Model prediction (dict or tensor)\n", "        target: Ground truth mask\n", "        smooth: Smoothing factor\n", "        pos_weight: Weight factor for positive class (very high)\n", "        \n", "    Returns:\n", "        Combined loss value\n", "    \"\"\"\n", "    # Extract heatmap\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Ensure proper shapes\n", "    if heatmap.shape != target.shape:\n", "        target = F.interpolate(target, size=heatmap.shape[2:], mode='nearest')\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Create weight tensor for BCE - EXTREME positive weighting\n", "    weight = torch.ones_like(target)\n", "    weight[target > 0.5] = pos_weight  # Very high weight for positive pixels\n", "    \n", "    # Calculate BCE loss with heavy class weighting\n", "    eps = 1e-6  # For numerical stability\n", "    bce = -(weight * target * torch.log(heatmap + eps) + \n", "            (1 - target) * torch.log(1 - heatmap + eps))\n", "    bce_loss = bce.mean()\n", "    \n", "    # Calculate Dice with class weighting\n", "    weighted_target = target * (pos_weight - 1) + 1\n", "    intersection = torch.sum(heatmap * target * weighted_target)\n", "    union = torch.sum(heatmap * weighted_target) + torch.sum(target * weighted_target) + smooth\n", "    dice = 2.0 * intersection / union\n", "    dice_loss = 1.0 - dice\n", "    \n", "    # Combined loss - more weight on BCE for better gradients\n", "    return 0.8 * bce_loss + 0.2 * dice_loss, {\n", "        'bce_loss': bce_loss.item(),\n", "        'dice_loss': dice_loss.item()\n", "    }"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def spot_focal_loss(pred, target, alpha=0.8, gamma=5.0):\n", "    \"\"\"\n", "    Specialized focal loss for spot detection with extreme class imbalance\n", "    \n", "    Args:\n", "        pred: Model prediction\n", "        target: Ground truth\n", "        alpha: Weight for positive class (high value)\n", "        gamma: Focusing parameter (higher value focuses more on hard examples)\n", "        \n", "    Returns:\n", "        Loss value\n", "    \"\"\"\n", "    # Extract heatmap\n", "    if isinstance(pred, dict):\n", "        heatmap = pred['heatmaps']\n", "    else:\n", "        heatmap = pred\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Basic BCE loss\n", "    eps = 1e-6\n", "    bce = -(target * torch.log(heatmap + eps) + (1 - target) * torch.log(1 - heatmap + eps))\n", "    \n", "    # Apply focal weighting - down-weight easy examples\n", "    p_t = heatmap * target + (1 - heatmap) * (1 - target)\n", "    focal_weight = (1 - p_t) ** gamma\n", "    \n", "    # Apply alpha weighting for class imbalance - heavily weight positive class\n", "    alpha_weight = alpha * target + (1 - alpha) * (1 - target)\n", "    \n", "    # Combine weights and calculate loss\n", "    focal_loss = (focal_weight * alpha_weight * bce).mean()\n", "    \n", "    return focal_loss, {'focal_loss': focal_loss.item()}"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def train_with_positive_sampling(model, train_loader, device, num_epochs=50, learning_rate=1e-4):\n", "    \"\"\"\n", "    Train with positive patch sampling for extreme class imbalance\n", "    \n", "    Args:\n", "        model: Model to train\n", "        train_loader: Training data loader\n", "        device: Training device\n", "        num_epochs: Number of training epochs\n", "        learning_rate: Initial learning rate\n", "    \"\"\"\n", "    # Initialize optimizer and scheduler\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Use spot focal loss\n", "    criterion = spot_focal_loss\n", "    \n", "    # Main training loop\n", "    for epoch in range(num_epochs):\n", "        model.train()\n", "        epoch_loss = 0\n", "        batch_count = 0\n", "        \n", "        # Track positive samples\n", "        total_patches = 0\n", "        positive_patches = 0\n", "        \n", "        for batch_idx, (images, masks) in enumerate(train_loader):\n", "            # Get batch of full images\n", "            images = images.to(device)\n", "            masks = masks.to(device)\n", "            \n", "            # Find images with positive pixels\n", "            pos_indices = []\n", "            for i in range(len(masks)):\n", "                if masks[i].sum() > 0:\n", "                    pos_indices.append(i)\n", "            \n", "            # If we have some positive images\n", "            if len(pos_indices) > 0:\n", "                # Extract positive images\n", "                pos_images = images[pos_indices]\n", "                pos_masks = masks[pos_indices]\n", "                \n", "                # Extract 64x64 patches centered on positive regions\n", "                patch_size = 64\n", "                patches_img = []\n", "                patches_mask = []\n", "                \n", "                for i in range(len(pos_indices)):\n", "                    # Find positive pixel locations\n", "                    pos_locs = torch.nonzero(pos_masks[i] > 0.5)\n", "                    if len(pos_locs) > 0:\n", "                        # Choose random positive pixel\n", "                        idx = torch.randint(0, len(pos_locs), (1,)).item()\n", "                        y, x = pos_locs[idx, -2], pos_locs[idx, -1]\n", "                        \n", "                        # Calculate patch boundaries with padding\n", "                        y1 = max(0, y - patch_size//2)\n", "                        x1 = max(0, x - patch_size//2)\n", "                        y2 = min(pos_masks.shape[-2], y1 + patch_size)\n", "                        x2 = min(pos_masks.shape[-1], x1 + patch_size)\n", "                        \n", "                        # Extract patch\n", "                        img_patch = pos_images[i, :, y1:y2, x1:x2]\n", "                        mask_patch = pos_masks[i, :, y1:y2, x1:x2]\n", "                        \n", "                        # Pad if needed to maintain patch size\n", "                        if img_patch.shape[-2] < patch_size or img_patch.shape[-1] < patch_size:\n", "                            p_y1 = patch_size - img_patch.shape[-2] if img_patch.shape[-2] < patch_size else 0\n", "                            p_x1 = patch_size - img_patch.shape[-1] if img_patch.shape[-1] < patch_size else 0\n", "                            img_patch = F.pad(img_patch, (0, p_x1, 0, p_y1))\n", "                            mask_patch = F.pad(mask_patch, (0, p_x1, 0, p_y1))\n", "                        \n", "                        patches_img.append(img_patch)\n", "                        patches_mask.append(mask_patch)\n", "                        positive_patches += 1\n", "                \n", "                # Also add some random negative patches for balance\n", "                for i in range(min(len(patches_img), 8)):  # Add up to 8 negative patches\n", "                    # Choose random image and location\n", "                    img_idx = torch.randint(0, len(images), (1,)).item()\n", "                    y = torch.randint(0, images.shape[-2] - patch_size, (1,)).item()\n", "                    x = torch.randint(0, images.shape[-1] - patch_size, (1,)).item()\n", "                    \n", "                    # Extract negative patch\n", "                    img_patch = images[img_idx, :, y:y+patch_size, x:x+patch_size]\n", "                    mask_patch = masks[img_idx, :, y:y+patch_size, x:x+patch_size]\n", "                    \n", "                    # Only add if it's truly negative (no positive pixels)\n", "                    if mask_patch.sum() == 0:\n", "                        patches_img.append(img_patch)\n", "                        patches_mask.append(mask_patch)\n", "                \n", "                # If we have patches, convert to tensors and train\n", "                if patches_img:\n", "                    total_patches += len(patches_img)\n", "                    \n", "                    # Stack patches into batch\n", "                    patch_images = torch.stack(patches_img)\n", "                    patch_masks = torch.stack(patches_mask)\n", "                    \n", "                    # Forward pass and loss calculation\n", "                    optimizer.zero_grad()\n", "                    outputs = model(patch_images)\n", "                    loss, _ = criterion(outputs, patch_masks, alpha=0.9, gamma=5.0)\n", "                    \n", "                    # Backward and optimize\n", "                    loss.backward()\n", "                    optimizer.step()\n", "                    \n", "                    epoch_loss += loss.item()\n", "                    batch_count += 1\n", "            \n", "            # Always process some full images too (mixed training)\n", "            if batch_idx % 2 == 0:\n", "                # Forward pass on full images\n", "                optimizer.zero_grad()\n", "                outputs = model(images)\n", "                loss, _ = criterion(outputs, masks, alpha=0.9, gamma=5.0)\n", "                \n", "                # Backward and optimize\n", "                loss.backward()\n", "                optimizer.step()\n", "                \n", "                epoch_loss += loss.item()\n", "                batch_count += 1\n", "        \n", "        # Calculate average epoch loss\n", "        avg_loss = epoch_loss / max(batch_count, 1)\n", "        print(f\"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.6f}, \"\n", "              f\"Positive patches: {positive_patches}/{total_patches}\")\n", "        \n", "        # Update learning rate\n", "        scheduler.step(avg_loss)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def initialize_model_for_spots(model):\n", "    \"\"\"Initialize model specifically for spot detection with extreme imbalance\"\"\"\n", "    # Initialize main convolutional layers\n", "    for m in model.modules():\n", "        if isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "            nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "            if m.bias is not None:\n", "                nn.init.constant_(m.bias, 0)\n", "        elif isinstance(m, (nn.<PERSON>chNorm2d, nn.BatchNorm3d)):\n", "            nn.init.constant_(m.weight, 1)\n", "            nn.init.constant_(m.bias, 0)\n", "    \n", "    # Specially initialize final layer to start very negative\n", "    for m in model.modules():\n", "        if isinstance(m, (nn.Conv2d, nn.Conv3d)) and m.out_channels == 1:\n", "            print(f\"Initializing output layer: {m}\")\n", "            nn.init.constant_(m.bias, -3.0)  # Start very negative (sigmoid will be close to 0)\n", "            nn.init.normal_(m.weight, std=0.01)  # Small weights\n", "    \n", "    # Update model forward if needed to ensure proper output range\n", "    original_forward = model.forward\n", "    \n", "    def improved_forward(x):\n", "        \"\"\"Modified forward pass for better stability with extreme imbalance\"\"\"\n", "        output = original_forward(x)\n", "        \n", "        # If dictionary output, ensure main heatmap has proper range\n", "        if isinstance(output, dict) and 'heatmaps' in output:\n", "            # No need to modify - already returning logits for BCE loss\n", "            pass\n", "        elif not isinstance(output, dict):\n", "            # If raw tensor, ensure it's returning logits not probabilities\n", "            pass\n", "        \n", "        return output\n", "    \n", "    model.forward = improved_forward\n", "    return model"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def train_model_for_extreme_imbalance(model, train_loader, val_loader, device, num_epochs=400):\n", "    \"\"\"Complete training solution for extreme class imbalance in spot detection\"\"\"\n", "    print(\"Starting specialized training for extreme spot class imbalance\")\n", "    \n", "    # Step 1: Initialize model weights optimally for spot detection\n", "    model = initialize_model_for_spots(model)\n", "    \n", "    # Step 2: Set up optimizer with low learning rate and weight decay\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=5e-5, weight_decay=1e-5)\n", "    \n", "    # Step 3: Set up learning rate scheduler for better convergence\n", "    scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "        optimizer, max_lr=2e-4, total_steps=num_epochs * len(train_loader),\n", "        pct_start=0.1, anneal_strategy='cos'\n", "    )\n", "    \n", "    # Step 4: Use our specialized loss function\n", "    def loss_fn(pred, target):\n", "        # Combined weighted BCE and focal loss\n", "        return weighted_bce_dice_loss(pred, target, pos_weight=1000.0)\n", "    \n", "    # Start training loop\n", "    pbar = tqdm(total=num_epochs, desc=\"Training progress\")\n", "    best_f1 = 0.0\n", "    \n", "    for epoch in range(num_epochs):\n", "        # Train epoch\n", "        model.train()\n", "        train_loss = 0.0\n", "        batch_count = 0\n", "        \n", "        for images, masks in train_loader:\n", "            images = images.to(device)\n", "            masks = masks.to(device)\n", "            \n", "            # Forward pass\n", "            optimizer.zero_grad()\n", "            outputs = model(images)\n", "            loss, _ = loss_fn(outputs, masks)\n", "            \n", "            # Backward pass\n", "            loss.backward()\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)  # Clip gradients\n", "            optimizer.step()\n", "            scheduler.step()\n", "            \n", "            train_loss += loss.item()\n", "            batch_count += 1\n", "        \n", "        # Validate epoch with low threshold\n", "        model.eval()\n", "        val_f1 = 0.0\n", "        val_loss = 0.0\n", "        val_count = 0\n", "        \n", "        with torch.no_grad():\n", "            for images, masks in val_loader:\n", "                images = images.to(device)\n", "                masks = masks.to(device)\n", "                \n", "                outputs = model(images)\n", "                loss, _ = loss_fn(outputs, masks)\n", "                val_loss += loss.item()\n", "                \n", "                # Calculate F1 with very low threshold\n", "                f1 = calculate_f1_with_low_threshold(outputs, masks, threshold=0.05)\n", "                val_f1 += f1\n", "                val_count += 1\n", "        \n", "        # Calculate averages\n", "        avg_train_loss = train_loss / max(batch_count, 1)\n", "        avg_val_loss = val_loss / max(val_count, 1)\n", "        avg_val_f1 = val_f1 / max(val_count, 1)\n", "        \n", "        # Update progress bar\n", "        pbar.set_description(\n", "            f\"Epoch {epoch+1}/{num_epochs}: \"\n", "            f\"train_loss={avg_train_loss:.4f}, \"\n", "            f\"val_loss={avg_val_loss:.4f}, \"\n", "            f\"val_f1={avg_val_f1:.4f}\"\n", "        )\n", "        pbar.update(1)\n", "        \n", "        # Save best model\n", "        if avg_val_f1 > best_f1:\n", "            best_f1 = avg_val_f1\n", "            print(f\"\\nNew best model! F1: {best_f1:.4f}\")\n", "            torch.save(model.state_dict(), \"best_spot_model.pth\")\n", "    \n", "    pbar.close()\n", "    return model\n", "\n", "def calculate_f1_with_low_threshold(outputs, targets, threshold=0.05):\n", "    \"\"\"Calculate F1 score with very low threshold for early training\"\"\"\n", "    with torch.no_grad():\n", "        # Extract heatmap\n", "        if isinstance(outputs, dict):\n", "            heatmap = outputs['heatmaps']\n", "        else:\n", "            heatmap = outputs\n", "            \n", "        # Apply sigmoid if needed\n", "        if heatmap.min() < 0 or heatmap.max() > 1:\n", "            heatmap = torch.sigmoid(heatmap)\n", "        \n", "        # Use low threshold during early training\n", "        pred_binary = (heatmap > threshold).float()\n", "        target_binary = (targets > 0.5).float()\n", "        \n", "        # Calculate metrics\n", "        tp = torch.sum(pred_binary * target_binary).item()\n", "        fp = torch.sum(pred_binary * (1 - target_binary)).item()\n", "        fn = torch.sum((1 - pred_binary) * target_binary).item()\n", "        \n", "        # Print diagnostic info\n", "        print(f\"TP: {tp}, FP: {fp}, FN: {fn}, Threshold: {threshold}\")\n", "        \n", "        # Calculate precision and recall\n", "        precision = tp / (tp + fp + 1e-8)\n", "        recall = tp / (tp + fn + 1e-8)\n", "        \n", "        # Calculate F1 score\n", "        f1 = 2 * precision * recall / (precision + recall + 1e-8)\n", "        \n", "        return f1"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing output layer: Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(32, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Starting specialized training for extreme spot class imbalance\n", "Initializing output layer: Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(32, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))\n", "Initializing output layer: Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5ab0e197ec8240109dcaa1d4990193e5", "version_major": 2, "version_minor": 0}, "text/plain": ["Training progress:   0%|          | 0/200 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["TP: 0.0, FP: 64666.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 161935.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 7573.0, FN: 59.0, Threshold: 0.05\n", "TP: 0.0, FP: 14512.0, FN: 1296.0, Threshold: 0.05\n", "TP: 0.0, FP: 97311.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 215131.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 162472.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 61449.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 22869.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 45716.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 25396.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 129989.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 108935.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 687.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 2900.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 143.0, FN: 59.0, Threshold: 0.05\n", "TP: 0.0, FP: 191.0, FN: 1296.0, Threshold: 0.05\n", "TP: 0.0, FP: 1671.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 3668.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 5149.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 798.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 330.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 671.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 532.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 2597.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 1742.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 6.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 28.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 5.0, FN: 59.0, Threshold: 0.05\n", "TP: 0.0, FP: 0.0, FN: 1296.0, Threshold: 0.05\n", "TP: 0.0, FP: 19.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 12.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 70.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 0.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 4.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 7.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 6.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 24.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 11.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 1254.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 534.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 902.0, FN: 59.0, Threshold: 0.05\n", "TP: 0.0, FP: 606.0, FN: 1296.0, Threshold: 0.05\n", "TP: 6.0, FP: 1021.0, FN: 1221.0, Threshold: 0.05\n", "TP: 1.0, FP: 604.0, FN: 900.0, Threshold: 0.05\n", "TP: 0.0, FP: 671.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 539.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 599.0, FN: 400.0, Threshold: 0.05\n", "TP: 4.0, FP: 837.0, FN: 372.0, Threshold: 0.05\n", "TP: 3.0, FP: 439.0, FN: 45.0, Threshold: 0.05\n", "TP: 0.0, FP: 509.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 355.0, FN: 82.0, Threshold: 0.05\n", "\n", "New best model! F1: 0.0020\n", "TP: 413.0, FP: 1966073.0, FN: 115.0, Threshold: 0.05\n", "TP: 210.0, FP: 1421370.0, FN: 157.0, Threshold: 0.05\n", "TP: 59.0, FP: 2152127.0, FN: 0.0, Threshold: 0.05\n", "TP: 399.0, FP: 1838316.0, FN: 897.0, Threshold: 0.05\n", "TP: 557.0, FP: 2002798.0, FN: 670.0, Threshold: 0.05\n", "TP: 535.0, FP: 1169543.0, FN: 366.0, Threshold: 0.05\n", "TP: 588.0, FP: 1288071.0, FN: 370.0, Threshold: 0.05\n", "TP: 288.0, FP: 1199690.0, FN: 176.0, Threshold: 0.05\n", "TP: 171.0, FP: 1657311.0, FN: 229.0, Threshold: 0.05\n", "TP: 302.0, FP: 1781643.0, FN: 74.0, Threshold: 0.05\n", "TP: 48.0, FP: 1448420.0, FN: 0.0, Threshold: 0.05\n", "TP: 76.0, FP: 1341482.0, FN: 69.0, Threshold: 0.05\n", "TP: 62.0, FP: 1131950.0, FN: 20.0, Threshold: 0.05\n", "TP: 528.0, FP: 4512437.0, FN: 0.0, Threshold: 0.05\n", "TP: 337.0, FP: 3386124.0, FN: 30.0, Threshold: 0.05\n", "TP: 59.0, FP: 4934057.0, FN: 0.0, Threshold: 0.05\n", "TP: 586.0, FP: 4157263.0, FN: 710.0, Threshold: 0.05\n", "TP: 1204.0, FP: 4636964.0, FN: 23.0, Threshold: 0.05\n", "TP: 899.0, FP: 2737318.0, FN: 2.0, Threshold: 0.05\n", "TP: 954.0, FP: 3223910.0, FN: 4.0, Threshold: 0.05\n", "TP: 462.0, FP: 2805364.0, FN: 2.0, Threshold: 0.05\n", "TP: 400.0, FP: 3741831.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 4203914.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3270014.0, FN: 0.0, Threshold: 0.05\n", "TP: 140.0, FP: 3108230.0, FN: 5.0, Threshold: 0.05\n", "TP: 82.0, FP: 2505048.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 5014015.0, FN: 0.0, Threshold: 0.05\n", "TP: 367.0, FP: 3727064.0, FN: 0.0, Threshold: 0.05\n", "TP: 59.0, FP: 5351445.0, FN: 0.0, Threshold: 0.05\n", "TP: 663.0, FP: 4514218.0, FN: 633.0, Threshold: 0.05\n", "TP: 1227.0, FP: 5107701.0, FN: 0.0, Threshold: 0.05\n", "TP: 901.0, FP: 3054521.0, FN: 0.0, Threshold: 0.05\n", "TP: 958.0, FP: 3576561.0, FN: 0.0, Threshold: 0.05\n", "TP: 464.0, FP: 3150118.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 4069127.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 4669267.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3667475.0, FN: 0.0, Threshold: 0.05\n", "TP: 145.0, FP: 3490440.0, FN: 0.0, Threshold: 0.05\n", "TP: 82.0, FP: 2750194.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 4630456.0, FN: 0.0, Threshold: 0.05\n", "TP: 367.0, FP: 3437005.0, FN: 0.0, Threshold: 0.05\n", "TP: 59.0, FP: 4949161.0, FN: 0.0, Threshold: 0.05\n", "TP: 599.0, FP: 4145086.0, FN: 697.0, Threshold: 0.05\n", "TP: 1227.0, FP: 4682574.0, FN: 0.0, Threshold: 0.05\n", "TP: 901.0, FP: 2806171.0, FN: 0.0, Threshold: 0.05\n", "TP: 958.0, FP: 3285932.0, FN: 0.0, Threshold: 0.05\n", "TP: 464.0, FP: 2879010.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 3712604.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 4320445.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3335798.0, FN: 0.0, Threshold: 0.05\n", "TP: 145.0, FP: 3208517.0, FN: 0.0, Threshold: 0.05\n", "TP: 82.0, FP: 2510343.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 4700971.0, FN: 0.0, Threshold: 0.05\n", "TP: 367.0, FP: 3547076.0, FN: 0.0, Threshold: 0.05\n", "TP: 59.0, FP: 5031153.0, FN: 0.0, Threshold: 0.05\n", "TP: 574.0, FP: 4251674.0, FN: 722.0, Threshold: 0.05\n", "TP: 1227.0, FP: 4786665.0, FN: 0.0, Threshold: 0.05\n", "TP: 901.0, FP: 2884515.0, FN: 0.0, Threshold: 0.05\n", "TP: 958.0, FP: 3329653.0, FN: 0.0, Threshold: 0.05\n", "TP: 464.0, FP: 2924513.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 3790791.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 4317096.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3374057.0, FN: 0.0, Threshold: 0.05\n", "TP: 145.0, FP: 3245661.0, FN: 0.0, Threshold: 0.05\n", "TP: 82.0, FP: 2572175.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 3484696.0, FN: 0.0, Threshold: 0.05\n", "TP: 348.0, FP: 2612875.0, FN: 19.0, Threshold: 0.05\n", "TP: 59.0, FP: 3848322.0, FN: 0.0, Threshold: 0.05\n", "TP: 874.0, FP: 3295154.0, FN: 422.0, Threshold: 0.05\n", "TP: 1027.0, FP: 3563944.0, FN: 200.0, Threshold: 0.05\n", "TP: 849.0, FP: 2118285.0, FN: 52.0, Threshold: 0.05\n", "TP: 902.0, FP: 2499490.0, FN: 56.0, Threshold: 0.05\n", "TP: 464.0, FP: 2297802.0, FN: 0.0, Threshold: 0.05\n", "TP: 395.0, FP: 3022733.0, FN: 5.0, Threshold: 0.05\n", "TP: 376.0, FP: 3318682.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 2621493.0, FN: 0.0, Threshold: 0.05\n", "TP: 129.0, FP: 2507386.0, FN: 16.0, Threshold: 0.05\n", "TP: 82.0, FP: 2052903.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 1303376.0, FN: 0.0, Threshold: 0.05\n", "TP: 248.0, FP: 951684.0, FN: 119.0, Threshold: 0.05\n", "TP: 59.0, FP: 1440972.0, FN: 0.0, Threshold: 0.05\n", "TP: 477.0, FP: 1231607.0, FN: 819.0, Threshold: 0.05\n", "TP: 496.0, FP: 1354792.0, FN: 731.0, Threshold: 0.05\n", "TP: 557.0, FP: 790067.0, FN: 344.0, Threshold: 0.05\n", "TP: 739.0, FP: 859047.0, FN: 219.0, Threshold: 0.05\n", "TP: 435.0, FP: 806269.0, FN: 29.0, Threshold: 0.05\n", "TP: 84.0, FP: 1122933.0, FN: 316.0, Threshold: 0.05\n", "TP: 376.0, FP: 1237707.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 965107.0, FN: 0.0, Threshold: 0.05\n", "TP: 100.0, FP: 942547.0, FN: 45.0, Threshold: 0.05\n", "TP: 82.0, FP: 788626.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 3730498.0, FN: 0.0, Threshold: 0.05\n", "TP: 366.0, FP: 2819063.0, FN: 1.0, Threshold: 0.05\n", "TP: 59.0, FP: 4073272.0, FN: 0.0, Threshold: 0.05\n", "TP: 556.0, FP: 3446728.0, FN: 740.0, Threshold: 0.05\n", "TP: 1125.0, FP: 3851014.0, FN: 102.0, Threshold: 0.05\n", "TP: 869.0, FP: 2253795.0, FN: 32.0, Threshold: 0.05\n", "TP: 925.0, FP: 2652607.0, FN: 33.0, Threshold: 0.05\n", "TP: 464.0, FP: 2266810.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 3061342.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 3452868.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 2686387.0, FN: 0.0, Threshold: 0.05\n", "TP: 141.0, FP: 2529942.0, FN: 4.0, Threshold: 0.05\n", "TP: 82.0, FP: 2064143.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 955058.0, FN: 0.0, Threshold: 0.05\n", "TP: 162.0, FP: 719573.0, FN: 205.0, Threshold: 0.05\n", "TP: 59.0, FP: 1029745.0, FN: 0.0, Threshold: 0.05\n", "TP: 364.0, FP: 885388.0, FN: 932.0, Threshold: 0.05\n", "TP: 431.0, FP: 1000649.0, FN: 796.0, Threshold: 0.05\n", "TP: 437.0, FP: 582906.0, FN: 464.0, Threshold: 0.05\n", "TP: 718.0, FP: 673847.0, FN: 240.0, Threshold: 0.05\n", "TP: 397.0, FP: 569347.0, FN: 67.0, Threshold: 0.05\n", "TP: 49.0, FP: 782743.0, FN: 351.0, Threshold: 0.05\n", "TP: 376.0, FP: 878898.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 699566.0, FN: 0.0, Threshold: 0.05\n", "TP: 87.0, FP: 665897.0, FN: 58.0, Threshold: 0.05\n", "TP: 82.0, FP: 538257.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 4222700.0, FN: 0.0, Threshold: 0.05\n", "TP: 367.0, FP: 3214904.0, FN: 0.0, Threshold: 0.05\n", "TP: 59.0, FP: 4595552.0, FN: 0.0, Threshold: 0.05\n", "TP: 470.0, FP: 3921742.0, FN: 826.0, Threshold: 0.05\n", "TP: 1227.0, FP: 4386681.0, FN: 0.0, Threshold: 0.05\n", "TP: 901.0, FP: 2543382.0, FN: 0.0, Threshold: 0.05\n", "TP: 958.0, FP: 2989806.0, FN: 0.0, Threshold: 0.05\n", "TP: 464.0, FP: 2535261.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 3465491.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 3911211.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3016569.0, FN: 0.0, Threshold: 0.05\n", "TP: 145.0, FP: 2809259.0, FN: 0.0, Threshold: 0.05\n", "TP: 82.0, FP: 2322771.0, FN: 0.0, Threshold: 0.05\n", "TP: 520.0, FP: 3441649.0, FN: 8.0, Threshold: 0.05\n", "TP: 334.0, FP: 2531704.0, FN: 33.0, Threshold: 0.05\n", "TP: 59.0, FP: 3591145.0, FN: 0.0, Threshold: 0.05\n", "TP: 470.0, FP: 3287237.0, FN: 826.0, Threshold: 0.05\n", "TP: 1114.0, FP: 3514688.0, FN: 113.0, Threshold: 0.05\n", "TP: 849.0, FP: 2119053.0, FN: 52.0, Threshold: 0.05\n", "TP: 922.0, FP: 2422312.0, FN: 36.0, Threshold: 0.05\n", "TP: 464.0, FP: 2095277.0, FN: 0.0, Threshold: 0.05\n", "TP: 399.0, FP: 2894887.0, FN: 1.0, Threshold: 0.05\n", "TP: 360.0, FP: 3176981.0, FN: 16.0, Threshold: 0.05\n", "TP: 48.0, FP: 2470344.0, FN: 0.0, Threshold: 0.05\n", "TP: 135.0, FP: 2337925.0, FN: 10.0, Threshold: 0.05\n", "TP: 82.0, FP: 1993459.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 2235132.0, FN: 0.0, Threshold: 0.05\n", "TP: 286.0, FP: 1527591.0, FN: 81.0, Threshold: 0.05\n", "TP: 59.0, FP: 2436724.0, FN: 0.0, Threshold: 0.05\n", "TP: 663.0, FP: 2206012.0, FN: 633.0, Threshold: 0.05\n", "TP: 625.0, FP: 2267128.0, FN: 602.0, Threshold: 0.05\n", "TP: 623.0, FP: 1294983.0, FN: 278.0, Threshold: 0.05\n", "TP: 761.0, FP: 1359147.0, FN: 197.0, Threshold: 0.05\n", "TP: 428.0, FP: 1516776.0, FN: 36.0, Threshold: 0.05\n", "TP: 118.0, FP: 2092803.0, FN: 282.0, Threshold: 0.05\n", "TP: 376.0, FP: 2225644.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 1696388.0, FN: 0.0, Threshold: 0.05\n", "TP: 98.0, FP: 1627369.0, FN: 47.0, Threshold: 0.05\n", "TP: 82.0, FP: 1458510.0, FN: 0.0, Threshold: 0.05\n", "TP: 495.0, FP: 817036.0, FN: 33.0, Threshold: 0.05\n", "TP: 138.0, FP: 614249.0, FN: 229.0, Threshold: 0.05\n", "TP: 59.0, FP: 879681.0, FN: 0.0, Threshold: 0.05\n", "TP: 405.0, FP: 773461.0, FN: 891.0, Threshold: 0.05\n", "TP: 356.0, FP: 853742.0, FN: 871.0, Threshold: 0.05\n", "TP: 385.0, FP: 495306.0, FN: 516.0, Threshold: 0.05\n", "TP: 664.0, FP: 592991.0, FN: 294.0, Threshold: 0.05\n", "TP: 321.0, FP: 518275.0, FN: 143.0, Threshold: 0.05\n", "TP: 20.0, FP: 690198.0, FN: 380.0, Threshold: 0.05\n", "TP: 373.0, FP: 770691.0, FN: 3.0, Threshold: 0.05\n", "TP: 48.0, FP: 588175.0, FN: 0.0, Threshold: 0.05\n", "TP: 76.0, FP: 559742.0, FN: 69.0, Threshold: 0.05\n", "TP: 82.0, FP: 468101.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 1033408.0, FN: 0.0, Threshold: 0.05\n", "TP: 257.0, FP: 787586.0, FN: 110.0, Threshold: 0.05\n", "TP: 59.0, FP: 1120186.0, FN: 0.0, Threshold: 0.05\n", "TP: 465.0, FP: 980800.0, FN: 831.0, Threshold: 0.05\n", "TP: 441.0, FP: 1083063.0, FN: 786.0, Threshold: 0.05\n", "TP: 494.0, FP: 632375.0, FN: 407.0, Threshold: 0.05\n", "TP: 730.0, FP: 767151.0, FN: 228.0, Threshold: 0.05\n", "TP: 416.0, FP: 671607.0, FN: 48.0, Threshold: 0.05\n", "TP: 59.0, FP: 882616.0, FN: 341.0, Threshold: 0.05\n", "TP: 376.0, FP: 1000142.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 753141.0, FN: 0.0, Threshold: 0.05\n", "TP: 89.0, FP: 718154.0, FN: 56.0, Threshold: 0.05\n", "TP: 82.0, FP: 593772.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 1093198.0, FN: 0.0, Threshold: 0.05\n", "TP: 284.0, FP: 825548.0, FN: 83.0, Threshold: 0.05\n", "TP: 59.0, FP: 1177697.0, FN: 0.0, Threshold: 0.05\n", "TP: 454.0, FP: 1023383.0, FN: 842.0, Threshold: 0.05\n", "TP: 470.0, FP: 1122580.0, FN: 757.0, Threshold: 0.05\n", "TP: 536.0, FP: 664463.0, FN: 365.0, Threshold: 0.05\n", "TP: 739.0, FP: 782471.0, FN: 219.0, Threshold: 0.05\n", "TP: 402.0, FP: 696778.0, FN: 62.0, Threshold: 0.05\n", "TP: 70.0, FP: 927101.0, FN: 330.0, Threshold: 0.05\n", "TP: 376.0, FP: 1040463.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 800075.0, FN: 0.0, Threshold: 0.05\n", "TP: 88.0, FP: 751273.0, FN: 57.0, Threshold: 0.05\n", "TP: 82.0, FP: 622083.0, FN: 0.0, Threshold: 0.05\n", "TP: 528.0, FP: 1014934.0, FN: 0.0, Threshold: 0.05\n", "TP: 229.0, FP: 754649.0, FN: 138.0, Threshold: 0.05\n", "TP: 59.0, FP: 1093722.0, FN: 0.0, Threshold: 0.05\n", "TP: 392.0, FP: 960515.0, FN: 904.0, Threshold: 0.05\n", "TP: 401.0, FP: 1050380.0, FN: 826.0, Threshold: 0.05\n", "TP: 474.0, FP: 615405.0, FN: 427.0, Threshold: 0.05\n", "TP: 719.0, FP: 705126.0, FN: 239.0, Threshold: 0.05\n", "TP: 330.0, FP: 623243.0, FN: 134.0, Threshold: 0.05\n", "TP: 42.0, FP: 856664.0, FN: 358.0, Threshold: 0.05\n", "TP: 374.0, FP: 951711.0, FN: 2.0, Threshold: 0.05\n", "TP: 48.0, FP: 738531.0, FN: 0.0, Threshold: 0.05\n", "TP: 77.0, FP: 685713.0, FN: 68.0, Threshold: 0.05\n", "TP: 82.0, FP: 578774.0, FN: 0.0, Threshold: 0.05\n", "TP: 448.0, FP: 1097487.0, FN: 80.0, Threshold: 0.05\n", "TP: 206.0, FP: 781844.0, FN: 161.0, Threshold: 0.05\n", "TP: 57.0, FP: 1137517.0, FN: 2.0, Threshold: 0.05\n", "TP: 385.0, FP: 1032916.0, FN: 911.0, Threshold: 0.05\n", "TP: 339.0, FP: 1127996.0, FN: 888.0, Threshold: 0.05\n", "TP: 429.0, FP: 680299.0, FN: 472.0, Threshold: 0.05\n", "TP: 657.0, FP: 790799.0, FN: 301.0, Threshold: 0.05\n", "TP: 126.0, FP: 680709.0, FN: 338.0, Threshold: 0.05\n", "TP: 12.0, FP: 934580.0, FN: 388.0, Threshold: 0.05\n", "TP: 352.0, FP: 996325.0, FN: 24.0, Threshold: 0.05\n", "TP: 48.0, FP: 799299.0, FN: 0.0, Threshold: 0.05\n", "TP: 67.0, FP: 724574.0, FN: 78.0, Threshold: 0.05\n", "TP: 82.0, FP: 651133.0, FN: 0.0, Threshold: 0.05\n", "TP: 92.0, FP: 112734.0, FN: 436.0, Threshold: 0.05\n", "TP: 21.0, FP: 79835.0, FN: 346.0, Threshold: 0.05\n", "TP: 21.0, FP: 111495.0, FN: 38.0, Threshold: 0.05\n", "TP: 93.0, FP: 103914.0, FN: 1203.0, Threshold: 0.05\n", "TP: 93.0, FP: 114294.0, FN: 1134.0, Threshold: 0.05\n", "TP: 98.0, FP: 68617.0, FN: 803.0, Threshold: 0.05\n", "TP: 118.0, FP: 79480.0, FN: 840.0, Threshold: 0.05\n", "TP: 0.0, FP: 72243.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 98397.0, FN: 400.0, Threshold: 0.05\n", "TP: 140.0, FP: 104347.0, FN: 236.0, Threshold: 0.05\n", "TP: 8.0, FP: 82461.0, FN: 40.0, Threshold: 0.05\n", "TP: 1.0, FP: 74653.0, FN: 144.0, Threshold: 0.05\n", "TP: 47.0, FP: 66106.0, FN: 35.0, Threshold: 0.05\n", "TP: 67.0, FP: 44852.0, FN: 461.0, Threshold: 0.05\n", "TP: 21.0, FP: 33650.0, FN: 346.0, Threshold: 0.05\n", "TP: 1.0, FP: 45827.0, FN: 58.0, Threshold: 0.05\n", "TP: 25.0, FP: 45061.0, FN: 1271.0, Threshold: 0.05\n", "TP: 66.0, FP: 45909.0, FN: 1161.0, Threshold: 0.05\n", "TP: 74.0, FP: 27239.0, FN: 827.0, Threshold: 0.05\n", "TP: 111.0, FP: 31333.0, FN: 847.0, Threshold: 0.05\n", "TP: 0.0, FP: 30653.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 42831.0, FN: 400.0, Threshold: 0.05\n", "TP: 39.0, FP: 42729.0, FN: 337.0, Threshold: 0.05\n", "TP: 8.0, FP: 34593.0, FN: 40.0, Threshold: 0.05\n", "TP: 0.0, FP: 31585.0, FN: 145.0, Threshold: 0.05\n", "TP: 40.0, FP: 26628.0, FN: 42.0, Threshold: 0.05\n", "TP: 112.0, FP: 132919.0, FN: 416.0, Threshold: 0.05\n", "TP: 24.0, FP: 104760.0, FN: 343.0, Threshold: 0.05\n", "TP: 31.0, FP: 143954.0, FN: 28.0, Threshold: 0.05\n", "TP: 76.0, FP: 127833.0, FN: 1220.0, Threshold: 0.05\n", "TP: 95.0, FP: 135536.0, FN: 1132.0, Threshold: 0.05\n", "TP: 100.0, FP: 83541.0, FN: 801.0, Threshold: 0.05\n", "TP: 196.0, FP: 98422.0, FN: 762.0, Threshold: 0.05\n", "TP: 0.0, FP: 89677.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 125649.0, FN: 400.0, Threshold: 0.05\n", "TP: 149.0, FP: 134667.0, FN: 227.0, Threshold: 0.05\n", "TP: 8.0, FP: 98992.0, FN: 40.0, Threshold: 0.05\n", "TP: 0.0, FP: 95639.0, FN: 145.0, Threshold: 0.05\n", "TP: 49.0, FP: 80510.0, FN: 33.0, Threshold: 0.05\n", "TP: 155.0, FP: 380876.0, FN: 373.0, Threshold: 0.05\n", "TP: 34.0, FP: 348762.0, FN: 333.0, Threshold: 0.05\n", "TP: 31.0, FP: 434715.0, FN: 28.0, Threshold: 0.05\n", "TP: 617.0, FP: 456671.0, FN: 679.0, Threshold: 0.05\n", "TP: 107.0, FP: 351351.0, FN: 1120.0, Threshold: 0.05\n", "TP: 127.0, FP: 367237.0, FN: 774.0, Threshold: 0.05\n", "TP: 326.0, FP: 460281.0, FN: 632.0, Threshold: 0.05\n", "TP: 18.0, FP: 619247.0, FN: 446.0, Threshold: 0.05\n", "TP: 8.0, FP: 549128.0, FN: 392.0, Threshold: 0.05\n", "TP: 168.0, FP: 531646.0, FN: 208.0, Threshold: 0.05\n", "TP: 8.0, FP: 407561.0, FN: 40.0, Threshold: 0.05\n", "TP: 12.0, FP: 423139.0, FN: 133.0, Threshold: 0.05\n", "TP: 57.0, FP: 426892.0, FN: 25.0, Threshold: 0.05\n", "TP: 143.0, FP: 152695.0, FN: 385.0, Threshold: 0.05\n", "TP: 34.0, FP: 125341.0, FN: 333.0, Threshold: 0.05\n", "TP: 31.0, FP: 189876.0, FN: 28.0, Threshold: 0.05\n", "TP: 130.0, FP: 146139.0, FN: 1166.0, Threshold: 0.05\n", "TP: 120.0, FP: 160271.0, FN: 1107.0, Threshold: 0.05\n", "TP: 139.0, FP: 108987.0, FN: 762.0, Threshold: 0.05\n", "TP: 298.0, FP: 137991.0, FN: 660.0, Threshold: 0.05\n", "TP: 32.0, FP: 125261.0, FN: 432.0, Threshold: 0.05\n", "TP: 0.0, FP: 156667.0, FN: 400.0, Threshold: 0.05\n", "TP: 169.0, FP: 171707.0, FN: 207.0, Threshold: 0.05\n", "TP: 12.0, FP: 127584.0, FN: 36.0, Threshold: 0.05\n", "TP: 14.0, FP: 125572.0, FN: 131.0, Threshold: 0.05\n", "TP: 57.0, FP: 108254.0, FN: 25.0, Threshold: 0.05\n", "TP: 528.0, FP: 4940319.0, FN: 0.0, Threshold: 0.05\n", "TP: 367.0, FP: 3528014.0, FN: 0.0, Threshold: 0.05\n", "TP: 59.0, FP: 5157154.0, FN: 0.0, Threshold: 0.05\n", "TP: 470.0, FP: 4348495.0, FN: 826.0, Threshold: 0.05\n", "TP: 1219.0, FP: 4955079.0, FN: 8.0, Threshold: 0.05\n", "TP: 901.0, FP: 2950558.0, FN: 0.0, Threshold: 0.05\n", "TP: 950.0, FP: 3579672.0, FN: 8.0, Threshold: 0.05\n", "TP: 464.0, FP: 3237630.0, FN: 0.0, Threshold: 0.05\n", "TP: 400.0, FP: 3953166.0, FN: 0.0, Threshold: 0.05\n", "TP: 376.0, FP: 4608196.0, FN: 0.0, Threshold: 0.05\n", "TP: 48.0, FP: 3647850.0, FN: 0.0, Threshold: 0.05\n", "TP: 145.0, FP: 3355587.0, FN: 0.0, Threshold: 0.05\n", "TP: 82.0, FP: 2660643.0, FN: 0.0, Threshold: 0.05\n", "TP: 93.0, FP: 76667.0, FN: 435.0, Threshold: 0.05\n", "TP: 21.0, FP: 62476.0, FN: 346.0, Threshold: 0.05\n", "TP: 23.0, FP: 85906.0, FN: 36.0, Threshold: 0.05\n", "TP: 37.0, FP: 77303.0, FN: 1259.0, Threshold: 0.05\n", "TP: 73.0, FP: 82981.0, FN: 1154.0, Threshold: 0.05\n", "TP: 92.0, FP: 49825.0, FN: 809.0, Threshold: 0.05\n", "TP: 215.0, FP: 67669.0, FN: 743.0, Threshold: 0.05\n", "TP: 0.0, FP: 58837.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 78277.0, FN: 400.0, Threshold: 0.05\n", "TP: 66.0, FP: 87135.0, FN: 310.0, Threshold: 0.05\n", "TP: 8.0, FP: 59014.0, FN: 40.0, Threshold: 0.05\n", "TP: 0.0, FP: 58008.0, FN: 145.0, Threshold: 0.05\n", "TP: 55.0, FP: 51651.0, FN: 27.0, Threshold: 0.05\n", "TP: 0.0, FP: 32873.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 37064.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 59169.0, FN: 59.0, Threshold: 0.05\n", "TP: 124.0, FP: 34390.0, FN: 1172.0, Threshold: 0.05\n", "TP: 0.0, FP: 16136.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 20024.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 61582.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 59431.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 50076.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 69739.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 39319.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 53671.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 62954.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 603.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 1082.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 3054.0, FN: 59.0, Threshold: 0.05\n", "TP: 57.0, FP: 4315.0, FN: 1239.0, Threshold: 0.05\n", "TP: 2.0, FP: 1114.0, FN: 1225.0, Threshold: 0.05\n", "TP: 0.0, FP: 414.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 6542.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 10981.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 7910.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 12906.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 3289.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 2951.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 3311.0, FN: 82.0, Threshold: 0.05\n", "TP: 18.0, FP: 21958.0, FN: 510.0, Threshold: 0.05\n", "TP: 1.0, FP: 17353.0, FN: 366.0, Threshold: 0.05\n", "TP: 0.0, FP: 22970.0, FN: 59.0, Threshold: 0.05\n", "TP: 10.0, FP: 21487.0, FN: 1286.0, Threshold: 0.05\n", "TP: 17.0, FP: 22815.0, FN: 1210.0, Threshold: 0.05\n", "TP: 31.0, FP: 15578.0, FN: 870.0, Threshold: 0.05\n", "TP: 61.0, FP: 18211.0, FN: 897.0, Threshold: 0.05\n", "TP: 0.0, FP: 15713.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 26258.0, FN: 400.0, Threshold: 0.05\n", "TP: 8.0, FP: 23144.0, FN: 368.0, Threshold: 0.05\n", "TP: 0.0, FP: 16354.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 20106.0, FN: 145.0, Threshold: 0.05\n", "TP: 35.0, FP: 20297.0, FN: 47.0, Threshold: 0.05\n", "TP: 35.0, FP: 39137.0, FN: 493.0, Threshold: 0.05\n", "TP: 0.0, FP: 32354.0, FN: 367.0, Threshold: 0.05\n", "TP: 10.0, FP: 43334.0, FN: 49.0, Threshold: 0.05\n", "TP: 0.0, FP: 40665.0, FN: 1296.0, Threshold: 0.05\n", "TP: 39.0, FP: 42131.0, FN: 1188.0, Threshold: 0.05\n", "TP: 55.0, FP: 26595.0, FN: 846.0, Threshold: 0.05\n", "TP: 222.0, FP: 34022.0, FN: 736.0, Threshold: 0.05\n", "TP: 0.0, FP: 27506.0, FN: 464.0, Threshold: 0.05\n", "TP: 2.0, FP: 42422.0, FN: 398.0, Threshold: 0.05\n", "TP: 0.0, FP: 41797.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 30463.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 31390.0, FN: 145.0, Threshold: 0.05\n", "TP: 57.0, FP: 31087.0, FN: 25.0, Threshold: 0.05\n", "TP: 0.0, FP: 0.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 735.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 173.0, FN: 59.0, Threshold: 0.05\n", "TP: 0.0, FP: 709.0, FN: 1296.0, Threshold: 0.05\n", "TP: 0.0, FP: 366.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 244.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 2755.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 15.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 3156.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 3450.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 97.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 466.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 288.0, FN: 82.0, Threshold: 0.05\n", "TP: 230.0, FP: 255584.0, FN: 298.0, Threshold: 0.05\n", "TP: 60.0, FP: 188461.0, FN: 307.0, Threshold: 0.05\n", "TP: 31.0, FP: 267423.0, FN: 28.0, Threshold: 0.05\n", "TP: 166.0, FP: 241837.0, FN: 1130.0, Threshold: 0.05\n", "TP: 156.0, FP: 270052.0, FN: 1071.0, Threshold: 0.05\n", "TP: 225.0, FP: 158814.0, FN: 676.0, Threshold: 0.05\n", "TP: 414.0, FP: 181786.0, FN: 544.0, Threshold: 0.05\n", "TP: 110.0, FP: 161202.0, FN: 354.0, Threshold: 0.05\n", "TP: 8.0, FP: 217679.0, FN: 392.0, Threshold: 0.05\n", "TP: 194.0, FP: 230579.0, FN: 182.0, Threshold: 0.05\n", "TP: 25.0, FP: 193937.0, FN: 23.0, Threshold: 0.05\n", "TP: 24.0, FP: 191487.0, FN: 121.0, Threshold: 0.05\n", "TP: 63.0, FP: 154918.0, FN: 19.0, Threshold: 0.05\n", "TP: 36.0, FP: 31241.0, FN: 492.0, Threshold: 0.05\n", "TP: 0.0, FP: 25261.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 33408.0, FN: 59.0, Threshold: 0.05\n", "TP: 11.0, FP: 31711.0, FN: 1285.0, Threshold: 0.05\n", "TP: 24.0, FP: 34220.0, FN: 1203.0, Threshold: 0.05\n", "TP: 46.0, FP: 21499.0, FN: 855.0, Threshold: 0.05\n", "TP: 130.0, FP: 26637.0, FN: 828.0, Threshold: 0.05\n", "TP: 0.0, FP: 20871.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 33956.0, FN: 400.0, Threshold: 0.05\n", "TP: 6.0, FP: 34504.0, FN: 370.0, Threshold: 0.05\n", "TP: 0.0, FP: 23618.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 23723.0, FN: 145.0, Threshold: 0.05\n", "TP: 39.0, FP: 22255.0, FN: 43.0, Threshold: 0.05\n", "TP: 0.0, FP: 3514.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 2782.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 4474.0, FN: 59.0, Threshold: 0.05\n", "TP: 2.0, FP: 2988.0, FN: 1294.0, Threshold: 0.05\n", "TP: 0.0, FP: 3476.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 2832.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 3373.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 3160.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 4101.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 5282.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 3223.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 2877.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 2920.0, FN: 82.0, Threshold: 0.05\n", "TP: 18.0, FP: 21332.0, FN: 510.0, Threshold: 0.05\n", "TP: 0.0, FP: 19903.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 22145.0, FN: 59.0, Threshold: 0.05\n", "TP: 238.0, FP: 24859.0, FN: 1058.0, Threshold: 0.05\n", "TP: 12.0, FP: 18895.0, FN: 1215.0, Threshold: 0.05\n", "TP: 8.0, FP: 19117.0, FN: 893.0, Threshold: 0.05\n", "TP: 49.0, FP: 30642.0, FN: 909.0, Threshold: 0.05\n", "TP: 0.0, FP: 26959.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 28634.0, FN: 400.0, Threshold: 0.05\n", "TP: 1.0, FP: 35289.0, FN: 375.0, Threshold: 0.05\n", "TP: 0.0, FP: 23788.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 24444.0, FN: 145.0, Threshold: 0.05\n", "TP: 27.0, FP: 29435.0, FN: 55.0, Threshold: 0.05\n", "\n", "New best model! F1: 0.0021\n", "TP: 0.0, FP: 153.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 339.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 635.0, FN: 59.0, Threshold: 0.05\n", "TP: 15.0, FP: 180.0, FN: 1281.0, Threshold: 0.05\n", "TP: 0.0, FP: 85.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 126.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 1072.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 381.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 1038.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 2080.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 530.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 274.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 399.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 1806.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 1661.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 3320.0, FN: 59.0, Threshold: 0.05\n", "TP: 373.0, FP: 2546.0, FN: 923.0, Threshold: 0.05\n", "TP: 0.0, FP: 1124.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 1536.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 5767.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 6601.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 4225.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 9041.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 3808.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 3900.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 2515.0, FN: 82.0, Threshold: 0.05\n", "\n", "New best model! F1: 0.0136\n", "TP: 0.0, FP: 1913.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 2188.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 3372.0, FN: 59.0, Threshold: 0.05\n", "TP: 382.0, FP: 3339.0, FN: 914.0, Threshold: 0.05\n", "TP: 0.0, FP: 1392.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 1658.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 7532.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 7237.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 5298.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 8779.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 3673.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 3994.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 2420.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 3389.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 3580.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 5406.0, FN: 59.0, Threshold: 0.05\n", "TP: 603.0, FP: 7410.0, FN: 693.0, Threshold: 0.05\n", "TP: 0.0, FP: 2662.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 2338.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 12273.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 12750.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 9195.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 15116.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 6938.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 6527.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 3356.0, FN: 82.0, Threshold: 0.05\n", "TP: 0.0, FP: 8212.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 8369.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 11207.0, FN: 59.0, Threshold: 0.05\n", "TP: 791.0, FP: 15911.0, FN: 505.0, Threshold: 0.05\n", "TP: 0.0, FP: 8091.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 7069.0, FN: 901.0, Threshold: 0.05\n", "TP: 17.0, FP: 22816.0, FN: 941.0, Threshold: 0.05\n", "TP: 0.0, FP: 25271.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 17656.0, FN: 400.0, Threshold: 0.05\n", "TP: 1.0, FP: 26238.0, FN: 375.0, Threshold: 0.05\n", "TP: 0.0, FP: 12378.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 16692.0, FN: 145.0, Threshold: 0.05\n", "TP: 1.0, FP: 8886.0, FN: 81.0, Threshold: 0.05\n", "TP: 0.0, FP: 7430.0, FN: 528.0, Threshold: 0.05\n", "TP: 0.0, FP: 6386.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 7980.0, FN: 59.0, Threshold: 0.05\n", "TP: 786.0, FP: 13365.0, FN: 510.0, Threshold: 0.05\n", "TP: 0.0, FP: 6027.0, FN: 1227.0, Threshold: 0.05\n", "TP: 0.0, FP: 4336.0, FN: 901.0, Threshold: 0.05\n", "TP: 0.0, FP: 22780.0, FN: 958.0, Threshold: 0.05\n", "TP: 0.0, FP: 24064.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 13107.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 20753.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 7907.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 11301.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 8630.0, FN: 82.0, Threshold: 0.05\n", "TP: 5.0, FP: 10449.0, FN: 523.0, Threshold: 0.05\n", "TP: 0.0, FP: 6325.0, FN: 367.0, Threshold: 0.05\n", "TP: 0.0, FP: 7859.0, FN: 59.0, Threshold: 0.05\n", "TP: 692.0, FP: 9128.0, FN: 604.0, Threshold: 0.05\n", "TP: 0.0, FP: 7565.0, FN: 1227.0, Threshold: 0.05\n", "TP: 2.0, FP: 4082.0, FN: 899.0, Threshold: 0.05\n", "TP: 1.0, FP: 12418.0, FN: 957.0, Threshold: 0.05\n", "TP: 0.0, FP: 11737.0, FN: 464.0, Threshold: 0.05\n", "TP: 0.0, FP: 6687.0, FN: 400.0, Threshold: 0.05\n", "TP: 0.0, FP: 11560.0, FN: 376.0, Threshold: 0.05\n", "TP: 0.0, FP: 6964.0, FN: 48.0, Threshold: 0.05\n", "TP: 0.0, FP: 7101.0, FN: 145.0, Threshold: 0.05\n", "TP: 0.0, FP: 7628.0, FN: 82.0, Threshold: 0.05\n", "TP: 4.0, FP: 7449.0, FN: 524.0, Threshold: 0.05\n", "TP: 2.0, FP: 9531.0, FN: 365.0, Threshold: 0.05\n", "TP: 6.0, FP: 8785.0, FN: 53.0, Threshold: 0.05\n", "TP: 692.0, FP: 11237.0, FN: 604.0, Threshold: 0.05\n", "TP: 1.0, FP: 8200.0, FN: 1226.0, Threshold: 0.05\n", "TP: 5.0, FP: 4792.0, FN: 896.0, Threshold: 0.05\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m model = initialize_model_for_spots(model)  \u001b[38;5;66;03m# Initialize properly\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# Train with all improvements\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m trained_model = \u001b[43mtrain_model_for_extreme_imbalance\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m      5\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      6\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtrain_loader\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      7\u001b[39m \u001b[43m    \u001b[49m\u001b[43mval_loader\u001b[49m\u001b[43m=\u001b[49m\u001b[43mval_loader\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      8\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m      9\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnum_epochs\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m200\u001b[39;49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Can reduce as this is more efficient\u001b[39;49;00m\n\u001b[32m     10\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 57\u001b[39m, in \u001b[36mtrain_model_for_extreme_imbalance\u001b[39m\u001b[34m(model, train_loader, val_loader, device, num_epochs)\u001b[39m\n\u001b[32m     54\u001b[39m val_count = \u001b[32m0\u001b[39m\n\u001b[32m     56\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m torch.no_grad():\n\u001b[32m---> \u001b[39m\u001b[32m57\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mimages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmasks\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mval_loader\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m     58\u001b[39m \u001b[43m        \u001b[49m\u001b[43mimages\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mimages\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     59\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmasks\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmasks\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/site-packages/torch/utils/data/dataloader.py:708\u001b[39m, in \u001b[36m_BaseDataLoaderIter.__next__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    705\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._sampler_iter \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    706\u001b[39m     \u001b[38;5;66;03m# TODO(https://github.com/pytorch/pytorch/issues/76750)\u001b[39;00m\n\u001b[32m    707\u001b[39m     \u001b[38;5;28mself\u001b[39m._reset()  \u001b[38;5;66;03m# type: ignore[call-arg]\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m708\u001b[39m data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_next_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    709\u001b[39m \u001b[38;5;28mself\u001b[39m._num_yielded += \u001b[32m1\u001b[39m\n\u001b[32m    710\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[32m    711\u001b[39m     \u001b[38;5;28mself\u001b[39m._dataset_kind == _DatasetKind.Iterable\n\u001b[32m    712\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._IterableDataset_len_called \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    713\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._num_yielded > \u001b[38;5;28mself\u001b[39m._IterableDataset_len_called\n\u001b[32m    714\u001b[39m ):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/site-packages/torch/utils/data/dataloader.py:1458\u001b[39m, in \u001b[36m_MultiProcessingDataLoaderIter._next_data\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1455\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._process_data(data)\n\u001b[32m   1457\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._shutdown \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._tasks_outstanding > \u001b[32m0\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1458\u001b[39m idx, data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1459\u001b[39m \u001b[38;5;28mself\u001b[39m._tasks_outstanding -= \u001b[32m1\u001b[39m\n\u001b[32m   1460\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._dataset_kind == _DatasetKind.Iterable:\n\u001b[32m   1461\u001b[39m     \u001b[38;5;66;03m# Check for _IterableDatasetStopIteration\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/site-packages/torch/utils/data/dataloader.py:1420\u001b[39m, in \u001b[36m_MultiProcessingDataLoaderIter._get_data\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1416\u001b[39m     \u001b[38;5;66;03m# In this case, `self._data_queue` is a `queue.Queue`,. But we don't\u001b[39;00m\n\u001b[32m   1417\u001b[39m     \u001b[38;5;66;03m# need to call `.task_done()` because we don't use `.join()`.\u001b[39;00m\n\u001b[32m   1418\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1419\u001b[39m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1420\u001b[39m         success, data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_try_get_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1421\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m success:\n\u001b[32m   1422\u001b[39m             \u001b[38;5;28;01mreturn\u001b[39;00m data\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/site-packages/torch/utils/data/dataloader.py:1251\u001b[39m, in \u001b[36m_MultiProcessingDataLoaderIter._try_get_data\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m   1238\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_try_get_data\u001b[39m(\u001b[38;5;28mself\u001b[39m, timeout=_utils.MP_STATUS_CHECK_INTERVAL):\n\u001b[32m   1239\u001b[39m     \u001b[38;5;66;03m# Tries to fetch data from `self._data_queue` once for a given timeout.\u001b[39;00m\n\u001b[32m   1240\u001b[39m     \u001b[38;5;66;03m# This can also be used as inner loop of fetching without timeout, with\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   1248\u001b[39m     \u001b[38;5;66;03m# Returns a 2-tuple:\u001b[39;00m\n\u001b[32m   1249\u001b[39m     \u001b[38;5;66;03m#   (bool: whether successfully get data, any: data if successful else None)\u001b[39;00m\n\u001b[32m   1250\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1251\u001b[39m         data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_data_queue\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1252\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m (\u001b[38;5;28;01mTrue\u001b[39;00m, data)\n\u001b[32m   1253\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   1254\u001b[39m         \u001b[38;5;66;03m# At timeout and error, we manually check whether any worker has\u001b[39;00m\n\u001b[32m   1255\u001b[39m         \u001b[38;5;66;03m# failed. Note that this is the only mechanism for Windows to detect\u001b[39;00m\n\u001b[32m   1256\u001b[39m         \u001b[38;5;66;03m# worker failures.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/multiprocessing/queues.py:113\u001b[39m, in \u001b[36mQueue.get\u001b[39m\u001b[34m(self, block, timeout)\u001b[39m\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m block:\n\u001b[32m    112\u001b[39m     timeout = deadline - time.monotonic()\n\u001b[32m--> \u001b[39m\u001b[32m113\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_poll\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    114\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m Empty\n\u001b[32m    115\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m._poll():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/multiprocessing/connection.py:257\u001b[39m, in \u001b[36m_ConnectionBase.poll\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    255\u001b[39m \u001b[38;5;28mself\u001b[39m._check_closed()\n\u001b[32m    256\u001b[39m \u001b[38;5;28mself\u001b[39m._check_readable()\n\u001b[32m--> \u001b[39m\u001b[32m257\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_poll\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/multiprocessing/connection.py:440\u001b[39m, in \u001b[36mConnection._poll\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    439\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_poll\u001b[39m(\u001b[38;5;28mself\u001b[39m, timeout):\n\u001b[32m--> \u001b[39m\u001b[32m440\u001b[39m     r = \u001b[43mwait\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    441\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mbool\u001b[39m(r)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/multiprocessing/connection.py:948\u001b[39m, in \u001b[36mwait\u001b[39m\u001b[34m(object_list, timeout)\u001b[39m\n\u001b[32m    945\u001b[39m     deadline = time.monotonic() + timeout\n\u001b[32m    947\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m948\u001b[39m     ready = \u001b[43mselector\u001b[49m\u001b[43m.\u001b[49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    949\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m ready:\n\u001b[32m    950\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m [key.fileobj \u001b[38;5;28;01mfor\u001b[39;00m (key, events) \u001b[38;5;129;01min\u001b[39;00m ready]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/Model_spot/lib/python3.11/selectors.py:415\u001b[39m, in \u001b[36m_PollLikeSelector.select\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    413\u001b[39m ready = []\n\u001b[32m    414\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m415\u001b[39m     fd_event_list = \u001b[38;5;28mself\u001b[39m._selector.poll(timeout)\n\u001b[32m    416\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n\u001b[32m    417\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m ready\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["\n", "model = initialize_model_for_spots(model)  # Initialize properly\n", "\n", "# Train with all improvements\n", "trained_model = train_model_for_extreme_imbalance(\n", "    model=model,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    device=device,\n", "    num_epochs=200  # Can reduce as this is more efficient\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start training\n", "trained_model, history = run_training(\n", "    model=model,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    device=device,\n", "    checkpoint_dir=checkpoint_dir,\n", "    log_dir=log_dir,\n", "    num_epochs=400,  # Adjust as needed\n", "    learning_rate=1e-3  # Adjust as needed\n", ")\n", "\n", "# Test the trained model on a sample from validation set\n", "print(\"\\nTesting model on sample image...\")\n", "try:\n", "    sample_batch = next(iter(val_loader))\n", "    sample_image = sample_batch[0][0:1].to(device)  # Take first image\n", "    sample_mask = sample_batch[1][0:1].to(device)\n", "    \n", "    # Generate prediction\n", "    with torch.no_grad():\n", "        prediction = trained_model(sample_image)\n", "        \n", "    # Visualize results\n", "    result_fig = visualize_results(\n", "        sample_image[0].cpu().numpy(), \n", "        prediction, \n", "        sample_mask[0].cpu().numpy(),\n", "        show=True,\n", "        save_path=os.path.join(checkpoint_dir, \"sample_prediction.png\")\n", "    )\n", "    \n", "    print(f\"Sample prediction saved to {os.path.join(checkpoint_dir, 'sample_prediction.png')}\")\n", "    \n", "    # Print spot count\n", "    print(f\"Detected {len(prediction.get('spot_coords', []))} spots in sample image\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error testing model: {e}\")\n", "\n", "print(\"\\nTraining complete!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def optimize_model_for_inference(model, input_shape=(1, 1, 512, 512)):\n", "    \"\"\"Optimize model for inference using TorchScript\"\"\"\n", "    import torch.onnx\n", "    \n", "    # Create dummy input\n", "    dummy_input = torch.randn(input_shape, device=next(model.parameters()).device)\n", "    \n", "    # Set model to evaluation mode\n", "    model.eval()\n", "    \n", "    # Create TorchScript version\n", "    print(\"Creating TorchScript model...\")\n", "    scripted_model = torch.jit.trace(model, dummy_input)\n", "    scripted_model.save(\"spot_detector_optimized.pt\")\n", "    \n", "    # Export to ONNX\n", "    print(\"Exporting to ONNX...\")\n", "    torch.onnx.export(\n", "        model, \n", "        dummy_input,\n", "        \"spot_detector.onnx\",\n", "        export_params=True,\n", "        opset_version=13,\n", "        do_constant_folding=True,\n", "        input_names=['input'],\n", "        output_names=['heatmap', 'flow'],\n", "        dynamic_axes={'input': {0: 'batch_size'},\n", "                     'heatmap': {0: 'batch_size'},\n", "                     'flow': {0: 'batch_size'}}\n", "    )\n", "    \n", "    print(\"Model optimized and exported to spot_detector.onnx\")\n", "    return scripted_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" =================================\n", "# Prediction and Visualization\n", "# =================================\n", "\n", "def detect_and_visualize(model, image, device=None, threshold=None, threshold_abs=None, min_distance=3):\n", "    \"\"\"\n", "    Detect spots and visualize results\n", "    \n", "    Args:\n", "        model: Trained model\n", "        image: Input image as tensor or numpy array\n", "        device: Device to use for processing\n", "        threshold: Detection threshold\n", "        threshold_abs: Minimum absolute threshold\n", "        min_distance: Minimum distance between spots\n", "        \n", "    Returns:\n", "        dict: Detection results and visualizations\n", "    \"\"\"\n", "    from scipy import ndimage as ndi\n", "    from skimage.feature import peak_local_max\n", "    import matplotlib.cm as cm\n", "    \n", "    if device is None:\n", "        device = next(model.parameters()).device\n", "    \n", "    # Default thresholds\n", "    if threshold is None:\n", "        threshold = model.threshold\n", "    if threshold_abs is None:\n", "        threshold_abs = model.threshold_abs\n", "        \n", "    # Convert to tensor if numpy array\n", "    if isinstance(image, np.ndarray):\n", "        # Handle 3D data\n", "        if image.ndim == 3:\n", "            image = torch.from_numpy(image).float().unsqueeze(0)\n", "        else:\n", "            image = torch.from_numpy(image).float().unsqueeze(0).unsqueeze(0)\n", "    \n", "    # Ensure image has batch dimension\n", "    if image.dim() == 3:\n", "        image = image.unsqueeze(0)\n", "    \n", "    # Move to device\n", "    image = image.to(device)\n", "    \n", "    # Process with model\n", "    model.eval()\n", "    with torch.no_grad():\n", "        outputs = model(image)\n", "        \n", "        # Get predictions\n", "        heatmap = outputs[\"heatmaps\"]\n", "        flow = outputs.get(\"flow\", None)\n", "        \n", "        # Convert to numpy\n", "        heatmap_np = heatmap.cpu().numpy()[0, 0]\n", "        \n", "        # Apply threshold\n", "        binary = (heatmap_np > threshold) & (heatmap_np > threshold_abs)\n", "        \n", "        # Find connected components for instance segmentation\n", "        labeled, num_spots = ndi.label(binary)\n", "        \n", "        # Find local maxima for spot centers\n", "        spot_centers = peak_local_max(\n", "            heatmap_np,\n", "            min_distance=min_distance,\n", "            threshold_abs=threshold_abs,\n", "            threshold_rel=0,\n", "            exclude_border=False\n", "        )\n", "        \n", "        # Apply flow-based refinement if available\n", "        refined_centers = []\n", "        if flow is not None:\n", "            flow_np = flow.cpu().numpy()[0]\n", "            \n", "            for y, x in spot_centers:\n", "                if 0 <= y < flow_np.shape[1] and 0 <= x < flow_np.shape[2]:\n", "                    # Extract flow vector\n", "                    flow_vec = flow_np[:, y, x]\n", "                    \n", "                    # Refine location using flow\n", "                    refined_y = y - 0.5 * flow_vec[0]  # Scale factor can be adjusted\n", "                    refined_x = x - 0.5 * flow_vec[1]\n", "                    \n", "                    refined_centers.append((refined_y, refined_x))\n", "                else:\n", "                    refined_centers.append((y, x))\n", "            \n", "            refined_centers = np.array(refined_centers)\n", "        else:\n", "            refined_centers = spot_centers\n", "        \n", "        # Create instance colored visualization\n", "        instance_colored = np.zeros((*binary.shape, 4), dtype=np.float32)\n", "        \n", "        if num_spots > 0:\n", "            # Create colormap\n", "            cmap = cm.get_cmap('tab20', num_spots + 1)\n", "            \n", "            for i in range(1, num_spots + 1):\n", "                instance_mask = (labeled == i)\n", "                color = cmap(i)\n", "                for c in range(3):\n", "                    instance_colored[..., c][instance_mask] = color[c]\n", "                instance_colored[..., 3][instance_mask] = 1.0  # Alpha\n", "        \n", "        # Create flow visualization if available\n", "        flow_viz = None\n", "        if flow is not None:\n", "            flow_viz = flow_to_rgb(flow_np)\n", "        \n", "        # Create result dictionary\n", "        result = {\n", "            \"heatmap\": heatmap_np,\n", "            \"binary\": binary,\n", "            \"instance_labels\": labeled,\n", "            \"instance_colored\": instance_colored,\n", "            \"spot_centers\": spot_centers,\n", "            \"refined_centers\": refined_centers,\n", "            \"flow\": flow_np if flow is not None else None,\n", "            \"flow_viz\": flow_viz\n", "        }\n", "        \n", "        return result\n", "\n", "def flow_to_rgb(flow):\n", "    \"\"\"Convert flow vectors to RGB visualization using HSV color wheel\"\"\"\n", "    import numpy as np\n", "    import cv2\n", "    \n", "    # Extract x and y components\n", "    if flow.shape[0] == 2:  # 2D flow\n", "        u = flow[0]\n", "        v = flow[1]\n", "    else:  # 3D flow, visualize xy plane\n", "        u = flow[0]\n", "        v = flow[1]\n", "    \n", "    # Calculate magnitude and angle\n", "    magnitude = np.sqrt(u*u + v*v)\n", "    angle = np.arctan2(v, u)\n", "    \n", "    # Normalize magnitude for better visualization\n", "    magnitude = np.clip(magnitude / np.percentile(magnitude, 95), 0, 1)\n", "    \n", "    # Convert to HSV\n", "    hsv = np.zeros((u.shape[0], u.shape[1], 3), dtype=np.float32)\n", "    hsv[..., 0] = (angle + np.pi) / (2 * np.pi)  # Hue from angle\n", "    hsv[..., 1] = np.ones_like(magnitude)  # Full saturation\n", "    hsv[..., 2] = magnitude  # Value from magnitude\n", "    \n", "    # Convert to RGB\n", "    rgb = cv2.cvtColor((hsv * 255).astype(np.uint8), cv2.COLOR_HSV2RGB)\n", "    \n", "    return rgb"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_detection_results(image, results, figsize=(20, 10)):\n", "    \"\"\"Visualize detection results\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    \n", "    fig, axes = plt.subplots(2, 3, figsize=figsize)\n", "    \n", "    # Original image\n", "    axes[0, 0].imshow(image, cmap='gray')\n", "    axes[0, 0].set_title('Original Image')\n", "    \n", "    # Heatmap\n", "    axes[0, 1].imshow(results[\"heatmap\"], cmap='viridis')\n", "    axes[0, 1].set_title('Heatmap')\n", "    \n", "    # Binary mask\n", "    axes[0, 2].imshow(results[\"binary\"], cmap='gray')\n", "    axes[0, 2].set_title('Binary Mask')\n", "    \n", "    # Instance segmentation\n", "    axes[1, 0].imshow(image, cmap='gray')\n", "    axes[1, 0].imshow(results[\"instance_colored\"], alpha=0.7)\n", "    axes[1, 0].set_title(f'Instance Segmentation ({len(results[\"spot_centers\"])} spots)')\n", "    \n", "    # Plot spot centers\n", "    if len(results[\"spot_centers\"]) > 0:\n", "        y, x = zip(*results[\"spot_centers\"])\n", "        axes[1, 0].scatter(x, y, c='r', s=40, marker='+')\n", "    \n", "    # Flow visualization if available\n", "    if results[\"flow_viz\"] is not None:\n", "        axes[1, 1].imshow(results[\"flow_viz\"])\n", "        axes[1, 1].set_title('Flow Field')\n", "    else:\n", "        axes[1, 1].set_visible(False)\n", "    \n", "    # Refined centers if available\n", "    if \"refined_centers\" in results and len(results[\"refined_centers\"]) > 0:\n", "        axes[1, 2].imshow(image, cmap='gray')\n", "        y_ref, x_ref = zip(*results[\"refined_centers\"])\n", "        axes[1, 2].scatter(x_ref, y_ref, c='g', s=40, marker='o', facecolors='none')\n", "        axes[1, 2].set_title('Refined Spot Centers')\n", "        \n", "        # Plot original centers for comparison\n", "        if len(results[\"spot_centers\"]) > 0:\n", "            y, x = zip(*results[\"spot_centers\"])\n", "            axes[1, 2].scatter(x, y, c='r', s=15, marker='x')\n", "    else:\n", "        axes[1, 2].set_visible(False)\n", "        \n", "    # Results summary\n", "    info_text = (\n", "        f\"Detected spots: {len(results['spot_centers'])}\\n\"\n", "        f\"Threshold: {threshold:.4f}\\n\"\n", "        f\"Min distance: {min_distance}\"\n", "    )\n", "    axes[1, 3].axis('off')\n", "    axes[1, 3].text(0.1, 0.5, info_text, fontsize=12)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(\"spot_detection_result.png\", dpi=150, bbox_inches='tight')\n", "    \n", "    return fig, results"]}], "metadata": {"kernelspec": {"display_name": "Model_spot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}