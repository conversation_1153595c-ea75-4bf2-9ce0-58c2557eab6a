{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Direct Channel Handling for VALIS Registration\n", "\n", "This notebook uses the `fix_channel_order_verified.py` script to directly handle each channel and ensure proper organization in the final merged image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pathlib\n", "from tqdm import tqdm\n", "from valis import registration, valtils, preprocessing, slide_io\n", "\n", "# Import the solution\n", "from fix_channel_order_verified import process_images_direct"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define your directories and reference slide\n", "src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/aligned_images/\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/aligned_images/LB06 BP 4i p21 green p16 red 25mar25.lif - R 6_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.3  # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the direct processing function\n", "merged_slide_path = process_images_direct(\n", "    src_dir=src_dir,\n", "    dst_dir=dst_dir,\n", "    reference_slide_path=reference_slide,\n", "    micro_reg_fraction=micro_reg_fraction\n", ")\n", "\n", "print(f\"\\nProcessing complete! Merged slide saved to: {merged_slide_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify Individual Channels\n", "\n", "The script also saves each channel individually for verification. You can check these files to confirm that each channel was properly processed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# List the individual channel files\n", "merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "channel_files = [f for f in os.listdir(merged_slide_dir) if f.startswith(\"channel_\")]\n", "print(f\"Found {len(channel_files)} individual channel files:\")\n", "for f in sorted(channel_files):\n", "    print(f\"  {f}\")"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}