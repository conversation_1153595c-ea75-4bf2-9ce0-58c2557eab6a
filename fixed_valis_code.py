    # Initialize Valis registration with the padded images
    registrar = registration.Valis(
        padded_dir,
        OUTPUT_BASE,
        img_list=padded_dapi_files,
        reference_img_f=padded_reference_img,
        micro_rigid_registrar_cls=MicroRigidRegistrar,
        align_to_reference=True,
        non_rigid_reg_params=NON_RIGID_REG_PARAMS,
        micro_rigid_registrar_params=MICRO_RIGID_REGISTRAR_PARAMS,
        use_non_rigid=True  # Explicitly enable non-rigid registration
    )