{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Improved VALIS Image Alignment\n", "\n", "This notebook implements optimized parameters for VALIS registration to improve alignment quality, especially in regions with merging issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "from valis import registration as reg\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the folder with your images\n", "folder_path = \"/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a registration object with optimized parameters\n", "registration = reg.Registration(\n", "    folder_path,\n", "    # Increase feature detection parameters\n", "    feature_detection_params={\n", "        'max_features': 5000,  # Increase number of features detected\n", "        'feature_params': {\n", "            'maxCorners': 5000,\n", "            'qualityLevel': 0.01,  # Lower quality threshold to detect more features\n", "            'minDistance': 7,      # Minimum distance between features\n", "            'blockSize': 7\n", "        }\n", "    },\n", "    # Improve rigid registration\n", "    rigid_registration_params={\n", "        'feature_retention_ratio': 0.9,  # Keep more features\n", "        'max_features': 5000,\n", "        'match_filter_method': 'ratio_test',\n", "        'match_ratio_threshold': 0.75,   # More permissive matching\n", "        'ransac_reproj_threshold': 5.0,  # More permissive RANSAC\n", "        'rigid_registration_method': 'affine'  # Use affine instead of rigid\n", "    },\n", "    # Improve non-rigid registration\n", "    non_rigid_registration_params={\n", "        'mesh_size': 30,           # Smaller mesh for finer control\n", "        'regularization_weight': 0.1,  # Lower regularization for more flexibility\n", "        'pyramid_levels': 5,       # More pyramid levels for multi-scale alignment\n", "        'epsilon': 1e-7,\n", "        'max_iterations': 1000     # More iterations for convergence\n", "    },\n", "    # Improve micro-registration\n", "    micro_registration_params={\n", "        'mesh_size': 20,           # Even smaller mesh for micro-registration\n", "        'regularization_weight': 0.05,\n", "        'pyramid_levels': 6,\n", "        'epsilon': 1e-8,\n", "        'max_iterations': 1500\n", "    },\n", "    # Use more ROIs for micro-registration\n", "    micro_registration_roi_params={\n", "        'roi_size': [600, 600],    # Larger ROI size\n", "        'num_rois': 8,             # More ROIs for better coverage\n", "        'roi_overlap_ratio': 0.2   # Allow some overlap between ROIs\n", "    },\n", "    # Improve error measurement\n", "    error_metric_params={\n", "        'metric': 'mutual_information',  # Better metric for multi-modal images\n", "        'metric_weights': None\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the registration\n", "registration.register()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the registered images\n", "registration.save_registered_images(os.path.join(folder_path, \"registered_improved\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the results to check alignment quality\n", "def visualize_alignment(registration, image_indices=[0, 1]):\n", "    \"\"\"Visualize alignment between two images\"\"\"\n", "    img1 = registration.registered_images[image_indices[0]]\n", "    img2 = registration.registered_images[image_indices[1]]\n", "    \n", "    # Create a composite image (red-green)\n", "    composite = np.zeros((img1.shape[0], img1.shape[1], 3), dtype=np.float32)\n", "    composite[:,:,0] = img1 / np.max(img1)  # Red channel\n", "    composite[:,:,1] = img2 / np.max(img2)  # Green channel\n", "    \n", "    plt.figure(figsize=(12, 10))\n", "    plt.imshow(composite)\n", "    plt.title(f\"Alignment between images {image_indices[0]} and {image_indices[1]}\")\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(folder_path, f\"alignment_check_{image_indices[0]}_{image_indices[1]}.png\"))\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check alignment between first image and all others\n", "for i in range(1, len(registration.registered_images)):\n", "    visualize_alignment(registration, [0, i])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Additional Troubleshooting\n", "\n", "If you're still seeing alignment issues in specific regions, you can try these additional approaches:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Option 1: Add manual control points for problematic regions\n", "# Uncomment and modify this code if needed\n", "\n", "# control_points = [\n", "#     # Format: [image_idx1, x1, y1, image_idx2, x2, y2]\n", "#     [0, 500, 500, 1, 510, 505],  # Example point pair between images 0 and 1\n", "#     [0, 1000, 1000, 1, 1020, 1010]  # Another point pair\n", "# ]\n", "\n", "# registration_with_manual_points = reg.Registration(\n", "#     folder_path,\n", "#     # Copy all parameters from above\n", "#     # ...\n", "#     manual_control_points=control_points\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Option 2: Pre-process images to enhance features\n", "# Uncomment and run if needed\n", "\n", "# from skimage import exposure\n", "\n", "# def preprocess_images(folder_path):\n", "#     \"\"\"Enhance image contrast to improve feature detection\"\"\"\n", "#     import glob\n", "#     import os\n", "#     import tifffile\n", "#     \n", "#     # Create output directory\n", "#     output_dir = os.path.join(folder_path, 'preprocessed')\n", "#     os.makedirs(output_dir, exist_ok=True)\n", "#     \n", "#     # Find all image files\n", "#     image_files = glob.glob(os.path.join(folder_path, '*.lif'))\n", "#     \n", "#     for img_file in image_files:\n", "#         # Load image\n", "#         img = tifffile.imread(img_file)\n", "#         \n", "#         # Enhance contrast\n", "#         p2, p98 = np.percentile(img, (2, 98))\n", "#         img_rescale = exposure.rescale_intensity(img, in_range=(p2, p98))\n", "#         \n", "#         # Save preprocessed image\n", "#         output_path = os.path.join(output_dir, os.path.basename(img_file))\n", "#         tifffile.imwrite(output_path, img_rescale)\n", "#     \n", "#     return output_dir"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}