{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Additional Required Changes to Supporting Functions\n", "You'll need these modifications to other parts of your code:\n", "\n", "1. Add 3D Support to watershed_instance_segmentation_enhanced:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary packages at the top\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime, timedelta\n", "\n", "# Force using just the first GPU and ignore others\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"  # Use only GPU 0\n", "\n", "# Optimize CUDA settings for performance\n", "torch.backends.cudnn.benchmark = True\n", "torch.backends.cudnn.deterministic = False\n", "torch.backends.cuda.matmul.allow_tf32 = True  # Use TensorFloat-32 if available\n", "torch.backends.cudnn.allow_tf32 = True\n", "\n", "# Clear GPU memory\n", "torch.cuda.empty_cache()\n", "\n", "# Check GPU status\n", "#!nvidia-smi\n", "\n", "# CHANGE 1: Set is_3d=True for 3D data\n", "model = EnhancedInstanceSpotDetector(\n", "    in_channels=1, \n", "    base_filters=16, \n", "    levels=3,\n", "    is_3d=True,  # Changed to True for 3D data\n", "    batch_norm=True, \n", "    use_attention=True,\n", "    use_residual=True, \n", "    dropout=0.1,\n", "    threshold=0.5, \n", "    min_distance=1, \n", "    compute_flow=True\n", ")\n", "\n", "print(f\"Model initialized: {model.__class__.__name__}\")\n", "\n", "# CHANGE 2: Update data paths for 3D images\n", "data_manager = SplitDatasetManager(\n", "    # Update these paths to your 3D data locations\n", "    images_dir=\"/mnt/d/Users/<USER>/FISH_spots/3d/images/*.tif\",\n", "    masks_dir=\"/mnt/d/Users/<USER>/FISH_spots/3d/masks/*.tif\",\n", "    val_ratio=0.2,\n", "    seed=42,\n", "    is_3d=True,  # Changed to True for 3D data\n", "    augmentation_strength='mild'  # Reduce augmentation for 3D to avoid memory issues\n", ")\n", "\n", "# Get device\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# First move model to device (before enabling channels_last)\n", "try:\n", "    model = model.to(device)\n", "    print(f\"Model successfully moved to {device}\")\n", "    \n", "    # Print initial memory usage\n", "    print(f\"Memory allocated: {torch.cuda.memory_allocated(0) / 1e9:.2f} GB\")\n", "    print(f\"Memory reserved: {torch.cuda.memory_reserved(0) / 1e9:.2f} GB\")\n", "    \n", "    # Try enabling channels_last memory format after model is on GPU\n", "    if device.type == \"cuda\":\n", "        # CHANGE 3: For 3D data, channels_last is not as optimized, so skip this\n", "        # model = model.to(memory_format=torch.channels_last)\n", "        print(\"Using default memory format for 3D data\")\n", "except RuntimeError as e:\n", "    print(f\"Failed to move model to GPU: {e}\")\n", "    print(\"Falling back to <PERSON>\")\n", "    device = torch.device(\"cpu\")\n", "    model = model.to(device)\n", "\n", "# CHANGE 4: Reduce batch size for 3D data (much more memory intensive)\n", "if device.type == \"cuda\":\n", "    # Optimize for 3D - much smaller batch size\n", "    batch_size = 4  # Start with very small batch size for 3D\n", "    num_workers = 4  # Keep workers reasonable\n", "else:\n", "    batch_size = 1  # Even smaller for CPU with 3D data\n", "    num_workers = 2\n", "\n", "# Setup data loaders with optimized parameters for 3D\n", "train_loader, val_loader = data_manager.setup(\n", "    batch_size=batch_size, \n", "    num_workers=num_workers,\n", "    pin_memory=(device.type == \"cuda\"),\n", "    # CHANGE 5: Add patch size parameters for 3D\n", "    patch_size=(64, 64, 64),  # Reasonable 3D patch size\n", "    patch_overlap=0.25,       # 25% overlap between patches\n", "    use_patches=True          # Enable patch-based training for 3D\n", ")\n", "\n", "# Create checkpoint directory\n", "checkpoint_dir = \"checkpoints/run_3d_\" + datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "os.makedirs(checkpoint_dir, exist_ok=True)\n", "\n", "# CHANGE 6: Reduce number of epochs for initial 3D testing\n", "train_epochs = 400  # Slightly fewer epochs due to longer training time\n", "\n", "# Train with optimized parameters for 3D\n", "try:\n", "    trained_model, history, checkpoint_dir = train_model_with_tracking(\n", "        model=model,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        num_epochs=train_epochs,\n", "        device=device,\n", "        # CHANGE 7: Slightly lower learning rate for 3D to improve stability\n", "        initial_lr=5e-4,\n", "        weight_decay=1e-4,\n", "        patience=20,\n", "        checkpoint_dir=checkpoint_dir,\n", "        loss_type=\"combined\",\n", "        mixed_precision=True,  # Even more important for 3D\n", "        clip_grad_norm=1.0,\n", "        warmup_epochs=5,\n", "        report_interval=1,\n", "        log_dir=None,\n", "        track_step_metrics=True,\n", "        detect_plateau=True,\n", "        plateau_patience=10,\n", "        plateau_factor=3.0,\n", "        # CHANGE 8: Slightly higher flow weight for 3D\n", "        flow_loss_weight=0.3  # Increase flow importance for 3D separation\n", "    )\n", "    \n", "    print(\"3D training completed successfully!\")\n", "\n", "    # Plot history\n", "    plt.figure(figsize=(12, 5))\n", "    plt.subplot(1, 2, 1)\n", "    plt.title(\"Loss\")\n", "    plt.plot(history['train_loss'], label='Train')\n", "    plt.plot(history['val_loss'], label='Validation')\n", "    plt.xlabel('Epoch')\n", "    plt.legend()\n", "    plt.subplot(1, 2, 2)\n", "    plt.title(\"Learning Rate\")\n", "    plt.plot(history['learning_rates'])\n", "    plt.xlabel('Epoch')\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(checkpoint_dir, \"training_plot_3d.png\"))\n", "    plt.show()\n", "    \n", "# CHANGE 9: More aggressive batch size reduction for OOM errors with 3D\n", "except Exception as e:\n", "    print(f\"Training failed with error: {e}\")\n", "    \n", "    if \"out of memory\" in str(e).lower() and batch_size > 1:\n", "        print(f\"Reducing batch size from {batch_size} to {batch_size // 2} and retrying...\")\n", "        batch_size = batch_size // 2\n", "        \n", "        # Re-setup data loaders with smaller batch size\n", "        train_loader, val_loader = data_manager.setup(\n", "            batch_size=batch_size, \n", "            num_workers=num_workers,\n", "            pin_memory=(device.type == \"cuda\"),\n", "            # Keep the same patch parameters\n", "            patch_size=(64, 64, 64),\n", "            patch_overlap=0.25,\n", "            use_patches=True\n", "        )\n", "        \n", "        # Try with even smaller patches if still OOM\n", "        if batch_size == 1:\n", "            print(\"Already at batch size 1, trying smaller patches...\")\n", "            train_loader, val_loader = data_manager.setup(\n", "                batch_size=1,\n", "                num_workers=num_workers, \n", "                pin_memory=(device.type == \"cuda\"),\n", "                patch_size=(32, 32, 32),  # Even smaller patches\n", "                patch_overlap=0.25,\n", "                use_patches=True\n", "            )\n", "        \n", "        # Try training again with reduced settings\n", "        trained_model, history, checkpoint_dir = train_model_with_tracking(\n", "            model=model,\n", "            train_loader=train_loader,\n", "            val_loader=val_loader,\n", "            num_epochs=train_epochs,\n", "            device=device,\n", "            initial_lr=5e-4,\n", "            weight_decay=1e-4,\n", "            patience=25,\n", "            checkpoint_dir=checkpoint_dir,\n", "            loss_type=\"combined\",\n", "            mixed_precision=True,\n", "            clip_grad_norm=1.0,\n", "            warmup_epochs=5,\n", "            report_interval=1,\n", "            log_dir=None,\n", "            track_step_metrics=True,\n", "            flow_loss_weight=0.3\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GitHub Copilot\n", "Modified Code for 3D Spot Detection\n", "Here are the changes needed to make your pipeline work with 3D data:\n", "\n", "Additional Required Changes to Supporting Functions\n", "You'll need these modifications to other parts of your code:\n", "\n", "1. Add 3D Support to watershed_instance_segmentation_enhanced:\n", "2. Update extract_spot_centers for 3D:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def watershed_instance_segmentation_enhanced(prob_map, binary_mask, min_distance=1, \n", "                                           min_spot_size=1, max_spot_size=500,\n", "                                           threshold_abs=0.001, flow=None):\n", "    \"\"\"3D-compatible watershed-based instance segmentation for spots\"\"\"\n", "    from scipy import ndimage as ndi\n", "    from skimage.feature import peak_local_max\n", "    from skimage.segmentation import watershed\n", "    from skimage.measure import regionprops\n", "    \n", "    # Detect if data is 3D\n", "    is_3d = len(prob_map.shape) == 3\n", "    \n", "    # Convert min_distance to integer\n", "    min_distance = int(min_distance)\n", "    \n", "    # Ensure we have positive pixels in the binary mask\n", "    if np.sum(binary_mask) == 0:\n", "        print(\"Warning: Binary mask is empty. Using adaptive threshold.\")\n", "        threshold_adaptive = np.percentile(prob_map, 90)\n", "        binary_mask = (prob_map > threshold_adaptive).astype(np.uint8)\n", "        \n", "        if np.sum(binary_mask) == 0:\n", "            print(\"Warning: Still no positive pixels. Using fixed threshold.\")\n", "            binary_mask = (prob_map > 0.1).astype(np.uint8)\n", "    \n", "    # Print diagnostics\n", "    print(f\"Binary mask: sum={np.sum(binary_mask)}, fraction={np.sum(binary_mask)/binary_mask.size:.6f}\")\n", "    \n", "    # Distance transform to find spot centers - works for both 2D and 3D\n", "    distance = ndi.distance_transform_edt(binary_mask)\n", "    \n", "    # Find local maxima with appropriate dimensionality\n", "    footprint_shape = (3, 3, 3) if is_3d else (3, 3)\n", "    \n", "    # Find local maxima in the distance transform\n", "    coords = peak_local_max(\n", "        distance, \n", "        min_distance=min_distance, \n", "        labels=binary_mask,\n", "        footprint=np.ones(footprint_shape)\n", "    )\n", "    \n", "    print(f\"Initial peaks detected: {len(coords)}\")\n", "    \n", "    if len(coords) == 0:\n", "        # If no peaks detected, try direct peaks on probability map\n", "        print(\"Warning: No peaks found in distance transform. Trying probability map directly.\")\n", "        coords = peak_local_max(\n", "            prob_map, \n", "            min_distance=min_distance,\n", "            threshold_abs=threshold_abs\n", "        )\n", "        print(f\"Direct peaks detected: {len(coords)}\")\n", "    \n", "    # Create markers for watershed\n", "    mask = np.zeros(binary_mask.shape, dtype=bool)\n", "    if len(coords) > 0:\n", "        mask[tuple(coords.T)] = True\n", "    markers, num_spots = ndi.label(mask, structure=np.ones(footprint_shape))\n", "    \n", "    # Apply watershed\n", "    labels = watershed(-prob_map, markers, mask=binary_mask)\n", "    \n", "    # Filter by size if we have any spots\n", "    if num_spots > 0:\n", "        props = regionprops(labels)\n", "        valid_labels = []\n", "        valid_coords = []\n", "        \n", "        for i, prop in enumerate(props):\n", "            if min_spot_size <= prop.area <= max_spot_size:\n", "                valid_labels.append(prop.label)\n", "                centroid = prop.centroid  # Works for both 2D and 3D\n", "                valid_coords.append(list(centroid))\n", "        \n", "        # Create new label image with only valid spots\n", "        filtered_labels = np.zeros_like(labels)\n", "        for i, label in enumerate(valid_labels, 1):\n", "            filtered_labels[labels == label] = i\n", "        \n", "        labels = filtered_labels\n", "        coordinates = np.array(valid_coords) if valid_coords else np.empty((0, 3 if is_3d else 2))\n", "        num_spots = len(valid_labels)\n", "    else:\n", "        labels = np.zeros_like(binary_mask)\n", "        coordinates = np.empty((0, 3 if is_3d else 2))\n", "        num_spots = 0\n", "    \n", "    print(f\"Final spots after filtering: {num_spots}\")\n", "    return labels, num_spots, coordinates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["3. Update compute_spot_flow_loss to handle 3D:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_spot_centers(masks_batch):\n", "    \"\"\"Extract center coordinates from binary masks (2D or 3D)\"\"\"\n", "    from scipy import ndimage as ndi\n", "    from skimage.measure import regionprops\n", "    \n", "    batch_centers = []\n", "    # Process each mask in the batch\n", "    for b in range(masks_batch.shape[0]):\n", "        mask = masks_batch[b, 0].cpu().numpy()\n", "        \n", "        # Detect if the mask is 3D\n", "        is_3d = len(mask.shape) == 3\n", "        \n", "        labeled_mask, num_spots = ndi.label(mask > 0.5)\n", "        \n", "        if num_spots == 0:\n", "            # Empty centers tensor with correct dimensions for 2D or 3D\n", "            empty_dims = 3 if is_3d else 2\n", "            batch_centers.append(torch.zeros((0, empty_dims), device=masks_batch.device))\n", "            continue\n", "            \n", "        centers = []\n", "        props = regionprops(labeled_mask)\n", "        for prop in props:\n", "            centers.append(list(prop.centroid))  # Works for both 2D and 3D\n", "            \n", "        centers_tensor = torch.tensor(centers, dtype=torch.float32, device=masks_batch.device)\n", "        batch_centers.append(centers_tensor)\n", "        \n", "    return batch_centers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_spot_flow_loss(pred_flow, gt_binary, gt_centers, weight=10.0):\n", "    \"\"\"Calculate a flow loss that improves spot separation for 2D and 3D\"\"\"\n", "    batch_size = pred_flow.shape[0]\n", "    device = pred_flow.device\n", "    \n", "    # Detect if we're working with 3D data\n", "    is_3d = pred_flow.shape[1] == 3\n", "    \n", "    loss = 0\n", "    \n", "    for b in range(batch_size):\n", "        # For each image/volume in batch\n", "        centers = gt_centers[b]  # [N,2] or [N,3]\n", "        if len(centers) == 0:\n", "            continue\n", "            \n", "        # Create target flow field - handle both 2D and 3D\n", "        if is_3d:\n", "            d, h, w = pred_flow.shape[2:]\n", "            z_grid, y_grid, x_grid = torch.meshgrid(\n", "                torch.arange(d, device=device).float(),\n", "                torch.arange(h, device=device).float(),\n", "                torch.arange(w, device=device).float()\n", "            )\n", "        else:\n", "            h, w = pred_flow.shape[2:]\n", "            y_grid, x_grid = torch.meshgrid(\n", "                torch.arange(h, device=device).float(),\n", "                torch.arange(w, device=device).float()\n", "            )\n", "        \n", "        # Initialize target flow\n", "        target_flow = torch.zeros_like(pred_flow[b])  # [2,H,W] or [3,D,H,W]\n", "        target_mask = torch.zeros(pred_flow.shape[2:], device=device)  # Mask of valid flow regions\n", "        \n", "        # For each pixel, find the closest center and compute flow vector\n", "        for center in centers:\n", "            if is_3d:\n", "                # 3D vector from each voxel to this center\n", "                cz, cy, cx = center\n", "                z_diff = z_grid - cz\n", "                y_diff = y_grid - cy\n", "                x_diff = x_grid - cx\n", "                \n", "                # Distance to this center in 3D\n", "                dist = torch.sqrt(z_diff**2 + y_diff**2 + x_diff**2) + 1e-6\n", "                \n", "                # Normalize vectors to get flow direction in 3D\n", "                z_flow = -z_diff / dist\n", "                y_flow = -y_diff / dist\n", "                x_flow = -x_diff / dist\n", "            else:\n", "                # 2D version\n", "                cy, cx = center\n", "                y_diff = y_grid - cy\n", "                x_diff = x_grid - cx\n", "                dist = torch.sqrt(y_diff**2 + x_diff**2) + 1e-6\n", "                y_flow = -y_diff / dist\n", "                x_flow = -x_diff / dist\n", "            \n", "            # Region influenced by this center\n", "            weight_mask = torch.exp(-dist/5.0)\n", "            \n", "            # Update flow where this center is closest or within its region\n", "            better_mask = (weight_mask > target_mask)\n", "            target_mask = torch.maximum(target_mask, weight_mask)\n", "            \n", "            # Only update flow where this center has stronger influence\n", "            if is_3d:\n", "                target_flow[0, better_mask] = z_flow[better_mask]\n", "                target_flow[1, better_mask] = y_flow[better_mask]\n", "                target_flow[2, better_mask] = x_flow[better_mask]\n", "            else:\n", "                target_flow[0, better_mask] = y_flow[better_mask]\n", "                target_flow[1, better_mask] = x_flow[better_mask]\n", "        \n", "        # Enhanced loss at boundaries between spots\n", "        # Compute distance to nearest boundary - works for both 2D and 3D\n", "        from scipy import ndimage\n", "        binary_mask = gt_binary[b, 0].cpu().numpy()\n", "        dist_transform = torch.from_numpy(\n", "            ndimage.distance_transform_edt(binary_mask)\n", "        ).to(device)\n", "        \n", "        # Smaller distance = closer to boundary = higher weight\n", "        boundary_weight = torch.exp(-dist_transform/2.0)\n", "        \n", "        # Calculate weighted MSE loss\n", "        boundary_weight = boundary_weight.unsqueeze(0).expand_as(pred_flow[b])\n", "        flow_error = ((pred_flow[b] - target_flow)**2) * boundary_weight * weight\n", "        \n", "        # Only apply loss within the binary mask\n", "        mask = gt_binary[b, 0] > 0\n", "        valid_pixels = mask.sum() + 1e-8\n", "        masked_error = flow_error[:, mask].sum() / valid_pixels\n", "        \n", "        loss += masked_error\n", "        \n", "    return loss / batch_size"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}