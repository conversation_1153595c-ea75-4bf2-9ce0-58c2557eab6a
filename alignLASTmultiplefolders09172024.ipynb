{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import time\n", "import os\n", "import numpy as np\n", "\n", "from tqdm import tqdm\n", "import sys\n", "import pathlib\n", "from valis import registration, valtils, preprocessing, slide_io\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration\n", "\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/0201/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/0201testresults/\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/0201/0201 UA p16 red p21 green 7may24_TileScan 1_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "# def get_round_name(src_f):\n", "#     # Extract the round name from the filename\n", "#     # Example filename: r02c02f01_round1_ch00.tiff\n", "#     img_name = os.path.basename(src_f)\n", "#     round_part = img_name.split('_round')[1]  # Split by '_round'\n", "#     round_name = round_part.split('_ch')[0]  # Get the part before '_ch'\n", "#     return round_name\n", "    \n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)\n", "\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    \n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Using 36 workers out of 36 available CPUs.\n", "INFO:root:Processing subfolder: sortedr02c02f01 using reference slide: /mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round1_ch00.tiff\n", "INFO:root:Full image list: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round1_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round2_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round2_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round2_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round2_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round3_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round3_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round3_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round3_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round4_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round4_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round4_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round4_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round5_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round5_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round5_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round5_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round6_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round6_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round6_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round6_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round7_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round7_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round7_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f01/r02c02f01_round7_ch03.tiff']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["SLF4J: Failed to load class \"org.slf4j.impl.StaticLoggerBinder\".\n", "SLF4J: Defaulting to no-operation (NOP) logger implementation\n", "SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.\n", "Converting images:   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round1_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d3be0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  14%|█▍        | 1/7 [00:00<00:02,  2.16image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round2_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d18a0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  29%|██▊       | 2/7 [00:00<00:02,  2.13image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round3_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f760a1c6e30> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  43%|████▎     | 3/7 [00:01<00:01,  2.11image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round4_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f793c536da0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  57%|█████▋    | 4/7 [00:01<00:01,  2.11image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round5_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f793f6e5870> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  71%|███████▏  | 5/7 [00:02<00:00,  2.11image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round6_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d1240> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  86%|████████▌ | 6/7 [00:02<00:00,  2.09image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f01_round7_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f760a1c7040> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 7/7 [00:03<00:00,  2.09image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images :   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  14%|█▍        | 1/7 [00:00<00:05,  1.20image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  29%|██▊       | 2/7 [00:01<00:03,  1.35image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  43%|████▎     | 3/7 [00:02<00:02,  1.40image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  57%|█████▋    | 4/7 [00:02<00:02,  1.44image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  71%|███████▏  | 5/7 [00:03<00:01,  1.40image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  86%|████████▌ | 6/7 [00:04<00:00,  1.40image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images : 100%|██████████| 7/7 [00:05<00:00,  1.37image/s]\n", "Normalizing images: 100%|██████████| 7/7 [00:00<00:00, 12.32image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 7/7 [00:00<00:00, 49.44image/s]\n", "QUEUEING TASKS | Matching images      :  43%|████▎     | 3/7 [00:00<00:00, 19.12image/s]/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: /home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 2Need at least 4 keypoints for RANSAC filtering, but only have 1/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 3\n", "  warnings.warn(warning_msg, warning_type)\n", "\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 41.48image/s]/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: /home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 3Need at least 4 keypoints for RANSAC filtering, but only have 1"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "  warnings.warn(warning_msg, warning_type)\n", "\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "NoneType: None\n", "NoneType: None\n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["PROCESSING TASKS | Matching images      :   0%|          | 0/7 [00:00<?, ?image/s]/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: /home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/serial_rigid.py:615: UserWarning: 0 between r02c02f01_round2_ch00 and r02c02f01_round3_ch00\n", "  warnings.warn(f\"{len(filtered_match_info12.matched_kp1_xy)} between {img_obj_1.name} and {img_obj_2.name}\")\n", "Need at least 4 keypoints for RANSAC filtering, but only have 1\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: \n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: \n", "  warnings.warn(warning_msg, warning_type)\n", "Need at least 4 keypoints for RANSAC filtering, but only have 1"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n", "Need at least 4 keypoints for RANSAC filtering, but only have 2\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "\n", "\n", "NoneType: None\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n", "/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/serial_rigid.py:615: UserWarning: 0 between r02c02f01_round1_ch00 and r02c02f01_round4_ch00\n", "  warnings.warn(f\"{len(filtered_match_info12.matched_kp1_xy)} between {img_obj_1.name} and {img_obj_2.name}\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: /home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: "]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Need at least 4 keypoints for RANSAC filtering, but only have 1Need at least 4 keypoints for RANSAC filtering, but only have 1\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n", "\n", "  warnings.warn(warning_msg, warning_type)\n", "/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "\n", "NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "NoneType: None\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1\n", "  warnings.warn(warning_msg, warning_type)\n", "/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "NoneType: None\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Need at least 4 keypoints for RANSAC filtering, but only have 1\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NoneType: None\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["PROCESSING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 99.73image/s]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 114241.74image/s]\n", "Finding transforms   :   0%|          | 0/6 [00:00<?, ?image/s]/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/numpy/linalg/linalg.py:2180: RuntimeWarning: invalid value encountered in det\n", "  r = _umath_linalg.det(a, signature=signature)\n", "Finding transforms   :  17%|█▋        | 1/6 [00:00<00:00, 444.55image/s]\n", "/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: SVD did not converge\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 4608, in register\n", "    rigid_registrar = self.rigid_register()\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 3291, in rigid_register\n", "    rigid_registrar = serial_rigid.register_images(self.processed_dir,\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/serial_rigid.py\", line 1565, in register_images\n", "    registrar.align_to_prev(transformer=transformer, qt_emitter=qt_emitter)\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/valis/serial_rigid.py\", line 1088, in align_to_prev\n", "    transformer.estimate(dst_xy, src_xy)\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/skimage/transform/_geometric.py\", line 1362, in estimate\n", "    self.params = _umeyama(src, dst, estimate_scale=True)\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/skimage/transform/_geometric.py\", line 137, in _umeyama\n", "    U, S, V = np.linalg.svd(A)\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/numpy/linalg/linalg.py\", line 1681, in svd\n", "    u, s, vh = gufunc(a, signature=signature, extobj=extobj)\n", "  File \"/home/<USER>/miniconda3/envs/VALIS2/lib/python3.10/site-packages/numpy/linalg/linalg.py\", line 121, in _raise_linalgerror_svd_nonconvergence\n", "    raise LinAlgError(\"SVD did not converge\")\n", "numpy.linalg.LinAlgError: SVD did not converge\n", "\n", "Exception during JVM shutdown: Shutdown must be called from main thread\n", "JV<PERSON> has been killed. If this was due to an error, then a new Python session will need to be started\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Processed batch 1\n", "INFO:root:Processing subfolder: sortedr02c02f02 using reference slide: /mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch00.tiff\n", "INFO:root:Full image list: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round2_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round2_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round2_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round2_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round3_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round3_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round3_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round3_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round4_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round4_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round4_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round4_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round5_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round5_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round5_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round5_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round6_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round6_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round6_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round6_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round7_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round7_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round7_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round7_ch03.tiff']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round1_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76097011e0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  14%|█▍        | 1/7 [00:00<00:02,  2.12image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round2_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a5a82e30> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  29%|██▊       | 2/7 [00:00<00:02,  2.15image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round3_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f793f6e5870> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  43%|████▎     | 3/7 [00:01<00:01,  2.14image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round4_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d3c10> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  57%|█████▋    | 4/7 [00:01<00:01,  2.14image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round5_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a6631d80> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  71%|███████▏  | 5/7 [00:02<00:00,  2.10image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round6_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d1cf0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  86%|████████▌ | 6/7 [00:02<00:00,  2.11image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f02_round7_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d3a00> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 7/7 [00:03<00:00,  2.11image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images :   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  14%|█▍        | 1/7 [00:00<00:05,  1.20image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  29%|██▊       | 2/7 [00:01<00:03,  1.35image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  43%|████▎     | 3/7 [00:02<00:02,  1.41image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  57%|█████▋    | 4/7 [00:02<00:02,  1.45image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  71%|███████▏  | 5/7 [00:03<00:01,  1.44image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  86%|████████▌ | 6/7 [00:04<00:00,  1.45image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images : 100%|██████████| 7/7 [00:04<00:00,  1.43image/s]\n", "Normalizing images: 100%|██████████| 7/7 [00:00<00:00, 15.03image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 7/7 [00:01<00:00,  5.26image/s]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 13.49image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 14.43image/s]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 66576.25image/s]\n", "Finding transforms   : 100%|██████████| 6/6 [00:00<00:00, 859.78image/s]\n", "Finalizing           : 100%|██████████| 7/7 [00:00<00:00, 4900.71image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 3.26 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Preparing images for non-rigid registration: 100%|██████████| 7/7 [00:03<00:00,  1.81image/s]\n", "Finding non-rigid transforms: 100%|██████████| 7/7 [00:17<00:00,  2.57s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 18.245 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 7/7 [00:01<00:00,  3.64image/s]\n", "Preparing images for non-rigid registration: 100%|██████████| 7/7 [00:03<00:00,  1.78image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 7/7 [00:05<00:00,  1.19image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 5.99 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 7/7 [00:00<00:00, 14.26image/s]\n", "  0%|          | 0/7 [00:00<?, ?it/s]INFO:root:Warping images associated with r02c02f02_round1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Sorted round slides: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f02/r02c02f02_round1_ch03.tiff']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Processing channel: r02c02f02_round1_ch00\n", "  0%|          | 0/7 [00:00<?, ?it/s]\n", "INFO:root:Processed batch 2\n", "INFO:root:Processing subfolder: sortedr02c02f03 using reference slide: /mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch00.tiff\n", "INFO:root:Full image list: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round2_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round2_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round2_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round2_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round3_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round3_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round3_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round3_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round4_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round4_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round4_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round4_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round5_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round5_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round5_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round5_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round6_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round6_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round6_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round6_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round7_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round7_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round7_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round7_ch03.tiff']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round1_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7668b9d120> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  14%|█▍        | 1/7 [00:00<00:03,  1.98image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round2_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76097026b0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  29%|██▊       | 2/7 [00:01<00:02,  1.98image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round3_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7609701bd0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  43%|████▎     | 3/7 [00:01<00:01,  2.03image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round4_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7668b9d330> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  57%|█████▋    | 4/7 [00:01<00:01,  2.04image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round5_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f793c537a90> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  71%|███████▏  | 5/7 [00:02<00:00,  2.05image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round6_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7668b9c6a0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  86%|████████▌ | 6/7 [00:02<00:00,  2.02image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f03_round7_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d1cf0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 7/7 [00:03<00:00,  2.03image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images :   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  14%|█▍        | 1/7 [00:00<00:04,  1.44image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  29%|██▊       | 2/7 [00:01<00:03,  1.48image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  43%|████▎     | 3/7 [00:02<00:02,  1.49image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  57%|█████▋    | 4/7 [00:02<00:02,  1.49image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  71%|███████▏  | 5/7 [00:03<00:01,  1.49image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images :  86%|████████▌ | 6/7 [00:04<00:00,  1.49image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Processing images : 100%|██████████| 7/7 [00:04<00:00,  1.49image/s]\n", "Normalizing images: 100%|██████████| 7/7 [00:00<00:00, 14.71image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 7/7 [00:01<00:00,  6.98image/s]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 300.11image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 24.81image/s]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 7/7 [00:00<00:00, 99189.62image/s]\n", "Finding transforms   : 100%|██████████| 6/6 [00:00<00:00, 1489.72image/s]\n", "Finalizing           : 100%|██████████| 7/7 [00:00<00:00, 6261.49image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 2.183 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.345543\n", "INFO:pyvips:VIPS: reducev: 19 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.345543\n", "INFO:pyvips:VIPS: reduceh: 19 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.47619\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.47619\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "Preparing images for non-rigid registration: 100%|██████████| 7/7 [00:03<00:00,  1.83image/s]\n", "Finding non-rigid transforms: 100%|██████████| 7/7 [00:17<00:00,  2.52s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 17.885 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 7/7 [00:01<00:00,  3.62image/s]\n", "Preparing images for non-rigid registration: 100%|██████████| 7/7 [00:04<00:00,  1.69image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 7/7 [00:06<00:00,  1.15image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 6.152 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n", "INFO:pyvips:VIPS: residual reducev by 0.488281\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: reducev: using vector path\n", "INFO:pyvips:VIPS: residual reduceh by 0.488281\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 7/7 [00:00<00:00, 14.47image/s]\n", "  0%|          | 0/7 [00:00<?, ?it/s]INFO:root:Warping images associated with r02c02f03_round1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Sorted round slides: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f03/r02c02f03_round1_ch03.tiff']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Processing channel: r02c02f03_round1_ch00\n", "  0%|          | 0/7 [00:00<?, ?it/s]\n", "INFO:root:Processed batch 3\n", "INFO:root:Processing subfolder: sortedr02c02f04 using reference slide: /mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round1_ch00.tiff\n", "INFO:root:Full image list: ['/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round1_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round1_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round1_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round1_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round2_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round2_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round2_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round2_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round3_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round3_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round3_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round3_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round4_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round4_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round4_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round4_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round5_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round5_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round5_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round5_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round6_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round6_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round6_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round6_ch03.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round7_ch00.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round7_ch01.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round7_ch02.tiff', '/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/sortedr02c02f04/r02c02f04_round7_ch03.tiff']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round1_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7609702ec0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  14%|█▍        | 1/7 [00:00<00:02,  2.10image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round2_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f793c537a90> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  29%|██▊       | 2/7 [00:00<00:02,  2.06image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round3_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f760975ae90> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  43%|████▎     | 3/7 [00:01<00:01,  2.04image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round4_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f7668b9e020> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  57%|█████▋    | 4/7 [00:01<00:01,  2.05image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round5_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76097021a0> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  71%|███████▏  | 5/7 [00:02<00:00,  2.03image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round6_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a6631d80> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  86%|████████▌ | 6/7 [00:02<00:00,  2.02image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = r02c02f04_round7_ch00>, width=2160, height=2160, channels=1, levels=1, RGB=False, dtype=uint16> <valis.slide_io.VipsSlideReader object at 0x7f76a57d3a00> False (1024, 1024)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 7/7 [00:03<00:00,  2.02image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images :   0%|          | 0/7 [00:00<?, ?image/s]INFO:pyvips:VIPS: residual reducev by 0.474074\n", "INFO:pyvips:VIPS: reducev: 13 point mask\n", "INFO:pyvips:VIPS: residual reduceh by 0.474074\n", "INFO:pyvips:VIPS: reduceh: 13 point mask\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["import os\n", "import numpy as np\n", "import logging\n", "import gc\n", "from tqdm import tqdm\n", "from concurrent.futures import ThreadPoolExecutor\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/ome-tiff\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Helper functions\n", "def get_available_workers(max_workers=None):\n", "    # Get the total number of CPU cores\n", "    cpu_count = os.cpu_count()\n", "    \n", "    # Adjust this number based on system load or reserve some cores\n", "    if max_workers:\n", "        num_workers = min(cpu_count, max_workers)\n", "    else:\n", "        num_workers = max(1, int(cpu_count * 0.75))  # Ensure at least 1 worker is available\n", "    \n", "    logging.info(f\"Using {num_workers} workers out of {cpu_count} available CPUs.\")\n", "    return num_workers\n", "\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "def get_round_name(src_f):\n", "    img_name = os.path.basename(src_f).lower()\n", "    try:\n", "        # Extract the part that starts with 'round'\n", "        round_part = next(part for part in img_name.split('_') if part.startswith('round'))\n", "        \n", "        # Get the correct round name (rXXcXXfXX_roundX)\n", "        round_name = '_'.join(img_name.split('_')[:2])  # Get the first 2 parts (rXXcXXfXX, roundX)\n", "        \n", "        logging.debug(f\"Extracted round name: {round_name}\")\n", "        \n", "    except (IndexError, StopIteration) as e:\n", "        logging.error(f\"Error extracting round name from filename {img_name}: {e}\")\n", "        round_name = 'unknown'\n", "\n", "    return round_name\n", "\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all TIFF files in the subfolder\n", "    \n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]\n", "    logging.info(f\"Full image list: {full_img_list}\")\n", "\n", "    # Register rounds using DAPI channels , which all end in \"ch00.tiff\"\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "    \n", "    \n", "    # Create a dictionary with the slides grouped by their round (like round1, round2, etc.)\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] \n", "                  for slide_obj in registrar.slide_dict.values()}\n", "\n", "    all_imgs = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]\n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {get_round_name(slide_warper.src_f)}\")\n", "        \n", "        # Sort the images based on both round and channel to ensure proper order\n", "        try:\n", "            valtils.sort_nicely(round_slides)\n", "        except Exception as e:\n", "            logging.error(f\"Error sorting slides: {e}\")\n", "            continue\n", "        \n", "        logging.debug(f\"Sorted round slides: {round_slides}\")\n", "\n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            \n", "            logging.info(f\"Processing channel: {img_name}\")\n", "\n", "            try:\n", "                warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "                logging.debug(f\"Warped channel shape: {warped_channel.shape}\")\n", "            except Exception as e:\n", "                logging.error(f\"Error warping slide {src_f}: {e}\")\n", "                continue\n", "\n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "            logging.info(f\"Processed channel: {img_name}\")\n", "\n", "    if warped_slide is None:\n", "        logging.error(\"No warped slide created.\")\n", "        return\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"merged_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "\n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving OME-TIFF file: {e}\")\n", "\n", "    del warped_slide\n", "    gc.collect()\n", "    \n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.debug(f\"Subfolders found: {len(subfolders)}\")\n", "\n", "    num_threads = get_available_workers(max_workers=len(subfolders))\n", "\n", "    batch_size = 1\n", "    for i in range(0, len(subfolders), batch_size):\n", "        batch = subfolders[i:i + batch_size]\n", "\n", "        with ThreadPoolExecutor(max_workers=num_threads) as executor:\n", "            executor.map(process_subfolder, batch)\n", "\n", "        logging.info(f\"Processed batch {i // batch_size + 1}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#no multithreading : \n", "import os\n", "import numpy as np\n", "import logging\n", "from tqdm import tqdm\n", "import gc\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/d/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Function to extract the reference slide dynamically for each subfolder\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "# Function to generate OME-XML metadata for the warped image\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Extract the round name from the filename\n", "def get_round_name(src_f):\n", "    # Example filename: r02c02f01_round1_ch00.tiff\n", "    img_name = os.path.basename(src_f)\n", "    round_part = img_name.split('_round')[1]\n", "    round_name = round_part.split('_ch')[0]\n", "    return round_name\n", "\n", "# Function to process a single subfolder\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all .tiff files in the subfolder\n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path) if f.endswith(\".tiff\")]\n", "\n", "    # Perform registration using DAPI channels (assumed to be \"ch00.tiff\")\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "\n", "    # Warp images based on registration results\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] for slide_obj in registrar.slide_dict.values()}\n", "    \n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {slide_warper.name}\")\n", "        valtils.sort_nicely(round_slides)\n", "        \n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "            \n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "    # Create OME-XML metadata\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"sorted_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "    \n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving {ome_tiff_path}: {e}\")\n", "\n", "    # Clear memory\n", "    del warped_slide\n", "    gc.collect()\n", "\n", "# Function to process all subfolders sequentially\n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.info(f\"Found {len(subfolders)} subfolders to process.\")\n", "\n", "    # Process each subfolder sequentially\n", "    for subfolder in subfolders:\n", "        process_subfolder(subfolder)\n", "        logging.info(f\"Completed processing subfolder: {os.path.basename(subfolder)}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "# Main entry point\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua\"\n", "dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results young207ua FILLA/\"\n", "reference_slide = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua/young ctrl skin 4i p16 red p21 green 25sep23_Region 4_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)\n", "\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}], "metadata": {"kernelspec": {"display_name": "VALIS", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}