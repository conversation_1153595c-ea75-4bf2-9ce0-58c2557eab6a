from setuptools import setup, find_packages

setup(
    name='napari-refine-segmentation-3D',  # Removed spaces and special characters
    version='0.1',
    author='Your Name',
    author_email='<EMAIL>',
    description='Napari plugin to refine segmentation masks',
    license='MIT',
    packages=find_packages(),
    install_requires=[
        'napari',
        'numpy',
        'scipy',
        'scikit-image',
        'magicgui',
    ],
    entry_points={
        'napari.plugin': [
            'napari-refine-segmentation = napari_refine_segmentation.widget',  # Ensure correct module path
        ],
    },
)