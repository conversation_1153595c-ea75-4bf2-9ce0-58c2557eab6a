import napari
from magicgui import magicgui
from skimage.morphology import remove_small_objects
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy.ndimage import distance_transform_edt
from skimage.measure import label
import numpy as np

def refine_segmentation(stardist_labels, min_size=10, footprint_size=3, min_distance=5):
    """Post-process StarDist output to separate only touching objects."""
    binary = stardist_labels > 0
    distance = distance_transform_edt(binary)
    local_maxi = peak_local_max(distance, min_distance=min_distance, footprint=np.ones((footprint_size,)*3), labels=binary)
    marker_mask = np.zeros_like(distance, dtype=bool)
    marker_mask[tuple(local_maxi.T)] = True
    markers = label(marker_mask)
    refined_labels = watershed(-distance, markers, mask=binary)
    refined_labels = remove_small_objects(refined_labels, min_size)
    return refined_labels.astype(stardist_labels.dtype)

@magicgui(
    min_size={"widget_type": "SpinBox", "min": 1, "max": 500, "value": 10},
    footprint_size={"widget_type": "SpinBox", "min": 1, "max": 10, "value": 3},
    min_distance={"widget_type": "SpinBox", "min": 1, "max": 50, "value": 5},
)
def refine_labels_widget(viewer: napari.Viewer, label_layer: napari.layers.Labels, min_size: int = 10, footprint_size: int = 3, min_distance: int = 5):
    if label_layer is None:
        print("⚠️ Please select a label layer!")
        return
    refined_labels = refine_segmentation(label_layer.data, min_size, footprint_size, min_distance)
    viewer.add_labels(refined_labels, name="Refined StarDist Labels", scale=label_layer.scale, translate=label_layer.translate, metadata=label_layer.metadata)

def napari_experimental_provide_dock_widget():
    return refine_labels_widget