from setuptools import setup, find_packages

setup(
    name='napari-refine-segmentation-split_objects in_3D',
    version='0.1',
    author='Your Name',
    author_email='<EMAIL>',
    description='Napari plugin to refine segmentation masks',
    license='antho',
    packages=find_packages(),
    install_requires=[
        'napari',
        'numpy',
        'scipy',
        'scikit-image',
        'magicgui',
    ],
    entry_points={
        'napari.plugin': [
            'napari-refine-segmentation = napari_refine_segmentation',
        ],
    },
)