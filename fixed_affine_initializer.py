import ants
import numpy as np
from skimage.registration import phase_cross_correlation
import cupy as cp

def affine_initializer_fixed(fixed, moving):
    """
    Create an affine initializer for ANTs registration using a simpler approach
    that avoids the matrixOffsetF2 error.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        
    Returns:
        An ANTs transform object
    """
    # Get the shift using phase correlation
    shift_y, shift_x, _ = phase_cross_correlation(cp.asnumpy(fixed), cp.asnumpy(moving), upsample_factor=10)
    
    # Create a simple translation transform using Euler2DTransform
    # This avoids the problematic matrixOffsetF2 function
    translation = ants.new_ants_transform(dimension=2, transform_type="Euler2DTransform")
    
    # Set parameters: x translation, y translation, rotation (in radians)
    translation.set_parameters((-shift_x, -shift_y, 0.0))
    
    return translation
