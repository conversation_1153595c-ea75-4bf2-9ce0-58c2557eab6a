from qtpy.QtWidgets import QPushButton, QComboBox
from napari import Viewer
from napari.layers import Image


class QHidePushButton(QPushButton):
    def __init__(self, viewer: Viewer, combobox: QComboBox, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.viewer: Viewer = viewer
        self.combobox: QComboBox = combobox

        self.clicked.connect(self.hideLayers)

    def hideLayers(self):
        for layer in self.viewer.layers:
            if self.combobox.currentText() != layer.name and isinstance(layer, Image):
                layer.visible = False
            if self.combobox.currentText() == layer.name and isinstance(layer, Image):
                layer.visible = True

        self.setEnabled(False)