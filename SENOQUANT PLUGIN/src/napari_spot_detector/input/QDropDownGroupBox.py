import sys
from typing import Type

import napari
from qtpy.QtWidgets import QComboBox

from ..processes import Process
from ..processes.filtering.Filtering import *
from ..processes.roi.ROIDetection import *
from ..processes.components.ConnectedComponents import *


class QDropDownGroupBox(QGroupBox):
    def __init__(self, class_name: Type[Process], viewer: napari.Viewer = None, nofilter=True,
                 input_box: Union[None, QComboBox] = None, *args, **kwargs) -> None:
        self.input_box = input_box
        super().__init__(*args, **kwargs)
        self.viewer = viewer

        self.setLayout(QVBoxLayout())

        self.combo: QComboBox = QComboBox()
        classes = [cls.__name__ for cls in class_name.__subclasses__()]
        if nofilter:
            classes.insert(0, "NoFiltering")
        self.process: Process = (getattr(sys.modules[__name__], classes[0]))(viewer=self.viewer)
        self.combo.addItems(classes)

        self.combo.installEventFilter(self)

        self.combo.setFocusPolicy(QtCore.Qt.StrongFocus)

        self.layout().addWidget(self.combo)
        self.layout().addWidget(self.process)

        # Add Function for Change for DropDown
        self.combo.currentTextChanged.connect(lambda text:
                                              self.on_combo_text_changed(text=text, input_box=self.input_box))

    def eventFilter(self, source, event):
        if event.type() == QtCore.QEvent.Wheel and source is self.combo:
            return True
        return super().eventFilter(source, event)

    def on_combo_text_changed(self, text: str, **kwargs) -> None:
        sender = self.sender()
        widget: QWidget = sender.parentWidget() if sender else QWidget()  # Getting the QWidget from parent
        process: Process = widget.layout().takeAt(1).widget()
        widget.layout().removeWidget(process)
        process.setParent(None)
        process.deleteLater()  # Should there only be 2 Widgets
        self.process = (eval(text))(**kwargs, viewer=self.viewer)
        widget.layout().addWidget(self.process)
        widget.update()
