import copy

import traceback
from typing import Final

import napari
from napari.layers import Labels, Image

from napari.utils.notifications import ErrorNotification

from qtpy.QtWidgets import QPush<PERSON>utton, QHBoxLayout, QLabel, QComboBox, QVBoxLayout, QGroupBox, \
    QWidget

from ..images import Sequence
from .QDropDownGroupBox import QDropDownGroupBox
from .QHidePushButton import QHidePushButton
from ..extensions.napari_senoquant.NucleiWidget import NucleiWidget
from ..images.Sequence import Sequence
from ..processes.filtering.Filtering import PostFiltering, PreFiltering, UFISHPreFiltering, EditFiltering
from ..tokens import GlobalDetectionToken


class EditInput(QWidget):
    TAB_NAME: Final[str] = "Edit Input"

    def __init__(self, viewer: napari.Viewer, nucleiWidget: NucleiWidget, *args, **kwarg):
        super().__init__(*args, **kwarg)
        self.viewer: napari.Viewer = viewer
        self.nucleiWidget: NucleiWidget = nucleiWidget

        self.setLayout(QVBoxLayout())

        self.editPanel: QGroupBox = QGroupBox(title="Edit Images")
        self.editPanel.setLayout(QVBoxLayout())
        self.layout().addWidget(self.editPanel)

        self.input_widget: QWidget = QWidget()
        self.input_widget.setLayout(QHBoxLayout())

        self.input_label: QLabel = QLabel("Input")
        self.input_box: QComboBox = QComboBox()

        # Add Labels
        items = [layer.name for layer in self.viewer.layers if isinstance(layer, Labels) or isinstance(layer, Image)]
        self.input_box.addItems(items)
        # self.input_box.addItems(
        #     [self.nucleiWidget.NucImage.itemText(i)
        #      for i in range(self.nucleiWidget.NucImage.count())]
        # )
        # self.input_box.addItems(label.name for label in self.viewer.layers if isinstance(label, Labels))
        # self.input_box.setCurrentIndex(self.nucleiWidget.NucImage.currentIndex())

        self.viewer.layers.events.inserted.connect(self.update_layer)
        self.viewer.layers.events.removed.connect(self.update_layer)

        self.input_widget.layout().addWidget(self.input_label)
        self.input_widget.layout().addWidget(self.input_box)

        self.editPanel.layout().addWidget(self.input_widget)

        self.editGroup: QDropDownGroupBox = QDropDownGroupBox(class_name=PreFiltering,
                                                              viewer=self.viewer, input_box=self.input_box)

        self.editPanel.layout().addWidget(self.editGroup)

        # self.button: QPushButton = QPushButton("Edit")
        self.button: QHidePushButton = QHidePushButton(self.viewer, self.input_box, text="Edit")
        self.button.clicked.connect(self.run)
        self.layout().addWidget(self.button)

    # def update_widget(self, text: str, widget: QWidget = QWidget(), **kwargs):
    #     sender = self.sender()
    #     filter_widget: QWidget = sender.parentWidget() if sender else QWidget()  # Getting the QWidget from parent
    #     filter = filter_widget.layout().takeAt(1).widget()
    #     filter_widget.layout().removeWidget(filter)
    #     filter.setParent(None)
    #     filter.deleteLater()  # Should there only be 2 Widgets
    #     filter = (eval(text))(**kwargs)
    #     filter_widget.layout().addWidget(filter)
    #     filter_widget.update()

    def update_layer(self):
        current_text: str = self.input_box.currentText()
        self.input_box.clear()

        # items = [self.nucleiWidget.NucImage.itemText(i)
        #          for i in range(self.nucleiWidget.NucImage.count())]
        # self.input_box.addItems(items)
        items = [layer.name for layer in self.viewer.layers if isinstance(layer, Labels) or isinstance(layer, Image)]
        self.input_box.addItems(items)
        if current_text is not '' and current_text in items:
            self.input_box.setCurrentText(current_text)
        else:
            self.input_box.setCurrentIndex(0)

    def run(self):
        button: QPushButton = self.sender()
        if button is None or not isinstance(button, QPushButton):
            return

        if self.input_box.currentText() == '':
            print("No selected image")
            return

        gdt: GlobalDetectionToken = GlobalDetectionToken()
        gdt.inputSequence = Sequence(imageName=self.input_box.currentText(), viewer=self.viewer)
        gdt.inputComputationSequence = copy.copy(gdt.inputSequence)

        worker = self.editGroup.process.run(gdt)

        worker.returned.connect(lambda file_path: self.editGroup.process.open_file_viewer(file_path=file_path, gdt=gdt))
        worker.finished.connect(lambda: button.setEnabled(True))
        worker.errored.connect(lambda error: (ErrorNotification(error),
                                              print(traceback.format_exc()))
                               )
        worker.start()
