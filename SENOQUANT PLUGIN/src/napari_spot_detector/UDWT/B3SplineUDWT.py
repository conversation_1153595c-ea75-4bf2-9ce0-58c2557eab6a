import concurrent.futures
import datetime
import math
import os
import platform
from itertools import repeat
from typing import List

import numpy as np
import scipy

from ..images import Sequence
from ..images.Sequence import Sequence
from ..wavelets.UDWT.WaveletConfigException import WaveletConfigException
from .UDWTScale import UDWTScale

import ctypes

# Determine the Extension for shared libraries based on the Operating System
if os.name == 'nt':
    ext = 'dll'
    # data_path_dependency = os.path.join(os.path.dirname(__file__), 'Library', 'libgomp-1.dll')
    # clib_dependency = ctypes.cdll.LoadLibrary(data_path_dependency)
elif platform.system() == 'Darwin':
    ext = 'dylib'
# elif os.name == 'posix':
else:
    ext = 'so'
# else:


data_path = os.path.join(os.path.dirname(__file__), 'Library', f'libB3SplineUDWT.{ext}')
# print('Prints')
# print(os.path.dirname(__file__))
# print(data_path)
# 'napari_spot_detector', 'UDWT',

# data_type_np = np.float16

clib = ctypes.cdll.LoadLibrary(data_path)
# clib = ctypes.cdll.LoadLibrary(r'C:\Network_File\Desktop\python-docker-cuda\napari-spot-detector\src\napari_spot_detector\UDWT\Library\libB3SplineUDWT.dll')
# clib = ctypes.cdll.LoadLibrary("src/napari_spot_detector/UDWT/Library/libB3SplineUDWT.dll")
# clib = ctypes.cdll.LoadLibrary("napari-spot-detector/src/napari_spot_detector/UDWT/Library/libB3SplineUDWT.dll")
# clib = ctypes.cdll.LoadLibrary("Library/libB3SplineUDWT.dll")
clib.filterAndSwap2D.argtypes = [
    np.ctypeslib.ndpointer(dtype=Sequence.data_type_np, ndim=1, flags='C_CONTIGUOUS'),
    np.ctypeslib.ndpointer(dtype=Sequence.data_type_np, ndim=1, flags='C_CONTIGUOUS'),
    ctypes.c_int, ctypes.c_int, ctypes.c_int]
clib.filterAndSwap2D.restype = ctypes.c_void_p

# C_CONTIGUOUS
# clib.filterZdirection.argtypes = [
#     np.ctypeslib.ndpointer(dtype=np.uintp, ndim=1, flags='C'),
#     np.ctypeslib.ndpointer(dtype=np.uintp, ndim=1, flags='C'),
#     ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int]
# clib.filterZdirection.restype = ctypes.c_void_p

# DOUBLE = ctypes.c_float
# PDOUBLE = ctypes.POINTER(DOUBLE)
# PPDOUBLE = ctypes.POINTER(PDOUBLE)
# PPPDOUBLE = ctypes.POINTER(PPDOUBLE)

clib.b3WaveletScales3D.argtypes = [
    # np.ctypeslib.ndpointer(dtype=np.uintp, ndim=1, flags='C'),
    # np.ctypeslib.ndpointer(dtype=np.uintp, ndim=1, flags='C'),

    # PPDOUBLE, PPPDOUBLE,
    np.ctypeslib.ndpointer(dtype=Sequence.data_type_np, ndim=2, flags='C_CONTIGUOUS'),
    np.ctypeslib.ndpointer(dtype=Sequence.data_type_np, ndim=3, flags='C_CONTIGUOUS'),
    ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int
]
clib.b3WaveletScales3D.restype = ctypes.c_void_p


def b3WaveletScales2DConvolve(dataIn: np.ndarray, width: int, height: int, numScales: int):
    if numScales < 1:
        print("Invalid number of wavelet scales. Number of scales should be an integer >=1")

    try:
        checkImageDimensions2D(width, height, numScales)  # TODO: Add Exception
    except WaveletConfigException as e:
        print(e)

    scales = []
    out = dataIn.astype(Sequence.data_type_np)
    kernel = np.array([3.0 / 8.0, 1.0 / 4.0, 1.0 / 16.0, 1.0 / 4.0, 1.0 / 16.0], dtype=Sequence.data_type_np)
    # kernel = np.array([1.0 / 16.0, 1.0 / 4.0, 3.0 / 8.0, 1.0 / 4.0, 1.0 / 16.0], dtype=Sequence.data_type_np)
    for s in range(1, numScales + 1):
        step_s = int(math.pow(2, s - 1))

        # kernel_step = np.pad(kernel, (step_s, step_s), 'constant')
        kernels = [kernel for _ in range(len(out.shape))]
        for i, k in reversed(list(enumerate(kernels))):
            out = scipy.ndimage.convolve1d(out, k, mode='mirror', axis=i, origin=step_s)
        scales.append(out)
    return np.array(scales)


def b3WaveletScales3DConvolve(dataIn: np.ndarray, width: int, height: int, depth: int, numScales: int) -> np.ndarray:
    if numScales < 1:  # at least on scale is required
        raise WaveletConfigException("Invalid number of wavelet scales. Number of scales should be an integer >=1")

    # check that image dimensions complies with the number of chosen scales
    try:
        checkImageDimensions(width, height, depth, numScales)
    except WaveletConfigException as e:
        raise WaveletConfigException(e)

    scales = []
    out = dataIn.astype(Sequence.data_type_np)
    # kernel = np.array([1.0 / 16.0, 1.0 / 4.0, 3.0 / 8.0, 1.0 / 4.0, 1.0 / 16.0], dtype=Sequence.data_type_np)
    kernel = np.array([3.0 / 8.0, 1.0 / 4.0, 1.0 / 16.0, 1.0 / 4.0, 1.0 / 16.0], dtype=Sequence.data_type_np)
    for s in range(1, numScales + 1):
        step_s = int(math.pow(2, s - 1))  # TODO: Need to look into parameters for scale might need to be removd

        # kernel_step = np.pad(kernel, (step_s, step_s), 'constant')
        kernels = [kernel for _ in range(len(out.shape))]
        for i, k in reversed(list(enumerate(kernels))):
            # for i, k in (enumerate([1, 2, 0], [kernels[1], kernels[2], kernels[0]])):
            out = scipy.ndimage.convolve1d(out, k, mode='mirror', axis=i, origin=step_s)
        scales.append(out)
    return np.array(scales)


# def worker(args):
def worker(currentArray3Dz, stepS, width, height):
    # z, currentArray3D, width, height, stepS = args
    # currentArray3Dz: np.ndarray = args

    wh = width * height
    # prevArray[z] = currentArray3D[z]  # array to filter, original data for the first scale
    currentArray: np.ndarray = np.empty(shape=wh, dtype=Sequence.data_type_np)  # filtered array

    # convolve along the x direction and swap dimensions
    # clib.filterAndSwap2D(currentArray3D[z], currentArray, width, height, stepS)
    clib.filterAndSwap2D(currentArray3Dz, currentArray, width, height, stepS)
    # filterAndSwap2D(currentArray3D[z], currentArray, width, height, stepS)

    # the filtered array becomes the array to filter
    prevArray_temp = currentArray
    # allocate memory for the next dimension filtering (preserve original data)
    currentArray = np.empty(shape=wh, dtype=Sequence.data_type_np)

    # convolve along the y direction and swap dimensions
    clib.filterAndSwap2D(prevArray_temp, currentArray, height, width, stepS)
    # filterAndSwap2D(prevArray_temp, currentArray, height, width, stepS)  # swap size of dimensions

    # prevArray[z] = currentArray
    return currentArray


# def workerZ(args):
def workerZ(prevArray, depth, stepS):
    # Speed up code
    # wh, prevArray, width, height, depth, stepS = args
    # prevArray = args

    # global d
    # global step
    # depth = d
    # stepS = step

    # depth = 2
    # stepS = 2

    # allocate memory for the next dimension filtering (preserve original data)
    currentArray = np.empty(shape=depth, dtype=Sequence.data_type_np, order='C')
    # currentArrayPointer = currentArray.ctypes.data_as(ctypes.POINTER(ctypes.c_float))

    currentArray = np.ascontiguousarray(currentArray)

    # array = np.transpose(prevArray)[wh]
    # array = np.ascontiguousarray(prevArray[:, wh])  # .ctypes.data_as(ctypes.POINTER(ctypes.c_float))
    array = np.ascontiguousarray(prevArray)

    clib.filterAndSwap2D(array, currentArray, depth, 1, stepS)

    return currentArray


def wrap_function(lib, funcname, restype, argtypes):
    ''' Simplify wrapping ctypes functions '''
    func = lib.__getattr__(funcname)
    func.restype = restype
    func.argtypes = argtypes
    return func


def filterAndSwap2D(arrayIn: np.ndarray, arrayOut: np.ndarray, width: int, height: int, stepS: int):
    # B3 spline wavelet configuration
    # the convolution kernel is {1/16, 1/4, 3/8, 1/4, 1/16}
    # with some zeros values inserted between the coefficients, depending on the scale
    w2 = 1.0 / 16.0
    w1 = 1.0 / 4.0
    w0 = 3.0 / 8.0

    w0idx: int
    w1idx1: int
    w2idx1: int
    w1idx2: int
    w2idx2: int
    arrayOutIter: int

    cntX: int
    w0idx = 0

    for y in range(height):  # loop over the second dimension
        # manage the left border with mirror symmetry
        arrayOutIter = 0 + y  # output pointer initialization, we swap dimensions at this point
        # w0idx = arrayIn + y*width;
        w1idx1 = w0idx + stepS - 1
        w2idx1 = w1idx1 + stepS
        w1idx2 = w0idx + stepS
        w2idx2 = w1idx2 + stepS

        cntX = 0
        while cntX < stepS:
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2])) + w1 * (
                    (arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx])
            w1idx1 -= 1
            w2idx1 -= 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += height
            cntX += 1

        w1idx1 += 1
        while cntX < 2 * stepS:
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2])) + w1 * (
                    (arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx])
            w1idx1 += 1
            w2idx1 -= 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += height
            cntX += 1

        w2idx1 += 1
        # filter the center area of the image (no border issue)
        while cntX < width - 2 * stepS:
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2])) + w1 * (
                    (arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += height
            cntX += 1

        w2idx2 -= 1
        # manage the right border with mirror symmetry
        while cntX < width - stepS:
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2])) + w1 * (
                    (arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 += 1
            w2idx2 -= 1
            w0idx += 1
            arrayOutIter += height
            cntX += 1
        w1idx2 -= 1
        while cntX < width:
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2])) + w1 * (
                    (arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 -= 1
            w2idx2 -= 1
            w0idx += 1
            arrayOutIter += height
            cntX += 1


# class B3SplineUDWT:

def b3WaveletScales3D_C(dataIn: np.ndarray, width: int, height: int, depth: int, numScales: int):
    if numScales < 1:  # at least on scale is required
        raise WaveletConfigException("Invalid number of wavelet scales. Number of scales should be an integer >=1")
    print('Starting b3WaveletScales3D')
    stt = datetime.datetime.now()

    wh = width * height

    # currentArray: np.ndarray = np.empty(shape=wh, dtype=Sequence.data_type_np)  # filtered array
    resArray: np.ndarray = np.zeros(shape=(numScales, depth, wh), dtype=Sequence.data_type_np)

    # check that image dimensions complies with the number of chosen scales
    try:
        checkImageDimensions(width, height, depth, numScales)
    except WaveletConfigException as e:
        print(e)
        import traceback
        traceback.print_exc()
        et = datetime.datetime.now()
        elapsed_time = et - stt
        print('b3WaveletScales3D finish (Error):', elapsed_time, 'seconds')
        raise e
        # return resArray

        # dask.array
    # sum_dask = sum_dask_dag.compute()


    # convolve along the x direction and swap dimensions
    # clib.filterAndSwap2D(currentArray3D[z], currentArray, width, height, stepS)
    clib.b3WaveletScales3D(dataIn, resArray, width, height, depth, numScales)

    # a = dataIn.ctypes.data_as(PPDOUBLE)
    # b = resArray.ctypes.data_as(PPPDOUBLE)
    # clib.b3WaveletScales3D(a, b, width, height, depth, numScales)

    et = datetime.datetime.now()
    elapsed_time = et - stt
    print('\nb3WaveletScales3D finish:', elapsed_time, 'seconds')

    return resArray

'''
 /**
 * Compute the scale images for a 3D image
 *
 * @param dataIn
 *        input data in float[XY][Z] format
 * @param width
 *        width of the image
 * @param height
 *        height of the image
 * @param depth
 *        depth of the image
 * @param numScales
 *        number of scale level
 * @return the wavelet scale image
 * @throws WaveletConfigException
 *         if wavelet configuration is incorrect for the given image dimension
 */
'''


def b3WaveletScales3D(dataIn: np.ndarray, width: int, height: int, depth: int, numScales: int) -> np.ndarray:
    if numScales < 1:  # at least on scale is required
        raise WaveletConfigException("Invalid number of wavelet scales. Number of scales should be an integer >=1")
    print('Starting b3WaveletScales3D')
    stt = datetime.datetime.now()

    # check that image dimensions complies with the number of chosen scales
    try:
        checkImageDimensions(width, height, depth, numScales)
    except WaveletConfigException as e:
        print(e)

    s: int  # scale
    stepS: int  # step between non zero coefficients of the convolution kernel, depends on the scale
    wh: int = width * height

    currentArray3D = dataIn  # array to be filtered

    # store wavelet scales in a new 2d double array
    resArray: np.ndarray = np.zeros(shape=(numScales, depth, wh), dtype=Sequence.data_type_np)
    for s in range(1, numScales + 1):  # for each scale
        prevArray: np.ndarray = np.zeros(shape=(depth, currentArray3D.shape[1]), dtype=Sequence.data_type_np)

        # st = datetime.datetime.now()

        # compute the step between non-zero coefficients of the convolution kernel = 2^(scale-1)
        stepS = int(math.pow(2, s - 1))
        # step = stepS
        # with concurrent.futures.ProcessPoolExecutor(max_workers=int(os.cpu_count() - 1)) as executor:
        #     for z, r in zip([z for z in range(depth)], executor.map(worker, [(z, currentArray3D, width,
        #                                                                       height, stepS) for z in range(depth)],
        #                                                             chunksize=int(depth / (os.cpu_count() - 1)))):
        currentArray3D_list = list(currentArray3D)
        with concurrent.futures.ProcessPoolExecutor(max_workers=int(os.cpu_count())) as executor:
            for z, r in zip([z for z in range(depth)], executor.map(worker, currentArray3D_list, repeat(stepS),
                                                                    repeat(width), repeat(height),
                                                                    chunksize=max(1, len(currentArray3D_list) // (
                                                                            2 * os.cpu_count())))):
                prevArray[z][:] = r

        # et = datetime.datetime.now()
        # print("Scale - {}:".format(s), et - st)

        currentArray3D = np.zeros(shape=(depth, wh), dtype=Sequence.data_type_np)

        # xpp = (prevArray.__array_interface__['data'][0]
        #        + np.arange(prevArray.shape[0]) * prevArray.strides[0]).astype(np.uintp)
        # ypp = (currentArray3D.__array_interface__['data'][0]
        #        + np.arange(currentArray3D.shape[0]) * currentArray3D.strides[0]).astype(np.uintp)

        prevArraySwap = list(np.swapaxes(prevArray, 0, 1))

        # st = datetime.datetime.now()
        with concurrent.futures.ProcessPoolExecutor(max_workers=int(os.cpu_count())) as executor:
            # for i, r in zip([wh_num for wh_num in range(wh)],
            #                  executor.map(workerZ, [(wh_num, prevArray, width, height, depth, stepS) for wh_num in
            #                                         range(wh)],
            #                               chunksize=int(wh / (os.cpu_count() - 1)))):
            for i, r in zip([wh_num for wh_num in range(wh)], executor.map(workerZ, prevArraySwap,
                                                                           repeat(depth), repeat(stepS),
                                                                           chunksize=max(1, len(prevArraySwap) //
                                                                                         (2 * os.cpu_count())))):
                currentArray3D[:, i] = r

        # et = datetime.datetime.now()
        # print("Scale z - {}:".format(s), et - st)

        # print('WorkerZ')
        # for wh in range(height * width - 1):
        #     currentArray3D[:, wh] = workerZ((wh, prevArray, width, height, depth, stepS))

        # clib.filterZdirection(xpp, ypp, width, height, depth, stepS)

        # filterZdirectionWorker(prevArray, currentArray3D, width, height, depth, stepS)

        # filterZdirection(prevArray, currentArray3D, width, height, depth, stepS)
        resArray[s - 1] = currentArray3D

    et = datetime.datetime.now()
    elapsed_time = et - stt
    print('b3WaveletScales3D finish:', elapsed_time, 'seconds')
    return resArray


'''
/**
 * filter a 2D image with the B3 spline wavelet scale kernel in the x direction when using the a trous algorithm, and swap dimensions
 * 
 * @param arrayIn
 *        input array
 * @param arrayOut
 *        output array
 * @param width
 *        width of the image
 * @param height
 *        height of the image
 * @param stepS
 *        internal step
 */
'''


def b3WaveletScales2D(dataIn: np.ndarray, width: int, height: int, numScales: int):
    if numScales < 1:
        raise WaveletConfigException("Invalid number of wavelet scales. Number of scales should be an integer >=1")

    try:
        checkImageDimensions2D(width, height, numScales)
    except WaveletConfigException as e:
        raise e

    print("Start b3WaveletScales2D")
    st = datetime.datetime.now()

    s: int  # scale
    stepS: int  # step between non zero coefficients of the convolution kernel, depends on the scale
    wh: int = width * height

    # store wavelet scales in a new 2d double array
    resArray: np.ndarray = np.empty(shape=(numScales, wh), dtype=Sequence.data_type_np)
    prevArray = dataIn  # array to filter, original data for the first scale
    currentArray = np.empty(shape=wh, dtype=Sequence.data_type_np)  # filtered array

    for s in range(1, numScales + 1):  # for each scale
        # compute the step between non-zero coefficients of the convolution kernel = 2^(scale-1)
        stepS = int(math.pow(2, s - 1))

        # convolve along the x direction and swap dimensions
        clib.filterAndSwap2D(prevArray, currentArray, width, height, stepS)
        # self.filterAndSwap2D(prevArray, currentArray, width, height, stepS)
        # swap current and previous array pointers
        if s == 1:
            prevArray = currentArray  # the filtered array becomes the array to filter

            # allocate memory for the next dimension filtering (preserve original data)
            currentArray = np.empty(shape=wh, dtype=Sequence.data_type_np)

        else:
            tmp: np.ndarray = currentArray
            currentArray = prevArray  # the filtered array becomes the array to filter
            prevArray = tmp  # the filtered array becomes the array to filter

        # convolve along the y direction and swap dimensions
        clib.filterAndSwap2D(prevArray, currentArray, height, width, stepS)

        # self.filterAndSwap2D(prevArray, currentArray, height, width, stepS)  # swap size of dimensions
        # swap current and previous array pointers
        tmp: np.ndarray = currentArray
        currentArray = prevArray
        prevArray = tmp

        # resArray[s - 1] = np.empty(shape=wh, dtype=Sequence.data_type_np)  # allocate memory to store the filtered array
        # System.arraycopy(prevArray, 0, resArray[s - 1], 0, wh);
        resArray[s - 1][0:wh] = prevArray[0: wh]

        et = datetime.datetime.now()
        print("Finished b3WaveletScales2D:", et - st)

    return resArray


def checkImageDimensions(width: int, height: int, depth: int, numScales: int):
    minSize: int = getMinSize(numScales)

    if width < minSize or height < minSize or depth < minSize:
        message = "Number of scales too large for the size of the image. These settings require: " \
                  "width> {:d} height {:d} and depth {:d}".format((minSize - 1), (minSize - 1), (minSize - 1))

        print(message)


def checkImageDimensions2D(width: int, height: int, numScales: int):
    minSize = 5 + int(math.pow(2, numScales - 1) - 1) * 4

    if width < minSize or height < minSize:
        message = "Number of scales too large for the size of the image. These settings require: width> %d, " \
                  "height > %d".format((minSize - 1), (minSize - 1))
        raise WaveletConfigException(message)


def b3WaveletCoefficients2D(scaleCoefficients: np.ndarray, originalImage: np.ndarray,
                            numScales: int, numPixels: int) -> np.ndarray:
    print('Start b3WaveletCoefficients2D')
    st = datetime.datetime.now()
    # waveletCoefficients = np.array([])
    waveletCoefficients: np.ndarray = np.empty(shape=(numScales + 1, numPixels), dtype=Sequence.data_type_np)
    iterPrev = originalImage

    for j in range(numScales):
        iterCurrent = scaleCoefficients[j]
        # wCoefficients = np.empty(shape=(numPixels, ))

        # for i in range(numPixels):
        #     wCoefficients[i] = iterPrev[i] - iterCurrent[i]
        wCoefficients = iterPrev - iterCurrent  # TODO: Check Size of arrays

        # np.append([waveletCoefficients, wCoefficients])
        waveletCoefficients[j] = wCoefficients
        iterPrev = iterCurrent

    # residual low pass image is the last wavelet Scale
    waveletCoefficients[numScales][:] = scaleCoefficients[numScales - 1][:]
    # waveletCoefficients[numScales] = np.empty(shape=(numPixels))
    # waveletCoefficients[0:numPixels] = scaleCoefficients[numScales - 1][0:numPixels]
    et = datetime.datetime.now()
    print("Finished b3WaveletCoefficients2D:", et - st)
    return waveletCoefficients


'''
/**
 * Compute the wavelet coefficients from wavelet scales
 * 
 * @param scaleCoefficients
 *        scale coefficients
 * @param originalImage
 *        original image data in float[][] ([XY][Z]) format
 * @param numScales
 *        number of scale level
 * @param numPixels
 *        number of pixels
 * @param depth
 *        depth of the image
 * @return nbScale + 1 , z , computed coefficients
 */
'''


# TODO: Try Threading (Might not work)
# def b3WaveletCoefficients3DHelper(args):
#     scaleCoefficients, iterPrev = args
#
#     # Optimized Code w/ Broadcasting
#     iterCurrent = scaleCoefficients #[j][:]
#     wCoefficients = np.subtract(iterPrev, iterCurrent)
#
#     return wCoefficients
#     # waveletCoefficients[j][:] = wCoefficients
#     # iterPrev = iterCurrent

def b3WaveletCoefficients3D(scaleCoefficients: np.ndarray, originalImage: np.ndarray, numScales: int,
                            numPixels: int, depth: int):
    print('Starting b3WaveletCoefficients3D')
    st = datetime.datetime.now()

    # numScales wavelet images to store, + one image for the low pass residual
    waveletCoefficients: np.ndarray = np.empty(shape=(numScales + 1, depth, numPixels), dtype=Sequence.data_type_np)

    # compute wavelet coefficients as the difference between scale coefficients of subsequent scales

    # the finest scale coefficient is the difference between the original image and the first scale.
    iterPrev: np.ndarray = originalImage
    iterCurrent: np.ndarray
    j: int = 0
    while j < numScales:
        # Optimized Code w/ Broadcasting
        # iterCurrent = scaleCoefficients[j] # z x []
        # wCoefficients = iterPrev[z, :] - iterCurrent
        # wCoefficients = np.subtract(iterPrev, iterCurrent)

        # wCoefficients = iterPrev - iterCurrent
        #
        # waveletCoefficients[j][:] = wCoefficients
        # iterPrev = iterCurrent

        for z in range(depth):
            iterCurrent = scaleCoefficients[j][z]
            # wCoefficients: np.ndarray = np.empty(shape=numPixels, dtype=Sequence.data_type_np)

            # Iterating through pixels (Y*X)
            # for i in range(numPixels):
            wCoefficients = iterPrev[z] - iterCurrent

            waveletCoefficients[j][z] = wCoefficients
            iterPrev[z] = iterCurrent

        j += 1

    # residual low pass image is the last wavelet Scale
    # Optimize Code
    # waveletCoefficients[numScales][:] = np.empty(shape=numPixels, dtype=float)
    waveletCoefficients[numScales][:] = scaleCoefficients[numScales - 1][:]
    # for z in range(depth):
    #     waveletCoefficients[numScales][z] = np.empty(shape=(numPixels), dtype=float)
    #     waveletCoefficients[numScales][z][0: numPixels] = scaleCoefficients[numScales - 1][z][0:numPixels]
    # System.arraycopy(scaleCoefficients[numScales - 1][z], 0, waveletCoefficients[numScales][z], 0, numPixels);

    et = datetime.datetime.now()
    elapsed_time = et - st
    print('b3WaveletCoefficients3D finish:', elapsed_time, 'seconds')

    return waveletCoefficients


def b3SpotConstruction3DMatrix(inputCoefficients: np.ndarray, numScales: int, numPixels: int,
                               depth: int, UDWTScaleArrayList: List[UDWTScale], intersection: bool):
    st = datetime.datetime.now()
    print('Starting b3SpotConstruction3DMatrix')

    UDWTScaleArrayListMatrix = [UDWTScaleArrayList[j].isEnabled() for j in range(numScales)]
    allNotNull = inputCoefficients[0:numScales][UDWTScaleArrayListMatrix] == 0
    v = np.sum((inputCoefficients[0:numScales])[UDWTScaleArrayListMatrix], axis=0)
    allNotNull = np.any(allNotNull, axis=0)
    output = v
    if not intersection:
        output[allNotNull] = 0

    et = datetime.datetime.now()
    elapsed_time = et - st
    print('b3SpotConstruction3DMatrix finish:', elapsed_time, 'seconds')

    return output


def b3SpotConstruction3D(inputCoefficients: np.ndarray, output: np.ndarray, numScales: int, numPixels: int,
                         depth: int, UDWTScaleArrayList: List[UDWTScale]):
    for z in range(depth):
        for i in range(numPixels):
            v: float = 0
            allNotNull: bool = True
            for j in range(numScales):
                if UDWTScaleArrayList[j].isEnabled():
                    if inputCoefficients[j][z][i] == 0:
                        allNotNull = False

                    v += inputCoefficients[j][z][i]

            if allNotNull:
                output[z][i] = v
            else:
                output[z][i] = 0


def b3SpotConstruction2DMatrix(inputCoefficients: np.ndarray, numScales: int, numVoxels: int,
                               UDWTScaleArrayList: List[UDWTScale], intersection: bool):
    UDWTScaleArrayListMatrix = [UDWTScaleArrayList[j].isEnabled() for j in range(numScales)]
    allNotNull = inputCoefficients[0:numScales][UDWTScaleArrayListMatrix] == 0
    v = np.sum((inputCoefficients[0:numScales])[UDWTScaleArrayListMatrix], axis=0)
    allNotNull = np.any(allNotNull, axis=0)
    output = v
    if not intersection:
        output[allNotNull] = 0

    return output


def b3SpotConstruction2D(inputCoefficients: np.ndarray, output: np.ndarray, numScales: int, numVoxels: int,
                         UDWTScaleArrayList: list):
    for i in range(numVoxels):
        allNotNull: bool = True
        y: float = 0
        for j in range(numScales):
            if UDWTScaleArrayList[i].isEnabled():
                if inputCoefficients[j][i] == 0:
                    allNotNull = False

                y += inputCoefficients[j][i]

        if allNotNull:
            output[i] = y
        else:
            output[i] = 0


# @staticmethod
def isNumberOfScaleOkForImage2D(width, height, numScales) -> bool:
    minSize = getMinSize(numScales)

    if width < minSize or height < minSize:
        return False

    return True


# @staticmethod
def isNumberOfScaleOkForImage3D(width, height, depth, numScales):
    minSize = getMinSize(numScales)

    if width < minSize or height < minSize or depth < minSize:
        return False

    return True


# @staticmethod
def getMinSize(numScale: int) -> int:
    return 5 + int(math.pow(2, numScale - 1)) * 4


def filterZdirectionWorker(arrayIn: np.ndarray, arrayOut: np.ndarray, width: int, height: int,
                           depth: int, stepS: int):
    with concurrent.futures.ProcessPoolExecutor(max_workers=int(os.cpu_count() - 1)) as executor:
        for wh, r in zip([wh for wh in range(arrayIn.shape[1])],
                         executor.map(filterZdirection_opt,
                                      [(arrayIn[:, wh], stepS) for wh in range(arrayIn.shape[1])],
                                      chunksize=int(arrayIn.shape[1] / (os.cpu_count() - 1)))):
            # with multiprocessing.Pool(multiprocessing.cpu_count() - 1) as pool:
            #     for z, r in zip([z for z in range(depth)], pool.map(worker, [(z, currentArray3D, width,
            #                                               height, stepS) for z in range(depth)])):
            arrayOut[:, wh] = r


# TODO: Need to Optimize Code
def filterZdirection_opt(args):
    bufferArrayIn, stepS = args
    # B3 spline wavelet configuration
    # the convolution kernel is {1/16, 1/4, 3/8, 1/4, 1/16}
    # with some zeros values inserted between the coefficients, depending on the scale
    # global int
    w2: float = 1.0 / 16.0
    w1: float = 1.0 / 4.0
    w0: float = 3.0 / 8.0

    depth = bufferArrayIn.size
    # bufferArrayIn: np.ndarray = np.empty(shape=(depth),
    #                                      dtype=float)  # create a buffer for each 2D location i
    bufferArrayOut: np.ndarray = np.empty(shape=(depth),
                                          dtype=Sequence.data_type_np)  # create an output buffer for each 2D location i

    # then filter the buffer
    cntZ: int = 0
    arrayOutIter: int = 0
    w0idx: int = 0
    w1idx1: int = w0idx + stepS - 1
    w2idx1: int = w1idx1 + stepS
    w1idx2: int = w0idx + stepS
    w2idx2: int = w1idx2 + stepS

    while cntZ < stepS:
        bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) + \
                                       w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                       w0 * (bufferArrayIn[w0idx])
        w1idx1 -= 1
        w2idx1 -= 1
        w1idx2 += 1
        w2idx2 += 1
        w0idx += 1
        arrayOutIter += 1
        cntZ += 1

    w1idx1 += 1
    while cntZ < 2 * stepS:
        bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                       + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) \
                                       + w0 * (bufferArrayIn[w0idx])
        w1idx1 += 1
        w2idx1 -= 1
        w1idx2 += 1
        w2idx2 += 1
        w0idx += 1
        arrayOutIter += 1
        cntZ += 1

    w2idx1 += 1
    # filter the center area of the image (no border issue)
    while cntZ < depth - 2 * stepS:
        bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                       + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) \
                                       + w0 * (bufferArrayIn[w0idx])
        w1idx1 += 1
        w2idx1 += 1
        w1idx2 += 1
        w2idx2 += 1
        w0idx += 1
        arrayOutIter += 1
        cntZ += 1

    w2idx2 -= 1
    # manage the right border with mirror symmetry
    while cntZ < depth - stepS:
        bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                       + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                       w0 * (bufferArrayIn[w0idx])
        w1idx1 += 1
        w2idx1 += 1
        w1idx2 += 1
        w2idx2 -= 1
        w0idx += 1
        arrayOutIter += 1
        cntZ += 1

    w1idx2 -= 1
    while cntZ < depth:
        bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                       + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                       w0 * (bufferArrayIn[w0idx])
        w1idx1 += 1
        w2idx1 += 1
        w1idx2 -= 1
        w2idx2 -= 1
        w0idx += 1
        arrayOutIter += 1
        cntZ += 1

    return bufferArrayOut


def filterZdirection(arrayIn: np.ndarray, arrayOut: np.ndarray, width: int, height: int,
                     depth: int, stepS: int):
    # B3 spline wavelet configuration
    # the convolution kernel is {1/16, 1/4, 3/8, 1/4, 1/16}
    # with some zeros values inserted between the coefficients, depending on the scale
    # global int
    w2: float = 1.0 / 16.0
    w1: float = 1.0 / 4.0
    w0: float = 3.0 / 8.0

    bufferArrayIn: np.ndarray = np.empty(shape=(depth),
                                         dtype=Sequence.data_type_np)  # create a buffer for each 2D location i
    bufferArrayOut: np.ndarray = np.empty(shape=(depth),
                                          dtype=Sequence.data_type_np)  # create an output buffer for each 2D location i
    # bufferArrayIn: np.ndarray = np.empty(shape=(depth, width* height), dtype=float)  # create a buffer for each 2D location i
    # bufferArrayOut: np.ndarray = np.empty(shape=(depth, width* height), dtype=float)  # create an output buffer for each 2D location i
    for i in range(width * height):  # loop in 2D over the pixels of the slices, can be done in a parallel manner
        for z in range(depth):
            bufferArrayIn[z] = arrayIn[z][i]

        # then filter the buffer
        cntZ: int = 0
        arrayOutIter: int = 0
        w0idx: int = 0
        w1idx1: int = w0idx + stepS - 1
        w2idx1: int = w1idx1 + stepS
        w1idx2: int = w0idx + stepS
        w2idx2: int = w1idx2 + stepS

        while cntZ < stepS:
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) + \
                                           w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                           w0 * (bufferArrayIn[w0idx])
            w1idx1 -= 1
            w2idx1 -= 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += 1
            cntZ += 1

        w1idx1 += 1
        while cntZ < 2 * stepS:
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                           + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) \
                                           + w0 * (bufferArrayIn[w0idx]);
            w1idx1 += 1
            w2idx1 -= 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += 1
            cntZ += 1

        w2idx1 += 1
        # filter the center area of the image (no border issue)
        while cntZ < depth - 2 * stepS:
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                           + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) \
                                           + w0 * (bufferArrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 += 1
            w2idx2 += 1
            w0idx += 1
            arrayOutIter += 1
            cntZ += 1

        w2idx2 -= 1
        # manage the right border with mirror symmetry
        while cntZ < depth - stepS:
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                           + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                           w0 * (bufferArrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 += 1
            w2idx2 -= 1
            w0idx += 1
            arrayOutIter += 1
            cntZ += 1

        w1idx2 -= 1
        while cntZ < depth:
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2])) \
                                           + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + \
                                           w0 * (bufferArrayIn[w0idx])
            w1idx1 += 1
            w2idx1 += 1
            w1idx2 -= 1
            w2idx2 -= 1
            w0idx += 1
            arrayOutIter += 1
            cntZ += 1

        # copy the buffer to the original structure
        for z in range(depth):
            arrayOut[z][i] = bufferArrayOut[z]
