import os.path
import traceback
from typing import Union, Optional

# import cv2
from joblib import Parallel, delayed
from napari.layers import Labels, Shapes
from napari_roi import R<PERSON><PERSON>rigin

from .B3SplineUDWT import *
from ..tokens import GlobalDetectionToken
from ..wavelets.UDWT.WaveletConfigException import WaveletConfigException
from ..images.ROI import ROI
from ..images.Sequence import Sequence
from ..images.DetectionSpot import DetectionSpot

import datetime
import cc3d
import numpy as np
import SimpleIT<PERSON> as sitk
from numba import njit

from .multiprocessing_helper import getIntensityMeasurements, getIntensityMeasurementsSITK, runOpenCV3DParallel


@njit
def my_function(sequence_list: np.ndarray, image: np.ndarray, component: np.ndarray):
    labels = list(sequence_list)
    result = np.zeros((len(labels), 5), dtype=np.uint32)
    x = sequence_list

    for i, s in enumerate(labels):
        extracted_image: np.ndarray = image[component == s]  # .astype(np.uint32)
        result[i, 0] = np.min(extracted_image)
        result[i, 1] = np.max(extracted_image)
        result[i, 2] = np.mean(extracted_image)
        result[i, 3] = np.sum(extracted_image)
        result[i, 4] = s
        # result[i] = np.array(
        #     [np.min(extracted_image), np.max(extracted_image), np.mean(extracted_image), np.sum(extracted_image), s])
        # print('Finished', s)
    # print('Finished')

    # f = np.empty((4, len(sequence_list)), dtype=np.int64)
    # # print(x.shape)
    # f[0] = x ** 2
    # f[1] = x ** 3
    # f[2] = x ** 4
    # f[3] = x ** 5
    # return f
    # f = np.empty((4, len(x)), dtype=np.int64)
    # print(x.shape)
    # f[0] = x**2
    # f[1] = x**3
    # f[2] = x**4
    # f[3] = x**5
    return result


class UDWTWaveletCore:
    def __init__(self):
        # Display Binary Label Layer
        self.binarySequence: Union[np.ndarray, None] = None

        self.task = None

    # def getBinarySequence(self):
    #     return self.binarySequence

    class UDWTWaveletCoreProcessors(object):
        def __init__(self, input_sequence: Sequence = None, t: int = 1, ns: int = 1, neg: bool = False,
                     useROIWat: bool = False, doBinary: bool = False,
                     crop: bool = False, intersection: bool = True):
            self.labels = None
            self.crop = crop
            self.sequence = input_sequence
            self.frame = t
            self.numScales = ns
            self.negative = neg
            self.useROIWat = useROIWat
            self.doBinary = doBinary
            self.intersection = intersection

            # self._parent_class = self
            # image: np.ndarray = self.sequence.getAllImage()
            # self._parent_class.binarySequence = np.zeros(image.shape, dtype=Sequence.data_type_np)

        def call(self):
            pass

        def run(self, seqid, image: np.ndarray, component: np.ndarray):
            extracted_image: np.ndarray = image[component == seqid[0]]
            maxValue = np.max(extracted_image)
            minValue = np.min(extracted_image)
            meanValue = np.mean(extracted_image)
            sumValue = np.sum(extracted_image)

            return [minValue, maxValue, meanValue, sumValue]

        #
        # @staticmethod
        # @njit
        # def getIntensityMeasurements(sequence_list: np.ndarray, image: np.ndarray, component: np.ndarray):
        #     print("Starting Intensity Measurements")
        #     # labels = np.unique(component[component != 0])
        #
        #     labels = list(sequence_list)
        #     result = np.zeros((len(labels), 5), dtype=np.uint32)
        #     # print(result.shape)
        #     for i, s in enumerate(labels):
        #         extracted_image: np.ndarray = image[component == s].astype(np.uint32)
        #         result[i] = np.array([s, np.min(extracted_image), np.max(extracted_image), np.mean(extracted_image), np.sum(extracted_image)])
        #         print('finsh', s)
        #     print('Finished')
        #     # return np.array([1])
        #
        #     # f = np.empty((4, len(sequence_list)), dtype=np.int64)
        #     # print(sequence_list.shape)
        #     # f[0] = sequence_list ** 2
        #     # f[1] = sequence_list ** 3
        #     # f[2] = sequence_list ** 4
        #     # f[3] = sequence_list ** 5
        #     # return f
        #     return result

        # @staticmethod
        # @njit
        # def my_function(sequence_list):
        #     labels = list(sequence_list)
        #     result = np.zeros((len(labels), 5), dtype=np.uint32)
        #     x = sequence_list
        #
        #     f = np.empty((4, len(sequence_list)), dtype=np.int64)
        #     # print(x.shape)
        #     f[0] = x ** 2
        #     f[1] = x ** 3
        #     f[2] = x ** 4
        #     f[3] = x ** 5
        #     return f

        def cropping_rebound(self, detectionList: DetectionSpot, crop: bool = False):
            if crop:
                self.sequence.crop = False
                reconstruction_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                                                       self.sequence.getSizeY(),
                                                       self.sequence.getSizeX()),
                                                dtype=detectionList.reconstruction.dtype)

                # print(reconstruction_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #       self.sequence.getCmin():self.sequence.getCmax() + 1].shape)

                reconstruction_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                self.sequence.getCmin():self.sequence.getCmax() + 1] = detectionList.reconstruction

                detectionList.reconstruction = reconstruction_bound
                # _parent_class.reconstruction = detectionList.reconstruction

                binaryDetectionResult3D_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                                                                self.sequence.getSizeY(),
                                                                self.sequence.getSizeX()),
                                                         dtype=detectionList.binaryDetectionResult.dtype)

                binaryDetectionResult3D_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                self.sequence.getCmin():self.sequence.getCmax() + 1] = detectionList.binaryDetectionResult

                detectionList.binaryDetectionResult = binaryDetectionResult3D_bound

                components_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                                                   self.sequence.getSizeY(),
                                                   self.sequence.getSizeX()),
                                            dtype=detectionList.components.dtype)

                components_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                self.sequence.getCmin():self.sequence.getCmax() + 1] = detectionList.components

                detectionList.components = components_bound

                # Fix position by shift using cmin and rmin - rxc
                detectionList.stats['centroids'][:, 1] += int(self.sequence.getRmin())
                detectionList.stats['centroids'][:, 2] += int(self.sequence.getCmin())

        def extract_cc3d(self, binaryDetectionResult3D: np.ndarray, image: np.ndarray,
                         detectionList: DetectionSpot, sequence: Sequence):
            connectivity = 26  # only 4,8 (2D) and 26, 18, and 6 (3D) are allowed

            print("Start Connected Components Function")
            s = datetime.datetime.now()
            labels_out, N = cc3d.connected_components(binaryDetectionResult3D.astype(np.uint8),
                                                      connectivity=connectivity,
                                                      return_N=True)
            stats = cc3d.statistics(labels_out)
            del stats['bounding_boxes']
            print("Results from Function -", datetime.datetime.now() - s)

            # Remove Background Information from Connect Components
            stats['centroids'] = np.delete(stats['centroids'], 0, axis=0)
            stats['voxel_counts'] = np.delete(stats['voxel_counts'], 0, axis=0)

            # TODO: Changing the datatype from the original float64
            detectionList.components = labels_out  # .astype(Sequence.data_type_np)
            detectionList.stats = stats
            detectionList.N = N

            # self.labels = labels_out

            print("Start Calculations")
            s = datetime.datetime.now()

            image_reduce = image.ravel().astype(np.float32)
            labels_out_reduce = labels_out.ravel().astype(np.uint16)

            # Get Intensity Measurements
            seqids = np.arange(1, detectionList.N + 1).astype(np.uint16)

            batches = np.array_split(seqids, 1000)

            print(type(batches[0]))

            print("Start Intensity Parallel")

            # Process the batches in parallel
            # my_array = np.arange(10000)
            # batches = np.array_split(my_array, 100)
            workers = os.cpu_count() - 1
            detectionList.intensity = np.concatenate(
                Parallel(n_jobs=workers, batch_size=max(2, int(len(batches) / workers)), pre_dispatch='8*n_jobs')(
                    delayed(getIntensityMeasurements)(i, image_reduce, labels_out_reduce) for i in batches))
            print(detectionList.intensity.shape)
            # detectionList.intensity = np.concatenate(Parallel(n_jobs=4, batch_size=8,
            #                                                   pre_dispatch='2*n_jobs') \
            #                                              (delayed(my_function(batch))(
            #                                                  batch) for batch in batches), axis=0)

            # Process the batches in parallel
            # detectionList.intensity = np.concatenate(Parallel(n_jobs=4, batch_size=8,
            #                                    pre_dispatch='2*n_jobs')\
            #     (delayed(self.getIntensityMeasurements(batch, image=image_reduce,
            #                                            component=labels_out_reduce))(batch) for batch in batches), axis=0)

            sorted_indices = np.argsort(detectionList.intensity[:, 4])
            detectionList.intensity = detectionList.intensity[sorted_indices][:, 0:4]
            print(detectionList.intensity.shape)
            # detectionList.intensity = self.getIntensityMeasurements(image=image_reduce, component=labels_out_reduce)
            # seqids = np.arange(1, detectionList.N + 1)[:, np.newaxis]
            # detectionList.intensity = np.apply_along_axis(self.run, 1, seqids, image=image, component=labels_out)

            print("Results from Calculations -", datetime.datetime.now() - s)

            # TODO: Create a Function instead of copying code
            z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Z)
            y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Y)
            x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
                sequence.aicsimage.physical_pixel_sizes.X)

            voxel = np.prod([z, y, x])
            # voxel = 1
            # if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
            #     voxel = np.prod(sequence.aicsimage.physical_pixel_sizes)

            # volume: np.ndarray = voxel * detectionList.stats['voxel_counts']
            # volume.tofile(os.path.join(os.path.expanduser('~user'), sequence.imageName + '-test.csv'), sep=',')  # TODO: Need to remove
            detectionList.volumes = stats['voxel_counts'] * voxel  # TODO: Need Voxel Dimensions
            detectionList.volumes = detectionList.volumes[:, np.newaxis]

        def extract(self, img: np.ndarray, detectionList: DetectionSpot, originalImage: np.ndarray, sequence: Sequence):
            # Get Intensity Measurements
            seqids = np.arange(img.shape[0])  # .astype(np.uint16).tolist()

            # batches = np.array_split(seqids, 10)

            workers = int(os.cpu_count() / 2)
            result, detectionList.components = Parallel(n_jobs=workers, batch_size=max(2, int(len(seqids) / workers)),
                                                        pre_dispatch='4*n_jobs')(
                delayed(runOpenCV3DParallel)(img[i].astype(np.uint8), i) for i in seqids)

            # results = []
            # cc = []
            # for i in seqids:
            #     result, detectionList.components = runOpenCV3DParallel(img[i].astype(np.uint8), i)
            #     results.append(result)
            #     cc.append(detectionList.components)
            #
            # result = np.concatenate(result)

            detectionList.stats = {'centroids': result[:, 5:8], 'voxel_counts': result[:, 8]}
            detectionList.volumes = result[:, 8]
            detectionList.intensity = result[:, :4]

            detectionList.N = np.sum(np.unique(result[:, -1]) - 1)
            # detectionList.N = detectionList.N + (num_label - 1)  # Take out the background count

            if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
                voxel = np.prod(sequence.aicsimage.physical_pixel_sizes)

                detectionList.volumes = voxel * detectionList.volumes

        # # @njit
        # def old_extract(self, img: np.ndarray, detectionList: DetectionSpot, originalImage: np.ndarray,
        #                 sequence: Sequence):
        #     volumes = []
        #     centroids = np.empty(shape=(0, 3))
        #     counts = np.zeros(shape=(0, 1), dtype=np.uint32)
        #     intensity = np.empty(shape=(0, 4))
        #     detectionList.components = np.zeros(img.shape, dtype=np.uint32)
        #
        #     first = True
        #     for i in range(img.shape[0]):
        #         slice = img[i]
        #         num_label, label_id, stat, centroid = \
        #             cv2.connectedComponentsWithStatsWithAlgorithm(slice.astype(np.uint8), 8,
        #                                                           cv2.CV_32S, ccltype=cv2.CCL_WU)
        #
        #         # remove 0 Label centroid
        #         centroid = np.delete(centroid, 0, 0)
        #
        #         centroid_fix = np.zeros(shape=(num_label - 1, 3), dtype=Sequence.data_type_np)
        #         voxel_counts = np.zeros(shape=(num_label - 1), dtype=np.uint32)
        #         # num = 0
        #         check = False
        #
        #         # image = slice
        #         # labels_out = label_id
        #
        #         # image_reduce = image.ravel().astype(np.float16)
        #         # labels_out_reduce = labels_out.ravel().astype(np.uint16)
        #
        #         # Get Intensity Measurements
        #         # seqids = np.arange(1, detectionList.N + 1).astype(np.uint16)
        #
        #         # batches = np.array_split(seqids, 1000)
        #         #
        #         # workers = os.cpu_count() - 1
        #         # detectionList.intensity = np.concatenate(
        #         #     Parallel(n_jobs=workers, batch_size=max(2, int(len(batches) / workers)), pre_dispatch='4*n_jobs')(
        #         #         delayed(getIntensityMeasurements)(i, image_reduce, labels_out_reduce) for i in batches))
        #
        #         for j in range(1, num_label):
        #             check = True
        #             mask = label_id == j
        #             mask_img = originalImage[i][(label_id == j)]
        #
        #             # mean_ = np.mean(mask_img)
        #             # average_intensity.append(mean_)
        #
        #             # sum_ = np.sum(mask_img)
        #             # total_intensity.append(sum_)
        #
        #             # min_ = np.min(mask_img)
        #             # min_intensity.append(min_)
        #
        #             # max_ = np.max(mask_img)
        #             # max_intensity.append(max_)
        #
        #             # intensity_stats = np.array([[np.min(mask_img), np.max(mask_img),
        #             #                              np.mean(mask_img), np.sum(mask_img)]])
        #             intensity = np.concatenate((intensity, np.array([[np.min(mask_img), np.max(mask_img),
        #                                                               np.mean(mask_img), np.sum(mask_img)]])), axis=0)
        #
        #             volumes.append(stat[j][cv2.CC_STAT_AREA])
        #             voxel_counts[j - 1] = np.count_nonzero(mask)
        #             num = num + 1
        #
        #         if first:
        #             first = False
        #             componentMask = (label_id != 0) * 0
        #             volumes = stat[1:, cv2.CC_STAT_AREA][:, np.newaxis]
        #             counts = stat[1:, cv2.CC_STAT_AREA]
        #             # _, counts = np.unique(label_id, return_counts=True)
        #         else:
        #             componentMask = (label_id != 0) * detectionList.N
        #             voxel_counts = stat[1:, cv2.CC_STAT_AREA]
        #             volumes = np.vstack((volumes, voxel_counts[:, np.newaxis]))
        #             # voxel_counts = np.copy(v)
        #             # _, voxel_counts = np.unique(label_id, return_counts=True)
        #             counts = np.concatenate((counts, voxel_counts))
        #
        #         # detectionList.components[i] = (label_id.astype(np.uint32) + componentMask.astype(np.uint32))
        #         detectionList.components[i] = (label_id + componentMask)
        #
        #         if check:
        #             centroid_fix[:, 0] = i
        #             centroid_fix[:, 1] = centroid[:, 1]
        #             centroid_fix[:, 2] = centroid[:, 0]
        #             detectionList.N = detectionList.N + (num_label - 1)  # Take out the background count
        #
        #             centroids = np.concatenate((centroids, centroid_fix), axis=0)
        #             # counts = np.append(counts, voxel_counts)
        #             # counts = np.concatenate((counts, voxel_counts), axis=0, dtype=np.uint32)
        #
        #     detectionList.stats = {'centroids': centroids, 'voxel_counts': counts}
        #     detectionList.volumes = volumes
        #     detectionList.intensity = intensity
        #
        #     # voxel = 1
        #     if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
        #         # import re
        #         voxel = np.prod(sequence.aicsimage.physical_pixel_sizes)
        #
        #         detectionList.volumes = voxel * detectionList.volumes
        #         # volume.tofile(
        #         #     os.path.join(os.path.expanduser('~'), re.sub(r'[ :]+', '_', sequence.imageName) + '-test.csv'),
        #         #     sep=',')

        def extractConnectedComponents(self, binaryDetectionResult3D: np.ndarray, image: np.ndarray,
                                       detectionList: DetectionSpot, sequence: Sequence):
            st = datetime.datetime.now()
            # cc = sitk.ConnectedComponentImageFilter()
            # # cc.FullyConnectedOn()
            # cc.SetFullyConnected(True)
            binaryDetectionResult3DImage = sitk.GetImageFromArray(binaryDetectionResult3D, isVector=False)
            # print(sequence.aicsimage.physical_pixel_sizes)

            # TODO: Create a Function instead of copying code
            # if sequence.__getattribute__()
            if sequence.aicsimage is not None:
                z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.Z)
                y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.Y)
                x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.X)
            else:
                z = 1
                y = 1
                x = 1
            binaryDetectionResult3DImage.SetSpacing([z, y, x])
            cc = sitk.ConnectedComponent(binaryDetectionResult3DImage)
            cc = sitk.RelabelComponent(cc)

            # im_cc = cc.Execute(binaryDetectionResult3DImage)
            detectionList.components = sitk.GetArrayFromImage(cc)
            # print(np.unique(detectionList.components))
            stats = sitk.LabelIntensityStatisticsImageFilter()
            # LabelIntensityStatisticsImageFilter()

            # voxel = 1
            imageObject = sitk.GetImageFromArray(image)
            # if sequence.path != 'None':
            #     imageObject = sitk.ReadImage(sequence.path)

            imageObject.SetSpacing([z, y, x])
            # if sequence.aicsimage != None and None not in sequence.aicsimage.physical_pixel_sizes:
            # imageObject.SetSpacing(sequence.aicsimage.physical_pixel_sizes)

            # cc.SetSpacing(sequence.aicsimage.physical_pixel_sizes)
            # voxel = np.prod(sequence.aicsimage.physical_pixel_sizes) # Not need because it is calculated later
            stats.Execute(cc, imageObject)

            et = datetime.datetime.now()

            print("Stat:", et - st)

            st = datetime.datetime.now()

            # stats_list = []
            # centroids = []

            # for i in stats.GetLabels():
            #     stats_list.append((stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
            #                        stats.GetStandardDeviation(i), stats.GetVariance(i),
            #                        stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i),
            #                        volume(detectionList.components == i), i))
            #     centroids.append(stats.GetCentroid(i))

            # stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
            #                          stats.GetStandardDeviation(i), stats.GetVariance(i),
            #                          stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i) for i in stats.GetLabels()])

            # print(stats.GetLabels())
            # print(stats.GetNumberOfLabels())
            # print(stats.GetCount())

            # center_gravity = stats.GetCenterOfGravity(foreground_value)
            # center_gravity_coordinate = roi.TransformPhysicalPointToIndex(center_gravity)

            #### Old Computing Method
            # stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
            #                          stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i)
            #                         for i in range(1, stats.GetNumberOfLabels() + 1)])
            #
            # print('stats_array')
            # stats_dict = {'centroids':
            #     np.array(
            #         [binaryDetectionResult3DImage.
            #          TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[::-1]
            #          for i in range(1, stats.GetNumberOfLabels() + 1)]),
            #     'voxel_counts': stats_array[:, 4], 'labels': stats_array[:, -1]}

            # stats_array_list = []
            # centroids = []
            # for i in range(1, stats.GetNumberOfLabels() + 1):
            #     matrix = (stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
            #               stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i,
            #               binaryDetectionResult3DImage.TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[
            #               ::-1])
            #
            #     stats_array_list.append(matrix)

            # centroids.append(matrix)

            stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
                                     stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i) +
                                    binaryDetectionResult3DImage.TransformPhysicalPointToContinuousIndex(
                                        stats.GetCentroid(i))[::-1] for i in range(1, stats.GetNumberOfLabels() + 1)])
            stats_dict = {'centroids': np.array(stats_array[:, 7:10]),
                          'voxel_counts': stats_array[:, 4], 'labels': stats_array[:, 5]}

            # Get Intensity Measurements

            # seqids = np.arange(1, stats.GetNumberOfLabels() + 1).astype(np.uint16)
            #
            # batches = np.array_split(seqids, 1000)
            #
            # print(type(batches[0]))
            #
            # # print("Start Intensity Parallel")
            #
            # # Process the batches in parallel
            # my_array = np.arange(10000)
            # batches = np.array_split(my_array, 100)
            #
            # workers = os.cpu_count() - 1
            # result_matrix = np.concatenate(
            #     Parallel(n_jobs=workers, batch_size=max(2, int(len(batches) / workers)), pre_dispatch='4*n_jobs')(
            #         delayed(getIntensityMeasurementsSITK)(stats, binaryDetectionResult3DImage, i) for i in batches))
            #
            # sorted_indices = np.argsort(result_matrix[:, -1])
            # detectionList.intensity = result_matrix[sorted_indices][:, 0:4]
            #
            # et = datetime.datetime.now()
            #
            # print("Stat variables:", et - st)
            #
            # stats_dict = {'centroids': result_matrix[:, 4:7], 'voxel_counts': result_matrix[:, 7]}

            detectionList.stats = stats_dict
            detectionList.N = stats_dict['voxel_counts'].size

            # detectionList.volumes = result_matrix[:, 8][:, np.newaxis]

            # self.labels = detectionList.components
            detectionList.intensity = stats_array[:, 0:4]
            detectionList.volumes = stats_array[:, 5][:, np.newaxis]

            # if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
            #     volume: np.ndarray = voxel * stats_dict['voxel_counts']
            #     volume.tofile(os.path.join(os.path.expanduser('~'), 'test.csv'), sep=',')

            # detectionList.components[detectionList.components == i]
            # detectionList.volumes_test = [volume(cc, i) for i in stats.GetLabels()]
            # detectionList.cc = cc
            # detectionList.label = stats

    def UDWTWaveletCore2DProcessorContrainer(self):
        _parent_class = self

        class UDWTWaveletCore2DProcessor(UDWTWaveletCore.UDWTWaveletCoreProcessors):
            def __init__(self, input_sequence: Sequence, t: int, ns: int, neg: bool, use_roi_wat: bool, do_binary: bool,
                         cropping: bool, intersection: bool):
                super().__init__(input_sequence=input_sequence, t=t, ns=ns, neg=neg, useROIWat=use_roi_wat,
                                 doBinary=do_binary, crop=cropping, intersection=intersection)

                image = self.sequence.getImage(z=0, t=self.frame).astype(Sequence.data_type_np)
                _parent_class.binarySequence = np.zeros(image.shape, dtype=Sequence.data_type_np)

            def call(self) -> DetectionSpot:
                detection_list = DetectionSpot(image=self.sequence)  # napari points
                rois: List[ROI] = list()  # Threshold and Rectangles Boundaries
                bounds = ROI(Shapes(data=[np.array([[0, 0], [self.sequence.getSizeY(),
                                                             self.sequence.getSizeX()]])], shape_type='rectangle',
                                    visible=False, name='ROI Image Bound'))
                # bounds = [(0, 0), (self.sequence.getSizeX(),
                #                    self.sequence.getSizeY())]  # Returns 2D Rect bounds of sequence {0, 0, sizeX, sizeY}

                if self.useROIWat:
                    rois.extend(self.sequence.rois)
                    self.sequence.crop = self.crop
                    if len(rois) == 0:
                        rois.append(bounds)

                masks: np.ndarray = UDWTWaveletCore.buildBinaryMask(bounds, rois, self.frame, self.sequence, z=1)
                image = self.sequence.getImage(z=0, t=self.frame).astype(Sequence.data_type_np)
                # data_in = image.reshape(image.shape[0] * image.shape[1])
                data_in = image.reshape(-1)
                # data_in = image

                # if self.crop:
                #     masks = masks.reshape(
                #         (self.sequence.getSizeZ(), self.sequence.getSizeY(), self.sequence.getSizeX()))
                #     self.sequence.bounding_box(masks[0])
                #     masks = masks[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #             self.sequence.getCmin():self.sequence.getCmax() + 1]
                #
                #     masks = masks.reshape((masks.shape[0], -1))

                # TODO: Add import traceback & add a DetectionSpot 2D

                try:
                    scales: np.ndarray = b3WaveletScales2D(data_in, image.shape[0], image.shape[1], self.numScales)
                    # scales: np.ndarray = b3WaveletScales2DConvolve(image, self.sequence.getSizeX(),
                    #                                                self.sequence.getSizeY(), self.numScales)
                    # scales = scales.reshape((scales.shape[0], -1))
                except WaveletConfigException as e:
                    print(e)
                    traceback.print_exc()
                    return detection_list

                coefficients = b3WaveletCoefficients2D(scales, data_in, self.numScales, image.shape[0] * image.shape[1])

                # coefficients = coefficients[:, np.newaxis, :]
                # Apply threshold to coefficients but not last one ( residual )

                # Filtering
                for i in range(coefficients.shape[0] - 1):
                    if self.negative:
                        coefficients[i] = -coefficients[i]

                    _parent_class.filter_wat(coefficients[i], i, self.sequence.getSizeX(),
                                                    self.sequence.getSizeY(), masks)

                # TODO: Instead of putting it to residual image 0 , remove it from reconstruction

                # coefficients = coefficients[:, 0, :]
                #  fill of 0 the residual image
                coefficients[len(coefficients) - 1] = np.zeros(shape=(len(coefficients[len(coefficients) - 1])),
                                                               dtype=Sequence.data_type_np)  # TODO: Change it to a fill system

                print('Num Scales: ', self.numScales)

                # reconstruct the image from the wavelet coefficients
                binaryDetectionResult = b3SpotConstruction2DMatrix(coefficients, self.numScales,
                                                                   image.shape[0] * image.shape[1],
                                                                   _parent_class.UDWTScaleArrayList, self.intersection)

                # Binarisation de la reconstruction.
                detection_list.reconstruction = np.copy(binaryDetectionResult)
                binaryDetectionResult3D: np.ndarray = binaryDetectionResult

                detection_list.reconstruction = \
                    detection_list.reconstruction.reshape((self.sequence.getSizeY(),
                                                           self.sequence.getSizeX()))[np.newaxis, :, :]
                binaryDetectionResult3D[binaryDetectionResult3D != 0] = 1

                binaryDetectionResult3D = binaryDetectionResult3D.reshape((self.sequence.getSizeY(),
                                                                           self.sequence.getSizeX())).astype(np.uint8)

                binaryDetectionResult3D = binaryDetectionResult3D[np.newaxis, :, :]
                detection_list.binaryDetectionResult = binaryDetectionResult3D

                # self.cropping_rebound(detectionList=detection_list, crop=self.crop)

                if self.doBinary:
                    _parent_class.binarySequence = detection_list.binaryDetectionResult


                # image = image[np.newaxis, :, :]
                # binaryDetectionResult3D = binaryDetectionResult3D[np.newaxis, :, :]
                #
                # print('Starting Connected Component')
                # if _parent_class.cc == 'OpenCV':
                #     print('OpenCV')
                #     self.extract(binaryDetectionResult3D, detection_list, image, self.sequence)
                # elif _parent_class.cc == 'SimpleITK':
                #     print('SimpleITK')
                #     self.extractConnectedComponents(binaryDetectionResult3D, image, detection_list, self.sequence)
                # else:
                #     print('cc3d')
                #     self.extract_cc3d(binaryDetectionResult3D, image, detection_list, self.sequence)

                return detection_list

        return UDWTWaveletCore2DProcessor

    def UDWTWaveletCore2D3DProcessorContrainer(self):
        _parent_class = self

        class UDWTWaveletCore2D3DProcessor(UDWTWaveletCore.UDWTWaveletCoreProcessors):
            def __init__(self, inputSequence: Sequence, t, ns, neg, useROIWat, doBinary, cropping, intersection):
                super().__init__(input_sequence=inputSequence, t=t, ns=ns, neg=neg, useROIWat=useROIWat,
                                 doBinary=doBinary, crop=cropping, intersection=intersection)
                image: np.ndarray = self.sequence.getAllImage()
                _parent_class.binarySequence = np.zeros(image.shape, dtype=Sequence.data_type_np)
                # self.sequence = input
                # self.frame = t
                # self.numScales = ns
                # self.negative = neg
                # self.useROIWat = useROIWat
                # self.doBinary = doBinary

            def call(self) -> DetectionSpot:
                detectionList = DetectionSpot(image=self.sequence)  # napari points
                rois: List[ROI] = list()  # Threshold and Rectangles Boundaries TODO: Create ROI class
                # bounds = [(0, 0), (self.sequence.getSizeX(),
                #                    self.sequence.getSizeY())]  # Returns 2D bounds of sequence {0, 0, sizeX, sizeY}
                bounds = ROI(Shapes(data=[np.array([[0, 0], [self.sequence.getSizeY(),
                                                             self.sequence.getSizeX()]])], shape_type='rectangle',
                                    visible=False, name='ROI Image Bound'))
                bounds.roi_origin = ROIOrigin.TOP_LEFT

                binaryDetectionResultCopy: np.ndarray

                if self.useROIWat:
                    rois.extend(self.sequence.rois)
                    self.sequence.crop = self.crop
                    if len(rois) == 0:
                        rois.append(bounds)

                masks_img: np.ndarray = UDWTWaveletCore.buildBinaryMask(bounds, rois, self.frame,
                                                                        sequence=self.sequence)

                binaryDetectionResult3D: np.ndarray = np.zeros(shape=(self.sequence.getSizeZ(),
                                                                      self.sequence.getSizeY() * self.sequence.getSizeX()),
                                                               dtype=bool)

                for z in range(self.sequence.getSizeZ()):
                    masks = masks_img[z]  # [np.newaxis, :]
                    image = self.sequence.getImage(z=z, t=self.frame).astype(Sequence.data_type_np)
                    # dataIn = image.reshape(image.shape[0] * image.shape[1])
                    dataIn = image.reshape(-1)
                    # print(dataIn.shape)
                    # dataIn = np.swapaxes(image, 0, 1).flatten()

                    # if self.crop:
                    #     masks = masks.reshape(
                    #         (self.sequence.getSizeZ(), self.sequence.getSizeY(), self.sequence.getSizeX()))
                    #     self.sequence.bounding_box(masks[0])
                    #     masks = masks[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                    #             self.sequence.getCmin():self.sequence.getCmax() + 1]
                    #
                    #     masks = masks.reshape((masks.shape[0], -1))

                    try:
                        scales = b3WaveletScales2D(dataIn, self.sequence.getSizeX(),
                                                   self.sequence.getSizeY(), self.numScales)

                        # scales: np.ndarray = b3WaveletScales2DConvolve(image, self.sequence.getSizeX(),
                        #                                                self.sequence.getSizeY(), self.numScales)
                        # scales = scales.reshape((scales.shape[0], -1))
                        # print(scales.shape)
                    except WaveletConfigException as e:
                        print(e)
                        traceback.print_exc()
                        return detectionList

                    # Reshaping
                    # scales = np.reshape(scales, (scales.shape[0], scales.shape[1] * scales.shape[2]))

                    coefficients = b3WaveletCoefficients2D(scales, dataIn, self.numScales,
                                                           image.shape[0] * image.shape[1])

                    # Apply threshold to coefficients but not last one ( residual )
                    # coefficients = coefficients[:, np.newaxis, :]

                    # Filtering
                    for i in range(coefficients.shape[0] - 1):
                        if self.negative:
                            if self.negative:
                                coefficients[i] = -1 * coefficients[i]
                            # for ii in range(coefficients[i].size()):
                            #     coefficients[i][ii] = -coefficients[i][ii]

                        _parent_class.filter_wat_matrix(coefficients[i], i, self.sequence.getSizeX(),
                                                        self.sequence.getSizeY(),
                                                        masks, axis=0)

                    # TODO: Instead of putting it to residual image 0 , remove it from reconstruction

                    # coefficients = coefficients[:, 0, :]
                    #  fill of 0 the residual image
                    #  Arrays.fill(coefficients[coefficients.length - 1], 0f);
                    coefficients[coefficients.shape[0] - 1] = np.zeros(shape=(len(coefficients[len(coefficients) - 1])),
                                                                       dtype=Sequence.data_type_np)  # TODO: Change it to a fill system
                    #
                    # reconstruct the image from the wavelet coefficients
                    # binaryDetectionResult = np.empty(shape=(image.shape[0] * image.shape[1]), dtype=Sequence.data_type_np)

                    # b3SpotConstruction2D(coefficients, binaryDetectionResult, self.numScales,
                    #                      image.shape[0] * image.shape[1],
                    #                      _parent_class.UDWTScaleArrayList)

                    binaryDetectionResult: np.ndarray = b3SpotConstruction2DMatrix(coefficients, self.numScales,
                                                                                   image.shape[0] * image.shape[1],
                                                                                   _parent_class.UDWTScaleArrayList,
                                                                                   self.intersection)

                    binaryDetectionResult3D[z] = binaryDetectionResult

                # addConnectedComponentDetection
                # st = datetime.datetime.now()
                #
                # print('Starting Connected Component')
                connectivity = 26  # only 4,8 (2D) and 26, 18, and 6 (3D) are allowed
                binaryDetectionResult3D = binaryDetectionResult3D.reshape((self.sequence.getSizeZ(),
                                                                           self.sequence.getSizeY(),
                                                                           self.sequence.getSizeX()))
                detectionList.reconstruction = np.copy(binaryDetectionResult3D)

                # Binarisation de la reconstruction.
                binaryDetectionResult3D: np.ndarray = binaryDetectionResult3D.astype(np.uint8)
                binaryDetectionResult3D[binaryDetectionResult3D != 0] = 1
                # labels_out, N = cc3d.connected_components(binaryDetectionResult3D,
                #                                           connectivity=connectivity, return_N=True)
                # print('Connect Component')
                # # stats = cc3d.statistics(labels_out)
                # # del stats['bounding_boxes']
                # #
                # # detectionList.reconstruction = binaryDetectionResultCopy
                # # _parent_class.reconstruction = binaryDetectionResultCopy
                # # _parent_class.labels = labels_out
                # # detectionList.components = labels_out
                # # detectionList.stats = stats
                # # detectionList.N = N
                # #
                # # # Get Intensity Measurements
                # # seqids = np.arange(0, N + 1)[:, np.newaxis]
                # #
                # image: np.ndarray = self.sequence.getAllImage()
                # # value = np.apply_along_axis(_parent_class.run, 1, seqids, image=image, component=labels_out)
                # # detectionList.intensity = value
                #
                # if _parent_class.cc == 'OpenCV':
                #     print('OpenCV')
                #     # self.extract(binaryDetectionResult3D, detectionList, image, self.sequence)
                # elif _parent_class.cc == 'SimpleITK':
                #     print('SimpleITK')
                #     self.extractConnectedComponents(binaryDetectionResult3D, image, detectionList, self.sequence)
                # else:
                #     print('cc3d')
                #     self.extract_cc3d(binaryDetectionResult3D, image, detectionList, self.sequence)
                #
                # et = datetime.datetime.now()
                # elapsed_time = et - st
                # print('Connected Component Finish:', elapsed_time, 'Seconds')

                detectionList.binaryDetectionResult = binaryDetectionResult3D
                # self.cropping_rebound(detectionList=detectionList, crop=self.crop)

                if self.doBinary:
                    _parent_class.binarySequence = detectionList.binaryDetectionResult

                return detectionList

        return UDWTWaveletCore2D3DProcessor

    def UDWTWaveletCore3DProcessorContrainer(self):
        _parent_class = self  # TODO: Only add the List that's needed

        class UDWTWaveletCore3DProcessor(UDWTWaveletCore.UDWTWaveletCoreProcessors):
            def __init__(self, inputSequence: Sequence, t, ns, neg, useROIWat, doBinary, crop: bool,
                         intersection: bool):
                # roi_napari_list: List[Shapes] = None,
                super().__init__(input_sequence=inputSequence, t=t, ns=ns, neg=neg, useROIWat=useROIWat,
                                 doBinary=doBinary, crop=crop, intersection=intersection)

                image: np.ndarray = self.sequence.getAllImage()
                _parent_class.binarySequence = np.zeros(image.shape, dtype=Sequence.data_type_np)

            @property
            def call(self):
                # print(len(self.roi_napari_list))
                detectionList: DetectionSpot = DetectionSpot(image=self.sequence)  # napari points
                rois: List[ROI] = list()  # Threshold and Rectangles Boundaries
                bounds = ROI(Shapes(data=[np.array([[0, 0], [self.sequence.getSizeY(),
                                                             self.sequence.getSizeX()]])], shape_type='rectangle',
                                    visible=False, name='ROI Image Bound'))
                bounds.roi_origin = ROIOrigin.TOP_LEFT

                binaryDetectionResultCopy: np.ndarray
                if self.useROIWat:
                    rois.extend(self.sequence.rois)
                    self.sequence.crop = self.crop
                    if len(rois) == 0:
                        rois.append(bounds)

                # calculate boundaries
                # masks: Union[np.ndarray, None] = None
                # for roi in rois:
                masks = UDWTWaveletCore.buildBinaryMask(bounds, rois, self.frame, self.sequence)

                # if self.crop:
                #     masks = masks.reshape(
                #         (self.sequence.getSizeZ(), self.sequence.getSizeY(), self.sequence.getSizeX()))
                #     self.sequence.bounding_box(masks[0])
                #     masks = masks[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #             self.sequence.getCmin():self.sequence.getCmax() + 1]
                #
                #     masks = masks.reshape((masks.shape[0], -1))
                #     self.sequence.crop = self.crop

                # Updated Wavelet Transform algorithm
                image: np.ndarray = self.sequence.getAllImage()
                dataIn = image.reshape(image.shape[0], -1).astype(Sequence.data_type_np)

                # if len(image.shape) == 3:
                #     dataIn = np.reshape(image, ((image.shape[0]), image.shape[1] * image.shape[2]))
                # else:
                # for z in range(self.sequence.getSizeZ()):
                #     dataIn[z] = reshape_data(image, "ZYX", "XY", Z=z).flatten()

                try:
                    # scales: np.ndarray = b3WaveletScales3D(dataIn, self.sequence.getSizeX(),
                    #                                        self.sequence.getSizeY(),
                    #                                        self.sequence.getSizeZ(),
                    #                                        self.numScales)

                    # scales: np.ndarray = b3WaveletScales3DConvolve(image, self.sequence.getSizeX(),
                    #                                                self.sequence.getSizeY(),
                    #                                                self.sequence.getSizeZ(), self.numScales)
                    # scales = scales.reshape((scales.shape[0], scales.shape[1], -1))
                    scales: np.ndarray = b3WaveletScales3D_C(dataIn, self.sequence.getSizeX(),
                                                             self.sequence.getSizeY(),
                                                             self.sequence.getSizeZ(),
                                                             self.numScales)
                except WaveletConfigException as el:
                    print(el)
                    traceback.print_exc()
                    return detectionList
                except Exception:
                    traceback.print_exc()
                    return detectionList

                # Reshaping
                # scales = np.reshape(scales, (scales.shape[0], scales.shape[1], scales.shape[2] * scales.shape[3]))

                coefficients = b3WaveletCoefficients3D(scales, dataIn, self.numScales,
                                                       self.sequence.getSizeX() *
                                                       self.sequence.getSizeY(),
                                                       self.sequence.getSizeZ())

                '''
                    Apply threshold to coefficients but not last one ( residual )
                '''

                # masks: np.ndarray = np.empty(
                #     shape=(self.sequence.getSizeZ(),
                #            self.sequence.getSizeY() * self.sequence.getSizeX()), dtype=bool)

                # masks: np.ndarray = np.empty(
                #     shape=(inputSequence.getSizeZ(), inputSequence.getSizeY() * inputSequence.getSizeX()), dtype=bool)
                # for z in range(len(masks)):

                # masks = UDWTWaveletCore.buildBinaryMask(bounds, rois, self.frame, self.sequence.getSizeZ())

                # for z in range(len(masks)):
                #     masks[z] = UDWTWaveletCore.buildBinaryMask(bounds, rois, self.frame, -1)

                for scale in range(coefficients.shape[0] - 1):
                    if self.negative:
                        coefficients[scale] = -1 * coefficients[scale]

                    _parent_class.filter_wat_matrix(coefficients[scale], scale,
                                                    self.sequence.getSizeX(),
                                                    self.sequence.getSizeY(), masks)

                    # for z in range(coefficients.shape[1]):
                    #     _parent_class.filter_wat(coefficients[scale][z], scale,
                    #                              self.sequence.getSizeX(),
                    #                              self.sequence.getSizeY(), masks if masks is None else masks[z])

                #  fill of 0 the residual image
                # c: np.ndarray = coefficients[len(coefficients) - 1]
                coefficients[len(coefficients) - 1][:] = np.zeros(shape=coefficients[len(coefficients) - 1].shape[1])

                reconstruction: np.ndarray = b3SpotConstruction3DMatrix(coefficients, self.numScales,
                                                                        self.sequence.getSizeX() *
                                                                        self.sequence.getSizeY(),
                                                                        self.sequence.getSizeZ(),
                                                                        _parent_class.UDWTScaleArrayList,
                                                                        self.intersection)
                detectionList.reconstruction = np.copy(reconstruction).reshape((self.sequence.getSizeZ(),
                                                                                self.sequence.getSizeY(),
                                                                                self.sequence.getSizeX()))

                # _parent_class.reconstruction = detectionList.reconstruction

                # Binarisation de la reconstruction.
                binaryDetectionResult: np.ndarray = reconstruction.astype(np.uint8)
                binaryDetectionResult[binaryDetectionResult != 0] = 1

                detectionList.binaryDetectionResult = binaryDetectionResult.reshape((self.sequence.getSizeZ(),
                                                                                     self.sequence.getSizeY(
                                                                                     ),
                                                                                     self.sequence.getSizeX(
                                                                                     )))

                # st = datetime.datetime.now()
                # print('Starting Connected Component')
                #
                # # TODO: Add the java of it
                # if _parent_class.cc == 'OpenCV':
                #     print('OpenCV')
                #     # TODO in the future
                #     # self.extract(detectionList.binaryDetectionResult, detectionList, image, self.sequence)
                # elif _parent_class.cc == 'SimpleITK':
                #     print('SimpleITK')
                #     self.extractConnectedComponents(detectionList.binaryDetectionResult, image, detectionList,
                #                                     self.sequence)
                # else:
                #     print('cc3d')
                #     # TODO: solve performance issues
                #     self.extract_cc3d(detectionList.binaryDetectionResult, image, detectionList, self.sequence)
                #
                # et = datetime.datetime.now()
                # elapsed_time = et - st
                # print('Connected Component Finish:', elapsed_time, 'Seconds')
                #
                # self.cropping_rebound(detectionList, crop=self.crop)

                # if self.crop:
                #     self.sequence.crop = False
                #     reconstruction_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                #                                            self.sequence.getSizeY(),
                #                                            self.sequence.getSizeX()),
                #                                     dtype=detectionList.reconstruction.dtype)
                #
                #     # print(reconstruction_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #     #       self.sequence.getCmin():self.sequence.getCmax() + 1].shape)
                #
                #     reconstruction_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #     self.sequence.getCmin():self.sequence.getCmax() + 1] = detectionList.reconstruction
                #
                #     detectionList.reconstruction = reconstruction_bound
                #     # _parent_class.reconstruction = detectionList.reconstruction
                #
                #     binaryDetectionResult3D_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                #                                                     self.sequence.getSizeY(),
                #                                                     self.sequence.getSizeX()),
                #                                              dtype=binaryDetectionResult3D.dtype)
                #
                #     binaryDetectionResult3D_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #     self.sequence.getCmin():self.sequence.getCmax() + 1] = binaryDetectionResult3D
                #
                #     binaryDetectionResult3D = binaryDetectionResult3D_bound
                #
                #     components_bound = np.zeros(shape=(self.sequence.getSizeZ(),
                #                                        self.sequence.getSizeY(),
                #                                        self.sequence.getSizeX()),
                #                                 dtype=detectionList.components.dtype)
                #
                #     components_bound[:, self.sequence.getRmin():self.sequence.getRmax() + 1,
                #     self.sequence.getCmin():self.sequence.getCmax() + 1] = detectionList.components
                #
                #     detectionList.components = components_bound
                #
                #     # Fix position by shift using cmin and rmin - rxc
                #     detectionList.stats['centroids'][:, 1] += int(self.sequence.getRmin())
                #     detectionList.stats['centroids'][:, 2] += int(self.sequence.getCmin())

                if self.doBinary:
                    _parent_class.binarySequence = detectionList.binaryDetectionResult

                return detectionList

        return UDWTWaveletCore3DProcessor

    # def run(self, seqid, image: np.ndarray, component: np.ndarray):
    #     extracted_image: np.ndarray = image[component == seqid[0]]
    #     maxValue = np.max(extracted_image)
    #     minValue = np.min(extracted_image)
    #     meanValue = np.mean(extracted_image)
    #     sumValue = np.sum(extracted_image)
    #
    #     return [minValue, maxValue, meanValue, sumValue]

    def isScaleEnabled(self, scale: int) -> bool:
        return self.UDWTScaleArrayList[scale].isEnabled()

    def getNumberOfScale(self):
        return len(self.UDWTScaleArrayList)

    @staticmethod
    def avesigmaMatrix(image: np.ndarray, mask: np.ndarray, axis=1) -> np.ndarray:
        return UDWTWaveletCore._getMeanAverageDistanceMatrix(image, mask, axis=axis)

    @staticmethod
    def _getMeanAverageDistanceMatrix(data: np.ndarray, mask: np.ndarray, axis=1) -> np.ndarray:
        # print("_getMeanAverageDistanceMatrix")
        # print(mask.min())
        # print(mask.max())
        # print(np.all(mask == False))
        # print(np.all(mask == True))
        # print(np.unique(mask))
        mean = UDWTWaveletCore._getMeanMatrix(data, mask, axis=axis)
        mean = mean if len(mean.shape) == 2 or len(mean.shape) == 0 else mean[:, np.newaxis]
        # print(mean.shape)
        # print(mean)

        if np.all(mask == False):
            return np.mean(np.abs(np.subtract(data, mean)), axis=axis)
        else:
            return np.ma.getdata(np.ma.mean(np.ma.abs(np.subtract(np.ma.masked_equal((data * mask), 0), mean)), axis=axis))
        # return np.mean(np.abs(np.subtract(data, mean)), axis=axis) if np.all(mask == False) \
        #     else np.ma.getdata(np.ma.mean(np.ma.abs(np.ma.masked_equal((data * mask), 0)), axis=axis))

    # @staticmethod
    # def _avesigma(image: np.ndarray, mask: np.ndarray) -> float:
    #     return UDWTWaveletCore._getMeanAverageDistance(image, mask)

    # @staticmethod
    # def _getMeanAverageDistance(data: np.ndarray, mask: np.ndarray) -> float:
    #     mean = UDWTWaveletCore._getMean(data, mask)
    #     a = 0
    #
    #     if mask is None:
    #         for i in range(data.shape[0]):
    #             s = data[i] - mean
    #             a = a + abs(s)
    #
    #         if data.shape[0] > 0:
    #             return a / data.shape[0]
    #
    #     else:
    #         nbValue = 0
    #         for i in range(data.shape[0]):
    #             if mask[i]:
    #                 s = data[i] - mean
    #                 a = a + abs(s)
    #                 nbValue += 1
    #
    #         if nbValue > 0:
    #             return a / nbValue
    #
    #     return 0

    @staticmethod
    def _getMeanMatrix(data: np.ndarray, mask: np.ndarray, axis=1) -> np.ndarray:
        # print("_getMeanMatrix")
        # print(mask.min())
        # print(mask.max())
        # print(np.all(mask == False))
        # print(np.all(mask == True))
        # print(np.unique(mask))
        if np.all(mask == False):
            return np.mean(data, axis=axis, dtype=Sequence.data_type_np)
        else:
            return np.ma.getdata(np.mean(np.ma.masked_equal((data * mask), 0), axis=axis, dtype=Sequence.data_type_np))
        # return data.mean(axis=axis, dtype=Sequence.data_type_np) if np.all(mask == False) else np.ma.getdata(
        #     np.ma.masked_equal((data * mask), 0).mean(axis=axis, dtype=Sequence.data_type_np))

    # @staticmethod
    # def _getMean(data: np.ndarray, mask: np.ndarray) -> float:
    #     mean = 0
    #     sum = 0
    #
    #     if mask is None:
    #         for i in range(data.shape[0]):
    #             sum += data[i]
    #
    #         if data.shape[0] > 0:
    #             mean = sum / data.shape[0]
    #
    #     else:
    #         nbValue = 0
    #         for i in range(data.shape[0]):
    #             if not np.any(mask[i]):
    #                 sum += data[i]
    #                 nbValue += 1
    #
    #         if nbValue > 0:
    #             mean = sum / nbValue
    #
    #     return mean

    def filter_wat_matrix(self, data: np.ndarray, depth: int, width: int, height: int, mask: np.ndarray, axis: int = 1):
        # if the scale is not selected, fill of 0. could be optimized by not rebuilding it.
        if not self.isScaleEnabled(depth):
            data[:] = np.zeros(shape=data.shape, dtype=Sequence.data_type_np)

            return

        st = datetime.datetime.now()
        print('Starting filter_wat_matrix')

        numScale = self.getNumberOfScale() + 2
        # lambdac = np.empty(shape=(data.shape[0], numScale), dtype=Sequence.data_type_np)
        lambdac = np.empty(shape=(numScale), dtype=Sequence.data_type_np)

        # lambdac[:, 0:numScale] = [math.sqrt(2 * math.log(width * height / (1 << (2 * i)))) for i in range(numScale)]
        lambdac[0:numScale] = [math.sqrt(2 * math.log(width * height / (1 << (2 * i)))) for i in range(numScale)]

        mad = UDWTWaveletCore.avesigmaMatrix(data, mask, axis=axis)  # Should return - Depth x 1 Shape
        numMax = self.getNumberOfMaxEnabledScale()
        # dcoeff = np.empty(shape=(data.shape[0], numMax), dtype=Sequence.data_type_np)  # depth x numMax
        dcoeff = np.zeros(shape=(numMax), dtype=Sequence.data_type_np)  # depth x numMax

        # dcoeff[:, 0:numMax] = [self.getScaleThreshold(i) / 100.00 for i in range(numMax)]
        for i in range(numMax):
            dcoeff[i] = self.getScaleThreshold(i) / 100.00
        # dcoeff = [self.getScaleThreshold(i) / 100.00 for i in range(numMax)]

        # coeffThr = np.divide(lambdac[:, depth + 1] * mad, dcoeff[:, depth])
        coeffThr = (lambdac[depth + 1] * mad) / (dcoeff[depth])

        f = lambda x: x < coeffThr # TODO: and smallest theshold
        f_bool = np.apply_along_axis(f, 0, data)

        # print(data.min())
        # print(data.max())
        # print(f_bool.min())
        # print(f_bool.max())

        data[f_bool] = 0

        # print(data.min())
        # print(data.max())

        et = datetime.datetime.now()
        elapsed_time = et - st
        print('filter_wat_matrix finish:', elapsed_time, 'seconds')

    def filter_wat(self, data: np.ndarray, depth: int, width: int, height: int, mask: np.ndarray):
        # if the scale is not selected, fill of 0. could be optimized by not rebuilding it.
        if not self.isScaleEnabled(depth):
            data[:] = np.zeros(data.shape[0], dtype=Sequence.data_type_np)

            return

        st = datetime.datetime.now()
        print('Starting filter_wat')

        # lambdac = np.empty((self.getNumberOfScale() + 2), dtype=Sequence.data_type_np)
        #
        # for i in range(self.getNumberOfScale() + 2):
        #     lambdac[i] = math.sqrt(2 * math.log(width * height / (1 << (2 * i))))

        lambdac = np.sqrt(2 * np.log(width * height / (1 << (2 * np.arange(0, self.getNumberOfScale() + 2, dtype=np.uint64)))))

        if len(data.shape) == 1 and len(mask.shape) != 1:
            mask = mask[0]
        # mad = UDWTWaveletCore._avesigma(data, mask)
        mad = UDWTWaveletCore.avesigmaMatrix(data, mask, axis=0)
        # dcoeff = np.empty(shape=(self.getNumberOfMaxEnabledScale()), dtype=Sequence.data_type_np)

        # for i in range(self.getNumberOfMaxEnabledScale()):
        #     dcoeff[i] = self.getScaleThreshold(i) / 100.00

        dcoeff = np.array([self.getScaleThreshold(i) / 100.00 for i in range(self.getNumberOfMaxEnabledScale())])

        coeffThr = (lambdac[depth + 1] * mad) / dcoeff[depth]

        f = lambda x: x < coeffThr  # 0 if x < coeffThr else x
        f_bool = np.apply_along_axis(f, 0, data)
        data[f_bool] = 0

        et = datetime.datetime.now()
        elapsed_time = et - st
        print('filter_wat finish:', elapsed_time, 'seconds')

    def getScaleThreshold(self, scale: int):
        return self.UDWTScaleArrayList[scale].getThreshold()

    @staticmethod
    def buildBinaryMask(bounds: ROI, rois: List[ROI], t: int, sequence: Sequence, z: Union[int, None] = None) -> Union[
        np.ndarray, None]:
        if z is None:
            zSize = sequence.getSizeZ()
        else:
            zSize = z
        crop = sequence.crop
        # if len(roi) == 0:
        #     return None

        # print(bounds[0].height)
        # print(bounds[0].width)
        # print(bounds[-1].height)
        # print(bounds[-1].width)
        result: np.ndarray = np.zeros(shape=(zSize, int(bounds[0].height), int(bounds[0].width)),
                                      dtype=bool)  # Shape - ZYX

        # roi: ROI
        # for roi in rois:
        # TODO: Return all ones or the whole shape w/ an area of ones of that specific area
        for roi in rois:
            result = np.logical_or(result,
                                   roi.getBooleanMask2D(zSize=zSize, t=t, c=-1, inclusive=True, bounds=bounds))

        if crop:
            # masks = masks.reshape(
            #     (self.sequence.getSizeZ(), self.sequence.getSizeY(), self.sequence.getSizeX()))

            sequence.bounding_box(result[0])
            result = result[:, sequence.getRmin():sequence.getRmax() + 1,
                     sequence.getCmin():sequence.getCmax() + 1]

            # masks = masks.reshape((masks.shape[0], -1))
            # self.sequence.crop = self.crop

        result = result.reshape((result.shape[0], -1))
        return result

    # @staticmethod
    # def buildBinaryMask(bounds, rois: List[ROI], t: int, z: int) -> np.ndarray:
    #     if len(rois) == 0:
    #         return np.empty(shape=(bounds[1][1] * bounds[1][0]), dtype=bool)
    #
    #     result: np.ndarray = np.zeros(shape=(bounds[1][1] * bounds[1][0]), dtype=bool)
    #
    #     roi: ROI
    #     for roi in rois:
    #         # TODO: Return all ones or the whole shape w/ an area of ones of that specific area
    #         result = np.logical_or(result,
    #                                roi.getBooleanMask2D(z=z, t=t, c=-1, inclusive=True))
    #
    #     return result

    def computeDetection(self, computeBinaryDetection: bool, UDWTScaleArrayList: List[UDWTScale], sequenceIn: Sequence,
                         detectNegative: bool, useROIforWATcomputation: bool, force2DWaveletsFor3D: bool,
                         gdt: GlobalDetectionToken, connectedComponent: str, cropping: bool, intersection: bool) -> DetectionSpot:

        st = datetime.datetime.now()
        self.UDWTScaleArrayList: List[UDWTScale] = UDWTScaleArrayList
        self.gdt: GlobalDetectionToken = gdt
        self.cc: str = connectedComponent

        detectionList: DetectionSpot = DetectionSpot(sequenceIn)  # Points from napari

        numScales: int = self.getNumberOfMaxEnabledScale()

        if sequenceIn is None:
            return detectionList
        if sequenceIn.getAllImage().size == 0:
            return detectionList

        # Display Binary Label Layer
        # self.binarySequence: np.ndarray

        # 2D Image
        if sequenceIn.getSizeZ() == 1:
            print('2D')
            if not isNumberOfScaleOkForImage2D(sequenceIn.getSizeX(), sequenceIn.getSizeY(),
                                               self.getNumberOfMaxEnabledScale()):
                print("Scale configuration error")  # Post Error
                return detectionList

            # 2D
            for t in range(sequenceIn.getSizeT()):
                # print("Time:", t)
                taskContrainer = self.UDWTWaveletCore2DProcessorContrainer()
                self.task = taskContrainer(sequenceIn, t, numScales, detectNegative,
                                      useROIforWATcomputation, computeBinaryDetection, cropping, intersection)

                detectionList = self.task.call()

        # 3D Image (with the force2DWavelets for 3D enabled )
        if sequenceIn.getSizeZ() > 1 and force2DWaveletsFor3D == True:
            print("Entering force 2D wavelet for 3D")
            if not isNumberOfScaleOkForImage2D(sequenceIn.getSizeX(),
                                               sequenceIn.getSizeY(),
                                               self.getNumberOfMaxEnabledScale()):
                print("Scale configuration error (your image is too small for this scale)")  # Post Error
                return detectionList

            # 3D
            for t in range(sequenceIn.getSizeT()):
                # print("Time:", t)
                taskContrainer = self.UDWTWaveletCore2D3DProcessorContrainer()
                self.task = taskContrainer(sequenceIn, t, numScales, detectNegative,
                                      useROIforWATcomputation, computeBinaryDetection, cropping, intersection)
                detectionList = self.task.call()

        # ** ** ** * END 2D ****** #

        # 3D Image (classic, without, the force2DWavelets for 3D enable
        if sequenceIn.getSizeZ() > 1 and force2DWaveletsFor3D == False:
            if not isNumberOfScaleOkForImage3D(sequenceIn.getSizeX(),
                                               sequenceIn.getSizeY(),
                                               sequenceIn.getSizeZ(),
                                               self.getNumberOfMaxEnabledScale()):
                scale2 = getMinSize(2)
                scale3 = getMinSize(3)
                scale4 = getMinSize(4)

                # TODO: Add it to activity napari pyqt5

                message = "<html><center>There is a problem with the scale configuration (but don't worry, read this):" \
                          + "<br><br>To run in 3D, the wavelet algorithm needs a number of slices depending " \
                            "on the scale(s) " \
                          + "you enabled: " + "<br>" + "<br>Scale 2 : " + str(scale2) + " slices" + "<br>Scale 3 : " + \
                          str(scale3) + " slices" + "<br>Scale 4 : " + str(scale4) + " slices" + \
                          "<br>" + "<br>To use those parameters you need more Z in your stack.<br>Still, you can bypass this " + \
                          "problem by selecting " \
                          + "<br>the option <b>Force use of 2D Wavelets for 3D</b> in the detector panel" \
                          + "<br>In this case, each 2D slices will be computed separately and results will be " \
                            "merged to create the resulting stack, " \
                          + "<br>(and you don't need to add more Z slices)," + "<br>The sequence file name is:" + \
                          str(sequenceIn.channel_name) + "</center></html>"

                print(message)
                # show_info(message)

                return detectionList

            for t in range(sequenceIn.getSizeT()):
                taskContrainer = self.UDWTWaveletCore3DProcessorContrainer()
                task = taskContrainer(sequenceIn, t, numScales, detectNegative, useROIforWATcomputation,
                                      computeBinaryDetection, cropping, intersection)
                detectionList = task.call

        et = datetime.datetime.now()
        elapsed_time = et - st
        print('process finish:', elapsed_time, 'seconds')

        # self.gdt.detectionResult = detectionList
        return detectionList

    def getNumberOfMaxEnabledScale(self):
        maxScale = 0

        scale: UDWTScale
        for scale in self.UDWTScaleArrayList:
            if scale.isEnabled():
                if scale.scaleNumber > maxScale:
                    maxScale = scale.scaleNumber

        return maxScale
