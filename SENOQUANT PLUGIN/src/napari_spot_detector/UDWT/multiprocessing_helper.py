import os

import cv2
import numpy as np
from joblib import <PERSON><PERSON><PERSON>, delayed, wrap_non_picklable_objects
from numba import njit
import SimpleIT<PERSON> as sitk

from ..images.Sequence import Sequence


@njit
def getIntensityMeasurements(sequence_list: np.ndarray, image: np.ndarray, component: np.ndarray):
    labels = list(sequence_list)
    result = np.zeros((len(labels), 5), dtype=np.uint32)

    for i, s in enumerate(labels):
        extracted_image: np.ndarray = image[component == s]
        extracted_image = extracted_image[extracted_image != 0]
        # result[i, 0] = extracted_image.min()
        # result[i, 1] = extracted_image.max()
        # result[i, 2] = extracted_image.mean()
        # result[i, 3] = extracted_image.sum()
        # result[i, 4] = s

        result[i, 0], result[i, 1], result[i, 2], result[i, 3], result[i, 4] = (
            extracted_image.min(), extracted_image.max(), extracted_image.mean(), extracted_image.sum(), s)

    return result

def runOpenCV3DParallel(slice: np.ndarray, i, connectivity: int = 8, ccltype = cv2.CCL_WU):
    num_label, label_id, stat, centroid = \
        cv2.connectedComponentsWithStatsWithAlgorithm(slice.astype(np.uint8), connectivity,
                                                      cv2.CV_32S, ccltype=ccltype) #cv.GRANA

    # remove 0 Label centroid
    centroid = np.delete(centroid, 0, 0)

    centroid_fix = np.zeros(shape=(num_label - 1, 3), dtype=Sequence.data_type_np)

    image = slice
    labels_out = label_id

    image_reduce = image.ravel()
    labels_out_reduce = labels_out.ravel()

    # Get Intensity Measurements
    seqids = np.arange(1, num_label)

    batches = np.array_split(seqids, 50)

    workers = os.cpu_count() - 1
    results = np.concatenate(
        Parallel(n_jobs=workers, batch_size=max(4, int(len(batches) / workers)), pre_dispatch='8*n_jobs')(
            delayed(getIntensityMeasurements)(j, image_reduce, labels_out_reduce) for j in batches))

    centroid_fix[:, 0] = i
    centroid_fix[:, 1] = centroid[:, 1]
    centroid_fix[:, 2] = centroid[:, 0]

    voxel_counts = stat[1:, cv2.CC_STAT_AREA][:, np.newaxis]

    num_label_array = np.ones(num_label - 1, 'uint16')[:, np.newaxis] * num_label

    # TODO: Fix Label naming after everything is done

    return np.concatenate(
        (results, centroid_fix, voxel_counts, num_label_array), axis=1), label_id


@wrap_non_picklable_objects
def getIntensityMeasurementsSITK(stats: sitk.LabelIntensityStatisticsImageFilter,
                                 binaryDetectionResult3DImage: sitk.Image, seq_list):
    # stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
    #                          stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i)
    #                         for i in range(1, stats.GetNumberOfLabels() + 1)])

    # range(1, stats.GetNumberOfLabels() + 1):
    stats_list = list()
    for i in seq_list:
        centroid = binaryDetectionResult3DImage.TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[::-1]
        stats_list.append([stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
                           centroid[0], centroid[1], centroid[2], stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i])

    return np.ndarray(stats_list)
    # print('stats_array')
    # stats_dict = {'centroids':
    #     np.array(
    #         [binaryDetectionResult3DImage.
    #          TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[::-1]
    #          for i in range(1, stats.GetNumberOfLabels() + 1)]),
    #     'voxel_counts': stats_array[:, 4], 'labels': stats_array[:, -1]}

# @njit
# def getIntensityMeasurementsCV(num_label, originalImage, label_id):
#     intensity = np.empty(shape=(0, 4))
#     for j in range(1, num_label):
#         mask_img = originalImage[(label_id == j)]
#
#         intensity = np.concatenate((intensity, np.array([[np.min(mask_img), np.max(mask_img),
#                                                           np.mean(mask_img), np.sum(mask_img)]])), axis=0)