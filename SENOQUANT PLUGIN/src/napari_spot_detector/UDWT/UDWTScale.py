from qtpy.QtGui import QDoubleValidator
from qtpy.QtWidgets import QWidget, QHBoxLayout, \
    QCheckBox, QLineEdit, QLabel


class UDWTScale(QWidget):
    def __init__(self, scaleNumber: int, enabled: bool, threshold: float, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.scaleNumber = scaleNumber

        # self.scalePanel = QGroupBox()

        # self.scalePanel = QWidget()

        # self.scalePanel.setSizePolicy(QSizePolicy.Fixed)
        # self.scalePanel.setFlat(True)
        # self.scalePanel.setChecked(True)
        self.setLayout(QHBoxLayout())
        # self.scalePanel.setFlat(True)

        self.enabledCheckBox = QCheckBox("Scale " + str(scaleNumber))
        # self.enabledCheckBox.size
        self.enabledCheckBox.setChecked(enabled)
        self.layout().addWidget(self.enabledCheckBox)

        self.sensitivityTextField = QLineEdit(str(threshold))
        self.sensitivityTextField.setValidator(QDoubleValidator(
            0.0,  # bottom
            100.0,  # top
            6,  # decimals
            notation=QDoubleValidator.StandardNotation))
        self.layout().addWidget(self.sensitivityTextField)

        self.descriptionLabel = QLabel("")
        if scaleNumber == 1:
            self.descriptionLabel.setText("~1 pixel")
        if scaleNumber == 2:
            self.descriptionLabel.setText("~3 pixel")
        if scaleNumber == 3:
            self.descriptionLabel.setText("~7 pixel")
        if scaleNumber == 4:
            self.descriptionLabel.setText("~13 pixel")
        if scaleNumber == 5:
            self.descriptionLabel.setText("~25 pixel")

        self.layout().addWidget(self.descriptionLabel)

    # def getPanel(self) -> QWidget:
    #     return self.scalePanel

    def isEnabled(self) -> bool:
        return self.enabledCheckBox.isChecked()

    def getThreshold(self):
        threshold: float

        try:
            threshold = float(self.sensitivityTextField.text())

        except TypeError:
            threshold = 0

        return threshold

    def row(self):
        return {'Name': self.enabledCheckBox.text(), 'Scale_Number': self.scaleNumber,
                'Scale_Enabled': 'Enabled' if self.isEnabled() else 'Disabled',
                'Sensitivity': self.getThreshold()}
