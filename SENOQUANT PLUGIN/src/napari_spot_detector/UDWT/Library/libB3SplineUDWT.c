#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <omp.h>

#if defined(_WIN32) && defined(BUILD_DLL)
	#ifdef BUILD_DLL
		#define EXPORT __declspec(dllexport)
	#else
		#define EXPORT __declspec(dllimport)
	#endif
#else
	#define EXPORT
#endif

/**
 * filter a 2D image with the B3 spline wavelet scale kernel in the x direction when using the a trous algorithm, and swap dimensions
 *
 * @param arrayIn
 *        input array
 * @param arrayOut
 *        output array
 * @param width
 *        width of the image
 * @param height
 *        height of the image
 * @param stepS
 *        internal step
*/
//__declspec(dllexport)
EXPORT void filterAndSwap2D(float arrayIn[], float arrayOut[], int width, int height, int stepS)
{
    // B3 spline wavelet configuration
    // the convolution kernel is {1/16, 1/4, 3/8, 1/4, 1/16}
    // with some zeros values inserted between the coefficients, depending on the scale
    float w2 = ((float) 1) / 16;
    float w1 = ((float) 1) / 4;
    float w0 = ((float) 3) / 8;

//    printf("Hi\n");
//    printf("Start - %d %d %d\n", width, height, stepS);

    int w0idx;
    int w1idx1;
    int w2idx1;
    int w1idx2;
    int w2idx2;
    int arrayOutIter;

    int cntX;
    w0idx = 0;


    for (int y = 0; y < height; y++)// loop over the second dimension
    {
//        printf("filterAndSwap2D - %d: %d\n", height, y);
        // manage the left border with mirror symmetry
        arrayOutIter = 0 + y;// output pointer initialization, we swap dimensions at this point
        // w0idx = arrayIn + y*width;
        w1idx1 = w0idx + stepS - 1;
        w2idx1 = w1idx1 + stepS;
        w1idx2 = w0idx + stepS;
        w2idx2 = w1idx2 + stepS;

        cntX = 0;
        while (cntX < stepS)
        {
//            printf("Hello: %d\n", arrayOutIter);
//            printf("c: %.2f\n", arrayIn[0]);
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2]))
                    + w1 * ((arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx]);
            w1idx1--;
            w2idx1--;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter += height;
            cntX++;
        }
//        printf("Hwllo: %d", arrayOutIter);
        w1idx1++;
        while (cntX < 2 * stepS)
        {
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2]))
                    + w1 * ((arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx]);
            w1idx1++;
            w2idx1--;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter += height;
            cntX++;
        }
//        printf("Hwllo2: %d", arrayOutIter);
        w2idx1++;
        // filter the center area of the image (no border issue)
        while (cntX < width - 2 * stepS)
        {
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2]))
                    + w1 * ((arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter += height;
            cntX++;
        }
        w2idx2--;
        // manage the right border with mirror symmetry
        while (cntX < width - stepS)
        {
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2]))
                    + w1 * ((arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2++;
            w2idx2--;
            w0idx++;
            arrayOutIter += height;
            cntX++;
        }
        w1idx2--;
        while (cntX < width)
        {
            arrayOut[arrayOutIter] = w2 * ((arrayIn[w2idx1]) + (arrayIn[w2idx2]))
                    + w1 * ((arrayIn[w1idx1]) + (arrayIn[w1idx2])) + w0 * (arrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2--;
            w2idx2--;
            w0idx++;
            arrayOutIter += height;
            cntX++;
        }
    }
}

/**
 * @param arrayIn
 * @param arrayOut
 * @param width
 * @param height
 * @param depth
 * @param stepS
 */

//__declspec(dllexport)
//EXPORT void filterZdirection(float **arrayIn, float **arrayOut, int width, int height, int depth, int stepS)
EXPORT void filterZdirection(float **arrayIn, float *arrayOut, int width, int height, int depth, int stepS)
{
//    printf("Check Here\n");
    // B3 spline wavelet configuration
    // the convolution kernel is {1/16, 1/4, 3/8, 1/4, 1/16}
    // with some zeros values inserted between the coefficients, depending on the scale
    float w2 = ((float) 1) / 16;
    float w1 = ((float) 1) / 4;
    float w0 = ((float) 3) / 8;

//    printf("Check Here\n");

    double bufferArrayIn[depth]; // create a buffer for each 2D location i
    double bufferArrayOut[depth];// create an output buffer for each 2D location i
//    printf("Check Here\n");
    int i;
    for (i = 0; i < width * height; i++) // loop in 2D over the pixels of the slices, can be done in a parallel manner
    {
//        printf("Check Here56: %d\n", i);
        int z;
        for (z = 0; z < depth; z++)
        {
//            printf("Check Here16: %d\n", z);
            bufferArrayIn[z] = arrayIn[z][i];
        }

//            printf("Check Here16: %.3f\n", arrayIn[z][i]);


//            bufferArrayIn[z] = arrayIn[z*(width * height) + i];
//            printf("HEre\n");

        // then filter the buffer
        int cntZ = 0;
        int arrayOutIter = 0;
        int w0idx = 0;
        int w1idx1 = w0idx + stepS - 1;
        int w2idx1 = w1idx1 + stepS;
        int w1idx2 = w0idx + stepS;
        int w2idx2 = w1idx2 + stepS;

//        printf("Check Here89\n");



        while (cntZ < stepS)
        {
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2]))
                    + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + w0 * (bufferArrayIn[w0idx]);
            w1idx1--;
            w2idx1--;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter++;
            cntZ++;
        }
//        printf("Check Here1\n");
        w1idx1++;
        while (cntZ < 2 * stepS)
        {
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2]))
                    + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + w0 * (bufferArrayIn[w0idx]);
            w1idx1++;
            w2idx1--;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter++;
            cntZ++;
        }
//         printf("Check Her2e\n");
        w2idx1++;
        // filter the center area of the image (no border issue)
        while (cntZ < depth - 2 * stepS)
        {
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2]))
                    + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + w0 * (bufferArrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2++;
            w2idx2++;
            w0idx++;
            arrayOutIter++;
            cntZ++;
        }
//        printf("Check Here3\n");
        w2idx2--;
        // manage the right border with mirror symmetry
        while (cntZ < depth - stepS)
        {
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2]))
                    + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + w0 * (bufferArrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2++;
            w2idx2--;
            w0idx++;
            arrayOutIter++;
            cntZ++;
        }
//         printf("Check Her4e\n");
        w1idx2--;
        while (cntZ < depth)
        {
            bufferArrayOut[arrayOutIter] = w2 * ((bufferArrayIn[w2idx1]) + (bufferArrayIn[w2idx2]))
                    + w1 * ((bufferArrayIn[w1idx1]) + (bufferArrayIn[w1idx2])) + w0 * (bufferArrayIn[w0idx]);
            w1idx1++;
            w2idx1++;
            w1idx2--;
            w2idx2--;
            w0idx++;
            arrayOutIter++;
            cntZ++;
        }
//        printf("Holllla\n");
        // copy the buffer to the original structure
        for (int z = 0; z < depth; z++)
            arrayOut[z*(width * height) + i] = bufferArrayOut[z];

//        printf("hola\n");
//        for (int z = 0; z < depth; z++)
//            arrayOut[z][i] = bufferArrayOut[z];
//             bufferArrayIn[z] = arrayIn[z*(width * height) + i];
    }
}

//EXPORT
//__declspec(dllexport) void b3WaveletScales3D(float dataIn[][depth], float resArray[][numScales][depth], int width, int height, int depth, int numScales)
EXPORT void b3WaveletScales3D(float *dataIn, float *resArray, int width, int height, int depth, int numScales)
{
    int s;// scale
    int stepS;// step between non zero coefficients of the convolution kernel, depends on the scale
    int wh = width * height;

    float *currentArray3D = dataIn;// array to be filtered

    for (s = 1; s <= numScales; s++)// for each scale
    {
        float *prevArray[depth]; // array to filter in 2D, original data for the first scale
        stepS = (int) pow(2, s - 1); // compute the step between non zero coefficients of the convolution kernel = 2^(scale-1)


//        printf("Hello: %d\n", s);

        // process each slice of the stack, can be done in a parallel manner
        #pragma omp parallel
        for (int z = 0; z < depth; z++)
        {
            float *currentArray;
            prevArray[z] = (currentArray3D + z*wh); // array to filter, original data for the first scale
            currentArray = (float*) malloc(wh * sizeof(float)); // filtered array

            // convolve along the x direction and swap dimensions
            filterAndSwap2D(prevArray[z], currentArray, width, height, stepS);

//            if (s != 1 && z != 0){
//                printf("filterAndSwap2D: %.2f\n", prevArray[z][0]);
//                printf("filterAndSwap2D: %.2f\n", currentArray[0]);
//            }

//            printf("Hellodtf\n");
//            {
            prevArray[z] = currentArray; // the filtered array becomes the array to filter
            currentArray = (float*) malloc(wh * sizeof(float)); // allocate memory for the next dimension filtering (preserve original data)
//            }
            // convolve along the y direction and swap dimensions
            filterAndSwap2D(prevArray[z], currentArray, height, width, stepS); // swap size of dimensions

//            if (s != 1 && z != 0){
//                printf("filterAndSwap2D: %.2f\n", prevArray[z][0]);
//                printf("filterAndSwap2D: %.2f\n", currentArray[0]);
//            }

            prevArray[z] = currentArray;


        }
//        printf("Hellod\n");

        currentArray3D = (float*)malloc(depth * wh * sizeof(float));
//        float currentArray3Dz[depth][wh];
//        float** currentArray3Dz = (float**)malloc(depth * sizeof(float*));
//        int i;
//        for (i = 0; i < depth; i++)
//            currentArray3Dz[i] = (float*)malloc(wh * sizeof(float));
//      printf("Other Check: %.2f\n", prevArray[0][0]);

        // convolve along the z direction
        filterZdirection(prevArray, currentArray3D, width, height, depth, stepS);
//        printf("currentArray3D: %.2f\n", currentArray3D[1 * wh + 0]);
//        printf("currentArray3D: %.2f\n", currentArray3D[2 * wh + 1]);
//        printf("Depth:%d\n", depth);
//        printf("wh:%d\n", wh);

        int d;

        for (d = 0; d < depth; d++) {
            int p = 0;
//            printf("d: %d\n", (wh * d));
//            printf("s: %d\n", ((s - 1) * depth));
//            printf("p: %d\n", p);

            for (p = 0; p < wh; p++){
//                printf("p: %d\n", p);
//                float *resArrayOffset = (resArray + (s - 1));
                resArray[(depth * wh * (s - 1)) + (wh * d) + p] = currentArray3D[d * wh + p];

//                resArray[(s - 1) * depth + d * wh + p] = currentArray3D[d * wh + p];
//                resArray[(s - 1) * depth + d * wh + p] = currentArray3Dz[d][p];
            }
        }



    }

}

