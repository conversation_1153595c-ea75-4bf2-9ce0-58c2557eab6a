# __version__ = "1.1.4"

from ._reader import napari_get_reader
from ._sample_data import make_sample_data
from ._writer import write_multiple, write_single_image

# from .images.ROI import ROI

# from .UDWT.UDWTWaveletCore import UDWT<PERSON><PERSON><PERSON><PERSON>ore
#
# from .tokens.GlobalDetectionToken import GlobalDetectionToken
# from .UDWTWaveletDetector import UDWTWaveletDetector
# from .wavelets.UDWT.WaveletConfigException import WaveletConfigException
# from .UDWT.UDWTScale import UDWTScale
# from .images.Sequence import Sequence

from setuptools import setup, find_packages, version
from importlib.metadata import version

# import pkg_resources

# __version__ = version
# print(version)

# TODO: Need a check of version for Release and Build Modes
# __version__ = version("napari-spot-detector")
#
# print("Build Version:", __version__)

# __version__ = pkg_resources.get_distribution("mypackage").version
#


__all__ = (
    "napari_get_reader",
    "write_single_image",
    "write_multiple",
    "make_sample_data",
    # "UDWTScale", "UDWTWaveletCore",
    # "UDWTWaveletDetector", "GlobalDetectionToken",
    # "Sequence", "ROI",
    # "WaveletConfigException"
)

from napari.utils.notifications import show_info, show_error

def show_hello_message():
    show_info('Hello, world!')

import yaml

# Doesn't Work
def update_napari_title():
    show_info('Hello, world!')
    with open('napari.yaml', 'r') as f:
        config = yaml.safe_load(f)
    config['widgets']['display_name'] = config['window_title'] #+ " " + __version__
    # ['window_title']
    with open('napari.yaml', 'w') as f:
        yaml.safe_dump(config, f)

# update_napari_title()


# setup(
#     package_data={
#         # If any package contains *.txt or *.rst files, include them:
#         "": ["plugins/fab/detector/*.png"],
#         #, "*.rst"],
#         # And include any *.msg files found in the "hello" package, too:
#         # "hello": ["*.msg"],
#     }
# )
