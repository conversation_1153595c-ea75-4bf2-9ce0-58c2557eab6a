import os
import traceback
from typing import Dict, Final, Any

import napari
import xarray
import xm<PERSON>odict
from aicsimageio import <PERSON>CS<PERSON>mage
from aicsimageio.dimensions import DimensionNames
from dicttoxml import dicttoxml
from qtpy.QtWidgets import QWidget, QVBoxLayout, QFileDialog, QTabWidget, QPushButton, QLineEdit, QComboBox

from .OutputWidget import FileSelector
from ..extensions.napari_senoquant.src.napari_senoquant.NucleiWidget import NucleiWidget
from ..extensions.napari_senoquant.src.napari_senoquant._widget import Marker1Widget, ProcessWidget


class Profile(QWidget):
    TAB_NAME: Final = "Settings"

    def __init__(self, viewer: napari.Viewer, tabWidget: QTabWidget):
        super().__init__()

        self.metadata: Dict = {}

        self.tabWidget = tabWidget
        self.viewer = viewer

        self.layout = QVBoxLayout()

        self.profile_widget = FileSelector(self._load_profile, group_name="Load Settings", label_name="File",
                                           filter_names="XML (*.xml)")
        self.profile_save_widget = FileSelector(self._set_xml_save_path, group_name="Save Settings", label_name="File",
                                                filter_names="XML (*.xml)")  # TODO: Autosave if selected w/ select checkbox or button to autofill with name of image from Nuclei combobox

        self.profile_save_widget_btn = QPushButton("Save")
        self.profile_save_widget_btn.clicked.connect(self._save)
        self.profile_save_widget.layout().addWidget(self.profile_save_widget_btn)

        self.layout.addWidget(self.profile_widget)
        self.layout.addWidget(self.profile_save_widget)

        self.setLayout(self.layout)

    def _set_xml_save_path(self, filter_names):  # "XML (*.xml)"
        file_name, _ = QFileDialog.getSaveFileName(self, "Select a File", directory="",
                                                   filter=filter_names)
        if file_name and file_name != '':
            path = os.path.realpath(file_name)
            self.profile_save_widget.dir_name_edit.setText(str(path))

        # if self._save in self.viewer.layers.events.inserted:

        # self.viewer.layers.events.inserted.disconnect(self._save)
        # self.viewer.layers.events.inserted.connect(self._save)

        # if self._save in self.viewer.layers.events.removed:
        #     self.viewer.layers.events.removed.disconnect(self._save)
        # self.viewer.layers.events.removed.connect(self._save)
        #
        # if self._save in self.viewer.layers.events.moved:
        #     self.viewer.layers.events.moved.disconnect(self._save)
        # self.viewer.layers.events.moved.connect(self._save)
        #
        # if self._save in self.viewer.layers.events.reordered:
        #     self.viewer.layers.events.reordered.disconnect(self._save)
        # self.viewer.layers.events.reordered.connect(self._save)

    def _save(self):
        self.profile_save_widget_btn.setEnabled(False)
        try:
            # Create metadata dictionary and save current state w/ autosave for every new layer
            for i in range(self.tabWidget.count()):
                if self.tabWidget.tabText(i) == NucleiWidget.TAB_NAME:
                    nuclei_widget: NucleiWidget = self.tabWidget.widget(i)
                    if hasattr(nuclei_widget, 'NucImage'):
                        self.metadata['NucImage'] = nuclei_widget.NucImage.currentText()  # TODO: adding image
                    if hasattr(nuclei_widget, 'ModelName'):
                        self.metadata['ModelName'] = nuclei_widget.ModelName.currentText()

                    # TODO: Information for image data like series and channel
                    # img = Sequence(self.metadata['NucImage'], viewer=self.viewer)
                    # self.metadata['NucImage_series'] = img.current_scene
                    # self.metadata['NucImage_series_index'] = img.current_scene_index
                    # self.metadata['NucImage_channel'] = img.channel
                    # self.metadata['NucImage_channel_index'] = img.index
                    # self.metadata['NucImage_file_path'] = img.path

                    if hasattr(nuclei_widget, 'modelScale'):
                        self.metadata['modelScale'] = nuclei_widget.modelScale.text()
                # if self.tabWidget.tabText(i) == UDWTWaveletDetector.TAB_NAME:
                #     udwt_widget: UDWTWaveletDetector = self.tabWidget.widget(i)
                #     m = udwt_widget.create_metadata()
                #     self.metadata.update(m)
                if self.tabWidget.tabText(i) == Marker1Widget.TAB_NAME:
                    marker_widget: Marker1Widget = self.tabWidget.widget(i)
                    if hasattr(marker_widget, 'Marker1NucMask'):
                        self.metadata['Marker1NucMask'] = marker_widget.Marker1NucMask.currentText()
                    if hasattr(marker_widget, 'DilationSizeSlider'):
                        self.metadata['DilationSizeSlider'] = marker_widget.DilationSizeSlider.value()
                    if hasattr(marker_widget, 'CytoImage'):
                        self.metadata['CytoImage'] = marker_widget.CytoImage.currentText()
                    # if isinstance(self.viewer.layers[self.metadata['CytoImage']], Image):
                    #     img = Sequence(self.metadata['CytoImage'], viewer=self.viewer)
                    #     self.metadata['CytoImage_series'] = img.current_scene
                    #     self.metadata['CytoImage_series_index'] = img.current_scene_index
                    #     self.metadata['CytoImage_channel'] = img.channel
                    #     self.metadata['CytoImage_channel_index'] = img.index
                    #     self.metadata['CytoImage_file_path'] = img.path
                    # else:
                    #     self.metadata['CytoImage_series'] = 0
                    #     self.metadata['CytoImage_series_index'] = 0
                    #     self.metadata['CytoImage_channel'] = 0
                    #     self.metadata['CytoImage_channel_index'] = 0
                    #     self.metadata['CytoImage_file_path'] = 0

                    # TODO: Information for Label data (don't know if labels are saved)
                if self.tabWidget.tabText(i) == ProcessWidget.TAB_NAME:
                    process_widget: ProcessWidget = self.tabWidget.widget(i)
                    if hasattr(process_widget, 'NucLabels'):
                        self.metadata['NucLabels'] = process_widget.NucLabels.currentText()
                    if hasattr(process_widget, 'TelImage'):
                        self.metadata['TelImage'] = process_widget.TelImage.currentText()

                    # img = Sequence(self.metadata['TelImage'], viewer=self.viewer)
                    # self.metadata['TelImage_series'] = img.current_scene
                    # self.metadata['TelImage_series_index'] = img.current_scene_index
                    # self.metadata['TelImage_channel'] = img.channel
                    # self.metadata['TelImage_channel_index'] = img.index
                    # self.metadata['TelImage_file_path'] = img.path

                    if hasattr(process_widget, 'TelFilterSizeSlider'):
                        self.metadata['TelFilterSizeSlider'] = process_widget.TelFilterSizeSlider.value()

                    if hasattr(process_widget, 'TelThreshSlider'):
                        self.metadata['TelThreshSlider'] = process_widget.TelThreshSlider.value()
                    if hasattr(process_widget, 'TelLabels'):
                        self.metadata['TelLabels'] = process_widget.TelLabels.currentText()
                    if hasattr(process_widget, 'FociImage'):
                        self.metadata['FociImage'] = process_widget.FociImage.currentText()

                    # img = Sequence(self.metadata['FociImage'], viewer=self.viewer)
                    # self.metadata['FociImage_series'] = img.current_scene
                    # self.metadata['FociImage_series_index'] = img.current_scene_index
                    # self.metadata['FociImage_channel'] = img.channel
                    # self.metadata['FociImage_channel_index'] = img.index
                    # self.metadata['FociImage_file_path'] = img.path

                    if hasattr(process_widget, 'FocThreshSlider'):
                        self.metadata['FocThreshSlider'] = process_widget.FocThreshSlider.value()
                    if hasattr(process_widget, 'FocMinSizeSlider'):
                        self.metadata['FocMinSizeSlider'] = process_widget.FocMinSizeSlider.value()
                    if hasattr(process_widget, 'FocMaxSizeSlider'):
                        self.metadata['FocMaxSizeSlider'] = process_widget.FocMaxSizeSlider.value()
                    if hasattr(process_widget, 'FociLabels'):
                        self.metadata['FociLabels'] = process_widget.FociLabels.currentText()
                    if hasattr(process_widget, 'markerList'):
                        # for m in process_widget.markerList:
                        #     process_widget.addMarkerPanel()
                        self.metadata['markerList'] = [{'nameWidget': m['nameWidget'].text(),
                                                        'imageWidget': m['imageWidget'].currentText(),
                                                        'labelsWidget': m['labelsWidget'].currentText(),
                                                        'markerNumber': m['markerNumber']} for m in
                                                       process_widget.markerList]

            xml = dicttoxml(self.metadata)
            xml_decode = xml.decode('utf-8')

            xmlfile = open(self.profile_save_widget.dir_name_edit.text(), "w")
            xmlfile.write(xml_decode)
            xmlfile.close()
            # self.profile_save_widget_btn.setEnabled(True)
        except (FileNotFoundError, FileExistsError) as e:
            print(e)
            traceback.print_exc()
            xmlfile.close()
            # self.profile_save_widget_btn.setEnabled(True)
        except Exception:
            traceback.print_exc()
            # self.profile_save_widget_btn.setEnabled(True)

        self.profile_save_widget_btn.setEnabled(True)


    def reload_image(self, filename, scene, channel, layer_name, scale):
        img = AICSImage(filename)
        img.set_scene(scene)
        img_channel: xarray.DataArray = img.xarray_data.squeeze()
        if DimensionNames.Channel in img_channel.dims:
            img_channel = img_channel.sel(C=channel)
        meta: Dict[str, Any] = dict()
        meta['name'] = layer_name
        meta['scale'] = scale

        # Apply all other metadata
        img_meta = {"aicsimage": img, "raw_image_metadata": img.metadata}
        try:
            img_meta["ome_types"] = img.ome_metadata
        except Exception:
            pass

        meta["metadata"] = img_meta

        self.viewer.add_image(img_channel, **meta)

    def _load_profile(self, filter_names):
        file_name, _ = QFileDialog.getOpenFileName(self, "Select a File", directory="",
                                                   filter=filter_names)  # "XML (*.xml)"
        if file_name and file_name != '':
            try:
                path = os.path.realpath(file_name)
                with open(path) as fd:
                    root = xmltodict.parse(fd.read())

                    doc = root['root']

                    for i in range(self.tabWidget.count()):
                        if self.tabWidget.tabText(i) == NucleiWidget.TAB_NAME:
                            nuclei_widget: NucleiWidget = self.tabWidget.widget(i)
                            # nuclei_widget.NucImage.setCurrentText(doc['NucImage']['#text'])

                            if hasattr(nuclei_widget, 'ModelName'):
                                nuclei_widget.ModelName.setCurrentText(doc.get('ModelName', {'#text': ""}).get('#text', ""))

                            if hasattr(nuclei_widget, 'modelScale'):
                                nuclei_widget.modelScale.setText(doc.get('modelScale', {'#text': ""}).get('#text', "1"))

                        if self.tabWidget.tabText(i) == Marker1Widget.TAB_NAME:
                            marker_widget: Marker1Widget = self.tabWidget.widget(i)
                            # marker_widget.Marker1NucMask.setCurrentText(
                            #     doc.get('Marker1NucMask', {'#text': ""})['#text'])
                            if hasattr(marker_widget, 'DilationSizeSlider'):
                                marker_widget.DilationSizeSlider.setValue(
                                    int(doc.get('DilationSizeSlider', {"#text": "11"}).get('#text', "11")))
                            # marker_widget.CytoImage.setCurrentText(doc['CytoImage']['#text']))
                            # TODO: Need to fix
                            # marker_widget.markerList = [{"nameWidget": i.get('markerName', {'#item': ''})['#text'],
                            #                              'imageWidget': i.get('markerImage', {'#item': ''})['#text'],
                            #                              'labelsWidget': i.get('markerLabel', {'#item': ''})['#text'],
                            #                              'markerNumber': i.get('markerNumber', {'#item': ''})['#text']}
                            #                             for i in doc.get('markerList', {'item': []})['item']]

                            # marker_widget.markerList = [{'nameWidget': QLineEdit(i.get('nameWidget', {'#item': ''})['#text']),
                            #                              'imageWidget': '','labelsWidget': '','markerNumber':int(i.get('markerNumber', {'#item': ''})['#text'])} for i in doc.get('markerList', {'item': []})['item']]

                        if self.tabWidget.tabText(i) == ProcessWidget.TAB_NAME:
                            process_widget: ProcessWidget = self.tabWidget.widget(i)

                            # process_widget.NucLabels.setCurrentText(doc['NucLabels']['#text'])
                            # process_widget.TelImage.setCurrentText(doc['TelImage']['#text'])
                            # if hasattr(process_widget, 'NucLabels'):
                            #     self.metadata['NucLabels'] = process_widget.NucLabels.currentText()
                            # if hasattr(process_widget, 'TelImage'):


                            #     self.metadata['TelFilterSizeSlider'] = process_widget.TelFilterSizeSlider.value()

                            #     self.metadata['TelThreshSlider'] = process_widget.TelThreshSlider.value()
                            # if hasattr(process_widget, 'TelLabels'):
                            #     self.metadata['TelLabels'] = process_widget.TelLabels.currentText()
                            # if hasattr(process_widget, 'FociImage'):


                            #     self.metadata['FocThreshSlider'] = process_widget.FocThreshSlider.value()

                            #     self.metadata['FocMinSizeSlider'] = process_widget.FocMinSizeSlider.value()

                            #     self.metadata['FocMaxSizeSlider'] = process_widget.FocMaxSizeSlider.value()
                            # if hasattr(process_widget, 'FociLabels'):
                            #     self.metadata['FociLabels'] = process_widget.FociLabels.currentText()
                            # if hasattr(process_widget, 'markerList'):

                            if hasattr(process_widget, 'TelFilterSizeSlider'):
                                process_widget.TelFilterSizeSlider.setValue(
                                    int(doc.get('TelFilterSizeSlider', {"#text": "5"}).get('#text', "5")))
                            if hasattr(process_widget, 'TelThreshSlider'):
                                process_widget.TelThreshSlider.setValue(
                                    int(doc.get('TelThreshSlider', {'#text': '30'}).get('#text', "30")))

                            # process_widget.TelLabels.setCurrentText(doc['TelLabels']['#text'])
                            # process_widget.FociImage.setCurrentText(doc['FociImage']['#text'])
                            if hasattr(process_widget, 'FocThreshSlider'):
                                process_widget.FocThreshSlider.setValue(int(doc.get('FocThreshSlider',
                                                                                    {'#text': '30'}).get('#text', "30")))
                            if hasattr(process_widget, 'FocMinSizeSlider'):
                                process_widget.FocMinSizeSlider.setValue(int(doc.get('FocMinSizeSlider',
                                                                                     {'#text': '10'}).get('#text', "10")))
                            if hasattr(process_widget, 'FocMaxSizeSlider'):
                                process_widget.FocMaxSizeSlider.setValue(int(doc.get('FocMaxSizeSlider',
                                                                                     {'#text': '100'}).get('#text', "100")))


                            for i in [doc.get('markerList', {'item': []})['item']]:
                                try:
                                    process_widget.addMarkerPanel()
                                    markerDict = process_widget.markerList[-1]

                                    markerName: QLineEdit = markerDict['nameWidget']
                                    markerName.setText(i.get('nameWidget', {'#text': ''}).get('#text', ""))

                                    markerImage: QComboBox = markerDict['imageWidget']
                                    markerImage.setCurrentText(i.get('imageWidget', {'#text': ''}).get('#text', ""))

                                    markerLabel: QComboBox = markerDict['labelsWidget']
                                    markerLabel.setCurrentText(i.get('labelsWidget', {'#item': ''}).get('#text', ""))

                                    markerDict['markerNumber'] = int(i.get('markerNumber', {'#text': ''}).get('#text', ""))


                                except (KeyError, AttributeError) as e:
                                    print(e)
                                    traceback.print_exc()



                            # marker_widget.markerList = [
                            #     {'nameWidget': QLineEdit(i.get('nameWidget', {'#item': ''})['#text']),
                            #      'imageWidget': '', 'labelsWidget': '',
                            #      'markerNumber': int(i.get('markerNumber', {'#item': ''})['#text'])} for i in
                            #     doc.get('markerList', {'item': []})['item']]

                            # process_widget.FociLabels.setCurrentText(doc['FociLabels']['#text'])

            except (KeyError) as e:
                print(e)
                traceback.print_exc()
            except (FileNotFoundError, FileExistsError) as e:
                print(e)
                traceback.print_exc()
            except ValueError as e:
                print(e)
                traceback.print_exc()
            except TypeError as e:
                print(e)
                traceback.print_exc()
