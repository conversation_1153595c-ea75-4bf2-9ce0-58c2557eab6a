# You may use this file to customize how your plugin page appears
# on the napari hub: https://www.napari-hub.org/
# See their wiki for details https://github.com/chanzuckerberg/napari-hub/wiki

# Please note that this file should only be used IN ADDITION to entering
# metadata fields (such as summary, description, authors, and various URLS)
# in your standard python package metadata (e.g. setup.cfg, setup.py, or
# pyproject.toml), when you would like those fields to be displayed
# differently on the hub than in the napari application.
