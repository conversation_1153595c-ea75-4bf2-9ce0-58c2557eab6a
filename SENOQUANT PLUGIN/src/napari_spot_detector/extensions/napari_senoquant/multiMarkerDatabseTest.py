# -*- coding: utf-8 -*-
"""
Created on Tue Jun 20 10:17:03 2023

@author: jjc
"""

import numpy as np
from typing import TYPE_CHECKING

# from magicgui import magic_factory
# from qtpy.QtWidgets import QHBoxLayout, QPushButton, QWidget, QListWidget,QComboBox,QLabel,QVBoxLayout,QRadioButton
# from qtpy.QtWidgets import QSlider
# from skimage.measure import label, regionprops, regionprops_table
# from skimage.filters import rank,unsharp_mask
# from skimage.morphology import disk,ball,dilation

# from IPython.display import display
import pandas as pd
# import numpy as np
from sqlalchemy import create_engine,text
import os
# import xarray

# for root, dirs, files in os.walk(os.environ['USERPROFILE']):
#     if 'napari-senoquant' in dirs:
#         MODELS_DIR = os.path.join(root,'napari-senoquant','models')
        # print(MODELS_DIR)

# os.chdir('C:\\Users\\<USER>\\Python\\napari-senoquant')
import platform
node = platform.uname()[1] 

# import datetime
# import time
if TYPE_CHECKING:
    import napari
# from napari.utils.progress import progrange
# from csbdeep.utils import Path, normalize
# import stardist
# from stardist import random_label_cmap
# from stardist.models import StarDist3D,StarDist2D


#%% Get Histogram
markerImage = np.random.randint(0, high=255, size=(1000,1000), dtype=int)
histogram, bin_edges = np.histogram(markerImage, bins=256, range=(0,255))
#%% Create Engine
if node ==  'R5318238':
    engine = create_engine('mysql+pymysql://Senoquant:<EMAIL>/Senoquant_test')
else:
    engine = create_engine('mysql+pymysql://Senoquant:<EMAIL>/Senoquant')
    

#%% CReate markerParamsDictFrame

markerParamsDict  = {}
markerParamsDict['AnalysisID'] = 33
markerParamsDict['filePath'] = 'file'
markerParamsDict['channel'] = 'Channel0'
markerParamsDict['scaleX'] = 1.1
markerParamsDict['scaleY'] = 1.2
markerParamsDict['scaleZ'] = 1.3
markerParamsDict['params'] = str({'source':'napari-senoquant','filePath':'filePath','channel':'channel0','labels':'NucleiLabelsx5Disk'})
markerParamsDict['histogram'] = [histogram]

#%% get parameters from metadata of markerLabels

try: # get metadata from nuclei mask
    markerParamsDict['params'] = str(self.viewer.layers[self.Marker1Labels.currentText()].metadata)
except Exception as e:
    print('nucleiParams',e)
    pass    


#%% Save to db
markerParamsFrame = pd.DataFrame(markerParamsDict)
markerParamsFrame.to_sql(name='markerParams', con=engine, if_exists = 'append', index=False)















