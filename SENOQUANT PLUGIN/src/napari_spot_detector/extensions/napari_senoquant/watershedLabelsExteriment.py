# -*- coding: utf-8 -*-
"""
Created on Fri Jul 28 09:11:55 2023

@author: jjc
"""

from skimage.measure import label, regionprops_table
from skimage.filters import unsharp_mask
from skimage.morphology import disk,ball,dilation

from IPython.display import display
import pandas as pd
import numpy as np
from sqlalchemy import create_engine,text
import os
import xarray
from superqt.utils import thread_worker
from napari_segment_blobs_and_things_with_membranes import voronoi_otsu_labeling, \
                                                           seeded_watershed, \
                                                           seeded_watershed_with_mask
                                                           #%
                                                           
#%%
IMAGE_DIR = 'D:\\BiomarkerAnalysis\\FOR CHICHI\\example cell boundaries nucleus'
from skimage import io
nucImagePath = os.path.join(IMAGE_DIR,'c1nucleus1.tif')
nucImage = io.imread(nucImagePath)
nucLabelPath = os.path.join(IMAGE_DIR,'c1nucleus1Labels.tif')
nucLabels = io.imread(nucLabelPath)
cytoImagePath = os.path.join(IMAGE_DIR,'c2membrane1.tif')
cytoImage = io.imread(cytoImagePath)
#%%
nucStats = regionprops_table(nucLabels,intensity_image=nucImage, properties=('centroid','area','Label','MeanIntensity'))
nucPropsTable = pd.DataFrame(nucStats)
#%%
cytoStats = regionprops_table(nucLabels,intensity_image=cytoImage, properties=('centroid','area','Label','MeanIntensity'))
cytoPropsTable = pd.DataFrame(cytoStats)
#%%
cytoLabels = np.array(seeded_watershed(cytoImage, nucLabels))
#%%
cytomask = cytoImage > 10
nucMask = nucLabels > 0
mask = (cytomask + nucMask).astype(np.uint8)
cellLabels = np.array(seeded_watershed_with_mask(cytoImage, nucLabels, mask))
cytoLabels = cellLabels - nucLabels

#%%
cytoStats = regionprops_table(cellLabels,intensity_image=cytoImage, properties=('centroid','area','Label','MeanIntensity'))
cytoPropsTable = pd.DataFrame(cytoStats)
