from qtpy.QtWidgets import Q<PERSON>roup<PERSON>ox, QVBoxLayout, QGridLayout, \
    QPushButton, QLineEdit, QLabel, QFileDialog, QComboBox, QCheckBox

import os


class FileSelector(QGroupBox):
    def __init__(self, call, group_name='Enable Automatic', button_name='Browse', label_name='Directory:',
                 checked=True, *args):
        super(FileSelector, self).__init__(*args)

        # self.setTitle(group_name)
        # self.setCheckable(True)
        # self.setChecked(checked)
        self.setLayout(QGridLayout())

        # directory selection
        dir_btn = QPushButton(button_name)
        dir_btn.clicked.connect(call)
        self.dir_name_edit = QLineEdit()  # TODO: Have a default path

        self.layout().addWidget(QLabel(label_name), 1, 0)
        self.layout().addWidget(self.dir_name_edit, 1, 1)
        self.layout().addWidget(dir_btn, 1, 2)


class OutputWidget(QGroupBox):
    def __init__(self, input_box: QComboBox, *args):
        super(OutputWidget, self).__init__(*args)

        # InputBox
        self.input_box: QComboBox = input_box

        # Output
        self.setTitle('Output')
        # self.output_widget = QGroupBox('Output')
        # self.output_widget.setLayout(QVBoxLayout())
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # self.layout = QGroupBox('Excel output settings:')
        # self.layout.setLayout(QVBoxLayout())

        # self.excel_output_settings.layout().addWidget(self.excel_output_settings)
        # self.layout.addWidget(self.output_widget)

        # self.automatic_xls_file_naming = QGroupBox('Automatic XLSX file naming')
        # self.automatic_xls_file_naming.setCheckable(True)
        # self.automatic_xls_file_naming.setChecked(True)
        # self.automatic_xls_file_naming.setLayout(QVBoxLayout())
        # self.directorySelector = FileSelector(self.open_dir_dialog, 
        # button_name='Browse',
        # label_name='Directory:', checked=True)
        # self.automatic_xls_file_naming.layout().addWidget(self.automatic_xls_file_naming_file_dialog)
        # self.layout.addWidget(self.directorySelector)

        # self.layout.layout().addWidget(self.automatic_xls_file_naming)

        # self.fileSelector = FileSelector(self.open_file_dialog,

        # button_name='Browse', label_name='File:',
        # checked=False)
        # self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)
        # self.automatic_xls_file_naming_file_dialog_append = FileSelector(self.open_file_dialog,
        #                                                                  group_name='Append data to existing files',
        #                                                                  button_name='Browse', label_name='File:',
        #                                                                  checked=False)
        # # self.automatic_xls_file_naming.layout().addWidget(self.automatic_xls_file_naming_file_dialog_append)

        # self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)
        # self.layout.addWidget(self.fileSelector)

        # self.textDescription = QLabel("The XLS file will be saved in the 'save' folder of the original image. The "
        #                               "file name will be 'originalfilename.xls'. If an XLS file already exists, "
        #                               "it will be replaced.")
        self.file_naming = FileSelector(self.find_save_file_dialog, group_name='Choose xlsx File',
                                        button_name='Browse', label_name='File:', checked=False)
        self.file_naming.setEnabled(False)
        self.layout.addWidget(self.file_naming)

        # Output Append
        self.file_dialog_append = FileSelector(self.open_file_dialog,
                                               group_name='Append data to existing files',
                                               button_name='Browse', label_name='File:',
                                               checked=False)
        self.file_dialog_append.setCheckable(True)
        self.file_dialog_append.setChecked(False)
        self.file_dialog_append.setEnabled(False)
        self.file_dialog_append.setTitle('Append data to existing files')
        self.layout.addWidget(self.file_dialog_append)

        self.enable_save_images = QCheckBox()
        self.enable_save_images.setToolTip(
            "If enabled, nuclei image and labels are stored as .tif files in the spreadsheet directory."
        )
        self.enable_save_images.setText("Enable save nuclei labels")
        self.enable_save_images.setChecked(False)
        self.layout.addWidget(self.enable_save_images)

        # self.layout.addWidget(self.excel_output_settings)
        self.saveButton = QPushButton("Save")
        self.saveButton.clicked.connect(self.save)
        self.saveButton.setEnabled(False)
        self.layout.addWidget(self.saveButton)

    # def open_dir_dialog(self):
    #     dir_name = QFileDialog.getExistingDirectory(self, "Select a Directory")
    #     if dir_name:
    #         # import re
    #         # pattern = '[-:._\\s]+'
    #         # file_name = re.sub(pattern, "_", self.input_box.currentText())
    #         path = os.path.realpath(dir_name)
    #         # self.directorySelector.dir_name_edit.setText(os.path.join(str(path),file_name + ".xlsx"))
    #         self.directorySelector.dir_name_edit.setText(os.path.join(str(path)))
    #         self.dirname = str(path)

    def open_file_dialog(self):
        # os.path.join(self.dir_name, self.file)
        file_name, _ = QFileDialog.getOpenFileName(self, "Select a File", directory=self.dir_name,
                                                   filter="Excel (*.xlsx)")
        if file_name and file_name != '':
            # path = Path(dir_name)
            # path = os.path.realpath(file_name)
            # self.fileSelector.dir_name_edit.setText(str(path))
            # directorySelector
            self.file_dialog_append.dir_name_edit.setText(os.path.join(self.dir_name, file_name))

        # self.dir_name = ""
        # self.file = ""

    def find_save_file_dialog(self):
        file_name, _ = QFileDialog.getSaveFileName(self, "Select a File",
                                                   directory=os.path.join(self.dir_name, self.file),
                                                   filter="Excel (*.xlsx)")

        if file_name and file_name != '':
            path = os.path.realpath(file_name)
            self.file_naming.dir_name_edit.setText(str(path))
            self.file = os.path.basename(path)
            self.dir_name = os.path.dirname(path)
            # self.xml_file_naming.dir_name_edit.setText(str(path))

    def save_file_dialog(self):
        file_name = os.path.join(self.dir_name, self.file)
        # file_name, _ = QFileDialog.getSaveFileName(self, "Select a File", directory=os.path.join(self.dir_name,self.file),filter="Excel (*.xlsx)")
        # print('Dialog returned',file_name)
        if file_name and file_name != '':
            # path = Path(dir_name)
            path = os.path.realpath(file_name)
            self.file_naming.dir_name_edit.setText(str(path))
            # self.saveProcess()
            # self.saveButton.setEnabled(False)
            self.saveProcess()
            self.saveButton.setEnabled(False)
            self.file_naming.setEnabled(False)
            self.file_dialog_append.setEnabled(False)

    def save(self):
        # TODO: Check the issue with append
        # print('Initiate save to',self.file_naming.dir_name_edit.text())
        # self.saveProcess()
        self.save_file_dialog()

    def set_save_process(self, process):
        self.saveProcess = process
        # print('set saveProcess to ',process)
