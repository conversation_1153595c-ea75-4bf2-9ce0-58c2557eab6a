import napari
from napari_roi import <PERSON><PERSON><PERSON><PERSON><PERSON>, ROIWidget
from napari_roi.qt import R<PERSON><PERSON>ayerAccessor
from qtpy.QtWidgets import Q<PERSON>abel, QComboBox, QGroupBox, QVBoxLayout, QFormLayout, QHBoxLayout


class ROIGroupWidget(QGroupBox):
    def __init__(self, viewer: napari.Viewer = napari.current_viewer()):
        super().__init__()

        self.viewer: napari.Viewer = viewer

        # List of ROI Labels
        # self.roiPanel = QGroupBox(title="List of Region of Interest")
        self.setTitle("List of Region of Interest")
        self.setLayout(QVBoxLayout())

        # Adding _roi_widget to roi group
        ROILayerAccessor.DEFAULT_ROI_ORIGIN = ROIOrigin.TOP_LEFT

        self.roiWidget: ROIWidget = ROIWidget(self.viewer)
        self.roiWidget._add_widget.setDisabled(True)
        self.roiWidget._add_widget.setHidden(True)
        self.roiWidget._save_widget.setDisabled(True)
        self.roiWidget._save_widget.setHidden(True)

        # Hide X/Y origin
        label_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.LabelRole)
        field_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.FieldRole)

        # Hide the items
        if label_item is not None:
            label_item.widget().setHidden(True)
        if field_item is not None:
            field_item.widget().setHidden(True)

        self.layout().addWidget(self.roiWidget)

