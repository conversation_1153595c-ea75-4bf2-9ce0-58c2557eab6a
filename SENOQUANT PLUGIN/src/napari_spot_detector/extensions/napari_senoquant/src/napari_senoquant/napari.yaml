name: napari_senoquant
display_name: Senoquant
contributions:
  commands:
    - id: napari_senoquant.get_reader
      python_name: napari_senoquant._reader:napari_get_reader
      title: Open data with Senoquant
    - id: napari_senoquant.write_multiple
      python_name: napari_senoquant._writer:write_multiple
      title: Save multi-layer data with Senoquant
    - id: napari_senoquant.write_single_image
      python_name: napari_senoquant._writer:write_single_image
      title: Save image data with Senoquant
    - id: napari_senoquant.make_sample_data
      python_name: napari_senoquant._sample_data:make_sample_data
      title: Load sample data from Senoquant
    # - id: napari_senoquant.make_qwidget
      # python_name: napari_senoquant._widget:ExampleQWidget
      # title: Make example QWidget
    - id: napari_senoquant.make_pwidget
      python_name: napari_senoquant._widget:ProcessWidget
      title: Make Process Widget
    # - id: napari_senoquant.make_twidget
      # python_name: napari_senoquant._widget:TelomereWidget
      # title: Make Telomere Widget
    # - id: napari_senoquant.make_fwidget
      # python_name: napari_senoquant._widget:FociWidget
      # title: Make Foci Widget

    - id: napari-senoquant.make_cmwidget
      python_name: napari_senoquant._widget:Marker1Widget
      title: CytoplasmicMarkerWidget
    # - id: napari-senoquant.make_magic_widget
      # python_name: napari_senoquant._widget:example_magic_widget
      # title: Make example magic widget
    # - id: napari_senoquant.make_func_widget
      # python_name: napari_senoquant._widget:example_function_widget
      # title: Make example function widget
    # - id: napari_senoquant.make_label_layer_selector
      # python_name: napari_senoquant._widget:label_layer_selector
      # title: Make label layer selection widget
  readers:
    - command: napari_senoquant.get_reader
      accepts_directories: false
      filename_patterns: ['*.npy']
  writers:
    - command: napari_senoquant.write_multiple
      layer_types: ['image*','labels*']
      filename_extensions: []
    - command: napari_senoquant.write_single_image
      layer_types: ['image']
      filename_extensions: ['.npy']
  sample_data:
    - command: napari_senoquant.make_sample_data
      display_name: Senoquant
      key: unique_id.1
  widgets:
    # - command: napari_senoquant.make_qwidget
      # display_name: Example QWidget
    # - command: napari_senoquant.make_twidget
      # display_name: Find Telomeres
    # - command: napari_senoquant.make_fwidget
      # display_name: Find Foci
    - command: napari_senoquant.make_pwidget
      display_name: Process

    - command: napari-senoquant.make_cmwidget
      display_name: Cytoplasmic Marker Labels
    # - command: napari-senoquant.make_label_layer_selector
      # display_name: Process Widget
    # - command: napari_senoquant.make_label_layer_selector
      # display_name: Label Selector Widget
    # - command: napari_senoquant.make_magic_widget
      # display_name: Example Magic Widget
    # - command: napari_senoquant.make_func_widget
      # autogenerate: true
      # display_name: Example Function Widget
