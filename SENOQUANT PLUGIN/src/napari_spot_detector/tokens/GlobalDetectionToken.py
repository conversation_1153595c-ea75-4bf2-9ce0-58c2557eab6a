from typing import List, Set, Dict

import numpy as np

from ..images.Sequence import Sequence
from ..images.DetectionSpot import DetectionSpot

class GlobalDetectionToken:
    inputSequence: Sequence

    stats: Dict

    labels: np.ndarray

    inputComputationSequence: Sequence

    roiArrayList: List
    roiSaver: Sequence

    detectionResult: DetectionSpot

    '''
    Result of detection by ROI
    filled by the ROI Processor
    '''
    roi2detection: Set