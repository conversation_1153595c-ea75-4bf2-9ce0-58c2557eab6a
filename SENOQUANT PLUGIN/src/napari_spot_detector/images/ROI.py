import numpy as np
from napari.layers import Shapes

from napari_roi.qt import ROILayerAccessor


class ROI(ROILayerAccessor):
    roi: np.ndarray

    # def __init__(self, vertices: List[Tuple[int, int]] = None,
    #              ROIData: np.ndarray = None):  # TODO: Might Need Fix Datatype of Label vs numpy array
    #     self.vertices: List[Tuple[int, int]] = vertices
    #     self.ROIData: np.ndarray = ROIData
    #
    #     # TODO: Look into this for non complete bounds
    #     if self.ROIData is None:
    #         self.ROIData = np.ones(shape=(1, 1, self.vertices[1][1], self.vertices[1][0]), dtype=bool) # TZYX

    # def __init__(self, roi: Labels):
    #     self.ROIData = roi

    def __init__(self, layer: Shapes):
        super(ROI, self).__init__(layer=layer)

    def getBooleanMask2D(self, zSize: int, t: int, c: int, inclusive: bool, bounds):
        data: np.ndarray = np.zeros(shape=(zSize, int(bounds[-1].height), int(bounds[-1].width)), dtype=bool)
        print(bounds[0])

        # TODO: Fix t and c and inclusive parameter
        # TODO: Check for out of bound issues
        # TODO: Prevent Shape change after being registered and add names
        # TODO: Look into cropping data internally
        # b: np.ndarray
        # for b in bounds.data():
        #     data[np.arrange(np.amax(b[0, :])), np.arrange(np.amax(b[:, 0]))] = 1
        for roi in self:
            print(roi.y)
            print(roi.height)
            print(roi.x)
            print(roi.width)
            # data[:, 567:1923, 281:1853] = True
            data[:, int(roi.y):int(roi.y + roi.height) + 1, int(roi.x):int(roi.x + roi.width) + 1] = True
            # data[np.ix_(np.arange(zSize), np.arange(int(roi.y), int(roi.height)),
            #                np.arange(int(roi.x), int(roi.width)))] = True

        # if c == -1:
        #     data = self.ROIData[t] # TZYX
        # elif len(self.ROIData.shape) == 5:
        #     data = self.ROIData[t][c]  # TCZYX
        #
        # if self.ROIData.shape[1] == 1: # ZYX
        #     data = np.empty(shape=(zSize, self.vertices[1][1], self.vertices[1][0]))
        #     if len(self.ROIData.shape) == 4:
        #         data[:] = self.ROIData[0, 0]
        #     elif len(self.ROIData.shape) == 3:
        #         data[:] = self.ROIData[0]
        # else:
        #     if len(self.ROIData.shape) == 5:
        #         data = self.ROIData[t, 0]
        #     else:
        #         data = self.ROIData  # When Data is loaded w/ Formatted ZYX

        return data

    # BooleanMask is always inclusive meaning that the bounds are included into the mask
    # Usually Mask are labels with their data already included and vertices are not needed
    # if the opposite is true create ROIData in this function with inclusive parameter
    # this is only true for rectangles
    # def getBooleanMask2D(self, z: int, t: int, c: int, inclusive: bool):
    #     # TODO: Add check if self.ROIs is None
    #     # if self.ROIData is None:
    #     #     if inclusive:
    #     #         self.ROIData = np.ones(shape=(self.vertices[1][0] * self.vertices[1][1]), dtype=bool)
    #     #     else:
    #     #         self.ROIData = np.pad(np.ones(shape=(self.vertices[1][0] + 1 * self.vertices[1][1] + 1),
    #     dtype=bool),
    #     #                               ((1, 1), (1, 1)), 'constant',
    #     #                               constant_values=((1, 1), (1, 1)))  # TODO add padding
    #     # if c == -1 and z == -1:  # Assumes that dims are TZYX:
    #     #     return self.ROIData[t].flatten()
    #     if c == -1:  # Assumes that dims are TZ{YX}:
    #         return self.ROIData[t][z].reshape(-1)
    #
    #     return self.ROIData[t][c][z].reshape(-1)  # Assume TCZ{YX}
