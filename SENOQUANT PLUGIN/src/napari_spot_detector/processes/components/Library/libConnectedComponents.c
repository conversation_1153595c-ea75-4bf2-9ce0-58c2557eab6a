#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include <omp.h>
#include <limits.h>
#include <stdbool.h>
#include <float.h>
#include <stdlib.h>

#if defined(_WIN32) && defined(BUILD_DLL)
#ifdef BUILD_DLL
#define EXPORT __declspec(dllexport)
#else
#define EXPORT __declspec(dllimport)
#endif
#else
#define EXPORT
#endif

struct Label {

    float imageValue;

    /**
     * final label that should replace the current label if fusion is needed
     */
    int targetLabelValue;

    /**
     * if non-null, indicates the parent object with which the current object should be fused
     */
    struct Label *targetLabel;

    int size;

    bool onEdgeX;

    bool onEdgeY;

    bool onEdgeZ;

    /**
     * Creates a new label with the given value. If no parent is set to this label, the given
     * value will be the final one
     *
     * @param value
     *        the pixel value
     * @param label
     *        the label value
     * @param onEdgeX
     *        true if the pixel is on the image edge along X
     * @param onEdgeY
     *        true if the pixel is on the image edge along Y
     * @param onEdgeZ
     *        true if the pixel is on the image edge along Z
     */

};

enum ExtractionType {
    /**
     * The user defined value is considered as the background, and all components with a
     * different intensity should be extracted (regardless of intensity variations)
     */
    BACKGROUND = -1,
    /**
     * The user defined value is considered as the background, and all components with a
     * different intensity should be extracted, accounting for intensity variations to
     * distinguish touching components
     */
    BACKGROUND_LABELED = 0,
    /**
     * Extracts components with pixel intensities matching the user defined value
     */
    VALUE = 1,
    /**
     * Extracts ROI from the input sequence
     */
    ROI = 2
};

int getFinalLabelValue(struct Label *label) {
    return label->targetLabel == NULL ? label->targetLabelValue : getFinalLabelValue(label->targetLabel);
}

/**
     * Extracts the connected components in the given volumetric image with specified size bounds.
     * The the method works on both binary gray-scale images. If the input is not binary, an option
     * can be set to distinguish touching objects which have a different intensity value, or to
     * extract a specific intensity value
     *
     * @param stack
     *        A binary or gray-scale volumetric image.
     * @param value
     *        the user value to be interpreted depending on the type parameter
     * @param type
     *        the extraction type (or how to interpret the value parameter)
     * @param noEdgeX
     *        set to true if components touching the image edge along X should be discarded
     *        during the extraction
     * @param noEdgeY
     *        set to true if components touching the image edge along Y should be discarded
     *        during the extraction
     * @param noEdgeZ
     *        set to true if components touching the image edge along Z should be discarded
     *        during the extraction
     * @param minSize
     *        the minimum size of the objects to extract
     * @param maxSize
     *        the maximum size of the objects to extract
     * @param labeledStack
     *        a volumetric image where labeled information will be stored during the extraction.
     *        Note that this volumetric image should be non-null and filled with zero-values (no
     *        cleaning is performed to optimize recursive processes)
     * @return a collection containing all components extracted.
     * @throws NullPointerException
     *         is labeledStack is null
     * @see ExtractionType
     * @see ConnectedComponent
     */
EXPORT float *extractConnectedComponents(int *stack, int value,
                                         int type, bool noEdgeX, bool noEdgeY, bool noEdgeZ,
                                         int minSize, int maxSize, int *labeledStack, int width, int height, int depth,
                                         float *image) {


    int neighborLabelValues[13];
    int neighborhoodSize = 0;

    bool extractUserValue = (type == VALUE);

    int n = width * height * depth;

    struct Label *labels = (struct Label *) calloc(n, sizeof(struct Label));

    // first image pass: naive labeling with simple backward neighborhood

    int highestKnownLabel = 0;

    bool onEdgeX = false;
    bool onEdgeY = false;
    bool onEdgeZ = false;

    for (int z = 0; z < depth; z++) {
        onEdgeZ = (z == 0 || z == depth - 1);

        int *currentSliceImage = (labeledStack + z * width * height); // 2D Array

        // retrieve the direct pointer to the current slice
        int *_labelsInCurrentSlice = currentSliceImage;
        // retrieve a direct pointer to the previous slice
        int *_labelsInUpperSlice = (z == 0) ? NULL : (labeledStack + (z - 1) * width * height);

        int voxelOffset = 0;

        int *inputData = (stack + z * width * height);

        for (int y = 0; y < height; y++) {
            onEdgeY = (y == 0 || y == height - 1);

            for (int x = 0; x < width; x++, voxelOffset++) {
                onEdgeX = (x == 0 || x == width - 1);

                int pixelValue = *(inputData + (y * width + x));

                bool pixelEqualsUserValue = (pixelValue == value);

                // do not process the current pixel if:
                // - extractUserValue is true and pixelEqualsUserValue is false
                // - extractUserValue is false and pixelEqualsUserValue is true

                if (extractUserValue != pixelEqualsUserValue)
                    continue;

                // the current pixel should be labeled

                // -> look for existing labels in its neighborhood

                // 1) define the neighborhood of interest here
                // NB: this is a single pass method, so backward neighborhood is sufficient

                // legend:
                // e = edge
                // x = current pixel
                // n = valid neighbor
                // . = other neighbor

                if (z == 0) {
                    if (y == 0) {
                        if (x == 0) {
                            // e e e
                            // e x .
                            // e . .

                            // do nothing
                        } else {
                            // e e e
                            // n x .
                            // . . .

                            neighborLabelValues[0] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 1;
                        }
                    } else {
                        int north = voxelOffset - width;

                        if (x == 0) {
                            // e n n
                            // e x .
                            // e . .

                            neighborLabelValues[0] = _labelsInCurrentSlice[north];
                            neighborLabelValues[1] = _labelsInCurrentSlice[north + 1];
                            neighborhoodSize = 2;
                        } else if (x == width - 1) {
                            // n n e
                            // n x e
                            // . . e

                            neighborLabelValues[0] = _labelsInCurrentSlice[north - 1];
                            neighborLabelValues[1] = _labelsInCurrentSlice[north];
                            neighborLabelValues[2] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 3;
                        } else {
                            // n n n
                            // n x .
                            // . . .

                            neighborLabelValues[0] = _labelsInCurrentSlice[north - 1];
                            neighborLabelValues[1] = _labelsInCurrentSlice[north];
                            neighborLabelValues[2] = _labelsInCurrentSlice[north + 1];
                            neighborLabelValues[3] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 4;
                        }
                    }
                } else {
                    if (y == 0) {
                        int south = voxelOffset + width;

                        if (x == 0) {
                            // e e e | e e e
                            // e n n | e x .
                            // e n n | e . .

                            neighborLabelValues[0] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[1] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[2] = _labelsInUpperSlice[south];
                            neighborLabelValues[3] = _labelsInUpperSlice[south + 1];
                            neighborhoodSize = 4;
                        } else if (x == width - 1) {
                            // e e e | e e e
                            // n n e | n x e
                            // n n e | . . e

                            neighborLabelValues[0] = _labelsInUpperSlice[voxelOffset - 1];
                            neighborLabelValues[1] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[2] = _labelsInUpperSlice[south - 1];
                            neighborLabelValues[3] = _labelsInUpperSlice[south];
                            neighborLabelValues[4] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 5;
                        } else {
                            // e e e | e e e
                            // n n n | n x .
                            // n n n | . . .

                            neighborLabelValues[0] = _labelsInUpperSlice[voxelOffset - 1];
                            neighborLabelValues[1] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[2] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[3] = _labelsInUpperSlice[south - 1];
                            neighborLabelValues[4] = _labelsInUpperSlice[south];
                            neighborLabelValues[5] = _labelsInUpperSlice[south + 1];
                            neighborLabelValues[6] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 7;
                        }
                    } else if (y == height - 1) {
                        int north = voxelOffset - width;

                        if (x == 0) {
                            // e n n | e n n
                            // e n n | e x .
                            // e e e | e e e

                            neighborLabelValues[0] = _labelsInUpperSlice[north];
                            neighborLabelValues[1] = _labelsInUpperSlice[north + 1];
                            neighborLabelValues[2] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[3] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[4] = _labelsInCurrentSlice[north];
                            neighborLabelValues[5] = _labelsInCurrentSlice[north + 1];
                            neighborhoodSize = 6;
                        } else if (x == width - 1) {
                            // n n e | n n e
                            // n n e | n x e
                            // e e e | e e e

                            neighborLabelValues[0] = _labelsInUpperSlice[north - 1];
                            neighborLabelValues[1] = _labelsInUpperSlice[north];
                            neighborLabelValues[2] = _labelsInUpperSlice[voxelOffset - 1];
                            neighborLabelValues[3] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[4] = _labelsInCurrentSlice[north - 1];
                            neighborLabelValues[5] = _labelsInCurrentSlice[north];
                            neighborLabelValues[6] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 7;
                        } else {
                            // n n n | n n n
                            // n n n | n x .
                            // e e e | e e e

                            neighborLabelValues[0] = _labelsInUpperSlice[north - 1];
                            neighborLabelValues[1] = _labelsInUpperSlice[north];
                            neighborLabelValues[2] = _labelsInUpperSlice[north + 1];
                            neighborLabelValues[3] = _labelsInUpperSlice[voxelOffset - 1];
                            neighborLabelValues[4] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[5] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[6] = _labelsInCurrentSlice[north - 1];
                            neighborLabelValues[7] = _labelsInCurrentSlice[north];
                            neighborLabelValues[8] = _labelsInCurrentSlice[north + 1];
                            neighborLabelValues[9] = _labelsInCurrentSlice[voxelOffset - 1];
                            neighborhoodSize = 10;
                        }
                    } else {
                        int north = voxelOffset - width;
                        int south = voxelOffset + width;

                        if (x == 0) {
                            // e n n | e n n
                            // e n n | e x .
                            // e n n | e . .

                            neighborLabelValues[0] = _labelsInUpperSlice[north];
                            neighborLabelValues[1] = _labelsInUpperSlice[north + 1];
                            neighborLabelValues[2] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[3] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[4] = _labelsInUpperSlice[south];
                            neighborLabelValues[5] = _labelsInUpperSlice[south + 1];
                            neighborLabelValues[6] = _labelsInCurrentSlice[north];
                            neighborLabelValues[7] = _labelsInCurrentSlice[north + 1];
                            neighborhoodSize = 8;
                        } else if (x == width - 1) {
                            int northwest = north - 1;
                            int west = voxelOffset - 1;

                            // n n e | n n e
                            // n n e | n x e
                            // n n e | . . e

                            neighborLabelValues[0] = _labelsInUpperSlice[northwest];
                            neighborLabelValues[1] = _labelsInUpperSlice[north];
                            neighborLabelValues[2] = _labelsInUpperSlice[west];
                            neighborLabelValues[3] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[4] = _labelsInUpperSlice[south - 1];
                            neighborLabelValues[5] = _labelsInUpperSlice[south];
                            neighborLabelValues[6] = _labelsInCurrentSlice[northwest];
                            neighborLabelValues[7] = _labelsInCurrentSlice[north];
                            neighborLabelValues[8] = _labelsInCurrentSlice[west];
                            neighborhoodSize = 9;
                        } else {
                            int northwest = north - 1;
                            int west = voxelOffset - 1;
                            int northeast = north + 1;
                            int southwest = south - 1;
                            int southeast = south + 1;

                            // n n n | n n n
                            // n n n | n x .
                            // n n n | . . .

                            neighborLabelValues[0] = _labelsInUpperSlice[northwest];
                            neighborLabelValues[1] = _labelsInUpperSlice[north];
                            neighborLabelValues[2] = _labelsInUpperSlice[northeast];
                            neighborLabelValues[3] = _labelsInUpperSlice[west];
                            neighborLabelValues[4] = _labelsInUpperSlice[voxelOffset];
                            neighborLabelValues[5] = _labelsInUpperSlice[voxelOffset + 1];
                            neighborLabelValues[6] = _labelsInUpperSlice[southwest];
                            neighborLabelValues[7] = _labelsInUpperSlice[south];
                            neighborLabelValues[8] = _labelsInUpperSlice[southeast];
                            neighborLabelValues[9] = _labelsInCurrentSlice[northwest];
                            neighborLabelValues[10] = _labelsInCurrentSlice[north];
                            neighborLabelValues[11] = _labelsInCurrentSlice[northeast];
                            neighborLabelValues[12] = _labelsInCurrentSlice[west];
                            neighborhoodSize = 13;
                        }
                    }
                }

                // 2) the neighborhood is ready, move to the labeling step

                int currentVoxelLabelValue = INT_MAX;

                // to avoid creating too many labels and fuse them later on,
                // find the minimum non-zero label in the neighborhood
                // and assign that minimum label right now

                for (int i = 0; i < neighborhoodSize; i++) {
                    int neighborLabelValue = neighborLabelValues[i];

                    // zero labels are not interesting...
                    if (neighborLabelValue == 0)
                        continue;

                    // neighbor labels should have the same/different ? image value
                    if (type == BACKGROUND_LABELED && labels[neighborLabelValue].imageValue != pixelValue)
                        continue;

                    // here, the neighbor label is valid
                    // => check if it is lower
                    if (neighborLabelValue < currentVoxelLabelValue) {
                        currentVoxelLabelValue = neighborLabelValue;
                    }
                }

                if (currentVoxelLabelValue == INT_MAX) {
                    // currentVoxelLabel didn't change
                    // => no lower neighbor value found
                    // => new label
                    highestKnownLabel++;
                    currentVoxelLabelValue = highestKnownLabel;
                    struct Label temp = {.imageValue=pixelValue, .targetLabelValue=currentVoxelLabelValue,
                            .size=0, .targetLabel=NULL, .onEdgeX=false, .onEdgeY=false, .onEdgeZ=false};

                    labels[currentVoxelLabelValue] = temp;

                } else {
                    // currentVoxelLabelValue has been modified
                    // -> browse its neighborhood again
                    // -> find all neighbors with a higher label value
                    // -> change their value to currentVoxelLabelValue
                    // -> change their target to currentVoxelLabel

                    struct Label currentVoxelLabel = labels[currentVoxelLabelValue];

                    for (int i = 0; i < neighborhoodSize; i++) {
                        int neighborLabelValue = neighborLabelValues[i];

                        if (neighborLabelValue > currentVoxelLabelValue) {
                            struct Label label = labels[neighborLabelValue];

                            if (type == BACKGROUND_LABELED && label.imageValue != pixelValue)
                                continue;

                            int finalLabelValue = getFinalLabelValue(&label);
                            struct Label finalLabel = labels[finalLabelValue];

                            if (currentVoxelLabel.targetLabelValue == finalLabelValue)
                                continue;

                            if (currentVoxelLabelValue < finalLabelValue) {
                                finalLabel.targetLabel = &currentVoxelLabel;
                                finalLabel.targetLabelValue = currentVoxelLabelValue;
                            } else if (currentVoxelLabelValue > finalLabelValue) {
                                currentVoxelLabel.targetLabel = &finalLabel;
                                currentVoxelLabel.targetLabelValue = finalLabelValue;
                            }
                        }
                    }
                }

                // -> store this label in the labeled image
                _labelsInCurrentSlice[voxelOffset] = currentVoxelLabelValue;
                labels[currentVoxelLabelValue].size++;
                labels[currentVoxelLabelValue].onEdgeX |= onEdgeX;
                labels[currentVoxelLabelValue].onEdgeY |= onEdgeY;
                labels[currentVoxelLabelValue].onEdgeZ |= onEdgeZ;
            }
        }

        // for data changed and proper cache update
        int *currentSliceImagePointer = (currentSliceImage + 0 * width * height);
        currentSliceImagePointer = _labelsInCurrentSlice;
    }

    // end of the first pass, all pixels have a label
    // (though might not be unique within a given component)


    // fusion strategy: fuse higher labels with lower ones
    // "highestKnownLabel" holds the highest known label
    // -> loop backward from there to accumulate object size recursively

    int finalLabel = 0;

    for (int labelValue = highestKnownLabel; labelValue > 0; labelValue--) {
        struct Label label = labels[labelValue];

        int targetLabelValue = label.targetLabelValue;

        if (targetLabelValue < labelValue) {
            // label should be fused to targetLabel

            struct Label targetLabel = labels[targetLabelValue];

            // -> add label's size to targetLabel
            targetLabel.size += label.size;

            // -> mark targetLabel as onEdge if label is
            targetLabel.onEdgeX |= label.onEdgeX;
            targetLabel.onEdgeY |= label.onEdgeY;
            targetLabel.onEdgeZ |= label.onEdgeZ;

            // -> mark label to fuse with targetLabel
            label.targetLabel = &labels[targetLabelValue];
        } else {
            // label has same labelValue and targetLabelValue
            // -> it cannot be fused to anything
            // -> this is a terminal label

            // -> check if it obeys to user constraints

            if (label.size < minSize || label.size > maxSize) {
                // the component size is out of the given range
                // -> mark the object for deletion
                label.targetLabelValue = 0;
            } else if ((noEdgeX && label.onEdgeX) || (noEdgeY && label.onEdgeY) || (noEdgeZ && label.onEdgeZ)) {
                // the component size is on an edge to discard
                // -> mark the object for deletion
                label.targetLabelValue = 0;
            } else {
                // the label is clean and user-valid
                // -> assign its final labelValue (for the final image labeling pass)
                finalLabel++;
                label.targetLabelValue = finalLabel;

                // -> add this label to the list of valid labels

            }
        }
    }

    // 3) second image pass: replace all labels by their final values

    int col = 9;
    int num = highestKnownLabel * col;
//    float stats[num];
//    float stats[highestKnownLabel][9] = {0};

    float *stats = calloc(num, sizeof(float));

//    printf("Here Hello\n");

//    int c = 0;

    for (int h = 0; h < highestKnownLabel; h++) {
        stats[h * col + 1] = FLT_MAX;
    }

    for (int z = 0; z < depth; z++) {
        int *sliceImage = (labeledStack + z * width * height);
        int *_outputSlice = sliceImage;

        float *img = (image + z * width * height);

        int pixelOffset = 0;

        for (int j = 0; j < height; j++) {
            for (int i = 0; i < width; i++, pixelOffset++) {
                int targetLabelValue = _outputSlice[pixelOffset];
                float pixelValue = img[pixelOffset];

                if (targetLabelValue == 0)
                    continue;

                // if a fusion was indicated, retrieve the final label value
                targetLabelValue = getFinalLabelValue(&labels[targetLabelValue]);

                // assign the final label in the output image
                _outputSlice[pixelOffset] = targetLabelValue;

                if (targetLabelValue == 0)
                    continue;

//                printf("Coordinate %d, %d, %d\n", z, j, i);

                // store the current pixel in the component
                stats[(targetLabelValue - 1) * col + 0] = targetLabelValue; // Target Number
                stats[(targetLabelValue - 1) * col + 1] =
                        pixelValue < stats[(targetLabelValue - 1) * col + 1] ? pixelValue : stats[
                                (targetLabelValue - 1) * col + 1]; // Min
                stats[(targetLabelValue - 1) * col + 2] =
                        pixelValue > stats[(targetLabelValue - 1) * col + 2] ? pixelValue : stats[
                                (targetLabelValue - 1) * col + 2];  // Max
                stats[(targetLabelValue - 1) * col + 3] += pixelValue; // Sum
                stats[(targetLabelValue - 1) * col + 4] += pixelValue; // Mean
                stats[(targetLabelValue - 1) * col + 5] += 1; // Size
                stats[(targetLabelValue - 1) * col + 6] += z; // Z Coordinate
                stats[(targetLabelValue - 1) * col + 7] += j; // Y Coordinate
                stats[(targetLabelValue - 1) * col + 8] += i; // X Coordinate

            }
        }

        // data changed and proper cache update
        sliceImage = _outputSlice;
    }

//    #pragma omp parallel
    for (int h = 0; h < highestKnownLabel; h++) {
        stats[h * col + 4] = stats[h * col + 4] / stats[h * col + 5]; // Mean
        stats[h * col + 6] = stats[h * col + 6] / stats[h * col + 5]; // Centroid Z
        stats[h * col + 7] = stats[h * col + 7] / stats[h * col + 5]; // Centroid Y
        stats[h * col + 8] = stats[h * col + 8] / stats[h * col + 5]; // Centroid X
    }

    free(labels);

    return stats;

}


