CC = gcc
CFLAGS = -Wall -Werror -fpic
LDFLAGS = -shared

OS := $(shell uname -s)


clean:
	@echo "Operating System: $(OS)"
	#ifeq ($(OS),Windows_NT)
	rm -f *.o
	rm -f *.dll
	#endif

#	ifeq ($(OS),Linux)
#		rm -f *.so
#	else
#		rm -f *.dylib
#	endif

libConnectedComponents.dll: libConnectedComponents.o
	gcc -shared -fPIC -o libConnectedComponents.dll libConnectedComponents.o -lgomp -Wl,--add-stdcall-alias

libConnectedComponents.o: libConnectedComponents.c
	gcc -c -Wall -g -O2 -fPIC -DBUILD_DLL libConnectedComponents.c
libConnectedComponents.so: libConnectedComponents.c
	mkdir -p build
	gcc -c  -Wall -g -O2 -fPIC -DBUILD_DLL -fopenmp  -o build/libConnectedComponents.o libConnectedComponents.c
	gcc -shared -Wignored-attributes -fms-extensions -o libConnectedComponents.so build/libConnectedComponents.o

all:
	make clean
	@echo "Operating System: $(OS)"
	#ifeq ($(OS),Windows_NT)
	make libConnectedComponents.o
	make libConnectedComponents.dll
	#endif
#   -fPIC
#-fopenmp
# -Werror
#  -std=c11
#  -std=c99
#  /opt/homebrew/Cellar/gcc/13.1.0/bin/gcc-13
# -fopenmp

libConnectedComponents_base.dylib: libConnectedComponents.c
	gcc -shared -Wignored-attributes -fdeclspec -fms-extensions -o libConnectedComponents.dylib libConnectedComponents.c
	#gcc -c -shared -Wall -Werror -fpic -o Mac/libConnectedComponents.o libConnectedComponents.c

libConnectedComponents-arm64.dylib: libConnectedComponents.c
	clang -arch arm64 -dynamiclib -o libConnectedComponents-arm64.dylib libConnectedComponents.c
libConnectedComponents-x86_64.dylib: libConnectedComponents.c
	clang -arch x86_64 -dynamiclib -o libConnectedComponents-x86_64.dylib libConnectedComponents.c

libConnectedComponents.dylib:
	make libConnectedComponents-arm64.dylib
	make libConnectedComponents-x86_64.dylib
	lipo -create -output libConnectedComponents.dylib libConnectedComponents-arm64.dylib libConnectedComponents-x86_64.dylib

clean-windows:
	rm *.o *.dll

clean-mac:
	rm *.dylib

# -dynamiclib
#libConnectedComponents-mac.o: libConnectedComponents-mac.c
#	gcc -c -Wall -Werror -fpic libConnectedComponents-mac.c
#
#libexample.so: example.o
#	$(CC) $(LDFLAGS) -o $@ $^
#
#example.o: example.c
#	$(CC) $(CFLAGS) -c $<


#gcc -c -Wall -Werror -fpic -fdeclspec -DBUILD_DLL -lm libConnectedComponents.c



#all: libexample.so



#mingw32-make.exe libConnectedComponents.o
#mingw32-make.exe libConnectedComponents.dll
