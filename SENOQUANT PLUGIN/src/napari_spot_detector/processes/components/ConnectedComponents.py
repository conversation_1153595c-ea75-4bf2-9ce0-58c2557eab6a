import ctypes
import os
import datetime
import sys
from sys import platform
from typing import Final

import cc3d
import cv2
from ...UDWT.multiprocessing_helper import getIntensityMeasurements, runOpenCV3DParallel
from overrides import override
from qtpy.QtCore import <PERSON><PERSON>ar<PERSON>
from qtpy.QtWidgets import <PERSON><PERSON>ox<PERSON>ayout, QCheckBox, QComboBox, QLabel, QHBoxLayout, QSpinBox, QWidget
from joblib import delayed, Parallel

from ...processes.Process import Process

import SimpleITK as sitk
import numpy as np

from ...tokens import GlobalDetectionToken


class ConnectedComponents(Process):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.name = 'ConnectedComponent'
        self.description = ''

        layout = QVBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)

        layoutBatchSize = QHBoxLayout()
        self.labelBatchSize = QLabel('Batch Size')
        self.spinboxBatchSize = QSpinBox()
        self.spinboxBatchSize.setMinimum(1)
        layoutBatchSize.addWidget(self.labelBatchSize)
        layoutBatchSize.addWidget(self.spinboxBatchSize)

        layoutThreads = QHBoxLayout()
        self.labelThreads = QLabel('Threads')
        self.spinboxThreads = QSpinBox()
        layoutThreads.addWidget(self.labelThreads)
        layoutThreads.addWidget(self.spinboxThreads)

        layout.addLayout(layoutBatchSize)
        layout.addLayout(layoutThreads)

        self.setLayout(layout)


class ConnectedComponentsSimpleITK(ConnectedComponents):
    FullyConnectedCheckBoxObjectName: Final[str] = "Fully Connected "
    CentroidCheckBoxCheckBoxObjectName: Final[str] = "Centroid"

    def __init__(self, *args, **kwargs):
        ConnectedComponents.__init__(self, *args, **kwargs)
        self.name = 'SimpleITK'
        # self.description = ''

        self.fullyConnectedCheckBox: QCheckBox = QCheckBox()
        self.fullyConnectedCheckBox.setObjectName(ConnectedComponentsSimpleITK.FullyConnectedCheckBoxObjectName)
        self.fullyConnectedCheckBox.setText("Fully Connected (face+edge+vertex)")
        self.fullyConnectedCheckBox.setChecked(True)

        self.CentroidCheckBox: QCheckBox = QCheckBox()
        self.CentroidCheckBox.setObjectName(ConnectedComponentsSimpleITK.CentroidCheckBoxCheckBoxObjectName)
        self.CentroidCheckBox.setText("Find Centroids by Shape (Geometry)")
        self.CentroidCheckBox.setChecked(True)

        self.layout().addWidget(self.fullyConnectedCheckBox)
        self.layout().addWidget(self.CentroidCheckBox)

    @override
    def process(self, gdt: GlobalDetectionToken):
        st = datetime.datetime.now()

        sequence = gdt.inputComputationSequence
        image = sequence.getAllImage(keep_three_dim=True)
        detectionList = gdt.detectionResult
        binaryDetectionResult3D = detectionList.binaryDetectionResult
        binaryDetectionResult3DImage = sitk.GetImageFromArray(binaryDetectionResult3D, isVector=False)

        if sequence.aicsimage is not None:
            z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Z)
            y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Y)
            x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
                sequence.aicsimage.physical_pixel_sizes.X)
        else:
            z = 1
            y = 1
            x = 1
        binaryDetectionResult3DImage.SetSpacing([z, y, x])

        imageObject = sitk.GetImageFromArray(image)
        imageObject.SetSpacing([z, y, x])

        relabel = sitk.RelabelComponentImageFilter()
        stats = sitk.LabelIntensityStatisticsImageFilter()
        shapes = sitk.LabelShapeStatisticsImageFilter()

        cc = sitk.ConnectedComponent(binaryDetectionResult3DImage, self.fullyConnectedCheckBox.isChecked())
        cc = sitk.RelabelComponent(cc)
        stats.Execute(cc, imageObject)
        shapes.Execute(cc)

        et = datetime.datetime.now()

        print("Stat:", et - st)

        st = datetime.datetime.now()

        detectionList.components = sitk.GetArrayFromImage(cc)

        # Decides which filter
        if self.CentroidCheckBox.isChecked():
            centroid_compute = shapes
        else:
            centroid_compute = stats

        stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
                                 centroid_compute.GetNumberOfPixels(i), centroid_compute.GetPhysicalSize(i), i) +
                                binaryDetectionResult3DImage.TransformPhysicalPointToContinuousIndex(
                                    centroid_compute.GetCentroid(i))[::-1] for i in
                                range(1, stats.GetNumberOfLabels() + 1)])

        et = datetime.datetime.now()

        print("Get Information:", et - st)

        stats_dict = {'centroids': np.array(stats_array[:, 7:10]),
                      'voxel_counts': stats_array[:, 4], 'labels': stats_array[:, 5]}

        detectionList.stats = stats_dict
        detectionList.N = stats_dict['voxel_counts'].size

        detectionList.intensity = stats_array[:, 0:4]
        detectionList.volumes = stats_array[:, 5][:, np.newaxis]


class ConnectedComponentsCCThreeDimension(ConnectedComponents):
    def __init__(self, *args, **kwargs):
        ConnectedComponents.__init__(self, *args, **kwargs)
        self.name = 'Connected Component Three Dimension'
        # self.description = ''

        layoutConnectivity: QHBoxLayout = QHBoxLayout()
        widgetConnectivity: QWidget = QWidget()
        self.connectivityLabel: QLabel = QLabel("Connectivity")
        self.connectivityComboBox: QComboBox = QComboBox()

        self.connectivityComboBox.addItems(['4', '8', '6', '18', '26'])

        layoutConnectivity.addWidget(self.connectivityLabel)
        layoutConnectivity.addWidget(self.connectivityComboBox)

        widgetConnectivity.setLayout(layoutConnectivity)
        self.layout().addWidget(widgetConnectivity)

    @override
    def process(self, gdt: GlobalDetectionToken):
        # connectivity = 26  # only 4,8 (2D) and 26, 18, and 6 (3D) are allowed
        connectivity = int(self.connectivityComboBox.currentText())

        sequence = gdt.inputComputationSequence
        image = sequence.getAllImage()
        detectionList = gdt.detectionResult
        binaryDetectionResult3D = detectionList.binaryDetectionResult

        print("Start Connected Components Function")
        s = datetime.datetime.now()
        b = binaryDetectionResult3D[0] if len(image.shape) == 2 & (connectivity == 4 or connectivity == 8) \
            else binaryDetectionResult3D
        labels_out, N = cc3d.connected_components(b.astype(np.uint16),
                                                  connectivity=connectivity,
                                                  return_N=True)
        stats = cc3d.statistics(labels_out)
        del stats['bounding_boxes']
        print("Results from Function -", datetime.datetime.now() - s)

        # Remove Background Information from Connect Components
        stats['centroids'] = np.delete(stats['centroids'], 0, axis=0)
        stats['voxel_counts'] = np.delete(stats['voxel_counts'], 0, axis=0)

        # TODO: Changing the datatype from the original float64
        detectionList.components = labels_out
        detectionList.stats = stats
        detectionList.N = N

        # self.labels = labels_out

        print("Start Calculations")
        s = datetime.datetime.now()

        image_reduce = image.ravel().astype(np.float32)
        labels_out_reduce = labels_out.ravel().astype(np.uint16)

        # Get Intensity Measurements
        seqids = np.arange(1, detectionList.N + 1).astype(np.uint16)

        batches = np.array_split(seqids, 1000)

        print(type(batches[0]))

        print("Start Intensity Parallel")

        # Process the batches in parallel
        workers = os.cpu_count() - 1
        detectionList.intensity = np.concatenate(
            Parallel(n_jobs=workers, batch_size=max(6, int(len(batches) / workers)), pre_dispatch='12*n_jobs')(
                delayed(getIntensityMeasurements)(i, image_reduce, labels_out_reduce) for i in batches))
        print(detectionList.intensity.shape)

        sorted_indices = np.argsort(detectionList.intensity[:, 4])
        detectionList.intensity = detectionList.intensity[sorted_indices][:, 0:4]
        print(detectionList.intensity.shape)

        print("Results from Calculations -", datetime.datetime.now() - s)

        # TODO: Create a Function instead of copying code
        z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
            sequence.aicsimage.physical_pixel_sizes.Z)
        y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
            sequence.aicsimage.physical_pixel_sizes.Y)
        x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
            sequence.aicsimage.physical_pixel_sizes.X)

        voxel = np.prod([z, y, x])

        detectionList.volumes = stats['voxel_counts'] * voxel  # TODO: Need Voxel Dimensions
        detectionList.volumes = detectionList.volumes[:, np.newaxis]


class ConnectedComponentsOpenCV(ConnectedComponents):
    def __init__(self, *args, **kwargs):
        ConnectedComponents.__init__(self, *args, **kwargs)

        layoutConnectivity: QHBoxLayout = QHBoxLayout()
        self.connectivityLabel: QLabel = QLabel("Connectivity")
        self.connectivityComboBox: QComboBox = QComboBox()

        self.connectivityComboBox.addItems(['4', '8'])

        layoutConnectivity.addWidget(self.connectivityLabel)
        layoutConnectivity.addWidget(self.connectivityComboBox)

        layoutAlg: QHBoxLayout = QHBoxLayout()
        self.algLabel: QLabel = QLabel("Connected Components Algorithm")
        self.algComboBox: QComboBox = QComboBox()

        self.algComboBox.addItem("WU", cv2.CCL_WU)
        self.algComboBox.addItem("GRANA", cv2.CCL_GRANA)
        self.algComboBox.addItem("BBDT", cv2.CCL_BBDT)
        self.algComboBox.addItem("SAUF", cv2.CCL_SAUF)
        self.algComboBox.addItem("BOLELLI", cv2.CCL_BOLELLI)
        self.algComboBox.addItem("SPAGHETTI", cv2.CCL_SPAGHETTI)

        layoutAlg.addWidget(self.algLabel)
        layoutAlg.addWidget(self.algComboBox)

        widgetConnectivity: QWidget = QWidget()
        widgetConnectivity.setLayout(layoutConnectivity)
        widgetAgl: QWidget = QWidget()
        widgetAgl.setLayout(layoutAlg)

        self.layout().addWidget(widgetConnectivity)
        self.layout().addWidget(widgetAgl)

    @override
    def process(self, gdt: GlobalDetectionToken):
        sequence = gdt.inputComputationSequence
        detectionList = gdt.detectionResult
        img = detectionList.binaryDetectionResult

        # Get Intensity Measurements
        seqids = np.arange(img.shape[0])

        workers = int(os.cpu_count() / 2)
        results = Parallel(n_jobs=workers, batch_size=max(6, int(len(seqids) / workers)),
                           pre_dispatch='12*n_jobs')(
            delayed(runOpenCV3DParallel)(img[i].astype(np.uint16), i, int(self.connectivityComboBox.currentText()),
                                         self.algComboBox.currentData()) for i in seqids)

        component_full: np.ndarray = np.zeros(shape=img.shape, dtype=np.uint32)
        result_full = []
        offset: int = 0
        for i, r in enumerate(results):
            result, components = r
            num_label = components.max()
            components[components != 0] += offset
            result[:, 4] += offset

            component_full[i] = components
            result_full.append(result)

            offset += num_label

        result_matrix = np.concatenate(result_full)

        detectionList.components = component_full
        detectionList.stats = {'centroids': result_matrix[:, 5:8], 'voxel_counts': result_matrix[:, 8]}
        detectionList.volumes = result_matrix[:, 8][:, np.newaxis]
        detectionList.intensity = result_matrix[:, :4]

        detectionList.N = np.sum(np.unique(result_matrix[:, -1]) - 1)

        if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
            voxel = np.prod(sequence.aicsimage.physical_pixel_sizes)

            detectionList.volumes = voxel * detectionList.volumes


class ConnectedComponentsIcy(ConnectedComponents):
    def __init__(self, *args, **kwargs):
        super(ConnectedComponentsIcy, self).__init__(*args, **kwargs)

        self.noEdgeZCheckBox: QCheckBox = QCheckBox("No Edge Along Z")
        self.noEdgeZCheckBox.setChecked(False)
        self.noEdgeYCheckBox: QCheckBox = QCheckBox("No Edge Along Y")
        self.noEdgeYCheckBox.setChecked(False)
        self.noEdgeXCheckBox: QCheckBox = QCheckBox("No Edge Along X")
        self.noEdgeXCheckBox.setChecked(False)

        self.layout().addWidget(self.noEdgeZCheckBox)
        self.layout().addWidget(self.noEdgeYCheckBox)
        self.layout().addWidget(self.noEdgeXCheckBox)

        if os.name == 'nt':
            ext = 'dll'
        elif platform.system() == 'Darwin':
            ext = 'dylib'
        else:
            ext = 'so'

        data_path = os.path.join(os.path.dirname(__file__), 'Library', f'libConnectedComponents.{ext}')

        self.clib = ctypes.cdll.LoadLibrary(data_path)

        self.clib.extractConnectedComponents.argtypes = [
            np.ctypeslib.ndpointer(dtype=np.int32, ndim=3, flags='C_CONTIGUOUS'), ctypes.c_int32, ctypes.c_int32,
            ctypes.c_bool, ctypes.c_bool, ctypes.c_bool, ctypes.c_int32, ctypes.c_int32,
            np.ctypeslib.ndpointer(dtype=np.int32, ndim=3, flags='C_CONTIGUOUS'),
            ctypes.c_int32, ctypes.c_int32, ctypes.c_int32,
            np.ctypeslib.ndpointer(dtype=np.float32, ndim=3, flags='C_CONTIGUOUS')]
        self.clib.extractConnectedComponents.restype = ctypes.POINTER(ctypes.c_float)
        # np.ctypeslib.ndpointer(dtype=np.float32, ndim=2, flags='C_CONTIGUOUS')
        # self.clib.extractConnectedComponents.restype = ctypes.c_void_p

    @override
    def process(self, gdt: GlobalDetectionToken):
        sequence = gdt.inputComputationSequence
        image = sequence.getAllImage(keep_three_dim=True)
        detectionList = gdt.detectionResult
        binaryDetectionResult3D = detectionList.binaryDetectionResult.astype(np.int32)
        labels = np.zeros(shape=binaryDetectionResult3D.shape, dtype=np.int32)

        width: int = sequence.getSizeX()
        height: int = sequence.getSizeY()
        depth: int = sequence.getSizeZ()

        print("Starting extractConnectedComponents")

        c_array = self.clib.extractConnectedComponents(binaryDetectionResult3D, 0, -1, self.noEdgeXCheckBox.isChecked(),
                                                       self.noEdgeYCheckBox.isChecked(),
                                                       self.noEdgeZCheckBox.isChecked(),
                                                       0, sys.maxsize,
                                                       labels, width, height, depth, image)

        # Get Intensity Measurements
        seqids = np.unique(labels)[1:]

        result_matrix = np.ctypeslib.as_array(c_array, shape=(seqids.shape[0], 9))

        centroids = result_matrix[:, 6:]

        detectionList.components = labels
        detectionList.stats = {'centroids': centroids, 'voxel_counts': result_matrix[:, 5]}
        detectionList.volumes = result_matrix[:, 5][:, np.newaxis]
        detectionList.intensity = result_matrix[:, 1:5]

        detectionList.N = result_matrix.shape[0]

        if sequence.aicsimage is not None and None not in sequence.aicsimage.physical_pixel_sizes:
            voxel = np.prod(sequence.aicsimage.physical_pixel_sizes)

            detectionList.volumes = voxel * detectionList.volumes
