from typing import Dict, Union

import napari

import openpyxl
import <PERSON><PERSON><PERSON> as sitk
from napari._qt.qthreading import thread_worker
from qtpy.QtWidgets import QWidget

from ..images.Sequence import Sequence
from ..tokens.GlobalDetectionToken import GlobalDetectionToken


class Process(QWidget):
    def __init__(self, description='', name='Process', viewer: napari.Viewer = napari.current_viewer(), *args, **kwargs):
        QWidget.__init__(self)
        self.description: str = description
        self.name: str = kwargs['Name']['#text'] if 'Name' in kwargs.keys() else 'Process'
        self.viewer: napari.Viewer = kwargs['Viewer'] if 'Viewer' in kwargs.keys() else viewer

    def process(self, gdt: GlobalDetectionToken):
        pass

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        pass

    def parameters(self) -> Dict:
        return {"Name": self.__class__.__name__}

    @thread_worker
    def run(self, gdt: GlobalDetectionToken):
        return self.process(gdt=gdt)

    def on_process_text_changed(self, text: Union[str, slice]):
        pass
        # print(text)

    def getImage(self, sequence: Sequence) -> sitk.Image:
        data = sequence.getAllImage(dtype=sequence.image.dtype, keep_three_dim=True)

        image = sitk.GetImageFromArray(data, isVector=False)
        if sequence.aicsimage is not None:
            z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Z)
            y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
                sequence.aicsimage.physical_pixel_sizes.Y)
            x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
                sequence.aicsimage.physical_pixel_sizes.X)
        else:
            z = 1
            y = 1
            x = 1
        image.SetSpacing([z, y, x])

        return image
