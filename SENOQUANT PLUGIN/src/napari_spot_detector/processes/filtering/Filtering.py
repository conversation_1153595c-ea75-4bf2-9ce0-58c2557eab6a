import ctypes
import math
import os.path
import platform
import traceback

from typing import <PERSON><PERSON>, Dict, List, Union

import numpy as np
import openpyxl.worksheet.worksheet
import pandas as pd
import xarray
from aicsimageio.types import PhysicalPixelSizes
from aicsimageio.writers import OmeTiffWriter
from napari.layers import Shapes, Layer, Labels
from napari.utils import Colormap
from napari.utils.notifications import ErrorNotification
from napari_roi import ROIOrigin, ROIWidget
from napari_roi.qt import ROILayerAccessor
from ome_types._autogenerated.ome_2016_06 import PixelType
from overrides import override
from qtpy import QtCore
from qtpy.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QGroupBox, QLineEdit, QCheckBox,
                            QSpinBox, QFormLayout, QDoubleSpinBox, QComboBox, QPushButton)

from ...images.Sequence import Sequence
from ...tokens.GlobalDetectionToken import GlobalDetectionToken
from .FilterHelper import parallel_apply_along_axis, f
from .UFishFilter import UFishFilter
from ..Process import Process
from ...UDWT.UDWTWaveletCore import UDWTWaveletCore
from ...images.ROI import ROI

from superqt import QRangeSlider, QLabeledRangeSlider

import SimpleITK as sitk


# from qtpy.QtWidgets import QSizePolicy, QGridLayout


# TODO: Parallelize Filtering Steps

class PreFiltering(Process):
    def __init__(self, description='PreFiltering', *args, **kwargs):
        super().__init__(description=description, *args, **kwargs)

    def open_file_viewer(self, file_path, gdt: GlobalDetectionToken):
        sequence: Sequence = gdt.inputSequence

        if file_path is not None:
            # Load a file using the napari-aicsimageio plugin
            layers: List[Layer] = self.viewer.open(file_path, plugin='napari-aicsimageio', blending='additive',
                                                   colormap=sequence.image.colormap)

            # Changing the computation sequence to recently added one
            self.setInputSequence(gdt=gdt, layer=layers[0])

    @staticmethod
    def setInputSequence(gdt: GlobalDetectionToken, layer: Layer):
        viewer = gdt.inputSequence.viewer
        # Set Data to inputComputationSequence
        gdt.inputComputationSequence = Sequence(layer.name, viewer)
        # gdt.inputComputationSequence = Sequence(viewer.layers[-1].name, viewer)

    @staticmethod
    def get_mask(sequence: Sequence, t: int = 1, crop: bool = False) -> np.ndarray:
        bounds = ROI(Shapes(data=[np.array([[0, 0], [sequence.getSizeY(),
                                                     sequence.getSizeX()]])], shape_type='rectangle',
                            visible=False, name='ROI Image Bound'))
        bounds.roi_origin = ROIOrigin.TOP_LEFT

        rois: List[ROI] = list()  # Threshold and Rectangles Boundaries
        rois.extend(sequence.rois)
        sequence.crop = crop
        if len(rois) == 0:
            rois.append(bounds)

        masks: np.ndarray = UDWTWaveletCore.buildBinaryMask(bounds=bounds, rois=rois, t=t, sequence=sequence)

        return masks

    @staticmethod
    def save_image_layer(sequence: Sequence, enh_img: np.ndarray) -> str:
        scale: Union[List, Tuple] = sequence.scale.tolist() if isinstance(sequence.scale,
                                                                          np.ndarray) else sequence.scale  # .tolist()
        if len(scale) == 2:
            scale.insert(0, None)
        physical_pixel_sizes = PhysicalPixelSizes(*scale)

        file_path = sequence.path
        if file_path is None or file_path == '':
            if os.name == 'nt':
                directory = os.environ.get('TEMP')
                directory = os.path.expanduser('~') if directory is None else directory
            else:
                directory = '/tmp'
                directory = os.path.expanduser('~') if not os.path.exists(directory) else directory

            file_path = os.path.join(directory, "tmp.tif")

        # try:
        #     file_path = sequence.aicsimage.reader._path
        # except Exception as e:
        #     file_path = sequence.path
        # print(e)
        # file_path = sequence.image.source.path

        # Specify the file path and name
        filename, ext = os.path.splitext(file_path)

        # Use Channel Name of File path
        name = sequence.imageName

        import re

        def clean_filename(filename):
            # Remove invalid characters
            cleaned = re.sub(r'[^\w\s.-]', '', filename.lower())
            # Replace spaces and repeated dashes with a single dash
            cleaned = re.sub(r'[-\s]+', '-', cleaned).strip('-_')
            return cleaned

        name = clean_filename(name)

        # name = sequence.image.name  # Actual Image Name from Layer List
        directory = os.path.dirname(f'{file_path}')
        file_path = os.path.join(directory, name + "_filter" + ext)  # To Match Channel Name

        # Old Method of file name
        # file_path: str = filename + "_filter" + ext

        filename, ext = os.path.splitext(file_path)
        counter = 1
        while os.path.exists(file_path):
            file_path = f"{filename}({counter}){ext}"
            counter += 1

        # Use the OmeTiffWriter to save the image with scale information
        colormap: Colormap = sequence.image.colormap
        channel_colors = list(colormap.colors[-1, 0:3] * 255)
        channel_colors = [[channel_colors]]
        image_index = 0
        try:
            ome_xml = sequence.aicsimage.ome_metadata
            # print(enh_img.dtype)
            ome_xml.images[image_index].pixels.type = (
                PixelType.FLOAT) if enh_img.dtype == np.float32 else ome_xml.images[image_index].pixels.type

            ome_xml.images[image_index].pixels.size_c = 1
            ll = [counter for counter, i in enumerate(ome_xml.images[image_index].pixels.channels) if
                  i.name in sequence.imageName]
            image_index_location = 0 if len(ll) == 0 else ll[0]
            c = ome_xml.images[image_index].pixels.channels[image_index_location]
            c.id = 'Channel:0'
            c.name = sequence.channel + " filter"
            ome_xml.images[image_index].pixels.channels = [c]
            ome_xml.images[image_index].pixels.size_x = enh_img.shape[-1]
            ome_xml.images[image_index].pixels.size_y = enh_img.shape[-2]

            OmeTiffWriter.save(
                data=enh_img,
                uri=file_path,
                ome_xml=ome_xml,
                image_name=sequence.channel + " filter",
                physical_pixel_sizes=physical_pixel_sizes,
            )
        except Exception as e:
            print(e)

            OmeTiffWriter.save(
                data=enh_img,
                uri=file_path,
                image_name=sequence.channel + " filter",
                physical_pixel_sizes=physical_pixel_sizes,
                # physical_pixel_sizes=tuple(scale),
                dim_order='ZYX' if len(enh_img.shape) == 3 else 'YX',
                channel_names=[sequence.channel + " filter"],
                channel_colors=channel_colors
            )

        # OmeTiffWriter.save(
        #     data=enh_img,
        #     uri=file_path,
        #     # pixels_physical_size=physical_pixel_sizes,
        #     # dim_order='TCZYX' if len(enh_img.shape) == 3 else 'TCYX',
        #     ome_xml=sequence.aicsimage.ome_metadata,
        #     # channel_names=[sequence.channel + " filter"],
        #     # channel_colors=channel_colors
        # )
        # OmeTiffWriter.save(
        #     data=enh_img,
        #     uri=file_path,
        #     pixels_physical_size=physical_pixel_sizes,
        #     dim_order='TCZYX' if len(enh_img.shape) == 3 else 'TCYX',
        #     ome_xml=sequence.aicsimage.ome_metadata,
        #     channel_names=[sequence.channel + " filter"],
        #     channel_colors=channel_colors
        # )

        return file_path

        # return sequence.viewer.layers[-1].name
        # return Sequence(sequence.viewer.layers[-1].name, sequence.viewer)


class PostFiltering(Process):
    def __init__(self, description='PostFiltering', *args, **kwargs):
        super().__init__(description=description, *args, **kwargs)


class NoFiltering(Process):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        self.layout = QVBoxLayout()
        self.layout.addWidget(QLabel('No Filtering'))
        self.setLayout(self.layout)


class SizeFiltering(PostFiltering):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        self.layout = QVBoxLayout()

        self.buttonGroupMin = QGroupBox()
        self.buttonGroupMin.setLayout(QHBoxLayout())
        self.buttonGroupMin.layout().addWidget(QLabel('Min size: '))
        self.minValueTextField = QLineEdit("0" if 'Min' not in kwargs.keys()
                                           else kwargs.get('Min', {"#text": "0"}).get('#text', "0"))
        self.buttonGroupMin.layout().addWidget(self.minValueTextField)

        self.buttonGroupMax = QGroupBox()
        self.buttonGroupMax.setLayout(QHBoxLayout())
        self.buttonGroupMax.layout().addWidget(QLabel("Max size: "))
        self.maxValueTextField = QLineEdit("3000" if 'Max' not in kwargs.keys()
                                           else kwargs.get('Max', {"#text": "3000"}).get('#text', "3000"))
        self.buttonGroupMax.layout().addWidget(self.maxValueTextField)

        self.layout.addWidget(QLabel("Range of accepted objects (in pixels)"))
        self.layout.addWidget(self.buttonGroupMin)
        self.layout.addWidget(self.buttonGroupMax)

        self.setLayout(self.layout)

    def parameters(self) -> Dict:
        return {'Name': super().parameters()['Name'],
                "Min": self.minValueTextField.text(), "Max": self.maxValueTextField.text()}

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        page.append(("Min:", self.minValueTextField.text()))
        page.append(("Max:", self.maxValueTextField.text()))

    def process(self, gdt: GlobalDetectionToken):
        highValue: int = int(self.maxValueTextField.text())
        lowValue: int = int(self.minValueTextField.text())

        detectionSpots = gdt.detectionResult.ROI2Detection['Surface'].to_numpy(dtype=np.float32)

        high = detectionSpots <= highValue
        low = detectionSpots >= lowValue
        condition = np.logical_not(np.logical_and(high, low))
        removeIdx = np.where(condition)

        index_list = removeIdx[0].tolist()

        gdt.detectionResult.ROI2Detection = gdt.detectionResult.ROI2Detection.loc[
            ~gdt.detectionResult.ROI2Detection.index.isin(index_list)]

        removeIdx_plus = removeIdx[0] + 1

        mask = np.isin(gdt.detectionResult.components, removeIdx_plus)  # remove labels

        gdt.detectionResult.components[mask] = 0
        gdt.detectionResult.reconstruction[mask] = 0


class DeClusterFiltering(PostFiltering):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        # self.gdt: Union[GlobalDetectionToken, None]
        self.nbSplitDetection: int = 0

        self.layout = QVBoxLayout()

        self.spotRaySizeBox = QGroupBox()
        self.spotRaySizeBox.setLayout(QHBoxLayout())
        self.spotRaySizeBox.layout().addWidget(QLabel("spot ray:"))
        self.spotRaySizeTextBox = QLineEdit("4") if "spot_ray" not in kwargs.keys() else QLineEdit(
            str(kwargs.get('spot_ray', {"#text", "4"}).get('#text', "4")))
        self.spotRaySizeBox.layout().addWidget(self.spotRaySizeTextBox)

        self.layout.addWidget(self.spotRaySizeBox)

        self.setLayout(self.layout)

    def parameters(self) -> Dict:
        return {'Name': super().parameters()['Name'], "spot_ray": self.spotRaySizeTextBox.text()}

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        page.append(("spot ray:", self.spotRaySizeTextBox.text()))

    @staticmethod
    def _getSurfaceOfOneSpot(spotRay: float) -> float:
        bound: int = int(math.ceil((spotRay + 1)))
        surface: int = 0

        x: int
        y: int
        for x in range(-bound, bound + 1):
            for y in range(-bound, bound + 1):
                p: Tuple = (x, y)
                if math.dist((0, 0), p) < spotRay:
                    surface += 1

        return surface

    # two spots right next to each other offset by 1 pixel.
    @staticmethod
    def _getSurfaceOfTwoContiguousSpot(spotRay: float) -> float:
        bound: int = int(math.ceil(spotRay + 1) + 1)
        surface: int = 0

        x: int
        y: int
        for x in range(-bound, bound + 1):
            for y in range(-bound, bound + 1):
                p: Tuple = (x, y)
                if math.dist((0, 0), p) < spotRay or math.dist((1, 0), p):
                    surface += 1

        return surface

    def filterTest(self, seqid: int, gdt: GlobalDetectionToken):
        maxValue: float
        p: Tuple

        image = gdt.inputSequence.getAllImage()
        if len(image.shape) == 2:
            image = image[np.newaxis, :, :]
        component = gdt.detectionResult.components

        seqids = gdt.detectionResult.ROI2Detection['Detection #'].to_numpy()[:, np.newaxis]

        maxValues = parallel_apply_along_axis(f, 1, seqids, image=image, component=component)

        condition_max = maxValues > 80
        condition_max_count = np.count_nonzero(condition_max)

        self.nbSplitDetection += condition_max_count

        decluster_df = gdt.detectionResult.ROI2Detection[condition_max]

        decluster_df['y'] = decluster_df['y'] + 1
        decluster_df['x'] = decluster_df['x'] + 1

        decluster_df.loc[:, ['Minimum Intensity', 'Maximum Intensity', 'Average Intensity', 'Sum Intensity']] = \
            [None, None, None, None]
        decluster_df['Surface'] = 1
        decluster_df['Volume'] = gdt.inputSequence.calc_volume_from_pixel()
        decluster_df['Detection #'] = np.arange(gdt.detectionResult.N + 1,
                                                gdt.detectionResult.N + len(decluster_df) + 1)

        gdt.detectionResult.ROI2Detection = pd.concat([gdt.detectionResult.ROI2Detection, decluster_df],
                                                      ignore_index=True)

    def process(self, gdt: GlobalDetectionToken):
        print('Cluster filtering...')
        # self.gdt = gdt

        spotRay: float = float(self.spotRaySizeTextBox.text())

        # compute surface of 1 spot:
        surfaceOfOneSpot: int = int(self._getSurfaceOfOneSpot(spotRay))
        surfaceOfTwoContiguousSpot: int = int(self._getSurfaceOfTwoContiguousSpot(spotRay))

        print("surface of one spot:", surfaceOfOneSpot)
        print("surface of 2 glued spots:", surfaceOfTwoContiguousSpot)
        print("difference between the 2:", (surfaceOfTwoContiguousSpot - surfaceOfOneSpot))
        self.nbSplitDetection = 0

        self.filterTest(gdt.detectionResult.N, gdt)

        print("Nombre de split phase 1:", self.nbSplitDetection)


class UFISHPreFiltering(PreFiltering):
    chunk_checkbox_label = "ufish_chunk"
    spinbox_batch_size_object_name = "ufish_batch_size"
    spinbox_threshold_label = "ufish_threshold"
    checkbox_3d_blend_object_name = "ufish_blend"
    # order_list = ['X', 'Y', 'Z', 'C', 'T']
    order_list = ['Z', 'Y', 'X']
    # order_list = ['X', 'Y', 'Z']

    num_dims = 3
    defaults = [1, 512, 512]
    ratios = [2, 3, 3]
    maximum = 10000

    def __init__(self, input_box: Union[None, QComboBox] = None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.ufish: UFishFilter = UFishFilter()

        self.input_box: QComboBox = input_box

        self.layout = QVBoxLayout()

        self.inference_ufish = QGroupBox("Inference U-Fish to Produce an Enhance Image")
        self.inference_ufish.setLayout(QVBoxLayout())
        self.layout.addWidget(self.inference_ufish)

        self.checkbox_3d_blend = QCheckBox("Blend 3D")
        self.checkbox_3d_blend.setObjectName(self.checkbox_3d_blend_object_name)
        self.inference_ufish.layout().addWidget(self.checkbox_3d_blend)

        self.batch_size_widget = QWidget()
        self.batch_size_widget.setLayout(QHBoxLayout())
        self.batch_size_widget.layout().addWidget(QLabel("Batch Size"))
        self.spinbox_batch_size: QSpinBox = QSpinBox()
        self.spinbox_batch_size.setValue(4)
        self.spinbox_batch_size.setRange(1, 50)
        self.spinbox_batch_size.setSingleStep(1)
        self.spinbox_batch_size.setObjectName(self.spinbox_batch_size_object_name)
        self.batch_size_widget.layout().addWidget(self.spinbox_batch_size)
        self.inference_ufish.layout().addWidget(self.batch_size_widget)

        # self.threshold_widget = QWidget()
        # self.threshold_widget.setLayout(QHBoxLayout())
        # self.threshold_widget.layout().addWidget(QLabel("P Threshold"))
        # self.spinbox_threshold = QDoubleSpinBox()
        # self.spinbox_threshold.setValue(0.5)
        # # self.spinbox_threshold.setMaximum(1.0)
        # self.spinbox_threshold.setObjectName(self.spinbox_threshold_label)
        # self.threshold_widget.layout().addWidget(self.spinbox_threshold)
        # self.inference_ufish.layout().addWidget(self.threshold_widget)

        self.chunk_checkbox: QCheckBox = QCheckBox("Chunking")
        self.chunk_checkbox.setObjectName(self.chunk_checkbox_label)

        self.chunk_widget: QWidget = QWidget()
        # grid_layout = QGridLayout()
        self.chunk_widget.setLayout(QVBoxLayout())
        self.layout.addWidget(self.chunk_widget)

        for n in range(self.num_dims):
            spinbox: QSpinBox = QSpinBox()
            spinbox.setObjectName(self.order_list[n])
            spinbox.setMinimum(1)
            spinbox.setMaximum(self.maximum)

            spinbox.setValue(self.defaults[n])
            self.chunk_widget.layout().addWidget(spinbox)

        label = QLabel("Orientation: " + "".join(self.order_list))
        label.setAlignment(QtCore.Qt.AlignRight)
        self.chunk_widget.layout().addWidget(label)
        self.layout.addWidget(self.chunk_widget)

        if self.viewer is not None and len(self.viewer.layers) != 0:
            for n in range(len(self.viewer.layers[0].data.shape), 0, -1):
                s: QSpinBox = self.chunk_widget.findChild(QSpinBox, self.order_list[-n])
                s.setValue(math.ceil(self.viewer.layers[0].data.shape[-n] / self.ratios[-n]))

        self.chunk_checkbox.setChecked(True)
        self.inference_ufish.layout().addWidget(self.chunk_checkbox)

        # Add Function for Change for DropDown (Not being used at the moment)
        if self.input_box is not None:
            self.input_box.currentTextChanged.connect(self.on_process_text_changed)

        self.setLayout(self.layout)

    @override
    def on_process_text_changed(self, text: Union[str, slice]):
        try:
            if text != '':
                for n in range(len(self.viewer.layers[text].data.shape), 0, -1):
                    s: QSpinBox = self.chunk_widget.findChild(QSpinBox, self.order_list[-n])
                    s.setValue(math.ceil(self.viewer.layers[text].data.shape[-n] / self.ratios[-n]))
        except Union[IndexError, AttributeError, RuntimeError, ValueError, TypeError] as e:
            print(e)
            traceback.print_stack()
            ErrorNotification(e)

    def parameters(self) -> Dict:
        d: Dict = {}
        for c in self.children():
            if c.objectName() != '':
                if isinstance(c, QSpinBox):
                    d[c.objectName()] = c.value()
                elif isinstance(c, QCheckBox):
                    d[c.objectName()] = c.isChecked()

        return d
        # return {self.chunk_checkbox_label: self.findChild()}
        # return {'Name': super().parameters()['Name'], "spot_ray": self.spotRaySizeTextBox.text()}

    def process(self, gdt: GlobalDetectionToken):
        layer_name = gdt.inputSequence.imageName
        data = gdt.inputSequence.getAllImage()
        print("Run inference on", layer_name)
        # input_axes = None
        blend_3d = self.checkbox_3d_blend.isChecked()
        batch_size = self.spinbox_batch_size.value()
        # p_thresh = self.spinbox_threshold.value()
        p_thresh = 0.5
        chunking = self.chunk_checkbox.isChecked()

        # chunk_size = (round(data.shape[-2] / batch_size), round(data.shape[-1] / batch_size))
        # chunk_size = (int(data.shape[-2]/batch_size), int(data.shape[-1]/batch_size))
        # chunk_size = (256, 256)
        # chunk_size = (512, 512)
        # chunk_size = (1024, 1024)
        # chunk_size = (2048, 2048)
        # chunk_size = (4096, 4096)
        # chunk_size = None
        # chunk_size = data.shape

        print("Min:", data.min(), "Max:", data.max(), "dtype:", data.dtype, "Shape:", data.shape)

        from onnxruntime.capi.onnxruntime_pybind11_state import RuntimeException
        try:
            if chunking:
                chunk_size: Tuple = tuple([self.findChild(QSpinBox, self.order_list[-o]).value() for o in
                                           range(len(data.shape), 0, -1)])
                # if len(data.shape) == 2:
                #     chunk_size: Tuple[int] = (self.findChild(QSpinBox, o).value() for o in self.order_list[1:])
                #     # chunk_size = (int(data.shape[-2] / 2), int(data.shape[-1] / 2))
                # else:
                #     chunk_size: Tuple[int] = (self.findChild(QSpinBox, o).value() for o in self.order_list)
                #     # chunk_size = (int(data.shape[-3] / 2), int(data.shape[-2] / 2), int(data.shape[-1] / 2))

                print("Size of Image ", data.shape)
                print("Size of Chunk ", chunk_size)

                _, enh_img = self.ufish.predict_chunks(data, blend_3d=blend_3d, batch_size=batch_size,
                                                       p_thresh=p_thresh, chunk_size=chunk_size)
            else:
                _, enh_img = self.ufish.predict(data, blend_3d=blend_3d, batch_size=batch_size, p_thresh=p_thresh)

            print("Enhance Image - Min:", enh_img.min(), "Max:", enh_img.max(), "dtype:", enh_img.dtype, "Shape:",
                  enh_img.shape)

            # Normalized Images
            img_normalized = (enh_img - np.min(enh_img)) / (np.max(enh_img) - np.min(enh_img))
            img_scaled = img_normalized * (data.max() - data.min()) + data.min()
            img_uint8 = img_scaled.astype(gdt.inputSequence.image.data.dtype)

            return PreFiltering.save_image_layer(sequence=gdt.inputSequence, enh_img=img_uint8)
        except Union[RuntimeException, RuntimeError, TypeError, MemoryError, IOError] as e:
            print(e)
            traceback.print_stack()
            ErrorNotification(e)
            return None


class EditFiltering(PreFiltering):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.setLayout(QVBoxLayout())

        # Adding _roi_widget to roi group
        ROILayerAccessor.DEFAULT_ROI_ORIGIN = ROIOrigin.TOP_LEFT

        self.roiWidget: ROIWidget = ROIWidget(self.viewer)
        self.roiWidget._add_widget.setDisabled(True)
        self.roiWidget._add_widget.setHidden(True)
        self.roiWidget._save_widget.setDisabled(True)
        self.roiWidget._save_widget.setHidden(True)

        # Hide X/Y origin
        label_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.LabelRole)
        field_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.FieldRole)

        # Hide the items
        if label_item is not None:
            label_item.widget().setHidden(True)
        if field_item is not None:
            field_item.widget().setHidden(True)

        self.layout().addWidget(self.roiWidget)

    @override
    def process(self, gdt: GlobalDetectionToken):
        sequence: Sequence = gdt.inputComputationSequence

        masks = PreFiltering.get_mask(sequence=sequence)
        masks = masks.reshape(
            sequence.getAllImage().shape)
        image: np.ndarray = sequence.getAllImage()
        enh_img: np.ndarray = image * masks.reshape(sequence.getAllImage().shape)  # Filtering Image using Mask

        return PreFiltering.save_image_layer(sequence=sequence, enh_img=enh_img)


# class InhomogeneityCorrectionFiltering(PreFiltering):
#     def __init__(self, *args, **kwargs) -> None:
#         super().__init__()
#
#         self.setLayout(QVBoxLayout())
#         # self.iterLabel: QLabel = QLabel("Maximum Number of Iteration")
#         # self.iterSpinBox: QSpinBox = QSpinBox()
#         # self.iterSpinBox.setMinimum(1)
#         # self.iterSpinBox.setMaximum(1000)
#         # self.iterSpinBox.setValue(500)
#
#         label = QLabel("Spline Order")
#         self.splineOrderSpinBox: QSpinBox = QSpinBox()
#         self.splineOrderSpinBox.setMinimum(1)
#         self.splineOrderSpinBox.setMaximum(50)
#         self.splineOrderSpinBox.setValue(3)
#
#         label1 = QLabel("Wiener Filter Noise")
#         self.WienerFilterNoiseSpinBox: QDoubleSpinBox = QDoubleSpinBox()
#         self.WienerFilterNoiseSpinBox.setMinimum(0)
#         self.WienerFilterNoiseSpinBox.setMaximum(1)
#         self.WienerFilterNoiseSpinBox.setValue(0.01)
#
#         label2 = QLabel("Bias Field")
#         self.biasFieldSpinBox: QDoubleSpinBox = QDoubleSpinBox()
#         self.biasFieldSpinBox.setMinimum(0)
#         self.biasFieldSpinBox.setMaximum(1)
#         self.biasFieldSpinBox.setValue(0.15)
#
#         label3 = QLabel("Number of Control Points")
#         self.numControlPointsSpinBox: QSpinBox = QSpinBox()
#         self.numControlPointsSpinBox.setMinimum(1)
#         self.numControlPointsSpinBox.setMaximum(50)
#         self.numControlPointsSpinBox.setValue(4)
#
#         # self.layout().addWidget(self.iterSpinBox)
#         self.layout().addWidget(label)
#         self.layout().addWidget(self.splineOrderSpinBox)
#         self.layout().addWidget(label1)
#         self.layout().addWidget(self.WienerFilterNoiseSpinBox)
#         self.layout().addWidget(label2)
#         self.layout().addWidget(self.biasFieldSpinBox)
#         self.layout().addWidget(label3)
#         self.layout().addWidget(self.numControlPointsSpinBox)
#     @override
#     def process(self, gdt: GlobalDetectionToken):
#         sequence: Sequence = gdt.inputComputationSequence
#
#         data = sequence.getAllImage()
#
#         image = sitk.GetImageFromArray(data, isVector=False)
#         if sequence.aicsimage is not None:
#             z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
#                 sequence.aicsimage.physical_pixel_sizes.Z)
#             y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
#                 sequence.aicsimage.physical_pixel_sizes.Y)
#             x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
#                 sequence.aicsimage.physical_pixel_sizes.X)
#         else:
#             z = 1
#             y = 1
#             x = 1
#         image.SetSpacing([z, y, x])
#         # Apply N4 correction
#         corrector = sitk.N4BiasFieldCorrectionImageFilter()
#         # corrector.SetMaximumNumberOfIterations(self.iterSpinBox.value())
#         # corrector.SetNumberOfControlPoints(self.numControlPointsSpinBox.value())
#         # corrector.SetBiasFieldFullWidthAtHalfMaximum(self.biasFieldSpinBox.value())
#         # corrector.SetSplineOrder(self.splineOrderSpinBox.value())
#         # corrector.SetWienerFilterNoise(self.WienerFilterNoiseSpinBox.value())
#
#         corrector.SetNumberOfThreads(os.cpu_count() - 1)
#         # corrector.SetNumberOfHistogramBins(int(data.max() - data.min() + 1)) # Might need check for non zero mininum
#         output_image = corrector.Execute(image)
#
#         output = sitk.GetArrayFromImage(output_image)
#
#         return PreFiltering.save_image_layer(sequence=sequence, enh_img=output)

class ThresholdIntensityFiltering(PreFiltering):
    QSS = """


    QSlider::groove:horizontal {
        border: 0px;
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #888, stop:1 #ddd);
        height: 15px;
    }

    QSlider::handle {
        background: qradialgradient(cx:0, cy:0, radius: 1.2, fx:0.35,
                                    fy:0.3, stop:0 #eef, stop:1 #002);
        width: 10px;
    }

    QRangeSlider {
        qproperty-barColor: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #227, stop:1 #77a);
    }
    """
    threshold_layer = "Threshold Intensity Layer"

    def __init__(self, input_box: Union[None, QComboBox] = None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.layout = QVBoxLayout()
        layout_min_max = QHBoxLayout()
        widget_min_max = QWidget()
        widget_min_max.setLayout(layout_min_max)

        layout_slider = QHBoxLayout()

        self.input_box: QComboBox = input_box

        self.minSpinbox = QDoubleSpinBox()
        self.minSpinbox_label = QLabel("Minimum Intensity")
        self.maxSpinbox = QDoubleSpinBox()
        self.maxSpinbox_label = QLabel("Maximum Intensity")
        self.minSpinbox.setMaximum(999)
        self.minSpinbox.setMinimum(0)
        self.maxSpinbox.setMaximum(1000)
        self.maxSpinbox.setMinimum(1)

        self.range_slider = QRangeSlider(QtCore.Qt.Horizontal)
        # self.range_slider = QLabeledRangeSlider(QtCore.Qt.Horizontal)

        # self.range_slider.setMinimum(0)
        # self.range_slider.setMaximum(256)
        self.range_slider.setStyleSheet(ThresholdIntensityFiltering.QSS)
        self.range_slider.setRange(min=0, max_=1000)
        self.range_slider.setValue((0, 256))

        # self.range_slider.setMinimumSize(15, 15)
        # self.range_slider.setMaximumSize(16, 16)

        # self.range_slider.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        self.minSpinbox.setValue(self.range_slider.value()[0])
        self.maxSpinbox.setValue(self.range_slider.value()[1])

        self.range_slider.valueChanged.connect(self.update_threshold)
        # self.minSpinbox.valueChanged.connect(self.update_range_min)
        # self.maxSpinbox.valueChanged.connect(self.update_range_max)

        # "Single Compute Threshold (Min & Max)"
        self.computeThresholdGroup: QGroupBox = QGroupBox("Compute Thresholds")
        self.computeThresholdGroup.setLayout(QVBoxLayout())

        self.computeSingleThresholdGroup: QGroupBox = QGroupBox("Single Compute Threshold (Min & Max)")
        self.computeSingleThresholdGroup.setLayout(QVBoxLayout())
        self.computeSingleThresholdGroup.setCheckable(False)
        # self.computeSingleThresholdGroup.setChecked(True)
        # self.computeSingleThresholdGroup.setEnabled(True)
        # self.computeSingleThresholdGroup.clicked.connect(self._enable_single_multiple_checkbox)
        self.computeThresholdGroup.layout().addWidget(self.computeSingleThresholdGroup)
        self.comboComputeThresholdMethod: QComboBox = QComboBox()
        self.computeSingleThresholdGroup.layout().addWidget(self.comboComputeThresholdMethod)
        self.comboComputeThresholdMethod.addItem("Otsu", 1)
        self.comboComputeThresholdMethod.addItem("Kittler", 2)
        self.comboComputeThresholdMethod.addItem("Huang", 3)
        # self.comboComputeThresholdMethod.addItem("Pun", 3)
        # self.comboComputeThresholdMethod.addItem("Reddi", 4)

        # self.computeMultiThresholdGroup: QGroupBox = QGroupBox("Multiple Compute Threshold (Max)")
        # self.computeMultiThresholdGroup.setLayout(QVBoxLayout())
        # self.computeMultiThresholdGroup.setCheckable(True)
        # self.computeMultiThresholdGroup.setChecked(False)
        # self.computeMultiThresholdGroup.setEnabled(False)
        # self.computeMultiThresholdGroup.clicked.connect(self._enable_single_multiple_checkbox)
        # self.computeThresholdGroup.layout().addWidget(self.computeMultiThresholdGroup)
        # self.comboMultiThresholdMethod: QComboBox = QComboBox()
        # self.computeMultiThresholdGroup.layout().addWidget(self.comboMultiThresholdMethod)
        # self.comboMultiThresholdMethod.addItem("ComputeMultipleThreshold", 1)
        # self.comboMultiThresholdMethod.addItem("ComputeMultipleOtsuThreshold", 2)

        # self.spinBoxMultiThreshold: QSpinBox = QSpinBox()

        # self.computeMultiThresholdGroup.layout().addWidget(self.spinBoxMultiThreshold)
        # self.spinBoxMultiThreshold.setRange(2, 8)

        self.groupMinComputeThreshold: QGroupBox = QGroupBox("Min")
        self.groupMinComputeThreshold.setLayout(QHBoxLayout())
        self.groupMinComputeThreshold.setCheckable(True)
        self.groupMinComputeThreshold.setChecked(True)

        self.groupMaxComputeThreshold: QGroupBox = QGroupBox("Max")
        self.groupMaxComputeThreshold.setLayout(QHBoxLayout())
        self.groupMaxComputeThreshold.setCheckable(True)
        self.groupMaxComputeThreshold.setChecked(True)

        self.comboMinComputeThreshold: QComboBox = QComboBox()
        self.groupMinComputeThreshold.layout().addWidget(self.comboMinComputeThreshold)
        self.comboMaxComputeThreshold: QComboBox = QComboBox()
        self.groupMaxComputeThreshold.layout().addWidget(self.comboMaxComputeThreshold)

        layout_min_max_group = QHBoxLayout()
        layout_min_max_group.addWidget(self.groupMinComputeThreshold)
        layout_min_max_group.addWidget(self.groupMaxComputeThreshold)
        min_max_widget = QWidget()
        min_max_widget.setLayout(layout_min_max_group)

        # self.computeMultiThresholdGroup.layout().addWidget(min_max_widget)
        # self.computeMultiThresholdGroup.layout().addWidget(self.groupMinComputeThreshold)
        # self.computeMultiThresholdGroup.layout().addWidget(self.groupMaxComputeThreshold)

        self.buttonComputeThreshold: QPushButton = QPushButton("Set")
        self.buttonComputeThreshold.clicked.connect(self.set_threshold)
        self.computeThresholdGroup.layout().addWidget(self.buttonComputeThreshold)

        # layout_min_max.addWidget(self.minSpinbox_label)
        # layout_min_max.addWidget(self.minSpinbox)
        # self.layout.addWidget(widget_min_max)
        # self.layout.addWidget(self.range_slider, 6)

        layout_slider.addWidget(self.minSpinbox, 1)
        layout_slider.addWidget(self.range_slider, 6)
        layout_slider.addWidget(self.maxSpinbox, 1)

        self.layout.addLayout(layout_slider, 9)
        self.layout.addWidget(self.computeThresholdGroup, 1)

        # layout_min_max.addWidget(self.maxSpinbox_label)
        # layout_min_max.addWidget(self.maxSpinbox)

        # Add Function for Change for DropDown (Not being used at the moment)
        if self.input_box is not None:
            self.on_process_text_changed(self.input_box.currentText())
            self.input_box.currentTextChanged.connect(self.on_process_text_changed)

        self.setLayout(self.layout)

        # Library
        # if os.name == 'nt':
        #     ext = 'dll'
        # elif platform.system() == 'Darwin':
        #     ext = 'dylib'
        # else:
        #     ext = 'so'

        # data_path = os.path.join(os.path.dirname(__file__), 'Library', f'threshold.{ext}')

        # self.clib = ctypes.cdll.LoadLibrary(data_path)
        #
        # self.clib.ComputeBinaryThresholds.argtypes = [
        #     ctypes.c_int32, ctypes.c_int32, ctypes.c_int32, ctypes.c_int32,
        #     np.ctypeslib.ndpointer(dtype=np.float64, ndim=1, flags='C_CONTIGUOUS'), ctypes.c_int32,
        #     ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double)
        # ]
        # self.clib.ComputeBinaryThresholds.restype = ctypes.c_void_p
        #
        # self.clib.ComputeMultipleThresholds.argtypes = [
        #     ctypes.c_int32, ctypes.c_int32, ctypes.c_int32, ctypes.c_int32,
        #     np.ctypeslib.ndpointer(dtype=np.float64, ndim=1, flags='C_CONTIGUOUS'), ctypes.c_int32,
        #     np.ctypeslib.ndpointer(dtype=np.float64, ndim=1, flags='C_CONTIGUOUS')
        # ]
        # self.clib.ComputeMultipleThresholds.restype = ctypes.c_void_p

        # self.comboMultiThresholdMethod.currentTextChanged.connect(self.update_threshold_components)
        # self.spinBoxMultiThreshold.valueChanged.connect(self.update_threshold_components)

    def _enable_single_multiple_checkbox(self, box):
        sender = self.sender()
        if sender == self.computeSingleThresholdGroup and (not box):
            self.computeMultiThresholdGroup.setChecked(True)
            self.computeSingleThresholdGroup.setChecked(False)
            self.update_threshold_components("")
        elif sender == self.computeSingleThresholdGroup and box:
            self.computeMultiThresholdGroup.setChecked(False)

        if sender == self.computeMultiThresholdGroup and (not box):
            self.computeSingleThresholdGroup.setChecked(True)
            self.computeMultiThresholdGroup.setChecked(False)
        elif sender == self.computeMultiThresholdGroup and box:
            self.computeSingleThresholdGroup.setChecked(False)
            self.update_threshold_components("")

    def set_threshold(self):
        try:
            self.reconnect(self.range_slider.valueChanged)
            self.reconnect(self.minSpinbox.valueChanged)
            self.reconnect(self.maxSpinbox.valueChanged)

            sequence: Sequence = Sequence(self.input_box.currentText(), self.viewer)
            data = sequence.getAllImage(dtype=sequence.image.dtype, keep_three_dim=True)
            # set step [expr ($::DataMax-$::DataMin)/1000.]
            step = 1

            threshold = (self.minSpinbox.value(), self.maxSpinbox.value())
            # t = threshold[0]

            # if self.computeSingleThresholdGroup.isChecked():
            # Maybe use skimage.exposure.histogram
            # hist, bin_edges = np.histogram(data, np.arange(data.min(), data.max() + 1, 1))

            # value_1 = ctypes.c_double(data.min())
            # ptr = ctypes.pointer(value_1)
            #
            # value_2 = ctypes.c_double(data.max())
            # ptr2 = ctypes.pointer(value_2)
            filterITK = None
            image = self.getImage(sequence)
            if self.comboComputeThresholdMethod.currentData() == 1:

                filterITK = sitk.OtsuThresholdImageFilter()

                # otsu = sitk.OtsuThreshold(data, value_1, value_2, returnBinMidpoint=False)
                # Image image, uint8_t insideValue=1, uint8_t outsideValue=0, uint32_t numberOfHistogramBins=128, bool maskOutput=True, uint8_t maskValue=255, bool returnBinMidpoint=False

            elif self.comboComputeThresholdMethod.currentData() == 2:
                filterITK = sitk.KittlerIllingworthThresholdImageFilter()

            elif self.comboComputeThresholdMethod.currentData() == 3:
                from .huang_thresholding import HuangThresholding  # https://github.com/dnhkng/Huang-Thresholding
                hist, bin_edges = np.histogram(data, np.arange(data.min(), data.max() + 1, 1))
                thresholding = HuangThresholding(hist)
                t = thresholding.find_threshold()
                threshold = np.array([t, data.max()])

            if filterITK != None:
                # print("Threshold Option not Available")

                filterITK.SetNumberOfHistogramBins(int(data.max() - data[np.nonzero(data)].min() + 1))
                # otsu_filter.SetReturnBinMidpoint(True)
                # otsu_filter.SetNumberOfThresholds(int(self.spinBoxMultiThreshold.text()))
                filterITK.SetNumberOfThreads(os.cpu_count() - 1)
                _ = filterITK.Execute(image)

                t = filterITK.GetThreshold()
                threshold = np.array([t, data.max()])
                # len(bin_edges[1:]), bin_edges[1:].astype(np.float64),
                # self.clib.ComputeBinaryThresholds(data.min(), data.max(), step, len(hist), hist.astype(np.float64),
                #                                   self.comboComputeThresholdMethod.currentData(), ptr, ptr2)
                #
                # threshold = (value_2.value, value_1.value)
                # self.update_threshold((value_2.value, value_1.value))
                # self.range_slider.setValue((value_2.value, value_1.value))
                # self.range_slider.update()

            # if self.computeMultiThresholdGroup.isChecked():
            #     if self.comboMinComputeThreshold.currentText() != '' and self.groupMinComputeThreshold.isChecked():
            #         t1 = float(self.comboMinComputeThreshold.currentText())
            #     else:
            #         t1 = self.minSpinbox.value()
            #     if self.comboMaxComputeThreshold.currentText() != '' and self.groupMaxComputeThreshold.isChecked():
            #         t2 = float(self.comboMaxComputeThreshold.currentText())
            #     else:
            #         t2 = self.maxSpinbox.value()
            #
            #     threshold = (t1, t2)
            # thresholds = np.zeros(int(self.spinBoxMultiThreshold.text()), dtype=np.float64)
            # self.clib.ComputeMultipleThresholds(data.min(), data.max(), step, len(hist),
            #                                     hist.astype(np.float64),
            #                                     int(self.spinBoxMultiThreshold.text()), thresholds)
            # print(thresholds)

            self.update_threshold(threshold)
            self.range_slider.setValue(threshold)
            self.range_slider.update()
        except Exception as e:
            pass
        finally:
            self.reconnect(self.range_slider.valueChanged, newhandler=self.update_threshold)
            self.reconnect(self.minSpinbox.valueChanged, newhandler=self.update_range_min)
            self.reconnect(self.maxSpinbox.valueChanged, newhandler=self.update_range_max)

    def update_threshold_components(self, text):
        sequence: Sequence = Sequence(self.input_box.currentText(), self.viewer)
        data = sequence.getAllImage(dtype=sequence.image.dtype, keep_three_dim=True)
        # set step [expr ($::DataMax-$::DataMin)/1000.]
        step = 1

        thresholds = np.zeros(int(self.spinBoxMultiThreshold.text()), dtype=np.float64)
        if self.computeMultiThresholdGroup.isChecked() and self.comboMultiThresholdMethod.currentData() == 1:
            # Maybe use skimage.exposure.histogram
            hist, bin_edges = np.histogram(data, np.arange(data.min(), data.max() + 1, 1))
            self.clib.ComputeMultipleThresholds(data.min(), data.max(), step, len(hist),
                                                hist.astype(np.float64),
                                                int(self.spinBoxMultiThreshold.text()), thresholds)

        if self.computeMultiThresholdGroup.isChecked() and self.comboMultiThresholdMethod.currentData() == 2:

            image = sitk.GetImageFromArray(data, isVector=False)
            if sequence.aicsimage is not None:
                z = 1 if sequence.aicsimage.physical_pixel_sizes.Z is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.Z)
                y = 1 if sequence.aicsimage.physical_pixel_sizes.Y is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.Y)
                x = 1 if sequence.aicsimage.physical_pixel_sizes.X is None else abs(
                    sequence.aicsimage.physical_pixel_sizes.X)
            else:
                z = 1
                y = 1
                x = 1
            image.SetSpacing([z, y, x])
            # sitk.OtsuMultipleThresholds(image1=image, numberOfThresholds=int(self.spinBoxMultiThreshold.text()),
            #                             numberOfHistogramBins=len(hist))
            # Set up the Otsu multiple thresholds filter
            otsu_filter = sitk.OtsuMultipleThresholdsImageFilter()
            otsu_filter.SetNumberOfHistogramBins(int(data.max() - data.min() + 1))
            otsu_filter.SetNumberOfThresholds(int(self.spinBoxMultiThreshold.text()))
            otsu_filter.SetNumberOfThreads(os.cpu_count() - 1)

            output_image = otsu_filter.Execute(image1=image)

            t = otsu_filter.GetThresholds()
            thresholds = np.array(t)

        self.comboMinComputeThreshold.clear()
        self.comboMaxComputeThreshold.clear()
        self.comboMinComputeThreshold.addItems(thresholds.astype(str).tolist())
        self.comboMaxComputeThreshold.addItems(thresholds.astype(str).tolist())
        self.comboMinComputeThreshold.setCurrentIndex(0)
        self.comboMaxComputeThreshold.setCurrentIndex(1)

    @override
    def process(self, gdt: GlobalDetectionToken):
        sequence: Sequence = gdt.inputComputationSequence

        data = sequence.getAllImage(dtype=sequence.image.dtype)
        data[~self.viewer.layers[ThresholdIntensityFiltering.threshold_layer].data.astype(bool)] = 0

        return PreFiltering.save_image_layer(sequence=sequence, enh_img=data)

    @override
    def on_process_text_changed(self, text: Union[str, slice]):
        try:
            if text != '':
                try:
                    self.layer: Labels = self.viewer.layers[ThresholdIntensityFiltering.threshold_layer]
                except Exception as e:
                    self.layer: Labels = self.viewer.add_labels(np.zeros(self.viewer.layers[text].data.shape,
                                                                         dtype=np.uint8),
                                                                name=ThresholdIntensityFiltering.threshold_layer,
                                                                scale=self.viewer.layers[text].scale)

                if isinstance(self.viewer.layers[text].data, xarray.DataArray):
                    minimum = self.viewer.layers[text].data.values.min()
                    maximum = self.viewer.layers[text].data.values.max()
                    # self.update_threshold((self.viewer.layers[text].data.values.min(),
                    #                        self.viewer.layers[text].data.values.max()))
                    # self.range_slider.setMaximum(self.viewer.layers[text].data.values.min())
                    # self.range_slider.setMaximum(self.viewer.layers[text].data.values.max())
                else:
                    minimum = self.viewer.layers[text].data.min()
                    maximum = self.viewer.layers[text].data.max()
                    # self.update_threshold((self.viewer.layers[text].data.min(), self.viewer.layers[text].data.max()))
                    # self.range_slider.setMaximum(self.viewer.layers[text].data.min())
                    # self.range_slider.setMaximum(self.viewer.layers[text].data.max())

                self.reconnect(self.range_slider.valueChanged)
                self.reconnect(self.minSpinbox.valueChanged)
                self.reconnect(self.maxSpinbox.valueChanged)
                # self.range_slider.valueChanged.disconnect()
                # self.minSpinbox.valueChanged.disconnect()
                # self.maxSpinbox.valueChanged.disconnect()

                self.minSpinbox.setMaximum(maximum - 1)
                self.maxSpinbox.setMaximum(maximum)
                self.range_slider.setRange(min(0, minimum), maximum)
                self.range_slider.setValue((minimum, maximum))
                self.update_threshold((minimum, maximum))

                # self.update_range_min()
                # self.update_range_max()

                # self.range_slider.valueChanged.connect(self.update_threshold)
                # self.minSpinbox.valueChanged.connect(self.update_range_min)
                # self.maxSpinbox.valueChanged.connect(self.update_range_max)


        except Exception as e:
            print(e)
            traceback.print_stack()
            ErrorNotification(e)
        finally:
            self.reconnect(self.range_slider.valueChanged, newhandler=self.update_threshold)
            self.reconnect(self.minSpinbox.valueChanged, newhandler=self.update_range_min)
            self.reconnect(self.maxSpinbox.valueChanged, newhandler=self.update_range_max)
            # self.range_slider.valueChanged.connect(self.update_threshold)
            # self.minSpinbox.valueChanged.connect(self.update_range_min)
            # self.maxSpinbox.valueChanged.connect(self.update_range_max)

    def reconnect(self, signal, newhandler=None, oldhandler=None):
        try:
            if oldhandler is not None:
                while True:
                    signal.disconnect(oldhandler)
            else:
                signal.disconnect()
        except Exception as e:
            pass
        if newhandler is not None:
            signal.connect(newhandler)

    def update_range_min(self, value):
        v = list(self.range_slider.value())
        # v[0] = self.minSpinbox.value()
        v[0] = value
        self.range_slider.setValue(v)
        self.update_threshold((v[0], v[1]))

    def update_range_max(self, value):
        v = list(self.range_slider.value())
        # v[1] = self.maxSpinbox.value()
        v[1] = value
        self.range_slider.setValue(v)
        self.update_threshold((v[0], v[1]))

    def update_threshold(self, thresholds: Tuple[float, float]):
        self.reconnect(self.minSpinbox.valueChanged)
        self.reconnect(self.maxSpinbox.valueChanged)
        # self.minSpinbox.valueChanged.disconnect()
        # self.maxSpinbox.valueChanged.disconnect()
        self.minSpinbox.setValue(thresholds[0])
        self.maxSpinbox.setValue(thresholds[1])

        # print("Min", self.minSpinbox.value(), "Max", self.maxSpinbox.value())
        # print(thresholds)

        try:
            condition1 = self.viewer.layers[self.input_box.currentText()].data >= thresholds[0]
            condition2 = self.viewer.layers[self.input_box.currentText()].data <= thresholds[1]
            condition = condition1 & condition2

            # print(np.unique(condition1, return_counts=True))
            # print(np.unique(condition2, return_counts=True))
            # print(np.unique(condition, return_counts=True))

            # print("First", np.unique(self.viewer.layers[ThresholdIntensityFiltering.threshold_layer].data, return_counts=True))
            modified_data = np.where(condition, 1,
                                     np.zeros(self.viewer.layers[ThresholdIntensityFiltering.threshold_layer]
                                              .data.shape,
                                              dtype=np.uint8))
            self.viewer.layers[ThresholdIntensityFiltering.threshold_layer].data = modified_data
            # self.viewer.layers[ThresholdIntensityFiltering.threshold_layer].data[condition1 & condition2] = 1
            # print("Second", np.unique(self.viewer.layers[ThresholdIntensityFiltering.threshold_layer].data, return_counts=True))
        except Exception as e:
            print(e)
        finally:
            self.reconnect(self.minSpinbox.valueChanged, newhandler=self.update_range_min)
            self.reconnect(self.maxSpinbox.valueChanged, newhandler=self.update_range_max)
            # self.minSpinbox.valueChanged.connect(self.update_range_min)
            # self.maxSpinbox.valueChanged.connect(self.update_range_max)
