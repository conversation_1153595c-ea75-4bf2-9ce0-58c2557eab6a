from typing import <PERSON>ple
import numpy as np
import pandas as pd

from overrides import override

from ufish.api import UFish

class UFishFilter(UFish):
    def __init__(self):
        super().__init__()
        self.load_weights()
        # print(self._cuda)

    @override
    def _pred_2d_or_3d(self, img: np.ndarray, axes: str, blend_3d: bool = False, batch_size: int = 4,
                       spots_calling_method='', **kwargs) -> \
            Tuple[pd.DataFrame, np.ndarray]:
        """Predict the spots in a 2D or 3D image. """
        assert img.ndim in (2, 3), 'Image must be 2D or 3D.'
        assert len(axes) == img.ndim, \
            "axes and image dimension must have the same length"
        enhanced_img = self._enhance_2d_or_3d(
            img, axes,
            batch_size=batch_size,
            blend_3d=(blend_3d and ('z' in axes))
        )

        # Temporary Dataframe for spot function that's not being used
        ndim = img.ndim
        columns = [f'axis-{i}' for i in range(ndim)]

        df = pd.DataFrame(columns=columns)

        return df, enhanced_img

    # @override
    # def _enhance_2d_or_3d(self, img: np.ndarray, axes: str, batch_size: int = 4, blend_3d: bool = False) -> np.ndarray:
    #     enhanced_img: np.ndarray = super()._enhance_2d_or_3d(img=img, axes=axes,
    #                                                          batch_size=batch_size, blend_3d=blend_3d)
    #     return enhanced_img.astype(np.uint8)
    #
    # @override
    # def _enhance_img2d(self, img: np.ndarray) -> np.ndarray:
    #     enhanced_img: np.ndarray = super()._enhance_img2d(img=img)
    #
    #     return enhanced_img.astype(np.uint8)

    # @override
    # def _infer_onnx(self, img: np.ndarray) -> np.ndarray:
    #     """Infer the image using the ONNX model."""
    #     ort_inputs = {self.ort_session.get_inputs()[0].name: img.astype(np.float32)}
    #     ort_outs = self.ort_session.run(None, ort_inputs)
    #     output = ort_outs[0]
    #     return output
