[build-system]
requires = ["setuptools", "wheel", "setuptools_scm"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 79

[tool.isort]
profile = "black"
line_length = 79

[project]
name = "napari-senoquant-spot-detector"
dynamic = ["version", "license", "classifiers", "entry-points", "dependencies", "optional-dependencies"]
description = "description"
readme = "README.md"
requires-python = ">=3.9,<3.12"

authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]