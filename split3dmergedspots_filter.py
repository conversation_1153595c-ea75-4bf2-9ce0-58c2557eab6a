import numpy as np
from scipy import ndimage
from skimage.segmentation import watershed
from skimage.feature import peak_local_max
from skimage.measure import regionprops
from joblib import Parallel, delayed
import time

def auto_split_merged_spots_optimized(
    labels, 
    image=None,
    n_jobs=-1,                 # Number of parallel jobs (-1 for all cores)
    small_object_ratio=0.3,    # Objects smaller than this * median_area are skipped
    elongation_threshold=1.8,  # Objects with elongation > this are considered for splitting
    area_ratio_threshold=1.4,  # Objects larger than this * median_area are considered for splitting
    peak_distance_factor=0.3,  # Controls distance between peaks (smaller = more peaks)
    peak_threshold_elongated=0.2,  # Intensity threshold for elongated objects (lower = more peaks)
    peak_threshold_regular=0.4,    # Intensity threshold for regular objects (lower = more peaks)
    max_peaks=5,               # Maximum number of peaks to detect per object
    verbose=True               # Whether to print progress information
):
    """
    Highly optimized version to split merged spots while preserving all single spots.
    
    Parameters:
    - labels: Label image from StarDist (xarray or numpy)
    - image: Optional raw intensity image (xarray or numpy)
    - n_jobs: Number of parallel jobs (-1 for all cores)
    - small_object_ratio: Skip objects smaller than this * median_area
    - elongation_threshold: Minimum elongation to consider for splitting
    - area_ratio_threshold: Consider objects larger than this * median_area for splitting
    - peak_distance_factor: Factor controlling minimum distance between peaks
    - peak_threshold_elongated: Relative threshold for peak detection in elongated objects
    - peak_threshold_regular: Relative threshold for peak detection in regular objects
    - max_peaks: Maximum number of peaks to detect per object
    - verbose: Whether to print progress information
    
    Returns:
    - Corrected label image with same format as input, all single spots preserved
    """
    start_time = time.time()
    
    # Check if input is xarray and preserve metadata
    is_xarray = hasattr(labels, 'values')
    xarray_metadata = None
    
    if is_xarray:
        # Store metadata for later reconstruction
        xarray_metadata = {
            'dims': labels.dims,
            'coords': labels.coords,
            'attrs': labels.attrs
        }
        if verbose:
            print("Input is xarray DataArray, metadata will be preserved.")
        labels_np = labels.values
    else:
        labels_np = np.asarray(labels)
        
    # Handle image data similarly
    if image is not None:
        if hasattr(image, 'values'):
            image_np = image.values
        else:
            image_np = np.asarray(image)
    else:
        image_np = None
    
    if verbose:
        print("Converting data to numpy arrays...")
    
    # Create result array by copying all original labels - this ensures ALL objects are preserved
    result = np.copy(labels_np)
    max_label = np.max(labels_np)
    if max_label == 0:
        if verbose:
            print("No objects found in label image.")
        
        # Return in the same format as input
        if is_xarray:
            import xarray as xr
            return xr.DataArray(
                result,
                dims=xarray_metadata['dims'],
                coords=xarray_metadata['coords'],
                attrs=xarray_metadata['attrs']
            )
        return result
    
    if verbose:
        print(f"Analyzing {max_label} objects...")
        
    # Get region properties - do this once
    props = regionprops(labels_np)
    
    # Pre-calculate statistics for parameter tuning
    areas = np.array([prop.area for prop in props])
    median_area = np.median(areas) if len(areas) > 0 else 100
    
    # Pre-filter objects that are likely merged to save processing time
    process_ids = []
    elongations = []
    bboxes = []
    
    if verbose:
        print("Pre-filtering objects likely to be merged...")
        
    for i, prop in enumerate(props):
        label_id = i + 1
        
        # Skip tiny objects from processing (but they're preserved in the result)
        if prop.area < median_area * small_object_ratio:
            continue
            
        # Calculate elongation
        if min(prop.inertia_tensor_eigvals) > 0:
            elong = max(prop.inertia_tensor_eigvals) / min(prop.inertia_tensor_eigvals)
        else:
            elong = 100
        
        # Only process objects that are possibly merged
        if elong > elongation_threshold or prop.area > median_area * area_ratio_threshold:
            process_ids.append(label_id)
            elongations.append(elong)
            bboxes.append(prop.bbox)
    
    if verbose:
        print(f"Processing {len(process_ids)} candidate objects for splitting...")
        print(f"Skipping {max_label - len(process_ids)} objects (kept intact as single spots)...")
    
    # Process objects in chunks for better memory efficiency
    def process_object(idx):
        label_id = process_ids[idx]
        elongation = elongations[idx]
        bbox = bboxes[idx]
        
        # Create slices for the bounding box
        if labels_np.ndim == 3:
            slices = (
                slice(bbox[0], bbox[3]),
                slice(bbox[1], bbox[4]),
                slice(bbox[2], bbox[5])
            )
        else:
            slices = (
                slice(bbox[0], bbox[2]),
                slice(bbox[1], bbox[3])
            )
            
        # Extract sub-region (much more efficient than working with whole volume)
        sub_labels = labels_np[slices]
        mask = sub_labels == label_id
        
        # Skip if mask is empty
        if not np.any(mask):
            return label_id, None
        
        # Distance transform on the sub-region
        distance = ndimage.distance_transform_edt(mask)
        
        # Prepare seed image for peak detection
        seed_image = distance
        if image_np is not None:
            sub_image = image_np[slices]
            img_masked = np.zeros_like(sub_image, dtype=float)
            img_masked[mask] = sub_image[mask]
            seed_image = img_masked
        
        # Adaptive parameters based on object properties
        adaptive_min_distance = max(2, int((areas[label_id-1]**(1/3)) * peak_distance_factor))
        threshold = peak_threshold_elongated if elongation > elongation_threshold else peak_threshold_regular
        
        # Find peaks efficiently
        local_max = peak_local_max(
            seed_image,
            min_distance=adaptive_min_distance,
            threshold_rel=threshold,
            labels=mask,
            num_peaks=max_peaks
        )
        
        # Check if we found enough peaks to split
        if len(local_max) <= 1:
            return label_id, None  # Keep as single spot
            
        # Create markers and run watershed
        markers = np.zeros_like(mask, dtype=np.int32)
        markers[tuple(local_max.T)] = np.arange(1, len(local_max) + 1)
        split_mask = watershed(-distance, markers, mask=mask)
        
        # Check if watershed actually split anything
        if np.max(split_mask) <= 1:
            return label_id, None
            
        # Return the results we need: label ID, bounding box, and the split mask
        return label_id, (slices, split_mask)
    
    # Process in parallel chunks if we have candidates
    if len(process_ids) > 0:
        try:
            if verbose:
                print(f"Running parallel processing with {n_jobs if n_jobs > 0 else 'all available'} jobs...")
            results = Parallel(n_jobs=n_jobs, prefer="threads")(
                delayed(process_object)(i) for i in range(len(process_ids))
            )
        except Exception as e:
            if verbose:
                print(f"Parallel processing failed ({str(e)}), switching to serial mode...")
            results = [process_object(i) for i in range(len(process_ids))]
    else:
        if verbose:
            print("No objects to split found. All objects kept as single spots.")
        results = []
    
    # Update the result with the split objects
    if verbose:
        print("Applying splits to output image...")
        
    next_label_id = max_label + 1
    splits_applied = 0
    objects_modified = set()
    
    for label_id, split_data in results:
        if split_data is None:
            continue
            
        slices, split_mask = split_data
        objects_modified.add(label_id)
        
        # Keep first segment with original ID, updating only split segments
        for i in range(2, np.max(split_mask) + 1):  # Start from 2 to keep first segment with original ID
            result[slices][split_mask == i] = next_label_id
            next_label_id += 1
            splits_applied += 1
    
    elapsed_time = time.time() - start_time
    if verbose:
        print(f"Splitting complete! Applied {splits_applied} splits to {len(objects_modified)} objects in {elapsed_time:.2f} seconds.")
        print(f"Preserved {max_label - len(objects_modified)} objects as single spots.")
    
    # Return in the same format as input
    if is_xarray:
        try:
            import xarray as xr
            result_xarray = xr.DataArray(
                result,
                dims=xarray_metadata['dims'],
                coords=xarray_metadata['coords'],
                attrs=xarray_metadata['attrs']
            )
            return result_xarray
        except Exception as e:
            if verbose:
                print(f"Failed to convert back to xarray: {str(e)}. Returning numpy array instead.")
            return result
    else:
        return result

# Usage in napari
def apply_optimized_split(viewer, label_layer_name, image_layer_name=None, **kwargs):
    """Apply optimized splitting to a napari label layer.
    
    Args:
        viewer: napari viewer instance
        label_layer_name: name of the label layer to process
        image_layer_name: optional name of the intensity image layer
        **kwargs: additional parameters to pass to auto_split_merged_spots_optimized
    
    Returns:
        The processed label image
    """
    # Get data from the layers
    print(f"Processing label layer: {label_layer_name}")
    labels_data = viewer.layers[label_layer_name].data
    
    # Get scale from original layer to apply to result
    original_scale = getattr(viewer.layers[label_layer_name], 'scale', None)
    
    image_data = None
    if image_layer_name and image_layer_name in viewer.layers:
        print(f"Using intensity data from: {image_layer_name}")
        image_data = viewer.layers[image_layer_name].data
    
    # Apply optimized splitting
    split_labels = auto_split_merged_spots_optimized(labels_data, image_data, **kwargs)
    
    # Add result to viewer
    new_name = f'{label_layer_name}_split'
    if new_name in viewer.layers:
        del viewer.layers[new_name]
        
    # Create new layer and ensure scale is preserved
    new_layer = viewer.add_labels(split_labels, name=new_name)
    
    # Manually apply scale if needed
    if original_scale is not None:
        new_layer.scale = original_scale
        print(f"Applied scale from original layer: {original_scale}")
    
    return split_labels


# Replace with your layer names
label_name = 'Label Components - 0 :: Series001 :: --FLUO--DFT51011 [1]'
image_name = '0 :: Series001 :: --FLUO--DFT51011 [1]'

# Run with default parameters that preserve all single spots
split_result = apply_optimized_split(viewer, label_name, image_name)




# OR run with more aggressive splitting parameters but still preserve singles
aggressive_params = {
    'elongation_threshold': 1.5,      # More sensitive to elongation
    'area_ratio_threshold': 1.2,      # More sensitive to large objects
    'peak_threshold_elongated': 0.15, # More sensitive peak detection
    'max_peaks': 8                    # Allow more splits per object
}

split_result = apply_optimized_split(
    viewer, 
    label_name, 
    image_name,
    **aggressive_params
)