{"cells": [{"cell_type": "markdown", "id": "intro-section", "metadata": {}, "source": ["# VALIS Image Alignment for Multiplex Imaging - Improved Version\n", "\n", "This notebook demonstrates how to use the VALIS (Versatile Alignment of Imaging Systems) library to align multiplex images with optimized parameters. This version fixes compatibility issues with the VALIS API."]}, {"cell_type": "code", "execution_count": 1, "id": "6207c632", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import gc\n", "import logging\n", "import numpy as np\n", "from tqdm import tqdm\n", "from tifffile import imread, imwrite\n", "from valis import registration\n", "import matplotlib.pyplot as plt\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration\n", "from skimage.transform import warp\n", "\n", "# ----------------------------------\n", "# Configuration\n", "# ----------------------------------\n", "MAIN_DIR = \"/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/\"\n", "OUTPUT_BASE = \"/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/aligned_improved/\"\n", "REFERENCE_IMG = (\n", "    \"/mnt/d/Users/<USER>/antho 4i alignment/\"\n", "    \"LB03/region R2/LB03 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\"\n", ")\n", "\n", "WARPED_DIR = os.path.join(OUTPUT_BASE, \"warped\")\n", "MULTICHANNEL_DIR = os.path.join(OUTPUT_BASE, \"multichannel\")\n", "INDIVIDUAL_DIR = os.path.join(MULTICHANNEL_DIR, \"individual\")\n", "PADDED_DIR = os.path.join(OUTPUT_BASE, \"padded\")\n", "\n", "for d in (WARPED_DIR, MULTICHANNEL_DIR, INDIVIDUAL_DIR, PADDED_DIR):\n", "    os.makedirs(d, exist_ok=True)\n", "\n", "logging.basicConfig(level=logging.INFO, format=\"%(levelname)s: %(message)s\")\n", "\n", "FILENAME_PATTERN = re.compile(r\"(?P<base>.+?)_Merged_ch(?P<ch>\\d{2})\\.tif{1,2}$\", re.IGNORECASE)"]}, {"cell_type": "markdown", "id": "params-section", "metadata": {}, "source": ["## Registration Parameters\n", "\n", "Based on the VALIS API documentation, we need to use the correct parameter names."]}, {"cell_type": "code", "execution_count": 2, "id": "params-cell", "metadata": {}, "outputs": [], "source": ["# ----------------------------------\n", "# Optimized Registration Parameters\n", "# ----------------------------------\n", "# These parameters are optimized for better alignment quality\n", "\n", "# Parameters for non-rigid registration\n", "NON_RIGID_REG_PARAMS = {\n", "    'mesh_size': 10,           # Smaller mesh for finer control\n", "    'regularization_weight': 0.1,  # Lower regularization for more flexibility\n", "    'pyramid_levels': 6,       # More pyramid levels for multi-scale alignment\n", "    'epsilon': 1e-8,\n", "    'max_iterations': 1000     # More iterations for convergence\n", "}\n", "\n", "# Parameters for micro-registration - using only valid parameters\n", "MICRO_RIGID_REGISTRAR_PARAMS = {\n", "    'scale': 0.5,            # Downsampling scale\n", "    'tile_wh': 256,            # Tile width/height\n", "    'roi': 'mask'             # Use mask for ROI\n", "}"]}, {"cell_type": "markdown", "id": "utility-section", "metadata": {}, "source": ["## Utility Functions"]}, {"cell_type": "code", "execution_count": 3, "id": "utility-cell", "metadata": {}, "outputs": [], "source": ["# ----------------------------------\n", "# Utility Functions\n", "# ----------------------------------\n", "def display_images(orig, warped, title, show=False):\n", "    if not show:\n", "        return\n", "    plt.figure(figsize=(12, 6))\n", "    for i, (img, label) in enumerate(zip([orig, warped], [\"Original\", \"Warped\"])):\n", "        plt.subplot(1, 2, i + 1)\n", "        plt.imshow(img, cmap='gray')\n", "        plt.title(f\"{label} {title}\")\n", "        plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def pad_images_to_largest(files, out_dir):\n", "    logging.info(\"Padding images to the largest size...\")\n", "    sizes = [imread(f).shape[:2] for f in files]\n", "    max_height, max_width = map(max, zip(*sizes))\n", "\n", "    for fpath in files:\n", "        img = imread(fpath)\n", "        h, w = img.shape[:2]\n", "        pad_h = (max_height - h) // 2, (max_height - h + 1) // 2\n", "        pad_w = (max_width - w) // 2, (max_width - w + 1) // 2\n", "        padded = np.pad(img, (pad_h, pad_w), mode='constant', constant_values=0)\n", "        imwrite(os.path.join(out_dir, os.path.basename(fpath)), padded)\n", "    return out_dir\n", "\n", "def visualize_alignment(img1, img2, title=\"Alignment Check\", save_path=None):\n", "    \"\"\"Visualize alignment between two images\"\"\"\n", "    # Create a composite image (red-green)\n", "    composite = np.zeros((img1.shape[0], img1.shape[1], 3), dtype=np.float32)\n", "    composite[:,:,0] = img1 / np.max(img1)  # Red channel\n", "    composite[:,:,1] = img2 / np.max(img2)  # Green channel\n", "    \n", "    plt.figure(figsize=(12, 10))\n", "    plt.imshow(composite)\n", "    plt.title(title)\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    if save_path:\n", "        plt.savefig(save_path)\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "processing-section", "metadata": {}, "source": ["## Processing Functions\n", "\n", "The main function has been fixed to use the correct parameter format for VALIS based on the API documentation."]}, {"cell_type": "code", "execution_count": 4, "id": "processing-cell", "metadata": {}, "outputs": [], "source": ["# ----------------------------------\n", "# Processing Functions\n", "# ----------------------------------\n", "def verify_registration(registrar):\n", "    \"\"\"Verify that registration completed successfully\"\"\"\n", "    print(\"Verifying registration...\")\n", "    \n", "    if registrar.non_rigid_registrar is None:\n", "        print(\"ERROR: Non-rigid registrar is None\")\n", "        return False\n", "        \n", "    if not hasattr(registrar.non_rigid_registrar, 'transforms'):\n", "        print(\"ERROR: No transforms attribute in non-rigid registrar\")\n", "        return False\n", "        \n", "    if not registrar.non_rigid_registrar.transforms:\n", "        print(\"ERROR: Empty transforms in non-rigid registrar\")\n", "        return False\n", "        \n", "    print(f\"Found {len(registrar.non_rigid_registrar.transforms)} transforms\")\n", "    return True\n", "\n", "def process_folder(path, display=False):\n", "    if not os.path.exists(REFERENCE_IMG):\n", "        logging.error(f\"Reference slide not found: {REFERENCE_IMG}\")\n", "        return\n", "\n", "    logging.info(f\"Processing folder: {path}\")\n", "    all_files = sorted(\n", "        os.path.join(path, f)\n", "        for f in os.listdir(path)\n", "        if f.lower().endswith((\".tif\", \".tiff\"))\n", "    )\n", "\n", "    dapi_files = [f for f in all_files if f.endswith(\"_Merged_ch00.tif\")]\n", "    if not dapi_files:\n", "        logging.error(\"No DAPI (ch00) files found.\")\n", "        return\n", "\n", "    padded_dir = pad_images_to_largest(all_files, PADDED_DIR)\n", "    padded_files = sorted(\n", "        os.path.join(padded_dir, f)\n", "        for f in os.listdir(padded_dir)\n", "        if f.lower().endswith((\".tif\", \".tiff\"))\n", "    )\n", "\n", "    # After padding, use the padded DAPI image as the reference\n", "    padded_dapi_files = [f for f in padded_files if f.endswith(\"_Merged_ch00.tif\")]\n", "    if not padded_dapi_files:\n", "        logging.error(\"No padded DAPI (ch00) files found.\")\n", "        return\n", "\n", "    # Use the padded DAPI image as the reference for the registration\n", "    padded_reference_img = padded_dapi_files[0]\n", "\n", "    # Initialize Valis registration with the padded images\n", "    registrar = registration.Valis(\n", "        padded_dir,\n", "        OUTPUT_BASE,\n", "        img_list=padded_dapi_files,\n", "        reference_img_f=padded_reference_img,\n", "        micro_rigid_registrar_cls=MicroRigidRegistrar,\n", "        align_to_reference=True,\n", "        non_rigid_reg_params=NON_RIGID_REG_PARAMS,\n", "        micro_rigid_registrar_params=MICRO_RIGID_REGISTRAR_PARAMS,\n", "        use_non_rigid=True  # Explicitly enable non-rigid registration\n", "    )\n", "    \n", "    print(\"Performing registration...\")\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    if not verify_registration(registrar):\n", "        logging.error(\"Registration failed verification\")\n", "        return None, None, None, None\n", "\n", "    # Store transforms in a dictionary\n", "    transforms = {}\n", "    for img_name in registrar.img_names:\n", "        try:\n", "            transform = non_rigid_registrar.get_transform(img_name)\n", "            if transform is not None:\n", "                transforms[img_name] = transform\n", "            else:\n", "                print(f\"Warning: No transform generated for {img_name}\")\n", "        except Exception as e:\n", "            print(f\"Error getting transform for {img_name}: {e}\")\n", "    \n", "    # Process all channels using the stored transforms\n", "    process_all_channels(registrar, padded_files, transforms, display)\n", "    \n", "    return registrar, rigid_registrar, non_rigid_registrar, error_df"]}, {"cell_type": "code", "execution_count": 5, "id": "channels-cell", "metadata": {}, "outputs": [], "source": ["def process_all_channels(registrar, padded_files, transforms, display=False):\n", "    \"\"\"Process all channels using the stored transforms\"\"\"\n", "    print(\"Processing all channels using stored transforms...\")\n", "    \n", "    for fpath in padded_files:\n", "        fname = os.path.basename(fpath)\n", "        match = FILENAME_PATTERN.match(fname)\n", "        if not match:\n", "            print(f\"SKIPPED (pattern): {fname}\")\n", "            continue\n", "\n", "        base, ch = match.group(\"base\"), match.group(\"ch\")\n", "        dapi_fname = f\"{base}_Merged_ch00.tif\"\n", "        \n", "        # Read the image\n", "        img = imread(fpath)\n", "        \n", "        try:\n", "            # Get the transform from our stored transforms\n", "            if dapi_fname not in transforms:\n", "                print(f\"No transform found for {dapi_fname}\")\n", "                continue\n", "                \n", "            transform = transforms[dapi_fname]\n", "            \n", "            # Apply the transform using warp function\n", "            warped = warp(\n", "                img,\n", "                transform,\n", "                output_shape=img.shape,\n", "                preserve_range=True,\n", "                mode='constant',\n", "                cval=0\n", "            ).astype(img.dtype)\n", "            \n", "            # Save the warped image\n", "            output_path = os.path.join(WARPED_DIR, fname)\n", "            imwrite(output_path, warped)\n", "            \n", "            if display:\n", "                display_images(img, warped, f\"{base}_ch{ch}\", show=True)\n", "                \n", "        except Exception as e:\n", "            print(f\"Error processing {fname}: {str(e)}\")\n", "            continue\n", "                \n", "    print(\"All channels processed.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "combine-cell", "metadata": {}, "outputs": [], "source": ["def combine_all_channels():\n", "    \"\"\"Combine all aligned channels into multichannel images\"\"\"\n", "    print(\"Combining aligned channels into multichannel images...\")\n", "    \n", "    # Group files by base name\n", "    file_groups = {}\n", "    for f in os.listdir(WARPED_DIR):\n", "        if not f.lower().endswith((\".tif\", \".tiff\")):\n", "            continue\n", "            \n", "        match = FILENAME_PATTERN.search(f)\n", "        if match:\n", "            base = match.group('base')\n", "            ch = match.group('ch')\n", "            if base not in file_groups:\n", "                file_groups[base] = []\n", "            file_groups[base].append((ch, os.path.join(WARPED_DIR, f)))\n", "    \n", "    # Process each group\n", "    for base, files in file_groups.items():\n", "        # Sort by channel\n", "        files.sort()\n", "        \n", "        # Load all channels\n", "        channels = {}\n", "        for ch, f in files:\n", "            channels[ch] = imread(f)\n", "        \n", "        # Save individual colored channels\n", "        if '00' in channels:  # DAPI (blue)\n", "            dapi = channels['00']\n", "            dapi_rgb = np.zeros((dapi.shape[0], dapi.shape[1], 3), dtype=np.uint16)\n", "            dapi_rgb[:,:,2] = dapi  # Blue channel\n", "            imwrite(os.path.join(INDIVIDUAL_DIR, f\"{base}_DAPI.tif\"), dapi_rgb)\n", "        \n", "        # Create combinations based on available channels\n", "        if len(channels) >= 2:\n", "            # Create RGB image with available channels\n", "            shape = next(iter(channels.values())).shape\n", "            rgb = np.zeros((shape[0], shape[1], 3), dtype=np.uint16)\n", "            \n", "            # Assign channels to RGB (customize as needed)\n", "            if '00' in channels:  # DAPI to blue\n", "                rgb[:,:,2] = channels['00']\n", "            if '01' in channels:  # Channel 1 to green\n", "                rgb[:,:,1] = channels['01']\n", "            if '02' in channels:  # Channel 2 to red\n", "                rgb[:,:,0] = channels['02']\n", "                \n", "            # Save the combined image\n", "            imwrite(os.path.join(MULTICHANNEL_DIR, f\"{base}_combined.tif\"), rgb)\n", "            \n", "    print(\"Channel combination complete.\")"]}, {"cell_type": "code", "execution_count": 7, "id": "quality-cell", "metadata": {}, "outputs": [], "source": ["def check_alignment_quality():\n", "    \"\"\"Check the quality of alignment by visualizing overlays\"\"\"\n", "    print(\"Checking alignment quality...\")\n", "    \n", "    # Find all DAPI images\n", "    dapi_files = [f for f in os.listdir(WARPED_DIR) if f.endswith(\"_Merged_ch00.tif\")]\n", "    \n", "    if len(dapi_files) < 2:\n", "        print(\"Not enough DAPI images to check alignment.\")\n", "        return\n", "    \n", "    # Load the first DAPI as reference\n", "    reference = imread(os.path.join(WARPED_DIR, dapi_files[0]))\n", "    \n", "    # Compare each other DAPI to the reference\n", "    for i, dapi_file in enumerate(dapi_files[1:], 1):\n", "        target = imread(os.path.join(WARPED_DIR, dapi_file))\n", "        visualize_alignment(\n", "            reference, \n", "            target, \n", "            f\"Alignment Check: Reference vs {dapi_file}\",\n", "            os.path.join(OUTPUT_BASE, f\"alignment_check_{i}.png\")\n", "        )\n", "    \n", "    print(\"Alignment quality check complete.\")"]}, {"cell_type": "code", "execution_count": 8, "id": "main-cell", "metadata": {}, "outputs": [], "source": ["# ----------------------------------\n", "# Main\n", "# ----------------------------------\n", "if __name__ == \"__main__\":\n", "    registrar, rigid_reg, non_rigid_reg, error_df = process_folder(MAIN_DIR, display=True)\n", "    \n", "    if registrar is not None and verify_registration(registrar):\n", "        combine_all_channels()\n", "        check_alignment_quality()\n", "    else:\n", "        print(\"Registration verification failed. Please check the registration parameters.\")"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}