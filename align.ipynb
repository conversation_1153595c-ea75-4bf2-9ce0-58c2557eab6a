{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.10/site-packages/numba/core/decorators.py:262: NumbaDeprecationWarning: \u001b[1mnumba.generated_jit is deprecated. Please see the documentation at: https://numba.readthedocs.io/en/stable/reference/deprecation.html#deprecation-of-generated-jit for more information and advice on a suitable replacement.\u001b[0m\n", "  warnings.warn(msg, NumbaDeprecationWarning)\n", "/home/<USER>/.local/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from valis import registration"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from valis import registration\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["slide_src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/images/\"\n", "results_dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/\"\n", "registered_slide_dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/Registered_slides.ome.tiff\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace with your TIFF file path\n", "file_path = '/mnt/c/Users/<USER>/antho 4i alignment/images/4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00.tif'\n", "\n", "# Load the image\n", "image = Image.open(file_path)\n", "\n", "# Display the image\n", "plt.imshow(image)\n", "plt.axis('off')  # Turn off axis numbers\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["SLF4J: Failed to load class \"org.slf4j.impl.StaticLoggerBinder\".\n", "SLF4J: Defaulting to no-operation (NOP) logger implementation\n", "SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["JVM has been initialized. Be sure to call registration.kill_jvm() or slide_io.kill_jvm() at the end of your script.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/valtils.py:75: UserWarning: Can't find slide file associated with New folder\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/11 [00:11<?, ?it/s]\n", "/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/valtils.py:75: UserWarning: invalid index to scalar variable.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/registration.py\", line 3364, in register\n", "    self.convert_imgs(series=self.series, reader_cls=reader_cls)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/registration.py\", line 1822, in convert_imgs\n", "    reader = reader_cls(f, series=series)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/slide_io.py\", line 1267, in __init__\n", "    self.metadata = self.create_metadata()\n", "                    ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/slide_io.py\", line 1293, in create_metadata\n", "    slide_meta.slide_dimensions = self._get_slide_dimensions(vips_img)\n", "                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/slide_io.py\", line 1504, in _get_slide_dimensions\n", "    slide_dimensions = self._get_slide_dimensions_vips(vips_img)\n", "                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/anaconda3/envs/VALIS/lib/python3.11/site-packages/valis/slide_io.py\", line 1567, in _get_slide_dimensions_vips\n", "    most_common_channel_count = stats.mode(all_channels)[0][0]\n", "                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^\n", "IndexError: invalid index to scalar variable.\n", "\n", "JV<PERSON> has been killed. If this was due to an error, then a new Python session will need to be started\n"]}], "source": ["registrar = registration.Valis(slide_src_dir, results_dst_dir, reference_img_f=\"/mnt/c/Users/<USER>/antho 4i alignment/images/4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00.tif\",align_to_reference=True)\n", "\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all registered slides as ome.tiff\n", "registrar.warp_and_save_slides(registered_slide_dst_dir, crop=\"overlap\")\n", "\n", "# Kill the JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "##The next example shows how align each image to a reference image, followed up by micro-registration. \n", "##The reference image the others should be aligned towards is set with the reference_img_f argument when initializing the Valis object. \n", "##This initial registration is followed up by micro-registration in order to better align features that were not present in the smaller images used for the first registration \n", "##(The size of the images used for micro-registration can is set with the max_non_rigid_registartion_dim_px argument in Valis.register_micro). \n", "##Setting align_to_reference to True will align each image directly to the reference image, as opposed to towards it.\n", "## max_processed_image_dim_px, which determines the size of the image used to find the rigid registration parameters. \n", "##The default value is 850, but if registration fails or is poor, try adjusting that value. Generally speaking, values between 500-2000 work well. \n", "##In cases where there is little empty space, around the tissue, smaller values may be better. However, if there is a large amount of empty space/slide (as in the images above), larger values may be needed so that the tissue is at a high enough resolution. \n", "from valis import registration\n", "\n", "slide_src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/images/\"\n", "results_dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/\"\n", "registered_slide_dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/Registered_slides.ome.tiff\"\n", "reference_slide = \"/mnt/c/Users/<USER>/antho 4i alignment/images/4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00.tif\"\n", "\n", "\n", "# Create a Valis object and use it to register the slides in slide_src_dir, aligning *towards* the reference slide.\n", "registrar = registration.Valis(slide_src_dir, results_dst_dir, reference_img_f=reference_slide)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Perform micro-registration on higher resolution images, aligning *directly to* the reference image\n", "registrar.register_micro(max_non_rigid_registration_dim_px=2000, align_to_reference=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["##If the results look good, then one can warp and save all of the slides as ome.tiffs. When saving the images, there are three cropping options:\n", "\n", "## crop=\"overlap\" will crop the images to the region where all of the images overlap.\n", "\n", "## crop=\"reference\" will crop the images to the region where they overlap with the reference image.\n", "\n", "## crop=\"all\" will not perform any cropping. While this keep the all of the image, the dimensions of the registered image can be substantially larger than one that was cropped, as it will need to be large enough accommodate all of the other images.\n", "\n", "\n", "\n", "# Save all registered slides as ome.tiff\n", "registrar.warp_and_save_slides(registered_slide_dst_dir, crop=\"overlap\")\n", "\n", "# Kill the JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.10/site-packages/valis/valtils.py:22: UserWarning: Can't find slide file associated with New folder\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 11/11 [04:25<00:00, 24.17s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 11/11 [02:45<00:00, 15.08s/image]\n", "Normalizing images: 100%|██████████| 11/11 [00:03<00:00,  3.27image/s]\n", "Denoising images  : 100%|██████████| 11/11 [00:01<00:00,  8.70image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 11/11 [00:01<00:00,  9.97image/s]\n", "Matching images      : 100%|██████████| 55/55 [00:01<00:00, 44.47image/s]\n", "Finding transforms   : 100%|██████████| 10/10 [00:00<00:00, 901.65image/s]\n", "Finalizing           : 100%|██████████| 11/11 [00:00<00:00, 3100.00image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 30.942 seconds\n", "\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["##High resolution registration\n", "\n", "import time\n", "import os\n", "import numpy as np\n", "from valis import registration\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration\n", "\n", "slide_src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/images/\"\n", "results_dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/\"\n", "merged_slide_dst_f = \"/mnt/c/Users/<USER>/antho 4i alignment/results/merged_slides.ome.tiff\"  # Where to save merged slide\n", "\n", "\n", "\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration\n", "\n", "# Perform high resolution rigid registration using the MicroRigidRegistrar\n", "start = time.time()\n", "registrar = registration.Valis(slide_src_dir, results_dst_dir, micro_rigid_registrar_cls=MicroRigidRegistrar)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Calculate what `max_non_rigid_registration_dim_px` needs to be to do non-rigid registration on an image that is 25% full resolution.\n", "img_dims = np.array([slide_obj.slide_dimensions_wh[0] for slide_obj in registrar.slide_dict.values()])\n", "min_max_size = np.min([np.max(d) for d in img_dims])\n", "img_areas = [np.multiply(*d) for d in img_dims]\n", "max_img_w, max_img_h = tuple(img_dims[np.argmax(img_areas)])\n", "micro_reg_size = np.floor(min_max_size*micro_reg_fraction).astype(int)\n", "\n", "# Perform high resolution non-rigid registration using 25% full resolution\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create function to extract channel names from the image.\n", "def cnames_from_filename(src_f):\n", "    \"\"\"Get channel names from file name\n", "    Note that the DAPI channel is not part of the filename\n", "    but is always the first channel.\n", "    \"\"\"\n", "\n", "    f = valtils.get_name(src_f)\n", "    return [\"DAPI\"] + f.split(\" \")[1:4]\n", "\n", "channel_name_dict = {f:cnames_from_filename(f) for f in registrar.original_img_list}\n", "merged_img, channel_names, ome_xml = \\\n", "    registrar.warp_and_merge_slides(merged_slide_dst_f,\n", "                                    channel_name_dict=channel_name_dict,\n", "                                    drop_duplicates=True)\n", "\n", "registration.kill_jvm() # Kill the JVM"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/5 [00:00<?, ?image/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 5/5 [01:49<00:00, 21.86s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 5/5 [01:25<00:00, 17.07s/image]\n", "Normalizing images: 100%|██████████| 5/5 [00:01<00:00,  4.24image/s]\n", "Denoising images  : 100%|██████████| 5/5 [00:00<00:00,  9.81image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 5/5 [00:00<00:00,  7.27image/s]\n", "Matching images      : 100%|██████████| 10/10 [00:00<00:00, 45.19image/s]\n", "Finding transforms   : 100%|██████████| 4/4 [00:00<00:00, 1420.23image/s]\n", "Finalizing           : 100%|██████████| 5/5 [00:00<00:00, 2330.95image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 13.731 seconds\n", "\n", "\n", "==== Non-rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 5/5 [01:44<00:00, 20.91s/image]\n", "Finding non-rigid transforms: 100%|██████████| 4/4 [00:27<00:00,  6.80s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 27.286 seconds\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 5/5 [00:00<00:00, 20.77image/s]\n", "  0%|          | 0/5 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["warping images associated with 4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|██        | 1/5 [00:17<01:09, 17.45s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["warping images associated with BCL2 5 round skin 4i 25may23_TileScan 1_Merged_ch00\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 2/5 [00:36<00:55, 18.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["warping images associated with p16 skin round 6 4i 26mai23_TileScan 1_Merged_ch00\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|██████    | 3/5 [00:54<00:36, 18.12s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["warping images associated with skin HMGB1 round 3 with neg ctrl_TileScan 1_Merged_ch00\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 4/5 [01:10<00:17, 17.25s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["warping images associated with skin PCNA round 2_TileScan 3_Merged_ch00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5/5 [01:24<00:00, 16.97s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["saving /mnt/c/Users/<USER>/antho 4i alignment/results/slides/merged.ome.tiff (19937 x 9069 and 5 channels)\n", "\n", "[====================================================================================================] 100.0% in 1.092 minutes\n", "Complete\n", "\n"]}], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'slide_obj' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32mc:/Users/<USER>/Documents/align.ipynb Cell 14\u001b[0m line \u001b[0;36m6\n\u001b[1;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Documents/align.ipynb#X16sZmlsZQ%3D%3D?line=65'>66</a>\u001b[0m \u001b[39m# Identify DAPI channels for each round\u001b[39;00m\n\u001b[1;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Documents/align.ipynb#X16sZmlsZQ%3D%3D?line=66'>67</a>\u001b[0m dapi_imgs \u001b[39m=\u001b[39m []\n\u001b[0;32m---> <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Documents/align.ipynb#X16sZmlsZQ%3D%3D?line=67'>68</a>\u001b[0m round_images \u001b[39m=\u001b[39m {slide_obj:[f \u001b[39mfor\u001b[39;00m f \u001b[39min\u001b[39;00m full_img_list \u001b[39mif\u001b[39;00m get_round_name(f) \u001b[39m==\u001b[39m get_round_name(slide_obj\u001b[39m.\u001b[39msrc_f)]}\n\u001b[1;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Documents/align.ipynb#X16sZmlsZQ%3D%3D?line=68'>69</a>\u001b[0m dapi_channel \u001b[39m=\u001b[39m \u001b[39mmax\u001b[39m(round_images, key\u001b[39m=\u001b[39m\u001b[39mlambda\u001b[39;00m x: \u001b[39mint\u001b[39m(re\u001b[39m.\u001b[39msearch(\u001b[39mr\u001b[39m\u001b[39m'\u001b[39m\u001b[39mch(\u001b[39m\u001b[39m\\\u001b[39m\u001b[39md+).tif\u001b[39m\u001b[39m'\u001b[39m, x)\u001b[39m.\u001b[39mgroup(\u001b[39m1\u001b[39m)))\n\u001b[1;32m     <a href='vscode-notebook-cell:/c%3A/Users/<USER>/Documents/align.ipynb#X16sZmlsZQ%3D%3D?line=69'>70</a>\u001b[0m dapi_imgs\u001b[39m.\u001b[39mappend(dapi_channel)\n", "\u001b[0;31mNameError\u001b[0m: name 'slide_obj' is not defined"]}], "source": ["import time\n", "import os\n", "import numpy as np\n", "\n", "from tqdm import tqdm\n", "import sys\n", "import pathlib\n", "from valis import registration, valtils, preprocessing, slide_io\n", "\n", "\n", "src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/images/\"\n", "dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results/\"\n", "\n", "\n", "def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_Merged\")[0]\n", "    return round_name\n", "\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml\n", "\n", "\n", "merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "\n", "# Identify DAPI channels for each round\n", "dapi_imgs = []\n", "round_images = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]}\n", "dapi_channel = max(round_images, key=lambda x: int(re.search(r'ch(\\d+).tif', x).group(1)))\n", "dapi_imgs.append(dapi_channel)\n", "\n", "# Register rounds using identified DAPI channels\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"56d\n", "#dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "#registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs)\n", "#rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "#round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "  #            for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide_obj = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from valis import registration\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}