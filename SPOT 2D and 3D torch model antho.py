import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from scipy import ndimage as ndi
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import glob
import os
from typing import Tuple, Optional, Union

#####################################
# 1. Squeeze-and-Excitation (SE) Block
#####################################

class SEBlock(nn.Module):
    """
    Squeeze-and-Excitation block that re-weights channel-wise features.
    """
    def __init__(self, channels: int, reduction: int = 16):
        super(SEBlock, self).__init__()
        self.fc1 = nn.Linear(channels, channels // reduction, bias=False)
        self.fc2 = nn.Linear(channels // reduction, channels, bias=False)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch, channels = x.shape[:2]
        # Global average pooling (works for 2D/3D)
        y = x.view(batch, channels, -1).mean(dim=2)
        y = F.relu(self.fc1(y))
        y = torch.sigmoid(self.fc2(y))
        y = y.view(batch, channels, *([1] * (x.dim() - 2)))
        return x * y

#####################################
# 2. Enhanced Convolution Block with Optional Residual and Attention
#####################################

class ConvBlock(nn.Module):
    """
    A convolution block that applies a convolution, optional batch normalization,
    activation, dropout, and optionally uses residual connections and SE attention.
    """
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 is_3d: bool = False, activation=nn.ReLU, batch_norm: bool = False,
                 dropout: float = 0.0, use_attention: bool = False, use_residual: bool = False):
        super(ConvBlock, self).__init__()
        self.use_residual = use_residual
        if is_3d:
            conv = nn.Conv3d
            bn = nn.BatchNorm3d
            dropout_layer = nn.Dropout3d
        else:
            conv = nn.Conv2d
            bn = nn.BatchNorm2d
            dropout_layer = nn.Dropout2d

        layers = [conv(in_channels, out_channels, kernel_size, padding='same',
                       bias=not batch_norm)]
        if batch_norm:
            layers.append(bn(out_channels))
        layers.append(activation())
        if dropout > 0:
            layers.append(dropout_layer(dropout))
        self.conv = nn.Sequential(*layers)
        self.use_attention = use_attention
        if use_attention:
            self.se = SEBlock(out_channels)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        out = self.conv(x)
        if self.use_attention:
            out = self.se(out)
        # Apply residual connection if enabled and dimensions match.
        if self.use_residual and x.shape[1] == out.shape[1]:
            out = x + out
        return out

#####################################
# 3. Enhanced UNet Backbone (Multi-scale)
#####################################

class EnhancedUNet(nn.Module):
    """
    A UNet-style backbone that extracts multi-scale features.
    Supports 2D and 3D inputs.
    Each level can optionally use SE attention and residual connections.
    """
    def __init__(self, in_channels: int = 1, base_filters: int = 16, levels: int = 3,
                 is_3d: bool = False, batch_norm: bool = False, use_attention: bool = False,
                 use_residual: bool = False, dropout: float = 0.0):
        super(EnhancedUNet, self).__init__()
        self.levels = levels
        self.is_3d = is_3d
        
        # Encoder (downsampling)
        self.down_blocks = nn.ModuleList()
        self.pool = nn.MaxPool3d(2) if is_3d else nn.MaxPool2d(2)
        current_channels = in_channels
        filters = base_filters
        self.skip_channels = []  # Save channel sizes for skip connections.
        for _ in range(levels):
            block = ConvBlock(current_channels, filters, kernel_size=3,
                              is_3d=is_3d, batch_norm=batch_norm,
                              use_attention=use_attention, use_residual=use_residual,
                              dropout=dropout)
            self.down_blocks.append(block)
            self.skip_channels.append(filters)
            current_channels = filters
            filters *= 2
        
        # Bottleneck
        self.bottleneck = ConvBlock(current_channels, filters, kernel_size=3,
                                    is_3d=is_3d, batch_norm=batch_norm,
                                    use_attention=use_attention, use_residual=use_residual,
                                    dropout=dropout)
        
        # Decoder (upsampling)
        self.up_transposes = nn.ModuleList()
        self.up_blocks = nn.ModuleList()
        for i in range(levels):
            filters //= 2
            if is_3d:
                up_trans = nn.ConvTranspose3d(self.skip_channels[-(i+1)] * 2, filters,
                                              kernel_size=2, stride=2)
            else:
                up_trans = nn.ConvTranspose2d(self.skip_channels[-(i+1)] * 2, filters,
                                              kernel_size=2, stride=2)
            self.up_transposes.append(up_trans)
            block = ConvBlock(filters * 2, filters, kernel_size=3,
                              is_3d=is_3d, batch_norm=batch_norm,
                              use_attention=use_attention, use_residual=use_residual,
                              dropout=dropout)
            self.up_blocks.append(block)
        
        # Final segmentation head: outputs a one-channel heatmap.
        self.out_conv = nn.Conv3d(base_filters, 1, kernel_size=1) if is_3d else nn.Conv2d(base_filters, 1, kernel_size=1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        skips = []
        for down in self.down_blocks:
            x = down(x)
            skips.append(x)
            x = self.pool(x)
        x = self.bottleneck(x)
        for i in range(self.levels):
            x = self.up_transposes[i](x)
            skip = skips[-(i+1)]
            x = torch.cat([x, skip], dim=1)
            x = self.up_blocks[i](x)
        heatmap = self.out_conv(x)
        return heatmap

#####################################
# 4. Post-Processing: Watershed Segmentation
#####################################

def post_process_watershed(heatmap: np.ndarray, threshold: float = 0.5,
                           min_distance: float = 5) -> np.ndarray:
    """
    Converts a heatmap (values in [0,1]) into instance labels using watershed segmentation.
    
    The function thresholds the heatmap to create a binary mask, computes a distance transform,
    and uses local maxima (with a minimum distance constraint) as markers for watershed segmentation.
    """
    binary_mask = heatmap > threshold
    distance = ndi.distance_transform_edt(binary_mask)
    local_maxi = peak_local_max(distance, indices=False, min_distance=int(min_distance), labels=binary_mask)
    markers = ndi.label(local_maxi)[0]
    labels = watershed(-distance, markers, mask=binary_mask)
    return labels

#####################################
# 5. Normalization and Auto-Tiling for Large Images
#####################################

def normalize_image(image: np.ndarray) -> np.ndarray:
    """
    Normalize image intensities to [0, 1] using min-max normalization.
    """
    min_val = image.min()
    max_val = image.max()
    if max_val - min_val == 0:
        return image
    return (image - min_val) / (max_val - min_val)

def auto_tile_inference(model: nn.Module, image: np.ndarray,
                        tile_size: Tuple[int, int] = (256, 256),
                        overlap: int = 32,
                        device: torch.device = torch.device("cpu"),
                        is_3d: bool = False) -> np.ndarray:
    """
    For large images, splits the image into overlapping tiles, runs inference on each tile,
    and stitches the output (averaging overlapping predictions). Supports 2D and 3D.
    """
    model.eval()
    with torch.no_grad():
        if is_3d:
            d, h, w = image.shape
            tile_d, tile_h, tile_w = tile_size
            output = np.zeros(image.shape, dtype=np.float32)
            count = np.zeros(image.shape, dtype=np.float32)
            for z in range(0, d, tile_d - overlap):
                for y in range(0, h, tile_h - overlap):
                    for x in range(0, w, tile_w - overlap):
                        z1 = min(z + tile_d, d)
                        y1 = min(y + tile_h, h)
                        x1 = min(x + tile_w, w)
                        tile = image[z:z1, y:y1, x:x1]
                        tile = normalize_image(tile)
                        tile_tensor = torch.tensor(tile, dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(device)
                        pred = model(tile_tensor)
                        pred_np = pred.squeeze().cpu().numpy()
                        output[z:z1, y:y1, x:x1] += pred_np
                        count[z:z1, y:y1, x:x1] += 1
            output /= np.maximum(count, 1)
            return output
        else:
            h, w = image.shape
            tile_h, tile_w = tile_size
            output = np.zeros(image.shape, dtype=np.float32)
            count = np.zeros(image.shape, dtype=np.float32)
            for y in range(0, h, tile_h - overlap):
                for x in range(0, w, tile_w - overlap):
                    y1 = min(y + tile_h, h)
                    x1 = min(x + tile_w, w)
                    tile = image[y:y1, x:x1]
                    tile = normalize_image(tile)
                    tile_tensor = torch.tensor(tile, dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(device)
                    pred = model(tile_tensor)
                    pred_np = pred.squeeze().cpu().numpy()
                    output[y:y1, x:x1] += pred_np
                    count[y:y1, x:x1] += 1
            output /= np.maximum(count, 1)
            return output

#####################################
# 6. Enhanced Instance Spot Detector with Learned Flow Branch
#####################################

class EnhancedInstanceSpotDetector(nn.Module):
    """
    An efficient, fast, and precise spot detection model for 2D/3D images.
    
    Components:
      - Multi-scale UNet backbone with optional SE attention and residual connections.
      - Optional learned flow branch to compute per-pixel displacements.
      - Watershed-based post-processing to produce instance labels.
      - Auto-tiling for large images.
    """
    def __init__(self, in_channels: int = 1, base_filters: int = 16, levels: int = 3,
                 is_3d: bool = False, batch_norm: bool = True, use_attention: bool = True,
                 use_residual: bool = True, dropout: float = 0.1,
                 threshold: float = 0.5, min_distance: float = 5.0, compute_flow: bool = True):
        super(EnhancedInstanceSpotDetector, self).__init__()
        self.is_3d = is_3d
        self.threshold = threshold
        self.min_distance = min_distance
        self.compute_flow = compute_flow
        
        # Backbone for heatmap generation.
        self.backbone = EnhancedUNet(in_channels=in_channels, base_filters=base_filters,
                                     levels=levels, is_3d=is_3d, batch_norm=batch_norm,
                                     use_attention=use_attention, use_residual=use_residual,
                                     dropout=dropout)
        # Learned flow branch.
        if compute_flow:
            ConvModule = nn.Conv3d if is_3d else nn.Conv2d
            BatchNormModule = nn.BatchNorm3d if is_3d else nn.BatchNorm2d
            self.flow_branch = nn.Sequential(
                ConvModule(self.backbone.skip_channels[0], self.backbone.skip_channels[0],
                           kernel_size=3, padding=1, bias=not batch_norm),
                BatchNormModule(self.backbone.skip_channels[0]) if batch_norm else nn.Identity(),
                nn.ReLU(inplace=True),
                ConvModule(self.backbone.skip_channels[0], self.backbone.skip_channels[0],
                           kernel_size=3, padding=1, bias=not batch_norm),
                BatchNormModule(self.backbone.skip_channels[0]) if batch_norm else nn.Identity(),
                nn.ReLU(inplace=True),
                # Output: 2 channels for 2D, 3 channels for 3D.
                ConvModule(self.backbone.skip_channels[0], 2 if not is_3d else 3,
                           kernel_size=3, padding=1)
            )
        
    def forward(self, x: torch.Tensor) -> Union[torch.Tensor, dict]:
        # Normalize input per batch to [0, 1]
        x = (x - x.min()) / (x.max() - x.min() + 1e-8)
        heatmap = self.backbone(x)
        heatmap = torch.sigmoid(heatmap)
        if self.compute_flow:
            # Compute flow from an early feature map.
            flow_features = self.backbone.down_blocks[0](x)
            flow = self.flow_branch(flow_features)
            flow = F.normalize(flow, dim=1)
            return {"heatmaps": heatmap, "flow": flow}
        else:
            return {"heatmaps": heatmap}
    
    def predict(self, image: np.ndarray, device: Union[str, torch.device] = "cpu",
                tile_size: Optional[Tuple[int, int]] = None, overlap: int = 32) -> dict:
        """
        Runs inference on an image and returns instance labels (and flow if computed).
        Applies auto-tiling if a tile_size is provided.
        """
        device = torch.device(device)
        self.to(device)
        self.eval()
        with torch.no_grad():
            if tile_size is not None:
                heatmap_np = auto_tile_inference(self, image, tile_size=tile_size,
                                                 overlap=overlap, device=device, is_3d=self.is_3d)
            else:
                norm_image = normalize_image(image)
                tensor = torch.tensor(norm_image, dtype=torch.float32).unsqueeze(0).unsqueeze(0).to(device)
                output = self.forward(tensor)
                heatmap_np = output["heatmaps"].squeeze().cpu().numpy()
                if self.compute_flow:
                    flow_np = output["flow"].squeeze().cpu().numpy()
            instance_labels = post_process_watershed(heatmap_np,
                                                     threshold=self.threshold,
                                                     min_distance=self.min_distance)
        if self.compute_flow:
            return {"instance_labels": instance_labels, "flow": flow_np}
        else:
            return {"instance_labels": instance_labels}

#####################################
# 7. Efficient Data Loader for Images & Masks
#####################################

class SpotDataset(Dataset):
    """
    Custom dataset for loading images and corresponding masks.
    Assumes images and masks are stored in separate folders with matching filenames.
    """
    def __init__(self, images_dir: str, masks_dir: str, transform=None):
        self.images_dir = images_dir
        self.masks_dir = masks_dir
        self.transform = transform
        self.image_files = sorted(glob.glob(os.path.join(images_dir, "*.*")))
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx: int):
        image_path = self.image_files[idx]
        filename = os.path.basename(image_path)
        mask_path = os.path.join(self.masks_dir, filename)
        
        image = np.array(Image.open(image_path).convert("L"), dtype=np.float32)
        mask = np.array(Image.open(mask_path).convert("L"), dtype=np.int64)
        image = normalize_image(image)
        
        if self.transform:
            augmented = self.transform(image=image, mask=mask)
            image, mask = augmented["image"], augmented["mask"]
        
        image = torch.tensor(image).unsqueeze(0)
        mask = torch.tensor(mask)
        return image, mask

#####################################
# 8. Optimized Training Routine with AdamW, Cosine Annealing, and Early Stopping
#####################################

def train_model(model, train_loader, val_loader, num_epochs: int = 50, device="cuda",
                initial_lr: float = 1e-3, weight_decay: float = 1e-4, patience: int = 10):
    model.to(device)
    optimizer = torch.optim.AdamW(model.parameters(), lr=initial_lr, weight_decay=weight_decay)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    criterion = nn.CrossEntropyLoss()
    
    best_val_loss = float('inf')
    epochs_no_improve = 0

    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        for images, masks in train_loader:
            images = images.to(device)
            masks = masks.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs["heatmaps"].squeeze(1), masks)
            loss.backward()
            optimizer.step()
            train_loss += loss.item() * images.size(0)
        avg_train_loss = train_loss / len(train_loader.dataset)
        
        # Validation loop.
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for images, masks in val_loader:
                images = images.to(device)
                masks = masks.to(device)
                outputs = model(images)
                loss = criterion(outputs["heatmaps"].squeeze(1), masks)
                val_loss += loss.item() * images.size(0)
        avg_val_loss = val_loss / len(val_loader.dataset)
        scheduler.step()
        
        print(f"Epoch [{epoch+1}/{num_epochs}], Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
        
        # Early stopping check.
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            epochs_no_improve = 0
            # Optionally, save the best model weights here.
        else:
            epochs_no_improve += 1
            if epochs_no_improve >= patience:
                print("Early stopping triggered.")
                break
                
    return model

#####################################
# 9. Example Usage (2D)
#####################################

if __name__ == "__main__":
    import matplotlib.pyplot as plt

    # Create a synthetic 2D image with spots.
    img = np.zeros((512, 512), dtype=np.float32)
    for _ in range(20):
        y, x = np.random.randint(50, 462, size=2)
        img[y-3:y+3, x-3:x+3] = 1.0  # simulate a small spot
    img += 0.2 * np.random.rand(512, 512).astype(np.float32)

    # Instantiate the enhanced model.
    model = EnhancedInstanceSpotDetector(in_channels=1, base_filters=16, levels=3,
                                         is_3d=False, batch_norm=True, use_attention=True,
                                         use_residual=True, dropout=0.1,
                                         threshold=0.5, min_distance=10, compute_flow=True)
    
    # For demonstration, use the untrained model.
    prediction = model.predict(img, device="cpu", tile_size=(256, 256), overlap=32)
    instance_labels = prediction["instance_labels"]
    flow = prediction.get("flow", None)
    
    # Display results.
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 3, 1)
    plt.title("Input Image")
    plt.imshow(img, cmap="gray")
    plt.subplot(1, 3, 2)
    plt.title("Instance Labels (Watershed)")
    plt.imshow(instance_labels, cmap="nipy_spectral")
    plt.subplot(1, 3, 3)
    if flow is not None:
        plt.title("Flow (x-component)")
        plt.imshow(flow[0], cmap="viridis")
    plt.show()
    
    # Example DataLoader usage:
    # train_dataset = SpotDataset("path/to/train/images", "path/to/train/masks")
    # val_dataset = SpotDataset("path/to/val/images", "path/to/val/masks")
    # train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
    # val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=4)
    # trained_model = train_model(model, train_loader, val_loader, num_epochs=50,
    #                             device="cuda" if torch.cuda.is_available() else "cpu")
    # torch.save(trained_model.state_dict(), "enhanced_instance_spot_detector.pth")
