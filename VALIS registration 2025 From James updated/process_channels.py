from pathlib import Path
import os
from bioio import BioImage
from bioio.writers import OmeTiffWriter
from valis import valtils, slide_io

def get_base_name(src_f):
    """Extract base name without channel number from filename"""
    img_name = os.path.basename(src_f).lower()
    base_name = img_name.split('_merged_ch')[0]
    return base_name

def get_channel_number(src_f):
    """Extract channel number from filename"""
    img_name = os.path.basename(src_f).lower()
    if "_ch00" in img_name:
        return 0
    elif "_ch01" in img_name:
        return 1
    elif "_ch02" in img_name:
        return 2
    return None

def get_ome_xml(warped_slide, reference_slide, channel_names=None):
    """Generate ome-xml for warped slide"""
    ref_meta = reference_slide.reader.metadata
    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
    out_xyczt = slide_io.get_shape_xyzct(
        (warped_slide.width, warped_slide.height), 
        warped_slide.bands
    )
    ome_xml_obj = slide_io.create_ome_xml(
        shape_xyzct=out_xyczt,
        bf_dtype=bf_dtype,
        is_rgb=False,
        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
        channel_names=channel_names,
        colormap=None
    )
    return ome_xml_obj.to_xml()

def process_channels(registrar, input_dir, output_dir):
    """Process all channels for each slide"""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    for _, slide_name in registrar.name_dict.items():
        slide_obj = registrar.get_slide(slide_name)
        base_name = get_base_name(slide_name)
        
        # Process each channel (0, 1, 2)
        for ch in range(3):
            src_file = f"{base_name}_Merged_ch{ch:02d}.tif"
            src_path = Path(input_dir) / src_file
            
            if not src_path.exists():
                continue
                
            img_to_warp = BioImage(src_path)
            data = img_to_warp.get_image_dask_data("CYX")
            data = data.compute()
            
            # Apply transformation
            warped_data = registrar.warp_slide(
                data,
                slide_obj,
                non_rigid=True
            )
            
            # Save warped image
            out_path = output_dir / f"registered_{src_file}"
            ome_xml = get_ome_xml(warped_data, slide_obj)
            writer = OmeTiffWriter(out_path)
            writer.write(warped_data, ome_xml=ome_xml)