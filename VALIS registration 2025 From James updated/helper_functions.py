import os
from valis import valtils
from valis import slide_io

def get_base_name(src_f):
    """Extract base name without channel number from filename"""
    img_name = os.path.basename(src_f).lower()
    base_name = img_name.split('_merged_ch')[0]
    return base_name

def get_channel_number(src_f):
    """Extract channel number from filename"""
    img_name = os.path.basename(src_f).lower()
    if "_ch00" in img_name:
        return 0
    elif "_ch01" in img_name:
        return 1
    elif "_ch02" in img_name:
        return 2
    return None

def get_ome_xml(warped_slide, reference_slide, channel_names=None):
    """Generate ome-xml for warped slide"""
    ref_meta = reference_slide.reader.metadata
    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
    out_xyczt = slide_io.get_shape_xyzct(
        (warped_slide.width, warped_slide.height), 
        warped_slide.bands
    )
    ome_xml_obj = slide_io.create_ome_xml(
        shape_xyzct=out_xyczt,
        bf_dtype=bf_dtype,
        is_rgb=False,
        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
        channel_names=channel_names,
        colormap=None
    )
    return ome_xml_obj.to_xml()