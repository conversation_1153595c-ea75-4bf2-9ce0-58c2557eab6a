{"cells": [{"cell_type": "code", "execution_count": null, "id": "b0c7d998", "metadata": {}, "outputs": [], "source": ["### ALIGN USING VALIS ###\n", "###STEP 1 : UPDATE THE FOLDER WITH YOUR IMAGES (all of them) in format filename  \" \"LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\"\"\n", "### STEP 2 : update the reference image to align to format : \"LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\")\n", "### STEP3 : RUN and have a coffee ! \n"]}, {"cell_type": "code", "execution_count": 3, "id": "8a75da1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Organizing channel files...\n", "Organizing files from: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1\n", "DAPI (ch00) files will go to: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/dapi\n", "Other channels will go to: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/all_channels\n", "------------------------------------------------------------\n", "📁 DAPI: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.lif - R 2_Merged-1.tif\n", "📁 DAPI: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch01.tif\n", "📁 DAPI: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch01.tif\n", "📁 DAPI: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch01.tif\n", "🔬 Channel: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch02.tif\n", "📁 DAPI: LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00.tif\n", "🔬 Channel: LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch01.tif\n", "\n", "============================================================\n", "ORGANIZATION COMPLETE!\n", "============================================================\n", "✅ DAPI files moved: 10\n", "✅ Other channel files moved: 17\n", "⚠️  Unrecognized files: 0\n", "✅ DAPI images directory: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/dapi\n", "✅ All channels directory: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/all_channels\n", "✅ Results directory: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration\n", "✅ Reference slide: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/dapi/LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\n", "Found 10 DAPI images for registration\n", "Found 17 other channel images\n", "Finding maximum dimensions across all images...\n", "Maximum dimensions: 11670 x 14775\n", "Creating padded images and CLAHE-padded DAPI images...\n", "Padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/padded_images/reference/padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "CLAHE-padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/clahe_padded_dapi/clahe_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00.tif\n", "Padded channel: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.lif - R 2_Merged-1.tif\n", "Padded channel: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch01.tif\n", "Performing registration with CLAHE-padded DAPI images...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/PIL/Image.py:3176: DecompressionBombWarning: Image size (172424250 pixels) exceeds limit of 89478485 pixels, could be decompression bomb DOS attack.\n", "  warnings.warn(\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: max_image_dim_px is 1024 but needs to be less or equal to 2000. Setting max_image_dim_px to 2000\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/10 [00:00<?, ?image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db412d510> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  10%|█         | 1/10 [00:04<00:39,  4.40s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db412c550> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  20%|██        | 2/10 [00:08<00:34,  4.29s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41dedd0> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  30%|███       | 3/10 [00:12<00:30,  4.31s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41dc4f0> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  40%|████      | 4/10 [00:17<00:25,  4.29s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41dcb50> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  50%|█████     | 5/10 [00:21<00:21,  4.34s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41df610> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  60%|██████    | 6/10 [00:25<00:17,  4.30s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41dce50> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  70%|███████   | 7/10 [00:30<00:12,  4.26s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41dfbb0> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  80%|████████  | 8/10 [00:34<00:08,  4.23s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41f33d0> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  90%|█████████ | 9/10 [00:38<00:04,  4.19s/image]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00>, width=11670, height=14775, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7f5db41f0970> False (2000, 1580)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 10/10 [00:42<00:00,  4.25s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 10/10 [00:49<00:00,  4.98s/image]\n", "Normalizing images: 100%|██████████| 10/10 [00:04<00:00,  2.49image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 10/10 [02:22<00:00, 14.29s/image]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 10/10 [00:00<00:00, 128.85image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 10/10 [00:13<00:00,  1.39s/image]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 10/10 [00:00<00:00, 81442.80image/s]\n", "Finding transforms   : 100%|██████████| 9/9 [00:00<00:00, 791.11image/s]\n", "Finalizing           : 100%|██████████| 10/10 [00:00<00:00, 4068.19image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 2.69 minutes\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:598: RuntimeWarning: divide by zero encountered in divide\n", "  weight = grey_img_list[i]/sum_img\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:598: RuntimeWarning: invalid value encountered in divide\n", "  weight = grey_img_list[i]/sum_img\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Micro-rigid registration\n", "\n", "Aligning clahe_padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1450.94746269  1837.95938104] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1567.67it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [01:05<00:00,  5.48s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 174762.67it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2534, N high rez matches = 32226. Low rez D= 0.626549323764273, high rez D=0.7150435625117583\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1454.64179104  1827.79980658] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1193.91it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:57<00:00,  4.79s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 157779.46it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2466, N high rez matches = 26828. Low rez D= 0.7823327342819202, high rez D=0.8801163437028209\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00 to clahe_padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1423.24        1843.50096712] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1372.29it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:52<00:00,  4.39s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 115175.40it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1373, N high rez matches = 25054. Low rez D= 0.6458698110326911, high rez D=0.7054161487366128\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1447.25313433  1825.95261122] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1378.53it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:51<00:00,  4.26s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 149796.57it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1996, N high rez matches = 28488. Low rez D= 0.789308417125362, high rez D=0.8896392044230425\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1448.17671642  1846.27176015] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1433.79it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:59<00:00,  4.96s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 175371.60it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1753, N high rez matches = 32412. Low rez D= 0.5769541590615364, high rez D=0.6806557883082865\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00 to clahe_padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1414.92776119  1837.03578337] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1420.67it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:58<00:00,  4.91s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 152059.36it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1586, N high rez matches = 30494. Low rez D= 0.7722528187501618, high rez D=0.878837708547222\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00. ROI width, height is [ 1433.39940299  1845.34816248] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1661.55it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [01:02<00:00,  5.20s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 177850.35it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1420, N high rez matches = 28800. Low rez D= 0.6122532854140905, high rez D=0.6883108474438349\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1402.92119403  1811.17504836] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1273.90it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:57<00:00,  4.81s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 148034.26it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2070, N high rez matches = 25706. Low rez D= 0.6250255484564078, high rez D=0.7268514332098409\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00 to clahe_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00. ROI width, height is [ 1428.78149254  1847.19535783] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 12/12 [00:00<00:00, 1289.96it/s]\n", "PROCESSING TASKS | : 100%|██████████| 12/12 [00:47<00:00,  4.00s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 12/12 [00:00<00:00, 129387.27it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2310, N high rez matches = 31997. Low rez D= 0.4552043447743701, high rez D=0.536495539007536\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 10/10 [00:12<00:00,  1.22s/image]\n", "Finding non-rigid transforms: 100%|██████████| 10/10 [00:41<00:00,  4.17s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 42.166 seconds\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 10/10 [00:06<00:00,  1.44image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Performing micro-registration...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 10/10 [00:14<00:00,  1.43s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 10/10 [01:15<00:00,  7.54s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 1.27 minutes\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 10/10 [00:11<00:00,  1.16s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Applying registration transforms to original intensity padded images...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/dapi_warped/warped_padded_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00.tif\n", "Finding matches for round: lb08 bp lung 4i cd45 green 18apr25.lif - r 1\n", "Found 1 matching channels\n", "Finding matches for round: lb08 bp lung 4i hmgb1 green pcna red 10apr25.lif - r 1\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i il1a green ip10 red 16apr25.lif - r1 \n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i laminb1 green bcl2red 11apr25.lif - r 1\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i mmp red 19apr25.lif - r 1\n", "Found 1 matching channels\n", "Finding matches for round: lb08 bp lung 4i pancytok green vimentin red 17apr25.lif - r 1\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i asma green cd31 red 15apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i as<PERSON> green didnt work cxcr3 red 14apr25.lif - r 1\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i p21 green p16 red 9apr25.lif - r 1\n", "Found 2 matching channels\n", "Finding matches for round: lb08 bp lung 4i vimentin red 22apr25.lif - r 1\n", "Found 1 matching channels\n", "Warping all other channels with original intensities...\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch01.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i IL1a green IP10 red 16apr25.lif - R1 _Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch01.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.lif - R 2_Merged-1.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.lif - R 2_Merged-1.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch01.tif\n", "Warping channel: padded_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00\n", "Warping channel: padded_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels/warped_LB08 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch01.tif\n", "\n", "============================================================\n", "REGISTRATION COMPLETE!\n", "============================================================\n", "✅ CLAHE applied ONLY to padded DAPI (ch00) for registration\n", "✅ All channels warped with ORIGINAL intensities preserved\n", "📁 Original padded images: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/padded_images\n", "📁 CLAHE-padded DAPI (temp): /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/clahe_padded_dapi\n", "📁 Final warped images: /mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/registration/warped_channels\n", "\n", "Cleaning up temporary CLAHE-padded files...\n", "✅ Cleaned up CLAHE-padded temporary files\n", "JV<PERSON> has been killed. If this was due to an error, then a new Python session will need to be started\n"]}], "source": ["import os\n", "import pathlib\n", "import numpy as np\n", "from pathlib import Path\n", "from tqdm.notebook import tqdm\n", "import pyvips\n", "import cv2\n", "from skimage import exposure\n", "from valis import valtils, slide_io, registration\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar\n", "import os\n", "import shutil\n", "from pathlib import Path\n", "import re\n", "\n", "# Add missing imports\n", "try:\n", "    from tqdm.notebook import tqdm\n", "except ImportError:\n", "    from tqdm import tqdm\n", "    print(\"tqdm.notebook not found. Using tqdm instead.\")\n", "\n", "\n", "\n", "\n", "def organize_channels_automatically(base_dir, target_dapi_dir=None, target_all_channels_dir=None):\n", "    \"\"\"\n", "    Automatically organize channel files from a base directory\n", "    \n", "    Args:\n", "        base_dir: Path to the folder containing all channel files (e.g., \".../lb06/R2/\")\n", "        target_dapi_dir: Where to move ch00 files (if None, creates dapi subfolder)\n", "        target_all_channels_dir: Where to move ch01/ch02 files (if None, creates all_channels subfolder)\n", "    \"\"\"\n", "    \n", "    base_path = Path(base_dir)\n", "    \n", "    # Create target directories if not specified\n", "    if target_dapi_dir is None:\n", "        target_dapi_dir = base_path / \"dapi\"\n", "    else:\n", "        target_dapi_dir = Path(target_dapi_dir)\n", "        \n", "    if target_all_channels_dir is None:\n", "        target_all_channels_dir = base_path / \"all_channels\"\n", "    else:\n", "        target_all_channels_dir = Path(target_all_channels_dir)\n", "    \n", "    # Create directories if they don't exist\n", "    target_dapi_dir.mkdir(parents=True, exist_ok=True)\n", "    target_all_channels_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"Organizing files from: {base_path}\")\n", "    print(f\"DAPI (ch00) files will go to: {target_dapi_dir}\")\n", "    print(f\"Other channels will go to: {target_all_channels_dir}\")\n", "    print(\"-\" * 60)\n", "    \n", "    # Get all files in the base directory\n", "    all_files = [f for f in base_path.iterdir() if f.is_file()]\n", "    \n", "    moved_files = {\"dapi\": [], \"other_channels\": [], \"unrecognized\": []}\n", "    \n", "    for file_path in all_files:\n", "        filename = file_path.name.lower()\n", "        \n", "        # Check for channel patterns\n", "        if \"_ch00\" in filename or \"_merged_ch00\" in filename:\n", "            # This is a DAPI file\n", "            target_path = target_dapi_dir / file_path.name\n", "            shutil.move(str(file_path), str(target_path))\n", "            moved_files[\"dapi\"].append(file_path.name)\n", "            print(f\"📁 DAPI: {file_path.name}\")\n", "            \n", "        elif \"_ch01\" in filename or \"_ch02\" in filename or \"_merged_ch01\" in filename or \"_merged_ch02\" in filename:\n", "            # This is another channel\n", "            target_path = target_all_channels_dir / file_path.name\n", "            shutil.move(str(file_path), str(target_path))\n", "            moved_files[\"other_channels\"].append(file_path.name)\n", "            print(f\"🔬 Channel: {file_path.name}\")\n", "            \n", "        else:\n", "            # Unrecognized file pattern\n", "            moved_files[\"unrecognized\"].append(file_path.name)\n", "            print(f\"❓ Unrecognized: {file_path.name}\")\n", "    \n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"ORGANIZATION COMPLETE!\")\n", "    print(\"=\"*60)\n", "    print(f\"✅ DAPI files moved: {len(moved_files['dapi'])}\")\n", "    print(f\"✅ Other channel files moved: {len(moved_files['other_channels'])}\")\n", "    print(f\"⚠️  Unrecognized files: {len(moved_files['unrecognized'])}\")\n", "    \n", "    if moved_files[\"unrecognized\"]:\n", "        print(\"\\nUnrecognized files (not moved):\")\n", "        for filename in moved_files[\"unrecognized\"]:\n", "            print(f\"  - {filename}\")\n", "    \n", "    return moved_files\n", "\n", "def organize_multiple_rounds(parent_dir, rounds=None):\n", "    \"\"\"\n", "    Organize multiple rounds at once\n", "    \n", "    Args:\n", "        parent_dir: Path containing multiple round folders (e.g., \".../lb06/\")\n", "        rounds: List of round names (e.g., [\"R1\", \"R2\", \"R3\"]) or None for auto-detect\n", "    \"\"\"\n", "    \n", "    parent_path = Path(parent_dir)\n", "    \n", "    if rounds is None:\n", "        # Auto-detect round folders\n", "        rounds = [d.name for d in parent_path.iterdir() if d.is_dir() and d.name.startswith('R')]\n", "        rounds.sort()  # Sort to process in order\n", "    \n", "    print(f\"Found rounds: {rounds}\")\n", "    \n", "    for round_name in rounds:\n", "        round_path = parent_path / round_name\n", "        if round_path.exists():\n", "            print(f\"\\n{'='*20} PROCESSING {round_name} {'='*20}\")\n", "            organize_channels_automatically(round_path)\n", "        else:\n", "            print(f\"⚠️  Round folder not found: {round_path}\")\n", "\n", "\n", "########################################## SET UP YOUR BASE DIRECTORY HERE ##############################################################################################################################################\n", "\n", "\n", "\n", "\n", "# Set up base directory and automatically organize files\n", "base_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/lb08/R1/\"\n", "\n", "\n", "#####################################################################################################################################################################################################\n", "# Organize files automatically\n", "print(\"Organizing channel files...\")\n", "organize_channels_automatically(base_dir)\n", "\n", "# Set up directories using the organized structure\n", "slide_src_dir = Path(base_dir) / \"dapi\"  # Now points to organized DAPI folder\n", "path_to_all_channels = Path(base_dir) / \"all_channels\"  # Now points to organized channels folder\n", "\n", "# Results directory\n", "results_dst_dir = Path(base_dir) / \"registration\"\n", "results_dst_dir.mkdir(parents=True, exist_ok=True)\n", "registered_slide_dst_dir = results_dst_dir / \"registered_slides\"\n", "registered_slide_dst_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "########################################## UPDATE THE REFERENCE SLIDE HERE ##############################################################################################################################################\n", "\n", "# Reference slide - now from the organized DAPI folder\n", "reference_slide_name = \"LB08 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\"  # Update this to your actual reference\n", "reference_slide = str(slide_src_dir / reference_slide_name)\n", "\n", "##########################################################################################################################################################################################\n", "\n", "\n", "print(f\"✅ DAPI images directory: {slide_src_dir}\")\n", "print(f\"✅ All channels directory: {path_to_all_channels}\")\n", "print(f\"✅ Results directory: {results_dst_dir}\")\n", "print(f\"✅ Reference slide: {reference_slide}\")\n", "\n", "# Define helper functions\n", "def get_round_name(src_f):\n", "    \"\"\"Extract the round name from the filename\"\"\"\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_channel_number(src_f):\n", "    \"\"\"Extract the channel number from the filename\"\"\"\n", "    img_name = os.path.basename(src_f).lower()\n", "    if \"_ch00\" in img_name:\n", "        return 0\n", "    elif \"_ch01\" in img_name:\n", "        return 1\n", "    elif \"_ch02\" in img_name:\n", "        return 2\n", "    else:\n", "        return None\n", "\n", "def pad_image(img, target_width, target_height):\n", "    \"\"\"Pad image to target dimensions, centering the original content\"\"\"\n", "    pad_width = max(0, target_width - img.width)\n", "    pad_height = max(0, target_height - img.height)\n", "    \n", "    if pad_width > 0 or pad_height > 0:\n", "        pad_left = pad_width // 2\n", "        pad_top = pad_height // 2\n", "        padded = img.embed(pad_left, pad_top, target_width, target_height, extend=\"black\")\n", "        return padded\n", "    return img\n", "\n", "def apply_clahe_normalization(img_array, clip_limit=2.0, tile_grid_size=(16,16)):\n", "    \"\"\"Apply CLAHE normalization to improve contrast for registration\"\"\"\n", "    if img_array.dtype != np.uint8:\n", "        # Convert to uint8 for CLAHE\n", "        img_normalized = exposure.rescale_intensity(img_array, out_range=(0, 255)).astype(np.uint8)\n", "    else:\n", "        img_normalized = img_array.copy()\n", "    \n", "    # Apply CLAHE\n", "    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)\n", "    clahe_img = clahe.apply(img_normalized)\n", "    \n", "    return clahe_img\n", "\n", "def create_clahe_padded_image(img_path, target_width, target_height, output_path, clip_limit=2.0, tile_grid_size=(16, 16)):\n", "    \"\"\"Pad image first, then apply CLAHE to the padded result\"\"\"\n", "    try:\n", "        # Load original image\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        \n", "        # Step 1: Pad the image\n", "        padded_img = pad_image(img, target_width, target_height)\n", "        \n", "        # Step 2: Convert padded image to numpy for CLAHE processing\n", "        img_array = np.ndarray(buffer=padded_img.write_to_memory(),\n", "                              dtype=np.uint8 if padded_img.format == 'uchar' else np.uint16,\n", "                              shape=[padded_img.height, padded_img.width, padded_img.bands])\n", "        \n", "        # Step 3: Apply CLAHE to first channel (DAPI)\n", "        if padded_img.bands == 1:\n", "            clahe_array = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "            clahe_array = clahe_array[:, :, np.newaxis]\n", "        else:\n", "            # For multi-channel, apply CLAHE to first channel only\n", "            clahe_array = img_array.copy()\n", "            clahe_array[:, :, 0] = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "        \n", "        # Step 4: Convert back to pyvips\n", "        clahe_padded_img = pyvips.Image.new_from_memory(clahe_array.data,\n", "                                                       clahe_array.shape[1],\n", "                                                       clahe_array.shape[0],\n", "                                                       clahe_array.shape[2],\n", "                                                       'uchar' if clahe_array.dtype == np.uint8 else 'ushort')\n", "        \n", "        # Step 5: Save CLAHE-processed padded image\n", "        clahe_padded_img.write_to_file(output_path)\n", "        return output_path\n", "        \n", "    except Exception as e:\n", "        print(f\"Error creating CLAHE-padded image for {img_path}: {str(e)}\")\n", "        return None\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    \"\"\"Generate ome-xml for warped slide\"\"\"\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Process images: PAD FIRST, then CLAHE on DAPI only\n", "print(\"Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\")\n", "\n", "# Create output directories\n", "src_dir = str(slide_src_dir)\n", "dst_dir = str(results_dst_dir)\n", "all_channels_dir = str(path_to_all_channels)\n", "\n", "# Directory for padded images (original intensities)\n", "padded_dir = os.path.join(dst_dir, \"padded_images\")\n", "os.makedirs(padded_dir, exist_ok=True)\n", "padded_dapi_dir = os.path.join(padded_dir, \"dapi\")\n", "os.makedirs(padded_dapi_dir, exist_ok=True)\n", "padded_all_channels_dir = os.path.join(padded_dir, \"all_channels\")\n", "os.makedirs(padded_all_channels_dir, exist_ok=True)\n", "padded_ref_dir = os.path.join(padded_dir, \"reference\")\n", "os.makedirs(padded_ref_dir, exist_ok=True)\n", "\n", "# Directory for CLAHE-padded DAPI images (for registration only)\n", "clahe_padded_dir = os.path.join(dst_dir, \"clahe_padded_dapi\")\n", "os.makedirs(clahe_padded_dir, exist_ok=True)\n", "\n", "# Directory for individual warped channels\n", "individual_channel_dir = os.path.join(dst_dir, \"warped_channels\")\n", "os.makedirs(individual_channel_dir, exist_ok=True)\n", "\n", "# Get all images\n", "dapi_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "all_channel_imgs = [os.path.join(all_channels_dir, f) for f in os.listdir(all_channels_dir)]\n", "\n", "print(f\"Found {len(dapi_imgs)} DAPI images for registration\")\n", "print(f\"Found {len(all_channel_imgs)} other channel images\")\n", "\n", "# STEP 1: Find maximum dimensions across all images\n", "max_width = 0\n", "max_height = 0\n", "\n", "print(\"Finding maximum dimensions across all images...\")\n", "for img_path in dapi_imgs + all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        max_width = max(max_width, img.width)\n", "        max_height = max(max_height, img.height)\n", "    except Exception as e:\n", "        print(f\"Error reading {img_path}: {str(e)}\")\n", "\n", "print(f\"Maximum dimensions: {max_width} x {max_height}\")\n", "\n", "# STEP 2: Create padded images (original intensities) AND CLAHE-padded DAPI images\n", "print(\"Creating padded images and CLAHE-padded DAPI images...\")\n", "\n", "# Process reference image\n", "ref_img = pyvips.Image.new_from_file(reference_slide)\n", "padded_ref_img = pad_image(ref_img, max_width, max_height)\n", "ref_basename = os.path.basename(reference_slide)\n", "padded_reference_slide = os.path.join(padded_ref_dir, f\"padded_{ref_basename}\")\n", "padded_ref_img.write_to_file(padded_reference_slide)\n", "print(f\"Padded reference: {padded_reference_slide}\")\n", "\n", "# Create CLAHE-padded version of reference for registration\n", "clahe_padded_reference = os.path.join(clahe_padded_dir, f\"clahe_padded_{ref_basename}\")\n", "create_clahe_padded_image(reference_slide, max_width, max_height, clahe_padded_reference)\n", "print(f\"CLAHE-padded reference: {clahe_padded_reference}\")\n", "\n", "# Process DAPI images\n", "padded_dapi_paths = []\n", "clahe_padded_dapi_paths = []\n", "\n", "for img_path in dapi_imgs:\n", "    try:\n", "        basename = os.path.basename(img_path)\n", "        \n", "        # Create padded version (original intensities)\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        padded_path = os.path.join(padded_dapi_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_dapi_paths.append(padded_path)\n", "        \n", "        # Create CLAHE-padded version (for registration)\n", "        clahe_padded_path = os.path.join(clahe_padded_dir, f\"clahe_padded_{basename}\")\n", "        if create_clahe_padded_image(img_path, max_width, max_height, clahe_padded_path):\n", "            clahe_padded_dapi_paths.append(clahe_padded_path)\n", "        \n", "        print(f\"Processed DAPI: {basename}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {img_path}: {str(e)}\")\n", "\n", "# Process all channel images (only padded, no CLAHE)\n", "padded_all_channel_paths = []\n", "for img_path in all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        \n", "        basename = os.path.basename(img_path)\n", "        padded_path = os.path.join(padded_all_channels_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_all_channel_paths.append(padded_path)\n", "        print(f\"Padded channel: {basename}\")\n", "    except Exception as e:\n", "        print(f\"Error padding {img_path}: {str(e)}\")\n", "\n", "# STEP 3: Perform registration using CLAHE-PADDED DAPI images\n", "print(\"Performing registration with CLAHE-padded DAPI images...\")\n", "registrar = registration.Valis(\n", "    clahe_padded_dir,  # Directory with CLAHE-padded DAPI images\n", "    dst_dir, \n", "    reference_img_f=clahe_padded_reference,\n", "    align_to_reference=True,\n", "    micro_rigid_registrar_cls=MicroRigidRegistrar,\n", "    max_processed_image_dim_px=2000,\n", "    max_non_rigid_registration_dim_px=1500,\n", "    norm_method='img_stats'\n", ")\n", "\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Perform micro-registration\n", "print(\"Performing micro-registration...\")\n", "micro_reg, micro_error = registrar.register_micro(\n", "    max_non_rigid_registration_dim_px=2000,\n", "    align_to_reference=True\n", ")\n", "\n", "# STEP 4: Apply registration transforms to original padded images\n", "print(\"Applying registration transforms to original intensity padded images...\")\n", "\n", "# Create mapping between CLAHE-padded slides and original padded slides\n", "clahe_to_original_mapping = {}\n", "for slide_name, slide_obj in registrar.slide_dict.items():\n", "    # Find corresponding original padded image\n", "    clahe_basename = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"padded_\")\n", "    original_path = os.path.join(padded_dapi_dir, clahe_basename)\n", "    clahe_to_original_mapping[slide_obj] = original_path\n", "\n", "# Warp original padded DAPI images\n", "dapi_warped_dir = os.path.join(individual_channel_dir, \"dapi_warped\")\n", "os.makedirs(dapi_warped_dir, exist_ok=True)\n", "\n", "for slide_obj, original_dapi_path in clahe_to_original_mapping.items():\n", "    try:\n", "        # Load original padded DAPI (original intensities)\n", "        original_dapi_img = pyvips.Image.new_from_file(original_dapi_path)\n", "        \n", "        # Apply transformation found using CLAHE-padded DAPI\n", "        warped_dapi = slide_obj.warp_img(img=original_dapi_img, non_rigid=True, crop=False)\n", "        \n", "        basename = os.path.basename(original_dapi_path)\n", "        warped_path = os.path.join(dapi_warped_dir, f\"warped_{basename}\")\n", "        warped_dapi.write_to_file(warped_path)\n", "        print(f\"Warped original DAPI: {warped_path}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error warping original DAPI {slide_obj.name}: {str(e)}\")\n", "\n", "# Create mapping between DAPI slides and their corresponding other channels\n", "round_dict = {}\n", "for slide_obj in registrar.slide_dict.values():\n", "    # Get the original filename\n", "    original_name = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"\")\n", "    base_round_name = get_round_name(original_name)\n", "    print(f\"Finding matches for round: {base_round_name}\")\n", "    \n", "    # Find all padded channels that belong to this round\n", "    matching_channels = []\n", "    for padded_path in padded_all_channel_paths:\n", "        padded_basename = os.path.basename(padded_path).replace(\"padded_\", \"\")\n", "        if get_round_name(padded_basename) == base_round_name:\n", "            matching_channels.append(padded_path)\n", "    \n", "    print(f\"Found {len(matching_channels)} matching channels\")\n", "    round_dict[slide_obj] = matching_channels\n", "\n", "# Warp all other channels (ch01, ch02) with original intensities\n", "print(\"Warping all other channels with original intensities...\")\n", "for slide_obj, round_slides in round_dict.items():\n", "    print(f\"\\nWarping channels for round: {slide_obj.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    \n", "    for src_f in round_slides:\n", "        img_name = os.path.basename(src_f)\n", "        print(f\"Warping channel: {img_name}\")\n", "        \n", "        try:\n", "            # Load original intensity padded channel\n", "            channel_img = pyvips.Image.new_from_file(src_f)\n", "            \n", "            # Apply transformation found using CLAHE-padded DAPI\n", "            warped_channel = slide_obj.warp_img(img=channel_img, non_rigid=True, crop=False)\n", "            \n", "            channel_basename = os.path.basename(src_f).replace(\"padded_\", \"\")\n", "            individual_channel_path = os.path.join(individual_channel_dir, f\"warped_{channel_basename}\")\n", "            warped_channel.write_to_file(individual_channel_path)\n", "            print(f\"Saved warped channel: {individual_channel_path}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing {src_f}: {str(e)}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"REGISTRATION COMPLETE!\")\n", "print(\"=\"*60)\n", "print(f\"✅ CLAHE applied ONLY to padded DAPI (ch00) for registration\")\n", "print(f\"✅ All channels warped with ORIGINAL intensities preserved\")\n", "print(f\"📁 Original padded images: {padded_dir}\")\n", "print(f\"📁 CLAHE-padded DAPI (temp): {clahe_padded_dir}\")\n", "print(f\"📁 Final warped images: {individual_channel_dir}\")\n", "\n", "# Clean up CLAHE-padded directory\n", "print(\"\\nCleaning up temporary CLAHE-padded files...\")\n", "import shutil\n", "shutil.rmtree(clahe_padded_dir)\n", "print(\"✅ Cleaned up CLAHE-padded temporary files\")\n", "\n", "# Kill JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": null, "id": "a67063e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\n", "Found 10 DAPI images for registration\n", "Found 17 other channel images\n", "Finding maximum dimensions across all images...\n", "Maximum dimensions: 11072 x 9344\n", "Creating padded images and CLAHE-padded DAPI images...\n", "Padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/padded_images/reference/padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\n", "CLAHE-padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/clahe_padded_dapi/clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00.tif\n", "Padded channel: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch01.tif\n", "Performing registration with CLAHE-padded DAPI images...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/PIL/Image.py:3176: DecompressionBombWarning: Image size (103456768 pixels) exceeds limit of 89478485 pixels, could be decompression bomb DOS attack.\n", "  warnings.warn(\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: max_image_dim_px is 1024 but needs to be less or equal to 2000. Setting max_image_dim_px to 2000\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/10 [00:00<?, ?image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a553f040> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  10%|█         | 1/10 [00:00<00:06,  1.36image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfe410> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  20%|██        | 2/10 [00:01<00:05,  1.36image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acf5d120> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  30%|███       | 3/10 [00:02<00:05,  1.36image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfd4b0> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  40%|████      | 4/10 [00:02<00:04,  1.36image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfe3e0> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  50%|█████     | 5/10 [00:03<00:03,  1.36image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe3582bcc40> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  60%|██████    | 6/10 [00:04<00:02,  1.35image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfe890> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  70%|███████   | 7/10 [00:05<00:02,  1.33image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfe530> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  80%|████████  | 8/10 [00:05<00:01,  1.33image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfeb00> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  90%|█████████ | 9/10 [00:06<00:00,  1.32image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00>, width=11072, height=9344, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe3582bd0f0> False (1688, 2000)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 10/10 [00:07<00:00,  1.34image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 10/10 [00:16<00:00,  1.66s/image]\n", "Normalizing images: 100%|██████████| 10/10 [00:05<00:00,  1.94image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Detecting features   : 100%|██████████| 10/10 [01:35<00:00,  9.56s/image]\n", "QUEUEING TASKS | Matching images      : 100%|██████████| 10/10 [00:00<00:00, 416.79image/s]\n", "PROCESSING TASKS | Matching images      : 100%|██████████| 10/10 [00:10<00:00,  1.07s/image]\n", "COLLECTING RESULTS | Matching images      : 100%|██████████| 10/10 [00:00<00:00, 95979.50image/s]\n", "Finding transforms   : 100%|██████████| 9/9 [00:00<00:00, 815.41image/s]\n", "Finalizing           : 100%|██████████| 10/10 [00:00<00:00, 5632.21image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 1.84 minutes\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:598: RuntimeWarning: divide by zero encountered in divide\n", "  weight = grey_img_list[i]/sum_img\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:598: RuntimeWarning: invalid value encountered in divide\n", "  weight = grey_img_list[i]/sum_img\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/viz.py:601: RuntimeWarning: invalid value encountered in add\n", "  blended_img += lab_clr * np.dstack([grey_img_list[i]/max_v * weight]*3)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Micro-rigid registration\n", "\n", "Aligning clahe_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1370.9506705   1064.26659586] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1282.84it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:33<00:00,  3.68s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 128835.28it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1457, N high rez matches = 15485. Low rez D= 0.8011137527926043, high rez D=0.9217354546024138\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1356.41762452  1147.99628253] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 2087.87it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:25<00:00,  2.83s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 98560.67it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2262, N high rez matches = 18499. Low rez D= 0.9738083961707527, high rez D=1.1510432832070545\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1382.02346743  1177.75146044] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1577.60it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:22<00:00,  2.53s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 112682.79it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2490, N high rez matches = 17637. Low rez D= 1.0969794892606826, high rez D=1.2599067340701176\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1356.41762452  1166.67976633] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1550.38it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:19<00:00,  2.22s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 111025.69it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2590, N high rez matches = 13861. Low rez D= 1.1333905242009092, high rez D=1.1860030500299832\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1382.71551724  1123.77695167] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1502.20it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:29<00:00,  3.28s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 118334.60it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2552, N high rez matches = 17699. Low rez D= 0.8998902987233657, high rez D=0.8053407584211313\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1375.10296935  1162.52788104] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1484.94it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:37<00:00,  4.17s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 99338.78it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1329, N high rez matches = 20039. Low rez D= 0.9710615093358899, high rez D=1.0967103554866042\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1379.94731801  1121.70100903] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1853.24it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:34<00:00,  3.88s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 120602.99it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1142, N high rez matches = 14953. Low rez D= 1.073439852234108, high rez D=1.3409404824926345\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1360.56992337  1143.84439724] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1153.55it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:34<00:00,  3.88s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 119081.19it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 1813, N high rez matches = 15754. Low rez D= 0.9763277336736573, high rez D=1.3608059474899927\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Aligning clahe_padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00 to clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00. ROI width, height is [ 1365.41427203  1160.4519384 ] pixels\n"]}, {"name": "stderr", "output_type": "stream", "text": ["QUEUEING TASKS | : 100%|██████████| 9/9 [00:00<00:00, 1695.81it/s]\n", "PROCESSING TASKS | : 100%|██████████| 9/9 [00:23<00:00,  2.66s/it]\n", "COLLECTING RESULTS | : 100%|██████████| 9/9 [00:00<00:00, 108162.57it/s]\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: micro rigid registration improved alignments. N low rez matches= 2201, N high rez matches = 17192. Low rez D= 0.856942600320012, high rez D=0.9558749220824163\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Non-rigid registration\n", "\n", "Creating non-rigid mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 10/10 [00:08<00:00,  1.11image/s]\n", "Finding non-rigid transforms: 100%|██████████| 10/10 [00:43<00:00,  4.34s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 43.828 seconds\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 10/10 [00:06<00:00,  1.53image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Performing micro-registration...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Preparing images for non-rigid registration: 100%|██████████| 10/10 [00:10<00:00,  1.10s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Performing microregistration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Finding non-rigid transforms: 100%|██████████| 10/10 [01:20<00:00,  8.10s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Non-rigid registration complete in 1.363 minutes\n", "\n", "\n", "==== Measuring error\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Measuring error: 100%|██████████| 10/10 [00:11<00:00,  1.13s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Applying registration transforms to original intensity padded images...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Warped original DAPI: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/dapi_warped/warped_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00.tif\n", "Finding matches for round: lb06 bp lung 4i cd45 green 18apr25.lif - r 2\n", "Found 1 matching channels\n", "Finding matches for round: lb06 bp lung 4i hmgb1 green pcna red 10apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i il1a green ip10 red 16apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i laminb1 green bcl2red 11apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i mmp red 19apr25.lif - r 2\n", "Found 1 matching channels\n", "Finding matches for round: lb06 bp lung 4i pancytok green vimentin red 17apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i asma green cd31 red 15apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i as<PERSON> green didnt work cxcr3 red 14apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i p21 green p16 red 9apr25.lif - r 2\n", "Found 2 matching channels\n", "Finding matches for round: lb06 bp lung 4i vimentin red 22apr25.lif - r 2\n", "Found 1 matching channels\n", "Warping all other channels with original intensities...\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch01.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i MMP red 19apr25.lif - R 2_Merged_ch01.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch01.tif\n", "Warping channel: padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch02.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch02.tif\n", "\n", "Warping channels for round: clahe_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch00\n", "Warping channel: padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch01.tif\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: scaling transformation for image with different shape. However, without knowing all of other image's shapes, the scaling may not be the same for all images, and so may not overlap.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved warped channel: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels/warped_LB06 BP lung 4i vimentin red 22apr25.lif - R 2_Merged_ch01.tif\n", "\n", "============================================================\n", "REGISTRATION COMPLETE!\n", "============================================================\n", "✅ CLAHE applied ONLY to padded DAPI (ch00) for registration\n", "✅ All channels warped with ORIGINAL intensities preserved\n", "📁 Original padded images: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/padded_images\n", "📁 CLAHE-padded DAPI (temp): /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/clahe_padded_dapi\n", "📁 Final warped images: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/warped_channels\n", "\n", "Cleaning up temporary CLAHE-padded files...\n", "✅ Cleaned up CLAHE-padded temporary files\n", "JV<PERSON> has been killed. If this was due to an error, then a new Python session will need to be started\n"]}], "source": ["import os\n", "import pathlib\n", "import numpy as np\n", "from pathlib import Path\n", "from tqdm.notebook import tqdm\n", "import pyvips\n", "import cv2\n", "from skimage import exposure\n", "from valis import valtils, slide_io, registration\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar\n", "\n", "# Add missing imports\n", "try:\n", "    from tqdm.notebook import tqdm\n", "except ImportError:\n", "    from tqdm import tqdm\n", "    print(\"tqdm.notebook not found. Using tqdm instead.\")\n", "\n", "# Set up directories and reference slide\n", "slide_src_dir = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/dapi/\")\n", "results_dst_dir = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/registration/\")\n", "results_dst_dir.mkdir(parents=True, exist_ok=True)\n", "registered_slide_dst_dir = results_dst_dir / \"registered_slides\"\n", "registered_slide_dst_dir.mkdir(parents=True, exist_ok=True)\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/dapi/LB06 BP lung 4i CD45 green 18apr25.lif - R 2_Merged_ch00.tif\"\n", "path_to_all_channels = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/all channels/\")\n", "\n", "# Define helper functions\n", "def get_round_name(src_f):\n", "    \"\"\"Extract the round name from the filename\"\"\"\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_channel_number(src_f):\n", "    \"\"\"Extract the channel number from the filename\"\"\"\n", "    img_name = os.path.basename(src_f).lower()\n", "    if \"_ch00\" in img_name:\n", "        return 0\n", "    elif \"_ch01\" in img_name:\n", "        return 1\n", "    elif \"_ch02\" in img_name:\n", "        return 2\n", "    else:\n", "        return None\n", "\n", "def pad_image(img, target_width, target_height):\n", "    \"\"\"Pad image to target dimensions, centering the original content\"\"\"\n", "    pad_width = max(0, target_width - img.width)\n", "    pad_height = max(0, target_height - img.height)\n", "    \n", "    if pad_width > 0 or pad_height > 0:\n", "        pad_left = pad_width // 2\n", "        pad_top = pad_height // 2\n", "        padded = img.embed(pad_left, pad_top, target_width, target_height, extend=\"black\")\n", "        return padded\n", "    return img\n", "\n", "def apply_clahe_normalization(img_array, clip_limit=2.0, tile_grid_size=(16,16)):\n", "    \"\"\"Apply CLAHE normalization to improve contrast for registration\"\"\"\n", "    if img_array.dtype != np.uint8:\n", "        # Convert to uint8 for CLAHE\n", "        img_normalized = exposure.rescale_intensity(img_array, out_range=(0, 255)).astype(np.uint8)\n", "    else:\n", "        img_normalized = img_array.copy()\n", "    \n", "    # Apply CLAHE\n", "    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)\n", "    clahe_img = clahe.apply(img_normalized)\n", "    \n", "    return clahe_img\n", "\n", "def create_clahe_padded_image(img_path, target_width, target_height, output_path, clip_limit=2.0, tile_grid_size=(16, 16)):\n", "    \"\"\"Pad image first, then apply CLAHE to the padded result\"\"\"\n", "    try:\n", "        # Load original image\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        \n", "        # Step 1: Pad the image\n", "        padded_img = pad_image(img, target_width, target_height)\n", "        \n", "        # Step 2: Convert padded image to numpy for CLAHE processing\n", "        img_array = np.ndarray(buffer=padded_img.write_to_memory(),\n", "                              dtype=np.uint8 if padded_img.format == 'uchar' else np.uint16,\n", "                              shape=[padded_img.height, padded_img.width, padded_img.bands])\n", "        \n", "        # Step 3: Apply CLAHE to first channel (DAPI)\n", "        if padded_img.bands == 1:\n", "            clahe_array = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "            clahe_array = clahe_array[:, :, np.newaxis]\n", "        else:\n", "            # For multi-channel, apply CLAHE to first channel only\n", "            clahe_array = img_array.copy()\n", "            clahe_array[:, :, 0] = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "        \n", "        # Step 4: Convert back to pyvips\n", "        clahe_padded_img = pyvips.Image.new_from_memory(clahe_array.data,\n", "                                                       clahe_array.shape[1],\n", "                                                       clahe_array.shape[0],\n", "                                                       clahe_array.shape[2],\n", "                                                       'uchar' if clahe_array.dtype == np.uint8 else 'ushort')\n", "        \n", "        # Step 5: Save CLAHE-processed padded image\n", "        clahe_padded_img.write_to_file(output_path)\n", "        return output_path\n", "        \n", "    except Exception as e:\n", "        print(f\"Error creating CLAHE-padded image for {img_path}: {str(e)}\")\n", "        return None\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    \"\"\"Generate ome-xml for warped slide\"\"\"\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Process images: PAD FIRST, then CLAHE on DAPI only\n", "print(\"Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\")\n", "\n", "# Create output directories\n", "src_dir = str(slide_src_dir)\n", "dst_dir = str(results_dst_dir)\n", "all_channels_dir = str(path_to_all_channels)\n", "\n", "# Directory for padded images (original intensities)\n", "padded_dir = os.path.join(dst_dir, \"padded_images\")\n", "os.makedirs(padded_dir, exist_ok=True)\n", "padded_dapi_dir = os.path.join(padded_dir, \"dapi\")\n", "os.makedirs(padded_dapi_dir, exist_ok=True)\n", "padded_all_channels_dir = os.path.join(padded_dir, \"all_channels\")\n", "os.makedirs(padded_all_channels_dir, exist_ok=True)\n", "padded_ref_dir = os.path.join(padded_dir, \"reference\")\n", "os.makedirs(padded_ref_dir, exist_ok=True)\n", "\n", "# Directory for CLAHE-padded DAPI images (for registration only)\n", "clahe_padded_dir = os.path.join(dst_dir, \"clahe_padded_dapi\")\n", "os.makedirs(clahe_padded_dir, exist_ok=True)\n", "\n", "# Directory for individual warped channels\n", "individual_channel_dir = os.path.join(dst_dir, \"warped_channels\")\n", "os.makedirs(individual_channel_dir, exist_ok=True)\n", "\n", "# Get all images\n", "dapi_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "all_channel_imgs = [os.path.join(all_channels_dir, f) for f in os.listdir(all_channels_dir)]\n", "\n", "print(f\"Found {len(dapi_imgs)} DAPI images for registration\")\n", "print(f\"Found {len(all_channel_imgs)} other channel images\")\n", "\n", "# STEP 1: Find maximum dimensions across all images\n", "max_width = 0\n", "max_height = 0\n", "\n", "print(\"Finding maximum dimensions across all images...\")\n", "for img_path in dapi_imgs + all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        max_width = max(max_width, img.width)\n", "        max_height = max(max_height, img.height)\n", "    except Exception as e:\n", "        print(f\"Error reading {img_path}: {str(e)}\")\n", "\n", "print(f\"Maximum dimensions: {max_width} x {max_height}\")\n", "\n", "# STEP 2: Create padded images (original intensities) AND CLAHE-padded DAPI images\n", "print(\"Creating padded images and CLAHE-padded DAPI images...\")\n", "\n", "# Process reference image\n", "ref_img = pyvips.Image.new_from_file(reference_slide)\n", "padded_ref_img = pad_image(ref_img, max_width, max_height)\n", "ref_basename = os.path.basename(reference_slide)\n", "padded_reference_slide = os.path.join(padded_ref_dir, f\"padded_{ref_basename}\")\n", "padded_ref_img.write_to_file(padded_reference_slide)\n", "print(f\"Padded reference: {padded_reference_slide}\")\n", "\n", "# Create CLAHE-padded version of reference for registration\n", "clahe_padded_reference = os.path.join(clahe_padded_dir, f\"clahe_padded_{ref_basename}\")\n", "create_clahe_padded_image(reference_slide, max_width, max_height, clahe_padded_reference)\n", "print(f\"CLAHE-padded reference: {clahe_padded_reference}\")\n", "\n", "# Process DAPI images\n", "padded_dapi_paths = []\n", "clahe_padded_dapi_paths = []\n", "\n", "for img_path in dapi_imgs:\n", "    try:\n", "        basename = os.path.basename(img_path)\n", "        \n", "        # Create padded version (original intensities)\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        padded_path = os.path.join(padded_dapi_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_dapi_paths.append(padded_path)\n", "        \n", "        # Create CLAHE-padded version (for registration)\n", "        clahe_padded_path = os.path.join(clahe_padded_dir, f\"clahe_padded_{basename}\")\n", "        if create_clahe_padded_image(img_path, max_width, max_height, clahe_padded_path):\n", "            clahe_padded_dapi_paths.append(clahe_padded_path)\n", "        \n", "        print(f\"Processed DAPI: {basename}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {img_path}: {str(e)}\")\n", "\n", "# Process all channel images (only padded, no CLAHE)\n", "padded_all_channel_paths = []\n", "for img_path in all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        \n", "        basename = os.path.basename(img_path)\n", "        padded_path = os.path.join(padded_all_channels_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_all_channel_paths.append(padded_path)\n", "        print(f\"Padded channel: {basename}\")\n", "    except Exception as e:\n", "        print(f\"Error padding {img_path}: {str(e)}\")\n", "\n", "# STEP 3: Perform registration using CLAHE-PADDED DAPI images\n", "print(\"Performing registration with CLAHE-padded DAPI images...\")\n", "registrar = registration.Valis(\n", "    clahe_padded_dir,  # Directory with CLAHE-padded DAPI images\n", "    dst_dir, \n", "    reference_img_f=clahe_padded_reference,\n", "    align_to_reference=True,\n", "    micro_rigid_registrar_cls=MicroRigidRegistrar,\n", "    max_processed_image_dim_px=2000,\n", "    max_non_rigid_registration_dim_px=1500,\n", "    norm_method='img_stats'\n", ")\n", "\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Perform micro-registration\n", "print(\"Performing micro-registration...\")\n", "micro_reg, micro_error = registrar.register_micro(\n", "    max_non_rigid_registration_dim_px=2000,\n", "    align_to_reference=True\n", ")\n", "\n", "# STEP 4: Apply registration transforms to original padded images\n", "print(\"Applying registration transforms to original intensity padded images...\")\n", "\n", "# Create mapping between CLAHE-padded slides and original padded slides\n", "clahe_to_original_mapping = {}\n", "for slide_name, slide_obj in registrar.slide_dict.items():\n", "    # Find corresponding original padded image\n", "    clahe_basename = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"padded_\")\n", "    original_path = os.path.join(padded_dapi_dir, clahe_basename)\n", "    clahe_to_original_mapping[slide_obj] = original_path\n", "\n", "# Warp original padded DAPI images\n", "dapi_warped_dir = os.path.join(individual_channel_dir, \"dapi_warped\")\n", "os.makedirs(dapi_warped_dir, exist_ok=True)\n", "\n", "for slide_obj, original_dapi_path in clahe_to_original_mapping.items():\n", "    try:\n", "        # Load original padded DAPI (original intensities)\n", "        original_dapi_img = pyvips.Image.new_from_file(original_dapi_path)\n", "        \n", "        # Apply transformation found using CLAHE-padded DAPI\n", "        warped_dapi = slide_obj.warp_img(img=original_dapi_img, non_rigid=True, crop=False)\n", "        \n", "        basename = os.path.basename(original_dapi_path)\n", "        warped_path = os.path.join(dapi_warped_dir, f\"warped_{basename}\")\n", "        warped_dapi.write_to_file(warped_path)\n", "        print(f\"Warped original DAPI: {warped_path}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error warping original DAPI {slide_obj.name}: {str(e)}\")\n", "\n", "# Create mapping between DAPI slides and their corresponding other channels\n", "round_dict = {}\n", "for slide_obj in registrar.slide_dict.values():\n", "    # Get the original filename\n", "    original_name = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"\")\n", "    base_round_name = get_round_name(original_name)\n", "    print(f\"Finding matches for round: {base_round_name}\")\n", "    \n", "    # Find all padded channels that belong to this round\n", "    matching_channels = []\n", "    for padded_path in padded_all_channel_paths:\n", "        padded_basename = os.path.basename(padded_path).replace(\"padded_\", \"\")\n", "        if get_round_name(padded_basename) == base_round_name:\n", "            matching_channels.append(padded_path)\n", "    \n", "    print(f\"Found {len(matching_channels)} matching channels\")\n", "    round_dict[slide_obj] = matching_channels\n", "\n", "# Warp all other channels (ch01, ch02) with original intensities\n", "print(\"Warping all other channels with original intensities...\")\n", "for slide_obj, round_slides in round_dict.items():\n", "    print(f\"\\nWarping channels for round: {slide_obj.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    \n", "    for src_f in round_slides:\n", "        img_name = os.path.basename(src_f)\n", "        print(f\"Warping channel: {img_name}\")\n", "        \n", "        try:\n", "            # Load original intensity padded channel\n", "            channel_img = pyvips.Image.new_from_file(src_f)\n", "            \n", "            # Apply transformation found using CLAHE-padded DAPI\n", "            warped_channel = slide_obj.warp_img(img=channel_img, non_rigid=True, crop=False)\n", "            \n", "            channel_basename = os.path.basename(src_f).replace(\"padded_\", \"\")\n", "            individual_channel_path = os.path.join(individual_channel_dir, f\"warped_{channel_basename}\")\n", "            warped_channel.write_to_file(individual_channel_path)\n", "            print(f\"Saved warped channel: {individual_channel_path}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing {src_f}: {str(e)}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"REGISTRATION COMPLETE!\")\n", "print(\"=\"*60)\n", "print(f\"✅ CLAHE applied ONLY to padded DAPI (ch00) for registration\")\n", "print(f\"✅ All channels warped with ORIGINAL intensities preserved\")\n", "print(f\"📁 Original padded images: {padded_dir}\")\n", "print(f\"📁 CLAHE-padded DAPI (temp): {clahe_padded_dir}\")\n", "print(f\"📁 Final warped images: {individual_channel_dir}\")\n", "\n", "# Clean up CLAHE-padded directory\n", "print(\"\\nCleaning up temporary CLAHE-padded files...\")\n", "import shutil\n", "shutil.rmtree(clahe_padded_dir)\n", "print(\"✅ Cleaned up CLAHE-padded temporary files\")\n", "\n", "# Kill JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": null, "id": "a5859382", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\n", "Found 10 DAPI images for registration\n", "Found 17 other channel images\n", "Finding maximum dimensions across all images...\n", "Maximum dimensions: 7596 x 8244\n", "Creating padded images and CLAHE-padded DAPI images...\n", "Padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/registration/padded_images/reference/padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n", "CLAHE-padded reference: /mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/registration/clahe_padded_dapi/clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00.tif\n", "Processed DAPI: LB06 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00.tif\n", "Padded channel: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch01.tif\n", "Padded channel: LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch02.tif\n", "Padded channel: LB06 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch01.tif\n", "Performing registration with CLAHE-padded DAPI images...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: max_image_dim_px is 1024 but needs to be less or equal to 2000. Setting max_image_dim_px to 2000\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:   0%|          | 0/10 [00:00<?, ?image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i CD45 green 18apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47a2110> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  10%|█         | 1/10 [00:00<00:04,  1.83image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i HMGB1 green PCNA red 10apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47bc430> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  20%|██        | 2/10 [00:01<00:04,  1.78image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i IL1a green IP10 red 16apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acf008b0> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  30%|███       | 3/10 [00:01<00:04,  1.75image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i LaminB1 green BCL2red 11apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47a1cf0> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  40%|████      | 4/10 [00:02<00:03,  1.70image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i MMP red 19apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47a19f0> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  50%|█████     | 5/10 [00:02<00:02,  1.72image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i PancytoK green vimentin red 17apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47bc400> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  60%|██████    | 6/10 [00:03<00:02,  1.71image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i aSMA green CD31 red 15apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47be110> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  70%|███████   | 7/10 [00:04<00:01,  1.74image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0a47bc460> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  80%|████████  | 8/10 [00:04<00:01,  1.75image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdfe500> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images:  90%|█████████ | 9/10 [00:05<00:00,  1.74image/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["<Slide, name = clahe_padded_LB06 BP lung 4i vimentin red 22apr25.lif - R 1_Merged_ch00>, width=7596, height=8244, channels=1, levels=1, RGB=False, dtype=uint8> <valis.slide_io.VipsSlideReader object at 0x7fe0acdffb80> False (2000, 1843)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 10/10 [00:05<00:00,  1.74image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 10/10 [00:21<00:00,  2.18s/image]\n", "Normalizing images: 100%|██████████| 10/10 [00:09<00:00,  1.08image/s]\n"]}], "source": ["import os\n", "import pathlib\n", "import numpy as np\n", "from pathlib import Path\n", "from tqdm.notebook import tqdm\n", "import pyvips\n", "import cv2\n", "from skimage import exposure\n", "from valis import valtils, slide_io, registration\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar\n", "\n", "# Add missing imports\n", "try:\n", "    from tqdm.notebook import tqdm\n", "except ImportError:\n", "    from tqdm import tqdm\n", "    print(\"tqdm.notebook not found. Using tqdm instead.\")\n", "\n", "# Set up directories and reference slide\n", "slide_src_dir = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/dapi/\")\n", "results_dst_dir = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/registration/\")\n", "results_dst_dir.mkdir(parents=True, exist_ok=True)\n", "registered_slide_dst_dir = results_dst_dir / \"registered_slides\"\n", "registered_slide_dst_dir.mkdir(parents=True, exist_ok=True)\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/dapi/LB06 BP lung 4i aSMA green didnt work CXCR3 red 14apr25.lif - R 1_Merged_ch00.tif\"\n", "path_to_all_channels = Path(\"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R1/all channels/\")\n", "\n", "# Define helper functions\n", "def get_round_name(src_f):\n", "    \"\"\"Extract the round name from the filename\"\"\"\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_channel_number(src_f):\n", "    \"\"\"Extract the channel number from the filename\"\"\"\n", "    img_name = os.path.basename(src_f).lower()\n", "    if \"_ch00\" in img_name:\n", "        return 0\n", "    elif \"_ch01\" in img_name:\n", "        return 1\n", "    elif \"_ch02\" in img_name:\n", "        return 2\n", "    else:\n", "        return None\n", "\n", "def pad_image(img, target_width, target_height):\n", "    \"\"\"Pad image to target dimensions, centering the original content\"\"\"\n", "    pad_width = max(0, target_width - img.width)\n", "    pad_height = max(0, target_height - img.height)\n", "    \n", "    if pad_width > 0 or pad_height > 0:\n", "        pad_left = pad_width // 2\n", "        pad_top = pad_height // 2\n", "        padded = img.embed(pad_left, pad_top, target_width, target_height, extend=\"black\")\n", "        return padded\n", "    return img\n", "\n", "def apply_clahe_normalization(img_array, clip_limit=2.0, tile_grid_size=(8, 8)):\n", "    \"\"\"Apply CLAHE normalization to improve contrast for registration\"\"\"\n", "    if img_array.dtype != np.uint8:\n", "        # Convert to uint8 for CLAHE\n", "        img_normalized = exposure.rescale_intensity(img_array, out_range=(0, 255)).astype(np.uint8)\n", "    else:\n", "        img_normalized = img_array.copy()\n", "    \n", "    # Apply CLAHE\n", "    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)\n", "    clahe_img = clahe.apply(img_normalized)\n", "    \n", "    return clahe_img\n", "\n", "def create_clahe_padded_image(img_path, target_width, target_height, output_path, clip_limit=2.0, tile_grid_size=(8, 8)):\n", "    \"\"\"Pad image first, then apply CLAHE to the padded result\"\"\"\n", "    try:\n", "        # Load original image\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        \n", "        # Step 1: Pad the image\n", "        padded_img = pad_image(img, target_width, target_height)\n", "        \n", "        # Step 2: Convert padded image to numpy for CLAHE processing\n", "        img_array = np.ndarray(buffer=padded_img.write_to_memory(),\n", "                              dtype=np.uint8 if padded_img.format == 'uchar' else np.uint16,\n", "                              shape=[padded_img.height, padded_img.width, padded_img.bands])\n", "        \n", "        # Step 3: Apply CLAHE to first channel (DAPI)\n", "        if padded_img.bands == 1:\n", "            clahe_array = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "            clahe_array = clahe_array[:, :, np.newaxis]\n", "        else:\n", "            # For multi-channel, apply CLAHE to first channel only\n", "            clahe_array = img_array.copy()\n", "            clahe_array[:, :, 0] = apply_clahe_normalization(img_array[:, :, 0], clip_limit, tile_grid_size)\n", "        \n", "        # Step 4: Convert back to pyvips\n", "        clahe_padded_img = pyvips.Image.new_from_memory(clahe_array.data,\n", "                                                       clahe_array.shape[1],\n", "                                                       clahe_array.shape[0],\n", "                                                       clahe_array.shape[2],\n", "                                                       'uchar' if clahe_array.dtype == np.uint8 else 'ushort')\n", "        \n", "        # Step 5: Save CLAHE-processed padded image\n", "        clahe_padded_img.write_to_file(output_path)\n", "        return output_path\n", "        \n", "    except Exception as e:\n", "        print(f\"Error creating CLAHE-padded image for {img_path}: {str(e)}\")\n", "        return None\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    \"\"\"Generate ome-xml for warped slide\"\"\"\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Process images: PAD FIRST, then CLAHE on DAPI only\n", "print(\"Starting image processing: PADDING FIRST, then CLAHE on DAPI only...\")\n", "\n", "# Create output directories\n", "src_dir = str(slide_src_dir)\n", "dst_dir = str(results_dst_dir)\n", "all_channels_dir = str(path_to_all_channels)\n", "\n", "# Directory for padded images (original intensities)\n", "padded_dir = os.path.join(dst_dir, \"padded_images\")\n", "os.makedirs(padded_dir, exist_ok=True)\n", "padded_dapi_dir = os.path.join(padded_dir, \"dapi\")\n", "os.makedirs(padded_dapi_dir, exist_ok=True)\n", "padded_all_channels_dir = os.path.join(padded_dir, \"all_channels\")\n", "os.makedirs(padded_all_channels_dir, exist_ok=True)\n", "padded_ref_dir = os.path.join(padded_dir, \"reference\")\n", "os.makedirs(padded_ref_dir, exist_ok=True)\n", "\n", "# Directory for CLAHE-padded DAPI images (for registration only)\n", "clahe_padded_dir = os.path.join(dst_dir, \"clahe_padded_dapi\")\n", "os.makedirs(clahe_padded_dir, exist_ok=True)\n", "\n", "# Directory for individual warped channels\n", "individual_channel_dir = os.path.join(dst_dir, \"warped_channels\")\n", "os.makedirs(individual_channel_dir, exist_ok=True)\n", "\n", "# Get all images\n", "dapi_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "all_channel_imgs = [os.path.join(all_channels_dir, f) for f in os.listdir(all_channels_dir)]\n", "\n", "print(f\"Found {len(dapi_imgs)} DAPI images for registration\")\n", "print(f\"Found {len(all_channel_imgs)} other channel images\")\n", "\n", "# STEP 1: Find maximum dimensions across all images\n", "max_width = 0\n", "max_height = 0\n", "\n", "print(\"Finding maximum dimensions across all images...\")\n", "for img_path in dapi_imgs + all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        max_width = max(max_width, img.width)\n", "        max_height = max(max_height, img.height)\n", "    except Exception as e:\n", "        print(f\"Error reading {img_path}: {str(e)}\")\n", "\n", "print(f\"Maximum dimensions: {max_width} x {max_height}\")\n", "\n", "# STEP 2: Create padded images (original intensities) AND CLAHE-padded DAPI images\n", "print(\"Creating padded images and CLAHE-padded DAPI images...\")\n", "\n", "# Process reference image\n", "ref_img = pyvips.Image.new_from_file(reference_slide)\n", "padded_ref_img = pad_image(ref_img, max_width, max_height)\n", "ref_basename = os.path.basename(reference_slide)\n", "padded_reference_slide = os.path.join(padded_ref_dir, f\"padded_{ref_basename}\")\n", "padded_ref_img.write_to_file(padded_reference_slide)\n", "print(f\"Padded reference: {padded_reference_slide}\")\n", "\n", "# Create CLAHE-padded version of reference for registration\n", "clahe_padded_reference = os.path.join(clahe_padded_dir, f\"clahe_padded_{ref_basename}\")\n", "create_clahe_padded_image(reference_slide, max_width, max_height, clahe_padded_reference)\n", "print(f\"CLAHE-padded reference: {clahe_padded_reference}\")\n", "\n", "# Process DAPI images\n", "padded_dapi_paths = []\n", "clahe_padded_dapi_paths = []\n", "\n", "for img_path in dapi_imgs:\n", "    try:\n", "        basename = os.path.basename(img_path)\n", "        \n", "        # Create padded version (original intensities)\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        padded_path = os.path.join(padded_dapi_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_dapi_paths.append(padded_path)\n", "        \n", "        # Create CLAHE-padded version (for registration)\n", "        clahe_padded_path = os.path.join(clahe_padded_dir, f\"clahe_padded_{basename}\")\n", "        if create_clahe_padded_image(img_path, max_width, max_height, clahe_padded_path):\n", "            clahe_padded_dapi_paths.append(clahe_padded_path)\n", "        \n", "        print(f\"Processed DAPI: {basename}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {img_path}: {str(e)}\")\n", "\n", "# Process all channel images (only padded, no CLAHE)\n", "padded_all_channel_paths = []\n", "for img_path in all_channel_imgs:\n", "    try:\n", "        img = pyvips.Image.new_from_file(img_path)\n", "        padded_img = pad_image(img, max_width, max_height)\n", "        \n", "        basename = os.path.basename(img_path)\n", "        padded_path = os.path.join(padded_all_channels_dir, f\"padded_{basename}\")\n", "        padded_img.write_to_file(padded_path)\n", "        padded_all_channel_paths.append(padded_path)\n", "        print(f\"Padded channel: {basename}\")\n", "    except Exception as e:\n", "        print(f\"Error padding {img_path}: {str(e)}\")\n", "\n", "# STEP 3: Perform registration using CLAHE-PADDED DAPI images\n", "print(\"Performing registration with CLAHE-padded DAPI images...\")\n", "registrar = registration.Valis(\n", "    clahe_padded_dir,  # Directory with CLAHE-padded DAPI images\n", "    dst_dir, \n", "    reference_img_f=clahe_padded_reference,\n", "    align_to_reference=True,\n", "    micro_rigid_registrar_cls=MicroRigidRegistrar,\n", "    max_processed_image_dim_px=2000,\n", "    max_non_rigid_registration_dim_px=1500,\n", "    norm_method='img_stats'\n", ")\n", "\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "# Perform micro-registration\n", "print(\"Performing micro-registration...\")\n", "micro_reg, micro_error = registrar.register_micro(\n", "    max_non_rigid_registration_dim_px=2000,\n", "    align_to_reference=True\n", ")\n", "\n", "# STEP 4: Apply registration transforms to original padded images\n", "print(\"Applying registration transforms to original intensity padded images...\")\n", "\n", "# Create mapping between CLAHE-padded slides and original padded slides\n", "clahe_to_original_mapping = {}\n", "for slide_name, slide_obj in registrar.slide_dict.items():\n", "    # Find corresponding original padded image\n", "    clahe_basename = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"padded_\")\n", "    original_path = os.path.join(padded_dapi_dir, clahe_basename)\n", "    clahe_to_original_mapping[slide_obj] = original_path\n", "\n", "# Warp original padded DAPI images\n", "dapi_warped_dir = os.path.join(individual_channel_dir, \"dapi_warped\")\n", "os.makedirs(dapi_warped_dir, exist_ok=True)\n", "\n", "for slide_obj, original_dapi_path in clahe_to_original_mapping.items():\n", "    try:\n", "        # Load original padded DAPI (original intensities)\n", "        original_dapi_img = pyvips.Image.new_from_file(original_dapi_path)\n", "        \n", "        # Apply transformation found using CLAHE-padded DAPI\n", "        warped_dapi = slide_obj.warp_img(img=original_dapi_img, non_rigid=True, crop=False)\n", "        \n", "        basename = os.path.basename(original_dapi_path)\n", "        warped_path = os.path.join(dapi_warped_dir, f\"warped_{basename}\")\n", "        warped_dapi.write_to_file(warped_path)\n", "        print(f\"Warped original DAPI: {warped_path}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error warping original DAPI {slide_obj.name}: {str(e)}\")\n", "\n", "# Create mapping between DAPI slides and their corresponding other channels\n", "round_dict = {}\n", "for slide_obj in registrar.slide_dict.values():\n", "    # Get the original filename\n", "    original_name = os.path.basename(slide_obj.src_f).replace(\"clahe_padded_\", \"\")\n", "    base_round_name = get_round_name(original_name)\n", "    print(f\"Finding matches for round: {base_round_name}\")\n", "    \n", "    # Find all padded channels that belong to this round\n", "    matching_channels = []\n", "    for padded_path in padded_all_channel_paths:\n", "        padded_basename = os.path.basename(padded_path).replace(\"padded_\", \"\")\n", "        if get_round_name(padded_basename) == base_round_name:\n", "            matching_channels.append(padded_path)\n", "    \n", "    print(f\"Found {len(matching_channels)} matching channels\")\n", "    round_dict[slide_obj] = matching_channels\n", "\n", "# Warp all other channels (ch01, ch02) with original intensities\n", "print(\"Warping all other channels with original intensities...\")\n", "for slide_obj, round_slides in round_dict.items():\n", "    print(f\"\\nWarping channels for round: {slide_obj.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    \n", "    for src_f in round_slides:\n", "        img_name = os.path.basename(src_f)\n", "        print(f\"Warping channel: {img_name}\")\n", "        \n", "        try:\n", "            # Load original intensity padded channel\n", "            channel_img = pyvips.Image.new_from_file(src_f)\n", "            \n", "            # Apply transformation found using CLAHE-padded DAPI\n", "            warped_channel = slide_obj.warp_img(img=channel_img, non_rigid=True, crop=False)\n", "            \n", "            channel_basename = os.path.basename(src_f).replace(\"padded_\", \"\")\n", "            individual_channel_path = os.path.join(individual_channel_dir, f\"warped_{channel_basename}\")\n", "            warped_channel.write_to_file(individual_channel_path)\n", "            print(f\"Saved warped channel: {individual_channel_path}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing {src_f}: {str(e)}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"REGISTRATION COMPLETE!\")\n", "print(\"=\"*60)\n", "print(f\"✅ CLAHE applied ONLY to padded DAPI (ch00) for registration\")\n", "print(f\"✅ All channels warped with ORIGINAL intensities preserved\")\n", "print(f\"📁 Original padded images: {padded_dir}\")\n", "print(f\"📁 CLAHE-padded DAPI (temp): {clahe_padded_dir}\")\n", "print(f\"📁 Final warped images: {individual_channel_dir}\")\n", "\n", "# Clean up CLAHE-padded directory\n", "print(\"\\nCleaning up temporary CLAHE-padded files...\")\n", "import shutil\n", "shutil.rmtree(clahe_padded_dir)\n", "print(\"✅ Cleaned up CLAHE-padded temporary files\")\n", "\n", "# Kill JVM\n", "registration.kill_jvm()"]}, {"cell_type": "code", "execution_count": null, "id": "e351242e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5daae1ff", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from bioio import BioImage\n", "from bioio.writers import OmeTiffWriter\n", "\n", "import numpy as np\n", "\n", "from valis import registration"]}, {"cell_type": "code", "execution_count": null, "id": "653cc13d", "metadata": {}, "outputs": [], "source": ["slide_src_dir = Path(\"out/valis_594\")\n", "results_dst_dir = Path(\"out/valis_594_registration\")\n", "results_dst_dir.mkdir(parents=True, exist_ok=True)\n", "registered_slide_dst_dir = results_dst_dir / \"registered_slides\"\n", "registered_slide_dst_dir.mkdir(parents=True, exist_ok=True)\n", "reference_slide = \"5_7_25_S2_B_594.ome.tiff\""]}, {"cell_type": "code", "execution_count": null, "id": "3680dce2", "metadata": {}, "outputs": [], "source": ["# Create a Valis object and use it to register the slides in slide_src_dir, aligning directly to the reference slide.\n", "registrar = registration.Valis(str(slide_src_dir),\n", "                               str(results_dst_dir),\n", "                               reference_img_f=reference_slide,\n", "                               align_to_reference=True,\n", "                               max_processed_image_dim_px=5000)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()"]}, {"cell_type": "code", "execution_count": null, "id": "05e8e104", "metadata": {}, "outputs": [], "source": ["# Use the registrar to warp all channels\n", "path_to_all_channels = Path(\"out/valis_all_channels\")\n", "\n", "for _, slide_name in registrar.name_dict.items():\n", "    slide_obj = registrar.get_slide(slide_name)\n", "    img_name = slide_name.replace(\"_594\", \".ome.tiff\")\n", "\n", "    img_to_warp = BioImage(path_to_all_channels / img_name)\n", "\n", "    data = img_to_warp.get_image_dask_data(\"CYX\")\n", "    data = data.compute()\n", "\n", "    # Perform the warp on each channel and concatenate the results\n", "    results = []\n", "    for i in range(data.shape[0]):\n", "        results.append(slide_obj.warp_img(data[i],\n", "                                          non_rigid=False,\n", "                                          crop=True))\n", "\n", "    data = np.stack(results, axis=0)\n", "\n", "    # Save the warped image\n", "    save_path = registered_slide_dst_dir / \"all_channels\" / img_name\n", "    save_path.parent.mkdir(parents=True, exist_ok=True)\n", "    OmeTiffWriter.save(data=data,\n", "                       uri=save_path,\n", "                       dim_order=\"CYX\",\n", "                       channel_names=img_to_warp.channel_names,\n", "                       )"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}