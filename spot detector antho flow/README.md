# Optimized Spot Detector

This is an optimized implementation of a spot detector that handles small, large, and dense spots with clean individual segmentation, even when spots are touching or very close to each other.

## Features

- **Adaptive Parameter Selection**: Automatically adjusts thresholds based on image characteristics
- **Multi-scale Peak Detection**: Detects spots of different sizes
- **Enhanced Watershed Segmentation**: Better separates touching spots
- **Improved Flow Field Utilization**: Uses flow divergence to identify spot centers in dense regions
- **Comprehensive Debugging and Visualization**: Saves detailed visualizations for debugging

## Key Improvements

1. **Improved Tiling Strategy**
   - Increased tile overlap for better handling of spots at tile boundaries
   - Smoother blending between tiles to avoid artifacts

2. **Enhanced Peak Detection**
   - Multi-scale peak detection to handle spots of different sizes
   - Adaptive thresholding based on local contrast
   - More robust peak filtering mechanism

3. **Optimized Watershed Segmentation**
   - Improved cost function for watershed to better separate touching spots
   - Enhanced boundary detection for dense spots
   - Two-stage watershed approach for difficult cases

4. **Post-processing for Clean Segmentation**
   - Spot splitting for elongated regions that might be merged spots
   - Shape-based filtering to remove artifacts
   - Confidence-based merging/splitting decision

5. **Better Flow Field Utilization**
   - Better integration of flow field information for spot separation
   - Flow divergence to identify spot centers in dense regions
   - Flow-guided watershed for difficult cases

## Usage

```python
from optimized_spot_detector import predict_with_spot_model_optimized

# Run the optimized spot detector
results = predict_with_spot_model_optimized(
    model_path="path/to/your/model_checkpoint.pth",
    images=["path/to/your/image1.tif", "path/to/your/image2.tif"],
    save_dir="spot_detection_results",
    # Tiling parameters
    tile_size=256,
    tile_overlap=96,  # Increased overlap for better boundary handling
    # Input processing
    input_norm_mode='adaptive',
    # Segmentation parameters
    threshold=0.3,  # Adjust based on your model
    min_spot_size=3,
    # Post-processing
    split_touching_spots=True,
    # Debugging
    enable_debug_plots=True
)
```

See `example_usage.py` for a complete example.

## Output

The function returns a dictionary with results for each image, and also saves:

- **Visualizations**: PNG images showing detected spots
- **TIFF Files**: Raw outputs (heatmap, boundary, distance, flow, labels)
- **CSV Files**: Spot coordinates and properties
- **Debug Images**: Detailed visualizations for debugging

## Requirements

- PyTorch
- NumPy
- scikit-image
- SciPy
- OpenCV
- Matplotlib
- pandas
- tifffile
- PIL

## Parameters

The `predict_with_spot_model_optimized` function accepts many parameters to fine-tune the spot detection:

- **model_path**: Path to the trained model checkpoint
- **images**: List of image paths or numpy arrays
- **device**: Device to use for inference (default: auto-detect)
- **save_dir**: Directory to save results
- **tile_size**: Size of tiles for processing large images
- **tile_overlap**: Overlap between tiles
- **input_norm_mode**: Normalization mode ('simple_scale', 'percentile', or 'adaptive')
- **threshold**: Detection threshold (adaptive if None)
- **min_spot_size**: Minimum spot size in pixels
- **max_spot_size**: Maximum spot size in pixels (adaptive if None)
- **min_distance**: Minimum distance between peaks (adaptive if None)
- **use_watershed**: Whether to use watershed for segmentation
- **split_touching_spots**: Whether to split touching spots
- **enable_debug_plots**: Whether to save debug plots
- **debug_level**: Level of debug information (0-3)
- **save_csv**: Whether to save spot coordinates as CSV
- **save_tiff**: Whether to save results as TIFF

See the function documentation for a complete list of parameters.
