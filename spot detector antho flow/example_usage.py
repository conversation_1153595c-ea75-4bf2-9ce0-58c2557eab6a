import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from optimized_spot_detector import predict_with_spot_model_optimized

# Example usage of the optimized spot detector
if __name__ == "__main__":
    # Path to the model checkpoint
    model_path = "path/to/your/model_checkpoint.pth"
    
    # Path to the images
    image_paths = [
        "path/to/your/image1.tif",
        "path/to/your/image2.tif",
    ]
    
    # Or you can use numpy arrays directly
    # Example: Create a synthetic test image with spots
    def create_test_image(width=512, height=512, num_spots=100, spot_size_range=(3, 15), noise_level=0.05):
        """Create a synthetic test image with spots of different sizes."""
        # Create empty image
        image = np.zeros((height, width), dtype=np.float32)
        
        # Add spots
        for _ in range(num_spots):
            # Random position
            x = np.random.randint(0, width)
            y = np.random.randint(0, height)
            
            # Random size
            size = np.random.randint(spot_size_range[0], spot_size_range[1])
            
            # Random intensity
            intensity = np.random.uniform(0.5, 1.0)
            
            # Create spot
            y_grid, x_grid = np.ogrid[-size:size+1, -size:size+1]
            mask = x_grid**2 + y_grid**2 <= size**2
            
            # Add spot to image (with bounds checking)
            y_min, y_max = max(0, y-size), min(height, y+size+1)
            x_min, x_max = max(0, x-size), min(width, x+size+1)
            
            # Adjust mask to match the bounds
            mask_height, mask_width = y_max - y_min, x_max - x_min
            mask_y_min = max(0, size - y)
            mask_y_max = mask_y_min + mask_height
            mask_x_min = max(0, size - x)
            mask_x_max = mask_x_min + mask_width
            
            # Apply mask
            image[y_min:y_max, x_min:x_max] = np.maximum(
                image[y_min:y_max, x_min:x_max],
                intensity * mask[mask_y_min:mask_y_max, mask_x_min:mask_x_max]
            )
        
        # Add noise
        image += np.random.normal(0, noise_level, image.shape)
        
        # Clip to [0, 1]
        image = np.clip(image, 0, 1)
        
        return image
    
    # Create a test image
    test_image = create_test_image(width=512, height=512, num_spots=100, spot_size_range=(3, 15), noise_level=0.05)
    
    # Save the test image
    plt.imsave("test_image.png", test_image, cmap='gray')
    
    # Use the test image
    image_paths.append(test_image)
    
    # Create a test image with dense spots
    dense_test_image = create_test_image(width=512, height=512, num_spots=500, spot_size_range=(3, 15), noise_level=0.05)
    
    # Save the dense test image
    plt.imsave("dense_test_image.png", dense_test_image, cmap='gray')
    
    # Use the dense test image
    image_paths.append(dense_test_image)
    
    # Output directory
    output_dir = "spot_detection_results"
    
    # Run the optimized spot detector
    results = predict_with_spot_model_optimized(
        model_path=model_path,
        images=image_paths,
        save_dir=output_dir,
        # Tiling parameters
        tile_size=256,
        tile_overlap=96,  # Increased overlap for better boundary handling
        # Input processing
        input_norm_mode='adaptive',
        input_p_low=0.5,
        input_p_high=99.5,
        # Segmentation parameters
        threshold=0.3,  # Adjust based on your model
        min_spot_size=3,
        max_spot_size=100,
        min_distance=3,
        # Peak detection parameters
        heatmap_peak_thresh=0.2,
        distance_peak_thresh=0.5,
        flow_div_peak_thresh=0.05,
        # Watershed parameters
        use_watershed=True,
        watershed_compactness=0.2,
        # Spot separation parameters
        boundary_weight=1.0,
        flow_weight=1.0,
        distance_weight=1.0,
        # Post-processing
        split_touching_spots=True,
        eccentricity_threshold=0.8,
        # Debugging
        enable_debug_plots=True,
        debug_level=2,  # 0=none, 1=basic, 2=detailed, 3=all
        # Other
        max_display_spots=500,
        save_csv=True,
        save_tiff=True
    )
    
    # Print summary
    print(f"Processed {len(image_paths)} images")
    for i, result in results.items():
        if 'error' in result:
            print(f"Image {i}: Error - {result['error']}")
        else:
            print(f"Image {i}: Detected {result['num_spots']} spots")
