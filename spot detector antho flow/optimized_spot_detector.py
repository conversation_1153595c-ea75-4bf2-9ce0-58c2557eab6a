import torch
import numpy as np
import os
import time
import logging
import traceback
from PIL import Image
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
import tifffile
from skimage.feature import peak_local_max
from skimage.segmentation import watershed, find_boundaries
from skimage.measure import label, regionprops
from skimage.morphology import remove_small_objects, binary_dilation, binary_erosion, disk
from skimage.filters import gaussian
from scipy import ndimage
from scipy.special import expit  # For numerically stable sigmoid
import pandas as pd
import cv2
import contextlib
import inspect
import gc
import torch.nn.functional as F

def predict_with_spot_model_optimized(
    model_path,
    images,
    device=None,
    save_dir=None,
    # Tiling parameters
    tile_size=256,
    tile_overlap=96,  # Increased overlap for better boundary handling
    # Input processing
    input_norm_mode='adaptive',  # 'simple_scale', 'percentile', or 'adaptive'
    input_p_low=0.5,
    input_p_high=99.5,
    # MoE specific
    router_temperature=1.2,
    # Output processing
    heatmap_blur_sigma=0.5,  # Small blur to smooth heatmap
    # Segmentation parameters
    threshold=None,  # Will be determined adaptively if None
    min_spot_size=2,
    max_spot_size=None,  # Will be determined adaptively if None
    min_distance=None,  # Will be determined adaptively if None
    # Peak detection parameters
    heatmap_peak_thresh=None,  # Will be determined adaptively if None
    distance_peak_thresh=0.5,
    flow_div_peak_thresh=0.05,
    # Watershed parameters
    use_watershed=True,
    watershed_compactness=0.2,  # Controls compactness of watershed regions
    # Spot separation parameters
    boundary_weight=1.0,
    flow_weight=1.0,
    distance_weight=1.0,
    # Post-processing
    split_touching_spots=True,
    eccentricity_threshold=0.8,  # Threshold for splitting elongated spots
    # Debugging
    enable_debug_plots=True,
    debug_level=1,  # 0=none, 1=basic, 2=detailed, 3=all
    # Other
    max_display_spots=500,
    save_csv=True,
    save_tiff=True,
    batch_size=1,
    mixed_precision=True):
    """
    Enhanced prediction function for SPOT model with optimizations for small, large, and dense spots.

    This function includes:
    - Adaptive parameter selection based on image characteristics
    - Multi-scale peak detection for spots of different sizes
    - Enhanced watershed segmentation for better spot separation
    - Improved handling of touching spots
    - Better utilization of flow fields for spot separation
    - Comprehensive debugging and visualization options

    Args:
        model_path (str): Path to the trained model checkpoint
        images (list): List of image paths or numpy arrays
        device (torch.device): Device to use for inference
        save_dir (str): Directory to save results
        tile_size (int): Size of tiles for processing large images
        tile_overlap (int): Overlap between tiles
        input_norm_mode (str): 'simple_scale', 'percentile', or 'adaptive'
        input_p_low, input_p_high (float): Percentiles for normalization
        router_temperature (float): Temperature for MoE router
        heatmap_blur_sigma (float): Sigma for Gaussian blur on heatmap
        threshold (float): Detection threshold (adaptive if None)
        min_spot_size (int): Minimum spot size in pixels
        max_spot_size (int): Maximum spot size in pixels (adaptive if None)
        min_distance (int): Minimum distance between peaks (adaptive if None)
        heatmap_peak_thresh (float): Threshold for peak detection (adaptive if None)
        distance_peak_thresh (float): Threshold for distance map peaks
        flow_div_peak_thresh (float): Threshold for flow divergence peaks
        use_watershed (bool): Whether to use watershed for segmentation
        watershed_compactness (float): Compactness parameter for watershed
        boundary_weight (float): Weight for boundary in cost function
        flow_weight (float): Weight for flow in cost function
        distance_weight (float): Weight for distance in cost function
        split_touching_spots (bool): Whether to split touching spots
        eccentricity_threshold (float): Threshold for splitting elongated spots
        enable_debug_plots (bool): Whether to save debug plots
        debug_level (int): Level of debug information (0-3)
        max_display_spots (int): Maximum number of spots to display
        save_csv (bool): Whether to save spot coordinates as CSV
        save_tiff (bool): Whether to save results as TIFF
        batch_size (int): Batch size for inference
        mixed_precision (bool): Whether to use mixed precision

    Returns:
        dict: Dictionary containing results for each image
    """
    # Configure logging
    logger = logging.getLogger("spot_prediction")
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)

    start_time = time.time()
    logger.info(f"Starting optimized prediction with model: {model_path}")

    # Set device
    if device is None:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Optimize CUDA settings for performance
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        if hasattr(torch.backends.cuda, 'matmul'):
            torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True

        if hasattr(torch.backends.cudnn, 'allow_fp16_reduced_precision_reduction'):
            torch.backends.cudnn.allow_fp16_reduced_precision_reduction = True

        if hasattr(torch._C, '_jit_set_profiling_executor'):
            torch._C._jit_set_profiling_executor(False)
        if hasattr(torch._C, '_jit_set_profiling_mode'):
            torch._C._jit_set_profiling_mode(False)

    # Create output directories
    if save_dir is not None:
        os.makedirs(save_dir, exist_ok=True)
        vis_dir = os.path.join(save_dir, "visualizations")
        tiff_dir = os.path.join(save_dir, "tiff_output")
        csv_dir = os.path.join(save_dir, "csv_output")
        flow_dir = os.path.join(save_dir, "flow_visualizations")
        os.makedirs(vis_dir, exist_ok=True)
        os.makedirs(tiff_dir, exist_ok=True)
        os.makedirs(csv_dir, exist_ok=True)
        os.makedirs(flow_dir, exist_ok=True)

        if enable_debug_plots:
            debug_dir = os.path.join(save_dir, "debug_segmentation")
            os.makedirs(debug_dir, exist_ok=True)
            logger.info(f"Debug plots will be saved to: {debug_dir}")

    # Helper function for debugging
    def save_debug_image(image_data, title, save_path, cmap='gray', vmin=None, vmax=None,
                         overlay_coords=None, overlay_mask=None, dpi=150):
        """Helper function to save a debug image with optional overlays."""
        if not enable_debug_plots or debug_level == 0:
            return

        try:
            plt.figure(figsize=(10, 10))
            plt.imshow(image_data, cmap=cmap, vmin=vmin, vmax=vmax)
            plt.title(title, fontsize=10)
            plt.colorbar()

            if overlay_mask is not None:
                # Use a contour plot for mask overlay to avoid obscuring the image
                plt.contour(overlay_mask, colors='white', levels=[0.5], linewidths=0.5, alpha=0.6)

            if overlay_coords is not None and len(overlay_coords) > 0:
                plt.scatter(overlay_coords[:, 1], overlay_coords[:, 0], c='cyan', marker='+', s=50, alpha=0.8)

            plt.axis('off')
            plt.tight_layout()
            plt.savefig(save_path, dpi=dpi)
            plt.close()
            logger.info(f"Saved debug image: {os.path.basename(save_path)}")
        except Exception as e:
            logger.warning(f"Could not save debug image '{title}': {e}")

    # Load model with special handling for PyTorch compatibility
    logger.info(f"Loading model from {model_path}...")
    try:
        # Try loading with map_location
        checkpoint = torch.load(model_path, map_location=device)
        logger.info("Successfully loaded checkpoint")
    except Exception as e:
        logger.error(f"Error loading model checkpoint: {e}\n{traceback.format_exc()}")
        return None

    # Extract model configuration
    use_moe = checkpoint.get('use_moe', True)  # Default to True for backward compatibility
    num_experts = checkpoint.get('num_experts', 4)  # Default to 4 experts

    # Try to infer num_experts from state dict if not in checkpoint
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
        expert_keys = [k for k in state_dict.keys() if 'experts.' in k]
        if expert_keys:
            expert_indices = [int(k.split('experts.')[1].split('.')[0]) for k in expert_keys
                             if len(k.split('experts.')) > 1]
            if expert_indices:
                num_experts = max(expert_indices) + 1
                logger.info(f"Inferred {num_experts} experts from state dict")

    logger.info(f"Model configuration: use_moe={use_moe}, num_experts={num_experts}")

    # Load state dict
    try:
        # Assume model is already defined and imported from the notebook
        model = checkpoint['model'] if 'model' in checkpoint else checkpoint
        logger.info("Loaded model from checkpoint")
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        logger.error("Cannot continue without model")
        return None

    model.to(device)
    model.eval()  # Set to evaluation mode

    # Set model to hybrid inference mode for better consistency with training
    if use_moe and hasattr(model, 'set_router_temperature'):
        model.set_router_temperature(router_temperature)
        logger.info(f"Set router temperature to {router_temperature}")

    # Get thresholds from checkpoint or use provided values
    adaptive_thresholds = checkpoint.get('adaptive_thresholds', {})

    # Set detection threshold
    if threshold is None:
        # Use threshold from checkpoint if available, otherwise use a reasonable default
        detection_threshold = adaptive_thresholds.get('default', 0.268)
        logger.info(f"Using threshold from checkpoint: {detection_threshold}")
    else:
        detection_threshold = threshold
        logger.info(f"Using user-specified threshold: {detection_threshold}")

    # Set minimum spot size
    if min_spot_size is None:
        spot_min_size = checkpoint.get('min_spot_size', 3)
        logger.info(f"Using min_spot_size from checkpoint: {spot_min_size}")
    else:
        spot_min_size = min_spot_size
        logger.info(f"Using user-specified min_spot_size: {spot_min_size}")

    # Set maximum spot size if not provided
    if max_spot_size is None:
        # Default to a reasonable value based on min size
        max_spot_size = spot_min_size * 20
        logger.info(f"Using calculated max_spot_size: {max_spot_size}")

    # Set minimum distance between peaks if not provided
    if min_distance is None:
        # Default to a reasonable value based on min size
        min_distance = max(2, int(spot_min_size * 0.5))
        logger.info(f"Using calculated min_distance: {min_distance}")

    # Set peak threshold if not provided
    if heatmap_peak_thresh is None:
        # Default to a reasonable value based on detection threshold
        heatmap_peak_thresh = max(0.1, detection_threshold * 0.7)
        logger.info(f"Using calculated heatmap_peak_thresh: {heatmap_peak_thresh}")

    # Get expert-specific thresholds if available for MoE models
    expert_thresholds = None
    if use_moe:
        expert_thresholds = adaptive_thresholds.get('expert_thresholds', None)
        if expert_thresholds:
            logger.info(f"Using expert-specific thresholds: {[round(t, 3) for t in expert_thresholds]}")
        else:
            # Generate optimized thresholds based on expert index
            # These follow a pattern where earlier experts handle clearer spots
            # and later experts handle more ambiguous spots
            expert_thresholds = []
            for i in range(num_experts):
                # Scale from higher to lower thresholds based on expert position
                base = detection_threshold
                # First expert gets highest threshold, last expert gets lowest
                offset = 0.15 - (0.3 * i / (num_experts - 1))
                expert_thresholds.append(max(0.1, min(0.9, base + offset)))
            logger.info(f"Generated expert thresholds: {[round(t, 3) for t in expert_thresholds]}")

    # Define image processing functions
    def normalize_image(img, mode='adaptive', p_low=0.5, p_high=99.5):
        """
        Normalize image with different strategies.

        Args:
            img: Input image
            mode: Normalization mode ('simple_scale', 'percentile', or 'adaptive')
            p_low, p_high: Percentiles for normalization

        Returns:
            Normalized image
        """
        if img.ndim == 3 and img.shape[0] == 1:  # Handle CHW format
            img = img[0]

        if mode == 'simple_scale':
            # Simple min-max scaling to [0, 1]
            min_val = img.min()
            max_val = img.max()
            if max_val > min_val:
                return (img - min_val) / (max_val - min_val)
            return img - min_val  # Avoid division by zero

        elif mode == 'percentile':
            # Percentile-based normalization
            p_low_val = np.percentile(img, p_low)
            p_high_val = np.percentile(img, p_high)
            if p_high_val > p_low_val:
                img_norm = (img - p_low_val) / (p_high_val - p_low_val)
                return np.clip(img_norm, 0, 1)
            return (img - p_low_val)  # Avoid division by zero

        elif mode == 'adaptive':
            # Adaptive normalization based on image statistics
            # First try percentile-based
            p_low_val = np.percentile(img, p_low)
            p_high_val = np.percentile(img, p_high)

            # Check if percentile range is too small (low contrast)
            if p_high_val - p_low_val < 1e-6 or p_high_val <= p_low_val:
                # Fall back to simple scaling
                min_val = img.min()
                max_val = img.max()
                if max_val > min_val:
                    return (img - min_val) / (max_val - min_val)
                return img - min_val  # Avoid division by zero

            # Apply percentile normalization
            img_norm = (img - p_low_val) / (p_high_val - p_low_val)

            # Enhance contrast for low-contrast images
            if p_high_val - p_low_val < 0.1 * (img.max() - img.min()):
                # Apply contrast stretching
                img_norm = np.clip(img_norm, 0, 1)
                # Apply gamma correction to enhance contrast
                gamma = 0.8  # Value < 1 enhances contrast
                img_norm = np.power(img_norm, gamma)

            return np.clip(img_norm, 0, 1)

        else:
            raise ValueError(f"Unknown normalization mode: {mode}")

    def preprocess_image(img, normalize=True, norm_mode='adaptive', p_low=0.5, p_high=99.5):
        """
        Preprocess image for model input.

        Args:
            img: Input image (numpy array)
            normalize: Whether to normalize the image
            norm_mode: Normalization mode
            p_low, p_high: Percentiles for normalization

        Returns:
            Preprocessed image as torch tensor
        """
        # Convert to numpy array if needed
        if not isinstance(img, np.ndarray):
            img = np.array(img)

        # Convert to float32
        if img.dtype != np.float32:
            img = img.astype(np.float32)

        # Normalize if requested
        if normalize:
            img = normalize_image(img, mode=norm_mode, p_low=p_low, p_high=p_high)

        # Add channel dimension if needed
        if img.ndim == 2:
            img = img[np.newaxis, ...]

        # Convert to torch tensor
        img_tensor = torch.from_numpy(img).float()

        return img_tensor

    def create_tiles(img, tile_size=256, overlap=64):
        """
        Create overlapping tiles from an image.

        Args:
            img: Input image (numpy array)
            tile_size: Size of tiles
            overlap: Overlap between tiles

        Returns:
            tiles: List of tiles
            coords: List of (x, y) coordinates for each tile
            weights: Weight maps for blending tiles
        """
        # Get image dimensions
        h, w = img.shape[-2:]

        # Calculate step size
        step = tile_size - overlap

        # Calculate number of tiles in each dimension
        n_h = max(1, int(np.ceil((h - overlap) / step)))
        n_w = max(1, int(np.ceil((w - overlap) / step)))

        # Adjust step size for edge cases
        if n_h > 1:
            step_h = (h - tile_size) / (n_h - 1)
        else:
            step_h = 0

        if n_w > 1:
            step_w = (w - tile_size) / (n_w - 1)
        else:
            step_w = 0

        # Create tiles
        tiles = []
        coords = []

        for i in range(n_h):
            for j in range(n_w):
                # Calculate tile coordinates
                y = min(int(i * step_h), h - tile_size)
                x = min(int(j * step_w), w - tile_size)

                # Extract tile
                if img.ndim == 3:  # Handle channel dimension
                    tile = img[:, y:y+tile_size, x:x+tile_size]
                else:
                    tile = img[y:y+tile_size, x:x+tile_size]

                tiles.append(tile)
                coords.append((x, y))

        # Create weight maps for blending
        weights = []

        # Create a base weight map that's higher in the center and lower at the edges
        # This creates a smooth transition between tiles
        y, x = np.mgrid[0:tile_size, 0:tile_size]
        y = y.astype(np.float32) / tile_size - 0.5
        x = x.astype(np.float32) / tile_size - 0.5

        # Create a radial weight map (1 in center, 0 at corners)
        weight_map = 1 - 2 * np.sqrt(x**2 + y**2)
        weight_map = np.clip(weight_map, 0, 1)

        # Apply sigmoid to create smoother transition
        weight_map = expit(10 * (weight_map - 0.25))

        # Create a separate weight map for each tile
        for i in range(len(tiles)):
            weights.append(weight_map)

        return tiles, coords, weights

    def blend_predictions(predictions, coords, weights, output_shape, num_outputs=4):
        """
        Blend predictions from multiple tiles.

        Args:
            predictions: List of predictions for each tile
            coords: List of (x, y) coordinates for each tile
            weights: Weight maps for blending
            output_shape: Shape of the output image
            num_outputs: Number of output channels

        Returns:
            Blended predictions
        """
        # Initialize output arrays
        h, w = output_shape
        blended = [np.zeros((h, w), dtype=np.float32) for _ in range(num_outputs)]
        weight_sum = np.zeros((h, w), dtype=np.float32)

        # Blend predictions
        for i, ((x, y), weight) in enumerate(zip(coords, weights)):
            pred = predictions[i]

            # Get tile dimensions
            tile_h, tile_w = weight.shape

            # Update weight sum
            weight_sum[y:y+tile_h, x:x+tile_w] += weight

            # Update blended predictions
            for j in range(num_outputs):
                blended[j][y:y+tile_h, x:x+tile_w] += pred[j] * weight

        # Normalize by weight sum
        for j in range(num_outputs):
            # Avoid division by zero
            mask = weight_sum > 0
            blended[j][mask] /= weight_sum[mask]

        return blended

    def process_image_in_tiles(model, img, tile_size=256, overlap=64, batch_size=4, device=None):
        """
        Process an image in tiles.

        Args:
            model: PyTorch model
            img: Input image (torch tensor)
            tile_size: Size of tiles
            overlap: Overlap between tiles
            batch_size: Batch size for inference
            device: Device to use for inference

        Returns:
            Processed image
        """
        # Get image dimensions
        c, h, w = img.shape

        # Check if tiling is needed
        if h <= tile_size and w <= tile_size:
            # No tiling needed, process the whole image
            with torch.no_grad():
                # Pad image to tile_size if needed
                if h < tile_size or w < tile_size:
                    pad_h = max(0, tile_size - h)
                    pad_w = max(0, tile_size - w)
                    img_padded = F.pad(img, (0, pad_w, 0, pad_h), mode='reflect')
                else:
                    img_padded = img

                # Process image
                img_padded = img_padded.unsqueeze(0).to(device)  # Add batch dimension
                outputs = model(img_padded)

                # Extract outputs
                if isinstance(outputs, tuple):
                    # Handle multiple outputs (e.g., from deep supervision)
                    outputs = outputs[0]  # Use the main output

                # Convert to numpy
                outputs = outputs.cpu().numpy()[0]  # Remove batch dimension

                # Crop to original size if padded
                if h < tile_size or w < tile_size:
                    outputs = outputs[:, :h, :w]

                return outputs

        # Create tiles
        tiles, coords, weights = create_tiles(img.numpy(), tile_size=tile_size, overlap=overlap)

        # Convert tiles to torch tensors
        tile_tensors = [torch.from_numpy(tile).float() for tile in tiles]

        # Process tiles in batches
        predictions = []

        for i in range(0, len(tile_tensors), batch_size):
            batch = tile_tensors[i:i+batch_size]
            batch = torch.stack(batch).to(device)

            with torch.no_grad():
                batch_outputs = model(batch)

                # Extract outputs
                if isinstance(batch_outputs, tuple):
                    # Handle multiple outputs (e.g., from deep supervision)
                    batch_outputs = batch_outputs[0]  # Use the main output

                # Convert to numpy
                batch_outputs = batch_outputs.cpu().numpy()

                predictions.extend([batch_outputs[j] for j in range(batch_outputs.shape[0])])

        # Blend predictions
        blended = blend_predictions(predictions, coords, weights, (h, w), num_outputs=predictions[0].shape[0])

        return np.array(blended)

    # Define spot detection and segmentation functions
    def compute_flow_divergence(flow_x, flow_y):
        """
        Compute divergence of flow field.

        Args:
            flow_x: X component of flow field
            flow_y: Y component of flow field

        Returns:
            Divergence of flow field
        """
        # Compute gradients
        grad_x = np.gradient(flow_x, axis=1)
        grad_y = np.gradient(flow_y, axis=0)

        # Compute divergence
        divergence = grad_x + grad_y

        return divergence

    def detect_peaks_multi_scale(heatmap, distance_map=None, flow_x=None, flow_y=None,
                                 threshold=0.5, min_distance=3,
                                 distance_threshold=0.5, flow_div_threshold=0.05,
                                 exclude_border=False):
        """
        Detect peaks in heatmap using multi-scale approach.

        Args:
            heatmap: Heatmap for spot detection
            distance_map: Distance map (optional)
            flow_x, flow_y: Flow field components (optional)
            threshold: Detection threshold
            min_distance: Minimum distance between peaks
            distance_threshold: Threshold for distance map peaks
            flow_div_threshold: Threshold for flow divergence peaks
            exclude_border: Whether to exclude border peaks

        Returns:
            Coordinates of detected peaks
        """
        # Compute flow divergence if flow field is provided
        flow_div = None
        if flow_x is not None and flow_y is not None:
            flow_div = compute_flow_divergence(flow_x, flow_y)
            # Normalize to [0, 1]
            flow_div = (flow_div - flow_div.min()) / (flow_div.max() - flow_div.min() + 1e-8)

        # Detect peaks in heatmap
        heatmap_peaks = peak_local_max(
            heatmap,
            min_distance=min_distance,
            threshold_abs=threshold,
            exclude_border=exclude_border,
            indices=True
        )

        # Detect peaks in distance map if provided
        distance_peaks = []
        if distance_map is not None:
            distance_peaks = peak_local_max(
                distance_map,
                min_distance=min_distance,
                threshold_abs=distance_threshold,
                exclude_border=exclude_border,
                indices=True
            )

        # Detect peaks in flow divergence if provided
        flow_div_peaks = []
        if flow_div is not None:
            flow_div_peaks = peak_local_max(
                flow_div,
                min_distance=min_distance,
                threshold_abs=flow_div_threshold,
                exclude_border=exclude_border,
                indices=True
            )

        # Combine peaks from different sources
        all_peaks = []

        # Add heatmap peaks
        for peak in heatmap_peaks:
            all_peaks.append((peak[0], peak[1], heatmap[peak[0], peak[1]], 'heatmap'))

        # Add distance peaks if not already in all_peaks
        if len(distance_peaks) > 0:
            for peak in distance_peaks:
                # Check if peak is already in all_peaks
                is_duplicate = False
                for existing_peak in all_peaks:
                    if np.sqrt((peak[0] - existing_peak[0])**2 + (peak[1] - existing_peak[1])**2) < min_distance:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    all_peaks.append((peak[0], peak[1], distance_map[peak[0], peak[1]], 'distance'))

        # Add flow divergence peaks if not already in all_peaks
        if len(flow_div_peaks) > 0:
            for peak in flow_div_peaks:
                # Check if peak is already in all_peaks
                is_duplicate = False
                for existing_peak in all_peaks:
                    if np.sqrt((peak[0] - existing_peak[0])**2 + (peak[1] - existing_peak[1])**2) < min_distance:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    all_peaks.append((peak[0], peak[1], flow_div[peak[0], peak[1]], 'flow_div'))

        # Convert to numpy array
        if len(all_peaks) > 0:
            all_peaks = np.array(all_peaks)
            # Sort by confidence (third column)
            all_peaks = all_peaks[np.argsort(all_peaks[:, 2])[::-1]]
            # Return coordinates
            return all_peaks[:, :2].astype(np.int32)
        else:
            return np.empty((0, 2), dtype=np.int32)

    def create_watershed_markers(peaks, shape):
        """
        Create watershed markers from peak coordinates.

        Args:
            peaks: Peak coordinates
            shape: Shape of the image

        Returns:
            Watershed markers
        """
        markers = np.zeros(shape, dtype=np.int32)

        for i, (y, x) in enumerate(peaks):
            # Ensure coordinates are within bounds
            y = max(0, min(y, shape[0] - 1))
            x = max(0, min(x, shape[1] - 1))
            markers[y, x] = i + 1  # Start from 1

        return markers

    def create_watershed_cost(heatmap, boundary=None, distance=None, flow_x=None, flow_y=None,
                             boundary_weight=1.0, distance_weight=1.0, flow_weight=1.0):
        """
        Create cost function for watershed segmentation.

        Args:
            heatmap: Heatmap for spot detection
            boundary: Boundary map (optional)
            distance: Distance map (optional)
            flow_x, flow_y: Flow field components (optional)
            boundary_weight: Weight for boundary in cost function
            distance_weight: Weight for distance in cost function
            flow_weight: Weight for flow in cost function

        Returns:
            Cost function for watershed segmentation
        """
        # Initialize cost with inverted heatmap
        cost = 1.0 - heatmap

        # Add boundary information if available
        if boundary is not None:
            cost += boundary_weight * boundary

        # Add distance information if available
        if distance is not None:
            # Invert distance map (higher values = higher cost)
            inv_distance = 1.0 - distance
            cost += distance_weight * inv_distance

        # Add flow information if available
        if flow_x is not None and flow_y is not None:
            # Compute flow magnitude
            flow_mag = np.sqrt(flow_x**2 + flow_y**2)
            # Normalize to [0, 1]
            flow_mag = flow_mag / (flow_mag.max() + 1e-8)
            # Higher flow magnitude = higher cost
            cost += flow_weight * flow_mag

        # Ensure cost is non-negative
        cost = np.maximum(cost, 0)

        return cost

    def segment_spots_watershed(heatmap, peaks, boundary=None, distance=None, flow_x=None, flow_y=None,
                               boundary_weight=1.0, distance_weight=1.0, flow_weight=1.0,
                               compactness=0.2, min_size=3, max_size=None):
        """
        Segment spots using watershed algorithm.

        Args:
            heatmap: Heatmap for spot detection
            peaks: Peak coordinates
            boundary: Boundary map (optional)
            distance: Distance map (optional)
            flow_x, flow_y: Flow field components (optional)
            boundary_weight: Weight for boundary in cost function
            distance_weight: Weight for distance in cost function
            flow_weight: Weight for flow in cost function
            compactness: Compactness parameter for watershed
            min_size: Minimum spot size
            max_size: Maximum spot size

        Returns:
            Segmented spots
        """
        # Create watershed markers
        markers = create_watershed_markers(peaks, heatmap.shape)

        # Create cost function
        cost = create_watershed_cost(
            heatmap, boundary, distance, flow_x, flow_y,
            boundary_weight, distance_weight, flow_weight
        )

        # Apply watershed
        labels = watershed(cost, markers, compactness=compactness)

        # Remove small objects
        if min_size > 0:
            labels = remove_small_objects(labels, min_size=min_size)

        # Remove large objects
        if max_size is not None and max_size > 0:
            # Get region properties
            regions = regionprops(labels)

            # Create mask for large objects
            large_mask = np.zeros_like(labels, dtype=bool)

            for region in regions:
                if region.area > max_size:
                    large_mask[labels == region.label] = True

            # Remove large objects
            labels[large_mask] = 0

        return labels

    def split_touching_spots(labels, eccentricity_threshold=0.8, min_size=3):
        """
        Split touching spots based on shape properties.

        Args:
            labels: Segmented spots
            eccentricity_threshold: Threshold for splitting elongated spots
            min_size: Minimum spot size

        Returns:
            Segmented spots with touching spots split
        """
        # Get region properties
        regions = regionprops(labels)

        # Create new labels
        new_labels = labels.copy()
        next_label = labels.max() + 1

        # Process each region
        for region in regions:
            # Skip small regions
            if region.area < min_size * 2:  # At least twice the minimum size
                continue

            # Check if region is elongated
            if region.eccentricity > eccentricity_threshold:
                # Get region mask
                mask = labels == region.label

                # Apply distance transform
                distance = ndimage.distance_transform_edt(mask)

                # Find local maxima in distance transform
                local_max = peak_local_max(
                    distance,
                    min_distance=min_size,
                    threshold_abs=0.5,
                    exclude_border=False,
                    indices=False
                )

                # Label local maxima
                local_max_labels = label(local_max)

                # If more than one local maximum, split the region
                if local_max_labels.max() > 1:
                    # Create markers for watershed
                    markers = np.zeros_like(mask, dtype=np.int32)

                    # Add markers for each local maximum
                    for i in range(1, local_max_labels.max() + 1):
                        markers[local_max_labels == i] = i

                    # Apply watershed
                    split_labels = watershed(-distance, markers, mask=mask)

                    # Update new labels
                    for i in range(1, split_labels.max() + 1):
                        if i == 1:
                            # Keep original label for first split
                            continue
                        else:
                            # Assign new label for other splits
                            new_labels[split_labels == i] = next_label
                            next_label += 1

        # Remove small objects
        if min_size > 0:
            new_labels = remove_small_objects(new_labels, min_size=min_size)

        return new_labels

    def extract_spot_properties(labels, heatmap, distance=None, flow_x=None, flow_y=None):
        """
        Extract properties of segmented spots.

        Args:
            labels: Segmented spots
            heatmap: Heatmap for spot detection
            distance: Distance map (optional)
            flow_x, flow_y: Flow field components (optional)

        Returns:
            Properties of segmented spots
        """
        # Get region properties
        regions = regionprops(labels, intensity_image=heatmap)

        # Extract properties
        properties = []

        for region in regions:
            # Basic properties
            prop = {
                'label': region.label,
                'y': region.centroid[0],
                'x': region.centroid[1],
                'area': region.area,
                'perimeter': region.perimeter,
                'eccentricity': region.eccentricity,
                'mean_intensity': region.mean_intensity,
                'max_intensity': heatmap[labels == region.label].max(),
                'confidence': region.mean_intensity
            }

            # Add distance information if available
            if distance is not None:
                prop['mean_distance'] = distance[labels == region.label].mean()
                prop['max_distance'] = distance[labels == region.label].max()

            # Add flow information if available
            if flow_x is not None and flow_y is not None:
                mask = labels == region.label
                prop['mean_flow_x'] = flow_x[mask].mean()
                prop['mean_flow_y'] = flow_y[mask].mean()
                prop['flow_magnitude'] = np.sqrt(prop['mean_flow_x']**2 + prop['mean_flow_y']**2)

            properties.append(prop)

        return properties

    # Define visualization functions
    def create_spot_visualization(image, labels, properties=None, max_spots=500):
        """
        Create visualization of detected spots.

        Args:
            image: Input image
            labels: Segmented spots
            properties: Properties of segmented spots (optional)
            max_spots: Maximum number of spots to display

        Returns:
            Visualization of detected spots
        """
        # Create RGB image
        if image.ndim == 2:
            vis = np.stack([image] * 3, axis=-1)
        else:
            vis = image.copy()

        # Normalize to [0, 1]
        vis = vis - vis.min()
        vis = vis / (vis.max() + 1e-8)

        # Create boundary mask
        boundaries = find_boundaries(labels, mode='thick')

        # Apply boundaries to visualization
        vis[boundaries, 0] = 1.0  # Red channel
        vis[boundaries, 1] = 0.0  # Green channel
        vis[boundaries, 2] = 0.0  # Blue channel

        # Add centroids if properties are provided
        if properties is not None:
            # Limit number of spots for visualization
            if len(properties) > max_spots:
                properties = properties[:max_spots]

            for prop in properties:
                y, x = int(prop['y']), int(prop['x'])

                # Ensure coordinates are within bounds
                if 0 <= y < vis.shape[0] and 0 <= x < vis.shape[1]:
                    # Draw cross at centroid
                    size = 3
                    y_min, y_max = max(0, y - size), min(vis.shape[0] - 1, y + size)
                    x_min, x_max = max(0, x - size), min(vis.shape[1] - 1, x + size)

                    # Vertical line
                    vis[y_min:y_max+1, x, 0] = 0.0  # Red channel
                    vis[y_min:y_max+1, x, 1] = 1.0  # Green channel
                    vis[y_min:y_max+1, x, 2] = 0.0  # Blue channel

                    # Horizontal line
                    vis[y, x_min:x_max+1, 0] = 0.0  # Red channel
                    vis[y, x_min:x_max+1, 1] = 1.0  # Green channel
                    vis[y, x_min:x_max+1, 2] = 0.0  # Blue channel

        return vis

    def create_flow_visualization(flow_x, flow_y, background=None):
        """
        Create visualization of flow field.

        Args:
            flow_x: X component of flow field
            flow_y: Y component of flow field
            background: Background image (optional)

        Returns:
            Visualization of flow field
        """
        # Compute flow magnitude and angle
        flow_mag = np.sqrt(flow_x**2 + flow_y**2)
        flow_ang = np.arctan2(flow_y, flow_x)

        # Normalize magnitude to [0, 1]
        flow_mag_norm = flow_mag / (flow_mag.max() + 1e-8)

        # Convert angle to hue (0-360 degrees)
        flow_hue = (flow_ang / (2 * np.pi) + 0.5) * 360

        # Create HSV image
        hsv = np.zeros((flow_x.shape[0], flow_x.shape[1], 3), dtype=np.float32)
        hsv[..., 0] = flow_hue / 360  # Hue
        hsv[..., 1] = flow_mag_norm    # Saturation
        hsv[..., 2] = 1.0              # Value

        # Convert to RGB
        rgb = cv2.cvtColor((hsv * 255).astype(np.uint8), cv2.COLOR_HSV2RGB)
        rgb = rgb.astype(np.float32) / 255

        # Blend with background if provided
        if background is not None:
            # Normalize background to [0, 1]
            bg = background.copy()
            bg = bg - bg.min()
            bg = bg / (bg.max() + 1e-8)

            # Convert to RGB if grayscale
            if bg.ndim == 2:
                bg = np.stack([bg] * 3, axis=-1)

            # Blend based on flow magnitude
            alpha = np.expand_dims(flow_mag_norm, axis=-1)
            rgb = alpha * rgb + (1 - alpha) * bg

        return rgb

    # Main processing function
    def process_image(img, model, device,
                     tile_size=256, tile_overlap=96,
                     input_norm_mode='adaptive', input_p_low=0.5, input_p_high=99.5,
                     detection_threshold=0.268, min_distance=3, min_spot_size=3, max_spot_size=None,
                     heatmap_peak_thresh=None, distance_peak_thresh=0.5, flow_div_peak_thresh=0.05,
                     use_watershed=True, watershed_compactness=0.2,
                     boundary_weight=1.0, flow_weight=1.0, distance_weight=1.0,
                     split_touching_spots_flag=True, eccentricity_threshold=0.8,
                     enable_debug_plots=True, debug_level=1, debug_dir=None,
                     max_display_spots=500):
        """
        Process a single image with the spot detection model.

        Args:
            img: Input image (numpy array)
            model: PyTorch model
            device: Device to use for inference
            tile_size: Size of tiles for processing large images
            tile_overlap: Overlap between tiles
            input_norm_mode: Normalization mode for input image
            input_p_low, input_p_high: Percentiles for normalization
            detection_threshold: Detection threshold
            min_distance: Minimum distance between peaks
            min_spot_size: Minimum spot size
            max_spot_size: Maximum spot size
            heatmap_peak_thresh: Threshold for peak detection
            distance_peak_thresh: Threshold for distance map peaks
            flow_div_peak_thresh: Threshold for flow divergence peaks
            use_watershed: Whether to use watershed for segmentation
            watershed_compactness: Compactness parameter for watershed
            boundary_weight: Weight for boundary in cost function
            flow_weight: Weight for flow in cost function
            distance_weight: Weight for distance in cost function
            split_touching_spots_flag: Whether to split touching spots
            eccentricity_threshold: Threshold for splitting elongated spots
            enable_debug_plots: Whether to save debug plots
            debug_level: Level of debug information (0-3)
            debug_dir: Directory to save debug plots
            max_display_spots: Maximum number of spots to display

        Returns:
            Dictionary containing results
        """
        # Set peak threshold if not provided
        if heatmap_peak_thresh is None:
            heatmap_peak_thresh = max(0.1, detection_threshold * 0.7)

        # Preprocess image
        img_tensor = preprocess_image(
            img,
            normalize=True,
            norm_mode=input_norm_mode,
            p_low=input_p_low,
            p_high=input_p_high
        )

        # Process image in tiles
        outputs = process_image_in_tiles(
            model,
            img_tensor,
            tile_size=tile_size,
            overlap=tile_overlap,
            batch_size=1,
            device=device
        )

        # Extract outputs
        heatmap = outputs[0]  # Spot heatmap
        boundary = outputs[1]  # Boundary map
        distance = outputs[2]  # Distance map
        flow_y = outputs[3]    # Y component of flow field
        flow_x = outputs[4]    # X component of flow field

        # Apply Gaussian blur to heatmap for smoother peaks
        if heatmap_blur_sigma > 0:
            heatmap = gaussian(heatmap, sigma=heatmap_blur_sigma)

        # Save debug images
        if enable_debug_plots and debug_level >= 1 and debug_dir is not None:
            save_debug_image(
                img,
                "Input Image",
                os.path.join(debug_dir, "01_input.png"),
                cmap='gray'
            )

            save_debug_image(
                heatmap,
                "Heatmap",
                os.path.join(debug_dir, "02_heatmap.png"),
                cmap='hot'
            )

            save_debug_image(
                boundary,
                "Boundary",
                os.path.join(debug_dir, "03_boundary.png"),
                cmap='gray'
            )

            save_debug_image(
                distance,
                "Distance",
                os.path.join(debug_dir, "04_distance.png"),
                cmap='viridis'
            )

            # Flow visualization
            flow_vis = create_flow_visualization(flow_x, flow_y, background=img)
            plt.figure(figsize=(10, 10))
            plt.imshow(flow_vis)
            plt.title("Flow Field")
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(os.path.join(debug_dir, "05_flow.png"), dpi=150)
            plt.close()

        # Detect peaks
        peaks = detect_peaks_multi_scale(
            heatmap,
            distance_map=distance,
            flow_x=flow_x,
            flow_y=flow_y,
            threshold=heatmap_peak_thresh,
            min_distance=min_distance,
            distance_threshold=distance_peak_thresh,
            flow_div_threshold=flow_div_peak_thresh,
            exclude_border=False
        )

        # Save debug image with peaks
        if enable_debug_plots and debug_level >= 1 and debug_dir is not None:
            save_debug_image(
                heatmap,
                f"Detected Peaks (n={len(peaks)})",
                os.path.join(debug_dir, "06_peaks.png"),
                cmap='hot',
                overlay_coords=peaks
            )

        # Segment spots
        if use_watershed and len(peaks) > 0:
            labels = segment_spots_watershed(
                heatmap,
                peaks,
                boundary=boundary,
                distance=distance,
                flow_x=flow_x,
                flow_y=flow_y,
                boundary_weight=boundary_weight,
                distance_weight=distance_weight,
                flow_weight=flow_weight,
                compactness=watershed_compactness,
                min_size=min_spot_size,
                max_size=max_spot_size
            )

            # Split touching spots if requested
            if split_touching_spots_flag:
                labels = split_touching_spots(
                    labels,
                    eccentricity_threshold=eccentricity_threshold,
                    min_size=min_spot_size
                )
        else:
            # Create binary mask from peaks
            labels = np.zeros_like(heatmap, dtype=np.int32)
            for i, (y, x) in enumerate(peaks):
                labels[y, x] = i + 1

            # Dilate to create spot regions
            if min_spot_size > 1:
                labels = binary_dilation(labels > 0, disk(min_spot_size // 2))
                labels = label(labels)

        # Save debug image with segmentation
        if enable_debug_plots and debug_level >= 1 and debug_dir is not None:
            # Create random colormap for visualization
            n_labels = np.max(labels) + 1
            colors = np.random.rand(n_labels, 3)
            colors[0] = [0, 0, 0]  # Background is black

            # Create label image
            label_img = colors[labels]

            # Overlay on input image
            overlay = 0.7 * label_img + 0.3 * np.stack([img] * 3, axis=-1) / np.max(img)

            plt.figure(figsize=(10, 10))
            plt.imshow(overlay)
            plt.title(f"Segmented Spots (n={n_labels-1})")
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(os.path.join(debug_dir, "07_segmentation.png"), dpi=150)
            plt.close()

        # Extract spot properties
        properties = extract_spot_properties(
            labels,
            heatmap,
            distance=distance,
            flow_x=flow_x,
            flow_y=flow_y
        )

        # Sort properties by confidence
        properties = sorted(properties, key=lambda x: x['confidence'], reverse=True)

        # Create visualization
        vis = create_spot_visualization(
            img,
            labels,
            properties=properties,
            max_spots=max_display_spots
        )

        # Return results
        return {
            'image': img,
            'heatmap': heatmap,
            'boundary': boundary,
            'distance': distance,
            'flow_x': flow_x,
            'flow_y': flow_y,
            'peaks': peaks,
            'labels': labels,
            'properties': properties,
            'visualization': vis
        }

    # Main function implementation
    results = {}

    # Process each image
    for i, img_path in enumerate(images):
        logger.info(f"Processing image {i+1}/{len(images)}: {img_path if isinstance(img_path, str) else 'array'}")

        try:
            # Load image
            if isinstance(img_path, str):
                # Load from file
                try:
                    # Try loading with PIL first
                    img = np.array(Image.open(img_path))
                except Exception as e:
                    logger.warning(f"PIL failed to open image: {e}, trying tifffile")
                    # Try loading with tifffile
                    img = tifffile.imread(img_path)
            else:
                # Assume it's already a numpy array
                img = img_path

            # Create output paths
            if save_dir is not None:
                # Create base filename
                if isinstance(img_path, str):
                    base_name = os.path.splitext(os.path.basename(img_path))[0]
                else:
                    base_name = f"image_{i:04d}"

                # Create debug directory for this image
                img_debug_dir = os.path.join(debug_dir, base_name) if enable_debug_plots else None
                if img_debug_dir is not None:
                    os.makedirs(img_debug_dir, exist_ok=True)
            else:
                base_name = None
                img_debug_dir = None

            # Process image
            result = process_image(
                img,
                model,
                device,
                tile_size=tile_size,
                tile_overlap=tile_overlap,
                input_norm_mode=input_norm_mode,
                input_p_low=input_p_low,
                input_p_high=input_p_high,
                detection_threshold=detection_threshold,
                min_distance=min_distance,
                min_spot_size=spot_min_size,
                max_spot_size=max_spot_size,
                heatmap_peak_thresh=heatmap_peak_thresh,
                distance_peak_thresh=distance_peak_thresh,
                flow_div_peak_thresh=flow_div_peak_thresh,
                use_watershed=use_watershed,
                watershed_compactness=watershed_compactness,
                boundary_weight=boundary_weight,
                flow_weight=flow_weight,
                distance_weight=distance_weight,
                split_touching_spots_flag=split_touching_spots,
                eccentricity_threshold=eccentricity_threshold,
                enable_debug_plots=enable_debug_plots,
                debug_level=debug_level,
                debug_dir=img_debug_dir,
                max_display_spots=max_display_spots
            )

            # Save results
            if save_dir is not None and base_name is not None:
                # Save visualization
                vis_path = os.path.join(vis_dir, f"{base_name}_spots.png")
                plt.figure(figsize=(10, 10))
                plt.imshow(result['visualization'])
                plt.title(f"Detected Spots (n={len(result['properties'])})")
                plt.axis('off')
                plt.tight_layout()
                plt.savefig(vis_path, dpi=150)
                plt.close()
                logger.info(f"Saved visualization to {vis_path}")

                # Save TIFF files if requested
                if save_tiff:
                    # Save heatmap
                    tifffile.imwrite(
                        os.path.join(tiff_dir, f"{base_name}_heatmap.tiff"),
                        (result['heatmap'] * 65535).astype(np.uint16)
                    )

                    # Save boundary
                    tifffile.imwrite(
                        os.path.join(tiff_dir, f"{base_name}_boundary.tiff"),
                        (result['boundary'] * 65535).astype(np.uint16)
                    )

                    # Save distance
                    tifffile.imwrite(
                        os.path.join(tiff_dir, f"{base_name}_distance.tiff"),
                        (result['distance'] * 65535).astype(np.uint16)
                    )

                    # Save flow
                    flow_combined = np.stack([
                        result['flow_x'],
                        result['flow_y'],
                        np.zeros_like(result['flow_x'])
                    ], axis=0)
                    tifffile.imwrite(
                        os.path.join(tiff_dir, f"{base_name}_flow.tiff"),
                        flow_combined.astype(np.float32)
                    )

                    # Save labels
                    tifffile.imwrite(
                        os.path.join(tiff_dir, f"{base_name}_labels.tiff"),
                        result['labels'].astype(np.uint16)
                    )

                    logger.info(f"Saved TIFF files to {tiff_dir}")

                # Save CSV if requested
                if save_csv and len(result['properties']) > 0:
                    # Convert properties to DataFrame
                    df = pd.DataFrame(result['properties'])

                    # Save to CSV
                    csv_path = os.path.join(csv_dir, f"{base_name}_spots.csv")
                    df.to_csv(csv_path, index=False)
                    logger.info(f"Saved {len(df)} spots to {csv_path}")

                # Save flow visualization
                flow_vis = create_flow_visualization(result['flow_x'], result['flow_y'], background=img)
                flow_vis_path = os.path.join(flow_dir, f"{base_name}_flow.png")
                plt.figure(figsize=(10, 10))
                plt.imshow(flow_vis)
                plt.title("Flow Field")
                plt.axis('off')
                plt.tight_layout()
                plt.savefig(flow_vis_path, dpi=150)
                plt.close()
                logger.info(f"Saved flow visualization to {flow_vis_path}")

            # Store result
            results[i] = {
                'path': img_path if isinstance(img_path, str) else f"array_{i}",
                'num_spots': len(result['properties']),
                'properties': result['properties']
            }

            logger.info(f"Detected {len(result['properties'])} spots in image {i+1}")

        except Exception as e:
            logger.error(f"Error processing image {i+1}: {e}\n{traceback.format_exc()}")
            results[i] = {
                'path': img_path if isinstance(img_path, str) else f"array_{i}",
                'error': str(e),
                'traceback': traceback.format_exc()
            }

    # Print summary
    logger.info("=" * 50)
    logger.info("Processing complete")
    logger.info(f"Processed {len(images)} images")

    # Count successful images
    successful = sum(1 for r in results.values() if 'error' not in r)
    logger.info(f"Successfully processed {successful}/{len(images)} images")

    # Count total spots
    total_spots = sum(r.get('num_spots', 0) for r in results.values())
    logger.info(f"Detected a total of {total_spots} spots")

    # Print processing time
    end_time = time.time()
    processing_time = end_time - start_time
    logger.info(f"Total processing time: {processing_time:.2f} seconds ({processing_time/len(images):.2f} seconds per image)")

    return results
