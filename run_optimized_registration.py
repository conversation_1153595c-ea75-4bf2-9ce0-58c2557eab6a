#!/usr/bin/env python3
"""
Run optimized image registration to speed up the non-rigid registration process.
"""

import os
import re
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union

import cupy as cp
import numpy as np
import tifffile
import ants
from tqdm import tqdm
from skimage.registration import phase_cross_correlation

# Import optimized functions
from optimized_registration import (
    normalize_image,
    optimized_ants_registration,
    optimized_non_rigid_registration,
    optimized_apply_transforms,
    cupy_fourier_shift
)

def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:
    """Pad all images to the size of the largest image."""
    max_h = max(img.shape[0] for img in images)
    max_w = max(img.shape[1] for img in images)
    padded = []
    for img in images:
        pad_h = max_h - img.shape[0]
        pad_w = max_w - img.shape[1]
        pad_top = pad_h // 2
        pad_bottom = pad_h - pad_top
        pad_left = pad_w // 2
        pad_right = pad_w - pad_left
        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),
                            mode='constant', constant_values=0)
        padded.append(img_padded)
    return padded

def get_basename_key(filename: str) -> str:
    """Extract base name from filename."""
    match = re.match(r"(.+)_ch\d{2}\.tif$", filename)
    if match:
        return match.group(1)
    raise ValueError(f"Filename {filename} does not match expected pattern *_chXX.tif")

def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:
    """Group images by their base name."""
    files = [f for f in os.listdir(folder) if f.endswith('.tif')]
    groups = {}
    for f in files:
        try:
            base = get_basename_key(f)
            ch_match = re.search(r"_ch(\d{2})\.tif$", f)
            if not ch_match:
                continue
            ch = f"ch{ch_match.group(1)}"
            groups.setdefault(base, {})[ch] = os.path.join(folder, f)
        except ValueError:
            # Skip files that don't match the pattern
            continue
    return groups

def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:
    """Load channel images from paths."""
    channels = {}
    for ch, p in paths.items():
        img = tifffile.imread(p)
        if img.ndim > 2:
            img = img[0]
        channels[ch] = cp.asarray(img)
    return channels

def save_image(img: cp.ndarray, output_dir: str, filename: str):
    """Save image to file."""
    out_path = os.path.join(output_dir, filename)
    np_img = cp.asnumpy(img)
    tifffile.imwrite(out_path, np_img.astype(np.float32))
    print(f"Saved image: {out_path}")

def optimized_batch_align(
    reference_img_path,
    input_folder,
    output_folder,
    affine_transform_type="Affine",
    use_non_rigid=True,
    use_micro_registration=False,  # Disable micro-registration by default for speed
    save_intermediate=False,
    downsample_factor=0.25  # Use 25% resolution for even faster processing
):
    """
    Optimized batch alignment function with downsampling for faster processing.
    
    Args:
        reference_img_path: Path to reference image
        input_folder: Folder containing images to align
        output_folder: Folder to save aligned images
        affine_transform_type: Type of affine transform
        use_non_rigid: Whether to use non-rigid registration
        use_micro_registration: Whether to use micro-registration
        save_intermediate: Whether to save intermediate results
        downsample_factor: Factor to downsample images for SyN registration
    """
    start_time = time.time()
    print(f"[INFO] Loading reference ch00 image from {reference_img_path}...")
    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))

    groups = group_images_by_basename(input_folder)
    print(f"[INFO] Found {len(groups)} groups to align.")

    Path(output_folder).mkdir(parents=True, exist_ok=True)

    for base, channel_paths in tqdm(groups.items(), desc="Aligning groups"):
        if 'ch00' not in channel_paths:
            print(f"[WARNING] Skipping '{base}': no ch00 channel found.")
            continue

        print(f"\n[INFO] Processing group: {base}")
        group_start_time = time.time()
        
        channels = load_channels(channel_paths)
        moving_ch00 = cp.asarray(channels.get('ch00'))
        if moving_ch00 is None:
            print(f"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.")
            continue

        # Pad all images to same size first
        print(" - Padding images...")
        all_imgs = [fixed_ch00] + list(channels.values())
        all_padded = pad_to_largest(all_imgs)
        fixed_ch00_padded = all_padded[0]
        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))

        # Phase correlation shift estimation
        print(" - Estimating initial shift using phase correlation...")
        shift, _, _ = phase_cross_correlation(
            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=10
        )

        # Apply shift to all channels
        for ch_name in channels_padded:
            current_img = channels_padded[ch_name]
            # Use direct spatial shift instead of Fourier transform
            shifted = cupy_fourier_shift(current_img, shift)
            channels_padded[ch_name] = shifted
            # Free memory after each iteration
            cp.get_default_memory_pool().free_all_blocks()

        best_similarity = -1
        best_img = None
        best_transforms = []

        # Affine registration
        print(" - Running affine registration...")
        affine_img, affine_transforms, aff_sim = optimized_ants_registration(
            fixed_ch00_padded,
            channels_padded['ch00'],
            transform_type=affine_transform_type,
            interpolator="linear",  # Use faster interpolation
            downsample_factor=1.0  # No downsampling for affine
        )
        print(f"   >> Affine similarity: {aff_sim:.6f}")
        best_similarity = aff_sim
        best_img = affine_img
        best_transforms = affine_transforms

        # Non-rigid registration
        if use_non_rigid:
            print(" - Running non-rigid registration...")
            nonrigid_img, nonrigid_transforms, nonrigid_sim = optimized_ants_registration(
                fixed_ch00_padded,
                best_img,
                transform_type="SyN",
                interpolator="linear",  # Use faster interpolation
                downsample_factor=downsample_factor  # Use downsampling for SyN
            )
            print(f"   >> Non-rigid similarity: {nonrigid_sim:.6f}")
            if nonrigid_sim > best_similarity:
                best_similarity = nonrigid_sim
                best_img = nonrigid_img
                best_transforms += nonrigid_transforms

        # Micro-registration (optional)
        if use_micro_registration:
            print(" - Running micro-registration...")
            micro_img, micro_transforms, micro_sim = optimized_ants_registration(
                fixed_ch00_padded,
                best_img,
                transform_type="SyN",
                interpolator="linear",  # Use faster interpolation
                downsample_factor=downsample_factor  # Use downsampling for SyN
            )
            print(f"   >> Micro-registration similarity: {micro_sim:.6f}")
            if micro_sim > best_similarity:
                best_similarity = micro_sim
                best_img = micro_img
                best_transforms += micro_transforms

        print(f"   >> Best similarity: {best_similarity:.6f}")

        # Apply final transforms to all padded channels
        for ch_name, img in channels_padded.items():
            if ch_name == 'ch00':
                warped = best_img
            else:
                warped = optimized_apply_transforms(
                    img, fixed_ch00_padded, best_transforms, interpolator="bSpline"
                )
            save_image(warped, output_folder, f"{base}_{ch_name}.tif")

        if save_intermediate:
            np.save(os.path.join(output_folder, f"{base}_transform_list.npy"), best_transforms)
            
        group_end_time = time.time()
        print(f"[INFO] Group {base} processed in {group_end_time - group_start_time:.2f} seconds")

    end_time = time.time()
    print(f"[INFO] Total processing time: {end_time - start_time:.2f} seconds")

if __name__ == "__main__":
    # Define paths
    PADDED_DIR = "/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/"
    REFERENCE_IMG = (
        "/mnt/d/Users/<USER>/antho 4i alignment/"
        "lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif"
    )
    OUTPUT_DIR = os.path.join(PADDED_DIR, "aligned_images_optimized")
    
    # Run the optimized batch alignment
    optimized_batch_align(
        REFERENCE_IMG,
        PADDED_DIR,
        OUTPUT_DIR,
        affine_transform_type="Affine",
        use_non_rigid=True,
        use_micro_registration=False,  # Disable micro-registration for speed
        save_intermediate=False,
        downsample_factor=0.25  # Use 25% resolution for even faster processing
    )
