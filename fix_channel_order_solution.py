import os
import numpy as np
import pathlib
from tqdm import tqdm
from valis import registration, valtils, slide_io

def get_round_name(src_f):
    """Extract the base name (round name) from the filename.
    For filenames like 'LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00.tif', 
    this returns 'lb06 bp 4i asma green cxcr3 red 28mar25_r 3'
    """
    img_name = valtils.get_name(src_f)
    round_name = img_name.lower().split("_merged")[0]
    return round_name

def get_channel_number(src_f):
    """Extract the channel number from the filename.
    For filenames like 'LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00.tif',
    this returns 0
    """
    img_name = valtils.get_name(src_f).lower()
    if "_ch00" in img_name:
        return 0
    elif "_ch01" in img_name:
        return 1
    elif "_ch02" in img_name:
        return 2
    # Add more channels as needed
    else:
        # Default case if channel can't be determined
        return 99

def get_ome_xml(warped_slide, reference_slide, channel_names=None):
    """Generate ome-xml for warped slide"""
    ref_meta = reference_slide.reader.metadata
    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)
    ome_xml_obj = slide_io.create_ome_xml(
        shape_xyzct=out_xyczt,
        bf_dtype=bf_dtype,
        is_rgb=False,
        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
        channel_names=channel_names,
        colormap=None
    )
    return ome_xml_obj.to_xml()

def process_images(src_dir, dst_dir, reference_slide_path, micro_reg_fraction=0.25):
    """Process images with proper channel ordering"""
    print("Starting image processing with proper channel ordering...")
    
    # Create output directory
    merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], "slides")
    pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)
    
    # Get all images
    full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]
    
    # Register rounds using DAPI channels (ch00)
    dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tif")]
    print(f"Found {len(dapi_imgs)} DAPI images for registration")
    
    # Initialize VALIS registrar
    print(f"Initializing VALIS with reference slide: {os.path.basename(reference_slide_path)}")
    registrar = registration.Valis(
        src_dir, 
        dst_dir, 
        img_list=dapi_imgs, 
        reference_img_f=reference_slide_path, 
        micro_rigid_registrar_cls=None,
        align_to_reference=True
    )
    
    # Perform registration
    print("Performing registration...")
    rigid_registrar, non_rigid_registrar, error_df = registrar.register()
    
    # Get reference slide and perform micro-registration
    reference_slide = registrar.get_ref_slide()
    micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)
    print(f"Performing micro-registration with size: {micro_reg_size}...")
    micro_reg, micro_error = registrar.register_micro(
        max_non_rigid_registration_dim_px=micro_reg_size,
        align_to_reference=True
    )
    
    # Group images by round
    print("Grouping images by round...")
    round_dict = {
        slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]
        for slide_obj in registrar.slide_dict.values()
    }
    
    # Initialize variables for warped slide
    channel_names = []
    warped_slide = None
    
    # Process each round
    print("Processing rounds and channels...")
    for slide_warper, round_slides in tqdm(round_dict.items()):
        round_name = get_round_name(slide_warper.src_f)
        print(f"\nProcessing round: {round_name}")
        
        # Group files by channel number
        channel_files = {}
        for src_f in round_slides:
            channel_num = get_channel_number(src_f)
            channel_files[channel_num] = src_f
            
        print(f"Found {len(channel_files)} channels for round {round_name}")
        
        # Process channels in order (ch00, ch01, ch02, etc.)
        for channel_num in sorted(channel_files.keys()):
            src_f = channel_files[channel_num]
            img_name = valtils.get_name(src_f)
            display_name = f"{round_name}_ch{channel_num:02d}"
            channel_names.append(display_name)
            print(f"  Warping channel {channel_num}: {img_name}")
            
            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)
            if warped_slide is None:
                warped_slide = warped_channel
                print(f"  Created initial warped slide with channel {channel_num}")
            else:
                warped_slide = warped_slide.bandjoin(warped_channel)
                print(f"  Added channel {channel_num} to warped slide")
    
    # Generate OME-XML and save the merged slide
    print("\nGenerating OME-XML and saving merged slide...")
    reference_slide = registrar.get_ref_slide()
    merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)
    merged_slide_f = os.path.join(merged_slide_dir, "merged.ome.tiff")
    
    print(f"Saving merged slide with {len(channel_names)} channels to: {merged_slide_f}")
    print(f"Channel names: {channel_names}")
    
    slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)
    print(f"Successfully saved merged slide to: {merged_slide_f}")
    
    return merged_slide_f

if __name__ == "__main__":
    # Example usage
    src_dir = "/path/to/source/directory"
    dst_dir = "/path/to/destination/directory"
    reference_slide = "/path/to/reference/slide.tif"
    
    process_images(src_dir, dst_dir, reference_slide)
