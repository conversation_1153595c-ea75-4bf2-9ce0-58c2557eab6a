{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "from pathlib import Path\n", "import time\n", "import cv2\n", "from skimage.feature import peak_local_max\n", "from skimage.measure import label, regionprops\n", "from scipy.ndimage import distance_transform_edt\n", "from scipy.spatial.distance import cdist\n", "import copy\n", "from torch.cuda.amp import autocast, GradScaler\n", "from torch.utils.tensorboard import SummaryWriter\n", "import albumentations as A\n", "from albumentations.pytorch import ToTensorV2\n", "from tqdm.auto import tqdm\n", "from skimage.segmentation import watershed\n", "from scipy import ndimage as ndi\n", "from datetime import datetime\n", "import random\n", "import torch.utils.checkpoint\n", "from scipy import ndimage\n", "import glob\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Force using just the first GPU and ignore others\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"  # Use only GPU 0\n", "\n", "# Optimize CUDA settings for performance\n", "torch.backends.cudnn.benchmark = True\n", "torch.backends.cudnn.deterministic = False\n", "if hasattr(torch.backends.cuda, 'matmul'):\n", "    torch.backends.cuda.matmul.allow_tf32 = True  # Use TensorFloat-32 if available\n", "torch.backends.cudnn.allow_tf32 = True\n", "\n", "# Clear GPU memory\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class SpotAugmenter:\n", "    \"\"\"\n", "    Class to handle image augmentations for spot detection,\n", "    supporting both 2D and 3D data with appropriate transformations\n", "    \"\"\"\n", "    \n", "    def __init__(self, is_3d=False, rotation=True, flip=True, elastic=False, \n", "                gaussian_noise=True, brightness_contrast=True):\n", "        \"\"\"\n", "        Initialize the augmenter with options\n", "        \n", "        Args:\n", "            is_3d: Whether to use 3D transformations\n", "            rotation: Whether to apply random rotation\n", "            flip: Whether to apply random flips\n", "            elastic: Whether to apply elastic deformation (careful with spots)\n", "            gaussian_noise: Whether to add random noise\n", "            brightness_contrast: Whether to adjust brightness/contrast\n", "        \"\"\"\n", "        self.is_3d = is_3d\n", "        self.rotation = rotation\n", "        self.flip = flip\n", "        self.elastic = elastic\n", "        self.gaussian_noise = gaussian_noise\n", "        self.brightness_contrast = brightness_contrast\n", "    \n", "    def get_transform(self, train=True):\n", "        \"\"\"\n", "        Get transformation pipeline based on settings\n", "        \n", "        Args:\n", "            train: Whether to return training or validation transforms\n", "        \n", "        Returns:\n", "            Transform function or composed transforms\n", "        \"\"\"\n", "        if not train:\n", "            # For validation, just return normalization/conversion\n", "            return self._get_basic_transform()\n", "        \n", "        if self.is_3d:\n", "            return self._get_3d_transform()\n", "        else:\n", "            return self._get_2d_transform()\n", "    \n", "    def _get_basic_transform(self):\n", "        \"\"\"Basic transform for validation (normalization only)\"\"\"\n", "        if self.is_3d:\n", "            # 3D basic transform\n", "            try:\n", "                from monai.transforms import (\n", "                    Compose, ScaleIntensityRanged, ToTensord\n", "                )\n", "                \n", "                return Compose([\n", "                    ScaleIntensityRanged(keys=[\"image\"], a_min=0, a_max=65535, b_min=0, b_max=1),\n", "                    ToTensord(keys=[\"image\", \"mask\"])\n", "                ])\n", "            except ImportError:\n", "                print(\"Warning: MONAI not installed. Using basic NumPy transforms for 3D.\")\n", "                return None\n", "        else:\n", "            \n", "            \n", "            return <PERSON><PERSON>([\n", "                # First convert 16-bit to float\n", "                <PERSON><PERSON>(max_value=65535.0),\n", "                A.Normalize(mean=[0.5], std=[0.5]),\n", "                ToTensorV2()\n", "            ])\n", "    \n", "    def _get_2d_transform(self):\n", "        \"\"\"Get GPU-optimized augmentation pipeline for 2D images\"\"\"\n", "        import albumentations as A\n", "        from albumentations.pytorch import ToTensorV2\n", "        \n", "        transforms = []\n", "        \n", "        # First convert 16-bit to float to avoid dtype issues\n", "        transforms.append(<PERSON><PERSON>(max_value=65535.0))\n", "        \n", "        # Spatial augmentations\n", "        if self.rotation:\n", "            transforms.append(A.<PERSON>(p=0.5))\n", "        \n", "        if self.flip:\n", "            # FIXED: Use separate HorizontalFlip and VerticalFlip instead of non-existent Flip\n", "            transforms.append(<PERSON><PERSON>(p=0.5))\n", "            transforms.append(<PERSON><PERSON>(p=0.5))\n", "        \n", "        if self.elastic:\n", "            # Very mild elastic transform to avoid destroying spots\n", "            transforms.append(\n", "                A.<PERSON>ran<PERSON>(\n", "                    alpha=30, sigma=3, alpha_affine=5, \n", "                    interpolation=1, border_mode=0,\n", "                    p=0.3\n", "                )\n", "            )\n", "        \n", "        # GPU-friendly intensity augmentations\n", "        if self.brightness_contrast:\n", "            transforms.append(\n", "                <PERSON><PERSON>([\n", "                    <PERSON><PERSON>(\n", "                        brightness_limit=0.1, contrast_limit=0.1, \n", "                        p=0.8\n", "                    ),\n", "                    <PERSON><PERSON>(clip_limit=2, p=0.2),\n", "                ], p=0.5)\n", "            )\n", "        \n", "        if self.gaussian_noise:\n", "            transforms.append(\n", "                <PERSON><PERSON>([\n", "                    <PERSON><PERSON>(var_limit=(5, 20), p=0.7),\n", "                    <PERSON><PERSON>(blur_limit=(1, 3), p=0.3)\n", "                ], p=0.3)\n", "            )\n", "        \n", "        # Always include normalization and conversion to tensor\n", "        transforms.extend([\n", "            A.Normalize(mean=[0.5], std=[0.5]),\n", "            ToTensorV2()\n", "        ])\n", "        \n", "        return <PERSON><PERSON>(transforms)\n", "    \n", "    def _get_3d_transform(self):\n", "        \"\"\"Get augmentation pipeline for 3D volumes\"\"\"\n", "        try:\n", "            # For 3D, we'll use MONAI transforms (GPU-accelerated)\n", "            from monai.transforms import (\n", "                <PERSON><PERSON>se, RandRotate90d, Rand<PERSON><PERSON>d, RandGaussian<PERSON>oised,\n", "                RandAdjustContrastd, ScaleIntensityRanged,\n", "                ToTensord\n", "            )\n", "            \n", "            transforms = []\n", "            \n", "            # Spatial augmentations\n", "            if self.rotation:\n", "                transforms.append(RandRotate90d(keys=[\"image\", \"mask\"], prob=0.5, spatial_axes=[0, 1]))\n", "            \n", "            if self.flip:\n", "                transforms.append(RandFlipd(keys=[\"image\", \"mask\"], prob=0.5, spatial_axis=0))\n", "                transforms.append(RandFlipd(keys=[\"image\", \"mask\"], prob=0.5, spatial_axis=1))\n", "            \n", "            # Intensity augmentations\n", "            if self.brightness_contrast:\n", "                transforms.append(RandAdjustContrastd(keys=[\"image\"], prob=0.3, gamma=(0.9, 1.1)))\n", "            \n", "            if self.gaussian_noise:\n", "                transforms.append(Rand<PERSON><PERSON><PERSON>Noised(keys=[\"image\"], prob=0.3, mean=0.0, std=0.05))\n", "            \n", "            # Always include normalization and conversion to tensor\n", "            transforms.extend([\n", "                ScaleIntensityRanged(keys=[\"image\"], a_min=0, a_max=65535, b_min=0, b_max=1),\n", "                ToTensord(keys=[\"image\", \"mask\"])\n", "            ])\n", "            \n", "            return Compose(transforms)\n", "        except ImportError:\n", "            print(\"Warning: MONAI not installed. Cannot create 3D transforms.\")\n", "            return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["The InstanceSpotDetector class is a unified 2D/3D spot instance detector with flow field and multi-scale heatmaps. It includes:\n", "\n", "Encoder blocks for feature extraction\n", "Decoder blocks with skip connections for reconstruction\n", "Multi-scale heatmap predictors\n", "Flow field predictor for instance separation\n", "Instance boundary predictor"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class InstanceSpotDetector(nn.Module):\n", "    \"\"\"\n", "    Unified 2D/3D spot instance detector with flow field and multi-scale heatmaps\n", "    Based on SpotiFlow but optimized for instance segmentation with learnable parameters\n", "    \"\"\"\n", "    def __init__(self, \n", "                 in_channels=1, \n", "                 feature_channels=[16, 32, 64, 128, 256],\n", "                 is_3d=False,\n", "                 use_instance_flow=True,\n", "                 use_attention=True,\n", "                 learn_instance_params=True,\n", "                 use_gradient_checkpointing=False):  # New parameter\n", "        \"\"\"\n", "        Initialize the spot instance detector\n", "        \n", "        Args:\n", "            in_channels: Number of input channels (typically 1 for grayscale)\n", "            feature_channels: List of channel counts for each level of the network\n", "            is_3d: Whether to use 3D convolutions for volumetric data\n", "            use_instance_flow: Whether to use flow field for instance separation\n", "            use_attention: Whether to use attention gates in the decoder\n", "            learn_instance_params: Whether to learn instance segmentation parameters\n", "        \"\"\"\n", "        super(InstanceSpotDetector, self).__init__()\n", "        self.is_3d = is_3d\n", "        self.use_instance_flow = use_instance_flow\n", "        self.use_attention = use_attention\n", "        self.learn_instance_params = learn_instance_params\n", "        self.use_gradient_checkpointing = use_gradient_checkpointing\n", "        \n", "        # Select proper convolution, pooling, and normalization based on dimensionality\n", "        self.Conv = nn.Conv3d if self.is_3d else nn.Conv2d\n", "        self.ConvTranspose = nn.ConvTranspose3d if self.is_3d else nn.ConvTranspose2d\n", "        self.BatchNorm = nn.BatchNorm3d if self.is_3d else nn.BatchNorm2d\n", "        self.MaxPool = nn.MaxPool3d if self.is_3d else nn.MaxPool2d\n", "        \n", "        # Define encoder blocks - FIXED: Removed duplicate initialization\n", "        self.encoders = nn.ModuleList()\n", "        in_ch = in_channels\n", "        \n", "        for i, out_ch in enumerate(feature_channels):\n", "            self.encoders.append(self._make_encoder_block(in_ch, out_ch))\n", "            in_ch = out_ch\n", "        \n", "        # Define decoder blocks with skip connections\n", "        self.decoders = nn.ModuleList()\n", "        decoder_channels = feature_channels[::-1]  # Reverse for decoder\n", "        \n", "        for i in range(len(decoder_channels) - 1):\n", "            in_ch = decoder_channels[i]\n", "            out_ch = decoder_channels[i + 1]\n", "            self.decoders.append(self._make_decoder_block(in_ch, out_ch))\n", "        \n", "        # Multi-scale heatmap predictors\n", "        self.multi_heatmaps = nn.ModuleList()\n", "        # For the bottleneck (deepest) features\n", "        self.multi_heatmaps.append(self.Conv(decoder_channels[0], 1, kernel_size=1))\n", "        \n", "        # For each decoder output\n", "        for i in range(len(decoder_channels) - 1):\n", "            self.multi_heatmaps.append(self.Conv(decoder_channels[i+1], 1, kernel_size=1))\n", "        \n", "        # Flow field predictor for instance separation (2 channels for 2D, 3 for 3D)\n", "        if self.use_instance_flow:\n", "            flow_channels = 3 if self.is_3d else 2\n", "            self.flow_conv = self.Conv(feature_channels[0], flow_channels, kernel_size=3, padding=1)\n", "        \n", "        # Instance boundary predictor to help separate adjacent instances\n", "        self.boundary_conv = self.Conv(feature_channels[0], 1, kernel_size=3, padding=1)\n", "        \n", "        # Add distance transform predictor for improved instance separation\n", "        self.distance_conv = self.Conv(feature_channels[0], 1, kernel_size=3, padding=1)\n", "        \n", "        # Add learnable parameters for instance segmentation\n", "        if learn_instance_params:\n", "            # Initialize with default values but make them learnable\n", "            self.boundary_factor = nn.Parameter(torch.tensor(0.3, dtype=torch.float32))\n", "            self.flow_div_factor = nn.Parameter(torch.tensor(0.5, dtype=torch.float32))\n", "            self.distance_factor = nn.Parameter(torch.tensor(0.5, dtype=torch.float32))\n", "            self.min_distance = nn.Parameter(torch.tensor(1.0, dtype=torch.float32))\n", "            self.peak_threshold = nn.Parameter(torch.tensor(0.7, dtype=torch.float32))\n", "            self.spot_min_size = nn.Parameter(torch.tensor(2.0, dtype=torch.float32))\n", "        else:\n", "            # Fixed parameters\n", "            self.boundary_factor = 0.3\n", "            self.flow_div_factor = 0.5\n", "            self.distance_factor = 0.5\n", "            self.min_distance = 1\n", "            self.peak_threshold = 0.7\n", "            self.spot_min_size = 2\n", "        \n", "        # Initialize weights with negative bias for output layers to handle class imbalance\n", "        self._initialize_weights()\n", "\n", "    def _make_encoder_block(self, in_channels, out_channels):\n", "        \"\"\"Create an encoder block with appropriate convolutions\"\"\"\n", "        return nn.Sequential(\n", "            self.Conv(in_channels, out_channels, kernel_size=3, padding=1),\n", "            self.BatchNorm(out_channels),\n", "            nn.ReLU(inplace=True),\n", "            self.Conv(out_channels, out_channels, kernel_size=3, padding=1),\n", "            self.BatchNorm(out_channels),\n", "            nn.ReLU(inplace=True)\n", "        )\n", "    \n", "    def _make_decoder_block(self, in_channels, out_channels):\n", "        \"\"\"Create a decoder block with skip connections\"\"\"\n", "        # FIXED: After upsampling we have out_channels, and skip has out_channels,\n", "        # so concat gives out_channels*2 input channels to conv1, not in_channels+out_channels\n", "        decoder = nn.ModuleDict({\n", "            'upconv': self.ConvTranspose(\n", "                in_channels, out_channels, \n", "                kernel_size=2, stride=2\n", "            ),\n", "            # FIXED: First convolution after concatenation gets 2*out_channels input\n", "            'conv1': self.Conv(out_channels * 2, out_channels, kernel_size=3, padding=1),\n", "            'bn1': self.BatchNorm(out_channels),\n", "            'conv2': self.Conv(out_channels, out_channels, kernel_size=3, padding=1),\n", "            'bn2': self.BatchNorm(out_channels)\n", "        })\n", "        \n", "        # Add attention gate if requested\n", "        if self.use_attention:\n", "            if self.is_3d:\n", "                decoder['attention'] = AttentionGate3D(\n", "                    out_channels, out_channels, out_channels//2\n", "                )\n", "            else:\n", "                decoder['attention'] = AttentionGate(\n", "                    out_channels, out_channels, out_channels//2\n", "                )\n", "        \n", "        return decoder\n", "    \n", "    def _initialize_weights(self):\n", "        \"\"\"Initialize weights with special handling for output layers\"\"\"\n", "        for m in self.modules():\n", "            if isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, (nn.<PERSON>chNorm2d, nn.BatchNorm3d)):\n", "                nn.init.constant_(m.weight, 1)\n", "                nn.init.constant_(m.bias, 0)\n", "        \n", "        # Special initialization for output layers to handle extreme class imbalance\n", "        for m in self.multi_heatmaps:\n", "            nn.init.constant_(m.bias, -2.0)  # Start with negative bias\n", "        \n", "        if self.use_instance_flow:\n", "            nn.init.normal_(self.flow_conv.weight, std=0.01)\n", "            nn.init.constant_(self.flow_conv.bias, 0)\n", "        \n", "        nn.init.constant_(self.boundary_conv.bias, -2.0)\n", "        \n", "        # Initialize distance conv - positive bias for better distance start\n", "        nn.init.constant_(self.distance_conv.bias, 1.0)\n", "    \n", "    def forward(self, x):\n", "        \"\"\"Forward pass with gradient checkpointing for memory efficiency\"\"\"\n", "        encoder_features = []\n", "        \n", "        # Encoder path with gradient checkpointing (manual implementation instead of checkpoint_sequential)\n", "        for i, encoder in enumerate(self.encoders):\n", "            # Apply gradient checkpointing to each encoder block separately\n", "            if self.training and i > 0:  # <PERSON>p checkpointing first block to avoid input shape issues\n", "                x = torch.utils.checkpoint.checkpoint(encoder, x, use_reentrant=False)\n", "            else:\n", "                x = encoder(x)  # Normal forward pass for first block or during inference\n", "                \n", "            encoder_features.append(x)\n", "            \n", "            # Apply pooling except for the last encoder\n", "            if i < len(self.encoders) - 1:\n", "                if self.is_3d:\n", "                    x = F.max_pool3d(x, kernel_size=2, stride=2)\n", "                else:\n", "                    x = F.max_pool2d(x, kernel_size=2, stride=2)\n", "        \n", "        # Reverse encoder features for decoder\n", "        encoder_features = encoder_features[::-1]\n", "        \n", "        # Multi-scale heatmaps will be stored here\n", "        multi_scale_heatmaps = []\n", "        \n", "        # Add first heatmap at lowest resolution\n", "        multi_scale_heatmaps.append(self.multi_heatmaps[0](x))\n", "        \n", "        # Decoder path with skip connections\n", "        for i, decoder in enumerate(self.decoders):\n", "            # Upample features\n", "            x = decoder['upconv'](x)\n", "            \n", "            # Get skip connection features\n", "            skip_features = encoder_features[i + 1]\n", "            \n", "            # Ensure compatible shapes\n", "            if x.shape[-2:] != skip_features.shape[-2:]:\n", "                x = F.interpolate(x, size=skip_features.shape[-2:], mode='nearest')\n", "            \n", "            # Apply attention if used\n", "            if self.use_attention and 'attention' in decoder:\n", "                if decoder['attention'] is not None:\n", "                    skip_features = decoder['attention'](skip_features, x)\n", "            \n", "            # Concatenate skip features\n", "            x = torch.cat([x, skip_features], dim=1)\n", "            \n", "            # Apply convolutions - use checkpointing for these too during training\n", "            if self.training:\n", "                def conv_block(x, conv, bn):\n", "                    return <PERSON>.relu(bn(conv(x)))\n", "                \n", "                x = torch.utils.checkpoint.checkpoint(conv_block, x, decoder['conv1'], decoder['bn1'], use_reentrant=False)\n", "                x = torch.utils.checkpoint.checkpoint(conv_block, x, decoder['conv2'], decoder['bn2'], use_reentrant=False)\n", "            else:\n", "                x = F.relu(decoder['bn1'](decoder['conv1'](x)))\n", "                x = F.relu(decoder['bn2'](decoder['conv2'](x)))\n", "            \n", "            # Generate heatmap at this scale\n", "            multi_scale_heatmaps.append(self.multi_heatmaps[i + 1](x))\n", "        \n", "        # Generate flow field for instance separation\n", "        flow = self.flow_conv(x) if self.use_instance_flow else None\n", "        \n", "        # Generate instance boundary prediction\n", "        boundaries = self.boundary_conv(x)\n", "        \n", "        # Generate distance transform prediction\n", "        distance = self.distance_conv(x)\n", "        \n", "        # Use highest resolution for the main output\n", "        main_heatmap = multi_scale_heatmaps[-1]\n", "        \n", "        # Prepare outputs as a dictionary\n", "        outputs = {\n", "            'heatmap': main_heatmap,  # Main output for spot detection\n", "            'multi_heatmaps': multi_scale_heatmaps,  # Multi-scale outputs\n", "            'boundary': boundaries,  # Instance boundaries\n", "            'distance': distance,  # Distance transform prediction\n", "            'features': x  # High-level features for auxiliary tasks\n", "        }\n", "        \n", "        if self.use_instance_flow:\n", "            outputs['flow'] = flow\n", "        \n", "        # Generate instance proposals using heatmap and flow\n", "        if not self.training and not torch.jit.is_scripting():\n", "            # Process instances during inference\n", "            try:\n", "                instance_map, coords = self._get_instances(\n", "                    main_heatmap, flow, boundaries, distance\n", "                )\n", "                outputs['instance_labels'] = instance_map\n", "                outputs['spot_coords'] = coords\n", "            except:\n", "                # Skip instance prediction if there's an error\n", "                outputs['instance_labels'] = None\n", "                outputs['spot_coords'] = None\n", "                    \n", "        return outputs\n", "        \n", "    # Add this to your InstanceSpotDetector class\n", "    def fuse_model(self):\n", "        \"\"\"Fuse batch normalization with convolutions for inference speed\"\"\"\n", "        if not self.training:\n", "            # Find fusable sequences in encoders\n", "            for encoder in self.encoders:\n", "                for i in range(len(encoder) - 2):\n", "                    if (isinstance(encoder[i], (nn.Conv2d, nn.Conv3d)) and\n", "                        isinstance(encoder[i+1], (nn.BatchNorm2d, nn.BatchNorm3d)) and\n", "                        isinstance(encoder[i+2], nn.ReLU)):\n", "                        # Fuse Conv+BN+ReLU\n", "                        torch.quantization.fuse_modules(encoder, [str(i), str(i+1), str(i+2)], inplace=True)\n", "            \n", "            # Similar for decoder convolutions\n", "            for decoder in self.decoders:\n", "                if isinstance(decoder, nn.ModuleDict):\n", "                    if 'conv1' in decoder and 'bn1' in decoder:\n", "                        torch.quantization.fuse_modules(decoder, ['conv1', 'bn1'], inplace=True)\n", "                    if 'conv2' in decoder and 'bn2' in decoder:\n", "                        torch.quantization.fuse_modules(decoder, ['conv2', 'bn2'], inplace=True)\n", "        return self\n", "        \n", "    \n", "    def _get_instances(self, heatmap, flow=None, boundaries=None, distance=None, threshold=0.5):\n", "        \"\"\"\n", "        Convert heatmap and flow to instance segmentation\n", "        Uses learnable parameters for watershed segmentation\n", "        \"\"\"\n", "        \n", "        \n", "        # Process batch item by item\n", "        batch_size = heatmap.shape[0]\n", "        device = heatmap.device\n", "        \n", "        # Get current parameters (constrain if learnable)\n", "        if self.learn_instance_params:\n", "            # Apply sigmoid to bound some parameters to [0,1]\n", "            boundary_factor = torch.sigmoid(self.boundary_factor).item()\n", "            flow_div_factor = torch.sigmoid(self.flow_div_factor).item()\n", "            distance_factor = torch.sigmoid(self.distance_factor).item()\n", "            # Ensure min_distance is reasonable using clipping\n", "            min_distance = max(1.0, min(10.0, self.min_distance.item()))\n", "            # Convert to int for peak_local_max\n", "            min_distance_int = int(round(min_distance))\n", "            # Other learnable parameters\n", "            peak_threshold = torch.sigmoid(self.peak_threshold).item() * 0.95 + 0.05  # Range [0.05, 1.0]\n", "            spot_min_size = max(1, int(self.spot_min_size.item()))\n", "        else:\n", "            # Use fixed parameters\n", "            boundary_factor = self.boundary_factor\n", "            flow_div_factor = self.flow_div_factor\n", "            distance_factor = self.distance_factor\n", "            min_distance_int = self.min_distance\n", "            peak_threshold = self.peak_threshold\n", "            spot_min_size = self.spot_min_size\n", "        \n", "        instance_maps = []\n", "        all_coords = []\n", "        \n", "        for b in range(batch_size):\n", "            # Get heatmap for this item\n", "            h_map = torch.sigmoid(heatmap[b, 0]).cpu().numpy()\n", "            \n", "            # Get boundary map for this item\n", "            boundary_map = torch.sigmoid(boundaries[b, 0]).cpu().numpy() if boundaries is not None else None\n", "            \n", "            # T<PERSON><PERSON><PERSON> heatmap\n", "            binary = h_map > threshold\n", "            \n", "            # Enhanced instance segmentation with flow and distance\n", "            if flow is not None and distance is not None:\n", "                # Get flow field\n", "                flow_field = flow[b].cpu().numpy()\n", "                \n", "                # Get distance prediction\n", "                dist_map = torch.sigmoid(distance[b, 0]).cpu().numpy()\n", "                \n", "                if self.is_3d:\n", "                    # 3D implementation\n", "                    # 3D flow divergence calculation\n", "                    dx = flow_field[0]\n", "                    dy = flow_field[1]\n", "                    dz = flow_field[2]\n", "                    \n", "                    # Compute 3D divergence\n", "                    div = ndimage.sobel(dx, axis=0) + ndimage.sobel(dy, axis=1) + ndimage.sobel(dz, axis=2)\n", "                    \n", "                    # Use boundary information if available\n", "                    if boundary_map is not None:\n", "                        # Combine information with learned weights\n", "                        combined_map = h_map - flow_div_factor * div * dist_map + boundary_factor * boundary_map\n", "                    else:\n", "                        combined_map = h_map - flow_div_factor * div * dist_map\n", "                    \n", "                    # Find peaks using learned min_distance\n", "                    coords = peak_local_max(combined_map, min_distance=min_distance_int, \n", "                                           threshold_abs=peak_threshold)\n", "                    \n", "                    # Create marker mask\n", "                    mask = np.zeros_like(combined_map, dtype=bool)\n", "                    if len(coords) > 0:\n", "                        mask[tuple(coords.T)] = True\n", "                        markers = ndimage.label(mask)[0]\n", "                        instance_map = watershed(-combined_map, markers, mask=binary)\n", "                    else:\n", "                        instance_map = np.zeros_like(combined_map, dtype=np.int32)\n", "                        coords = np.array([])\n", "                        \n", "                else:\n", "                    # 2D implementation\n", "                    # Calculate flow divergence (negative for convergence)\n", "                    dx = flow_field[0]\n", "                    dy = flow_field[1]\n", "                    \n", "                    # Sobel operator for computing divergence\n", "                    div = ndimage.sobel(dx, axis=0) + ndimage.sobel(dy, axis=1)\n", "                    \n", "                    # Combine information with learned weights\n", "                    if boundary_map is not None:\n", "                        combined_map = h_map - flow_div_factor * div * dist_map + boundary_factor * boundary_map\n", "                    else:\n", "                        combined_map = h_map - flow_div_factor * div * dist_map\n", "                    \n", "                    # Find peaks using learned min_distance\n", "                    coords = peak_local_max(combined_map, min_distance=min_distance_int,\n", "                                          threshold_abs=peak_threshold)\n", "                    \n", "                    # Create marker mask\n", "                    mask = np.zeros_like(combined_map, dtype=bool)\n", "                    if len(coords) > 0:\n", "                        mask[coords[:, 0], coords[:, 1]] = True\n", "                        markers = ndimage.label(mask)[0]\n", "                        # Use watershed with combined information\n", "                        instance_map = watershed(-combined_map, markers, mask=binary)\n", "                    else:\n", "                        instance_map = np.zeros_like(combined_map, dtype=np.int32)\n", "                        coords = np.array([])\n", "            \n", "            # Flow-only based instance segmentation\n", "            elif flow is not None:\n", "                # Get flow field\n", "                flow_field = flow[b].cpu().numpy()\n", "                \n", "                if self.is_3d:\n", "                    # 3D implementation\n", "                    distance_transform = ndimage.distance_transform_edt(binary)\n", "                    coords = peak_local_max(distance_transform, min_distance=min_distance_int,\n", "                                           threshold_abs=peak_threshold)\n", "                    mask = np.zeros_like(distance_transform, dtype=bool)\n", "                    mask[tuple(coords.T)] = True\n", "                    markers = ndimage.label(mask)[0]\n", "                    instance_map = watershed(-distance_transform, markers, mask=binary)\n", "                else:\n", "                    # For 2D, use flow magnitude to help with segmentation\n", "                    flow_mag = np.sqrt(np.sum(flow_field**2, axis=0))\n", "                    \n", "                    # Use boundary information if available with learned weights\n", "                    if boundary_map is not None:\n", "                        enhanced_map = h_map + distance_factor * flow_mag + boundary_factor * boundary_map\n", "                    else:\n", "                        enhanced_map = h_map + distance_factor * flow_mag\n", "                    \n", "                    coords = peak_local_max(enhanced_map, min_distance=min_distance_int,\n", "                                          threshold_abs=peak_threshold)\n", "                    \n", "                    # Create marker mask\n", "                    mask = np.zeros_like(enhanced_map, dtype=bool)\n", "                    if len(coords) > 0:\n", "                        mask[coords[:, 0], coords[:, 1]] = True\n", "                        markers = ndimage.label(mask)[0]\n", "                        \n", "                        # Use watershed with flow magnitude\n", "                        instance_map = watershed(-enhanced_map, markers, mask=binary)\n", "                    else:\n", "                        instance_map = np.zeros_like(enhanced_map, dtype=np.int32)\n", "                        coords = np.array([])\n", "                        \n", "            else:\n", "                # Without flow, just use connected components with boundary enhancement\n", "                if boundary_map is not None:\n", "                    # Use boundary information with learned weights\n", "                    enhanced_binary = binary.astype(float) + boundary_factor * boundary_map\n", "                    # Re-threshold to get improved binary\n", "                    enhanced_binary = enhanced_binary > 0.5\n", "                    instance_map = label(enhanced_binary)\n", "                else:\n", "                    instance_map = label(binary)\n", "                \n", "                # Extract coordinates\n", "                props = regionprops(instance_map)\n", "                coords = np.array([p.centroid for p in props] if props else [])\n", "            \n", "            # Filter out small objects using learnable min size if enabled\n", "            if spot_min_size > 1:\n", "                props = regionprops(instance_map)\n", "                for prop in props:\n", "                    if prop.area < spot_min_size:\n", "                        instance_map[instance_map == prop.label] = 0\n", "                \n", "                # Re-label to ensure consecutive labels\n", "                if np.any(instance_map):\n", "                    instance_map = label(instance_map > 0)\n", "                    \n", "                    # Update coordinates\n", "                    props = regionprops(instance_map)\n", "                    coords = np.array([p.centroid for p in props] if props else [])\n", "            \n", "            instance_maps.append(torch.from_numpy(instance_map).to(device))\n", "            all_coords.append(coords)\n", "        \n", "        # Stack instance maps into a batch\n", "        if len(instance_maps) > 0:\n", "            instance_tensor = torch.stack([\n", "                inst.unsqueeze(0) for inst in instance_maps\n", "            ], dim=0)\n", "        else:\n", "            # Create empty tensor if no instances\n", "            shape = (batch_size, 1) + heatmap.shape[2:]\n", "            instance_tensor = torch.zeros(shape, device=device)\n", "            \n", "        return instance_tensor, all_coords"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class AttentionGate(nn.Module):\n", "    \"\"\"Attention Gate for focusing on relevant features\"\"\"\n", "    def __init__(self, x_channels, g_channels, inter_channels):\n", "        super(AttentionGate, self).__init__()\n", "        self.W_x = nn.Conv2d(x_channels, inter_channels, kernel_size=1)\n", "        self.W_g = nn.Conv2d(g_channels, inter_channels, kernel_size=1)\n", "        self.psi = nn.Conv2d(inter_channels, 1, kernel_size=1)\n", "        \n", "    def forward(self, x, g):\n", "        # Apply 1x1 convolutions\n", "        x1 = self.W_x(x)\n", "        g1 = self.W_g(g)\n", "        \n", "        # Sum and apply non-linearity\n", "        out = <PERSON>.relu(x1 + g1)\n", "        \n", "        # Apply 1x1 convolution and sigmoid activation\n", "        out = self.psi(out)\n", "        out = torch.sig<PERSON><PERSON>(out)\n", "        \n", "        # Multiply input with attention weights\n", "        return x * out\n", "    \n", "    \n", "class AttentionGate3D(nn.Module):\n", "    \"\"\"3D Attention Gate for volumetric data\"\"\"\n", "    def __init__(self, x_channels, g_channels, inter_channels):\n", "        super(AttentionGate3D, self).__init__()\n", "        self.W_x = nn.Conv3d(x_channels, inter_channels, kernel_size=1)\n", "        self.W_g = nn.Conv3d(g_channels, inter_channels, kernel_size=1)\n", "        self.psi = nn.Conv3d(inter_channels, 1, kernel_size=1)\n", "        \n", "    def forward(self, x, g):\n", "        x1 = self.W_x(x)\n", "        g1 = self.W_g(g)\n", "        out = <PERSON>.relu(x1 + g1)\n", "        out = self.psi(out)\n", "        out = torch.sig<PERSON><PERSON>(out)\n", "        return x * out"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class SpotInstanceDataset(Dataset):\n", "    \"\"\"Dataset for spot instance segmentation with balanced sampling\"\"\"\n", "    \n", "    def __init__(self, images, masks=None, is_train=True, is_3d=False, patch_size=None,\n", "                 augmentation_params=None):\n", "        \"\"\"\n", "        Initialize the dataset\n", "        \n", "        Args:\n", "            images: List of image paths or numpy arrays\n", "            masks: List of mask paths or numpy arrays (instance labels)\n", "            is_train: Whether this is for training (enables augmentation)\n", "            is_3d: Whether data is 3D\n", "            patch_size: Size of patches to extract (helps with memory and balance)\n", "            augmentation_params: Dictionary of augmentation parameters to pass to SpotAugmenter\n", "        \"\"\"\n", "        self.images = images\n", "        self.masks = masks\n", "        self.is_train = is_train\n", "        self.is_3d = is_3d\n", "        self.patch_size = patch_size\n", "        \n", "        # FIXED: Initialize tracking variables BEFORE running precompute\n", "        self.max_mask_value_seen = 0\n", "        self.binary_conversion_count = 0\n", "        \n", "        # Create augmenter with proper settings\n", "        aug_params = augmentation_params or {}\n", "        self.augmenter = SpotAugmenter(\n", "            is_3d=is_3d,\n", "            rotation=aug_params.get('rotation', True),\n", "            flip=aug_params.get('flip', True),\n", "            elastic=aug_params.get('elastic', False),\n", "            gaussian_noise=aug_params.get('gaussian_noise', True),\n", "            brightness_contrast=aug_params.get('brightness_contrast', True)\n", "        )\n", "        \n", "        # Get appropriate transforms based on training/validation mode\n", "        self.transform = self.augmenter.get_transform(train=is_train)\n", "        \n", "        # Pre-compute spot locations for sampling\n", "        self.spot_indices = None\n", "        if is_train and masks is not None and patch_size:\n", "            self._precompute_spot_indices()\n", "    \n", "    def visualize_sample_masks(self, num_samples=5):\n", "        \"\"\"Visualize sample masks to debug loading issues\"\"\"\n", "        import matplotlib.pyplot as plt\n", "        import numpy as np\n", "        import os\n", "        \n", "        # Create output directory\n", "        os.makedirs('debug', exist_ok=True)\n", "        \n", "        # Select random indices\n", "        indices = np.random.choice(len(self.masks), min(num_samples, len(self.masks)), replace=False)\n", "        \n", "        fig, axes = plt.subplots(2, num_samples, figsize=(15, 6))\n", "        if num_samples == 1:\n", "            axes = np.array([[axes[0]], [axes[1]]])\n", "        \n", "        for i, idx in enumerate(indices):\n", "            if isinstance(self.masks[idx], str):\n", "                mask_path = self.masks[idx]\n", "                mask = self._load_image(mask_path)\n", "            else:\n", "                mask = self.masks[idx]\n", "                mask_path = f\"Array {idx}\"\n", "            \n", "            # Original mask (instance labels)\n", "            if mask.max() > 0:\n", "                norm_mask = mask / mask.max()\n", "            else:\n", "                norm_mask = mask\n", "            \n", "            # Binary mask (for training)\n", "            binary_mask = (mask > 0).astype(np.float32)\n", "            \n", "            # Plot original with colorbar\n", "            im1 = axes[0, i].imshow(norm_mask, cmap='viridis')\n", "            axes[0, i].set_title(f\"Instance Mask {idx}\\nMax: {mask.max()}\")\n", "            axes[0, i].axis('off')\n", "            plt.colorbar(im1, ax=axes[0, i], fraction=0.046, pad=0.04)\n", "            \n", "            # Plot binary with colorbar\n", "            im2 = axes[1, i].imshow(binary_mask, cmap='gray')\n", "            axes[1, i].set_title(f\"Binary Mask {idx}\")\n", "            axes[1, i].axis('off')\n", "            plt.colorbar(im2, ax=axes[1, i], fraction=0.046, pad=0.04)\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig('debug/sample_masks_comparison.png')\n", "        plt.close()\n", "        \n", "        print(f\"Sample masks comparison saved to 'debug/sample_masks_comparison.png'\")\n", "    \n", "    def visualize_image_mask_pairs(self, num_samples=5, save_path='debug/image_mask_qc.png', threshold=0.5):\n", "        \"\"\"\n", "        Visualize image-mask pairs for quality control with threshold applied\n", "        \n", "        Args:\n", "            num_samples: Number of samples to visualize\n", "            save_path: Path to save the visualization\n", "            threshold: Thresh<PERSON> for binary mask conversion (default: 0.5)\n", "        \"\"\"\n", "        import matplotlib.pyplot as plt\n", "        import numpy as np\n", "        import os\n", "        from matplotlib.colors import LinearSegmentedColormap\n", "        \n", "        # Create output directory if needed\n", "        os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "        \n", "        # Select random indices, preferring images with spots if available\n", "        if hasattr(self, 'spot_indices') and self.spot_indices and len(self.spot_indices) >= num_samples:\n", "            indices = np.random.choice(self.spot_indices, num_samples, replace=False)\n", "        else:\n", "            indices = np.random.choice(len(self), num_samples, replace=False)\n", "        \n", "        # Create figure with 3 rows (original, mask, overlay) and num_samples columns\n", "        fig, axes = plt.subplots(3, num_samples, figsize=(4*num_samples, 12))\n", "        \n", "        # Handle case of single sample\n", "        if num_samples == 1:\n", "            axes = axes.reshape(3, 1)\n", "        \n", "        # Create custom colormap for mask (red for spots)\n", "        spot_cmap = LinearSegmentedColormap.from_list('spot_mask', [(0,0,0,0), (1,0,0,0.7)])\n", "        \n", "        for i, idx in enumerate(indices):\n", "            # Get image and mask\n", "            if isinstance(self.images[idx], str):\n", "                image = self._load_image(self.images[idx])\n", "                image_name = os.path.basename(self.images[idx])\n", "            else:\n", "                image = self.images[idx].copy()\n", "                image_name = f\"Array {idx}\"\n", "            \n", "            if self.masks is not None:\n", "                if isinstance(self.masks[idx], str):\n", "                    mask = self._load_image(self.masks[idx])\n", "                    mask_name = os.path.basename(self.masks[idx])\n", "                else:\n", "                    mask = self.masks[idx].copy()\n", "                    mask_name = f\"Array {idx}\"\n", "                    \n", "                # Convert to binary with threshold 0.5\n", "                binary_mask = (mask > threshold).astype(np.float32)\n", "                \n", "                # Calculate mask statistics\n", "                num_spots = np.max(mask) if mask.max() > 1 else np.sum(binary_mask > 0)\n", "                mask_coverage = np.mean(binary_mask) * 100  # percentage of positive pixels\n", "            else:\n", "                mask = np.zeros_like(image)\n", "                binary_mask = np.zeros_like(image)\n", "                mask_name = \"None\"\n", "                num_spots = 0\n", "                mask_coverage = 0\n", "            \n", "            # Normalize image for display\n", "            if image.max() > image.min():\n", "                norm_image = (image - image.min()) / (image.max() - image.min())\n", "            else:\n", "                norm_image = image\n", "                \n", "            # Select the 2D image/slice to display if 3D\n", "            if self.is_3d:\n", "                if len(image.shape) == 3:  # 3D image\n", "                    middle_slice = image.shape[0] // 2\n", "                    display_image = norm_image[middle_slice]\n", "                    display_mask = binary_mask[middle_slice]\n", "                else:\n", "                    display_image = norm_image\n", "                    display_mask = binary_mask\n", "            else:\n", "                display_image = norm_image\n", "                display_mask = binary_mask\n", "                \n", "            # Ensure display mask is 2D\n", "            if len(display_mask.shape) > 2:\n", "                display_mask = display_mask[0]  # Take first channel\n", "            \n", "            # Row 1: Show original image\n", "            axes[0, i].imshow(display_image, cmap='gray')\n", "            axes[0, i].set_title(f\"Image {idx}\\n{image_name}\")\n", "            axes[0, i].axis('off')\n", "            \n", "            # Row 2: Show binary mask\n", "            mask_img = axes[1, i].imshow(display_mask, cmap='hot')\n", "            axes[1, i].set_title(f\"Binary Mask (t={threshold})\\nSpots: {num_spots}, Coverage: {mask_coverage:.2f}%\")\n", "            axes[1, i].axis('off')\n", "            plt.colorbar(mask_img, ax=axes[1, i], fraction=0.046, pad=0.04)\n", "            \n", "            # Row 3: Show overlay\n", "            axes[2, i].imshow(display_image, cmap='gray')\n", "            axes[2, i].imshow(display_mask, cmap=spot_cmap)\n", "            axes[2, i].set_title(\"Overlay\")\n", "            axes[2, i].axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.savefig(save_path)\n", "        plt.close()\n", "        \n", "        print(f\"Image-mask QC visualization saved to {save_path}\")\n", "        \n", "        # Return statistics about the masks\n", "        mask_stats = {}\n", "        total_masks = len(self.masks) if self.masks is not None else 0\n", "        positive_masks = len(self.spot_indices) if hasattr(self, 'spot_indices') and self.spot_indices else \"Unknown\"\n", "        \n", "        mask_stats[\"total_masks\"] = total_masks\n", "        mask_stats[\"positive_masks\"] = positive_masks\n", "        if isinstance(positive_masks, (int, float)) and total_masks > 0:\n", "            mask_stats[\"percent_positive\"] = f\"{100 * positive_masks / total_masks:.1f}%\"\n", "        else:\n", "            mask_stats[\"percent_positive\"] = \"Unknown\"\n", "        mask_stats[\"max_value_seen\"] = self.max_mask_value_seen\n", "        # FIX: Use consistent key name\n", "        mask_stats[\"binary_conversion_count\"] = self.binary_conversion_count\n", "        \n", "        print(f\"\\nMask Statistics:\")\n", "        print(f\"  - Total masks: {mask_stats['total_masks']}\")\n", "        print(f\"  - Masks with spots: {mask_stats['positive_masks']} ({mask_stats['percent_positive']})\")\n", "        print(f\"  - Max value encountered: {mask_stats['max_value_seen']}\")\n", "        \n", "        \n", "        return mask_stats\n", "    \n", "    def _precompute_spot_indices(self):\n", "        \"\"\"Pre-compute indices of images with spots for balanced sampling and filter out empty images/masks.\"\"\"\n", "        self.spot_indices = []\n", "        self.spot_locations = []\n", "        \n", "        # New lists to store filtered images and masks\n", "        filtered_images = []\n", "        filtered_masks = []\n", "        \n", "        for i, mask_path in enumerate(tqdm(self.masks, desc=\"Finding spots\")):\n", "            if isinstance(mask_path, str):\n", "                mask = self._load_image(mask_path)\n", "            else:\n", "                mask = mask_path\n", "            \n", "            # Find spots in mask (values above threshold indicate spots)\n", "            spot_coords = np.argwhere(mask > 0)\n", "            \n", "            if len(spot_coords) > 0:\n", "                # Keep images/masks with spots\n", "                self.spot_indices.append(len(filtered_images))  # Index in the filtered list\n", "                self.spot_locations.append(spot_coords)\n", "                filtered_images.append(self.images[i])\n", "                filtered_masks.append(self.masks[i])\n", "        \n", "        # Replace original images and masks with filtered ones\n", "        self.images = filtered_images\n", "        self.masks = filtered_masks\n", "        \n", "        print(f\"Filtered dataset: {len(self.images)} images with spots out of {len(self.masks)} total masks.\")\n", "        \n", "        # Add visualization of a few sample masks for debugging\n", "        self.visualize_sample_masks(5)\n", "        \n", "        # At the end of _precompute_spot_indices\n", "        # Add QC visualization of image-mask pairs with threshold applied\n", "        if len(self.spot_indices) > 0:\n", "            self.visualize_image_mask_pairs(\n", "                num_samples=min(5, len(self.spot_indices)),\n", "                save_path='debug/sample_image_mask_qc.png',\n", "                threshold=0.5\n", "            )\n", "            \n", "    def _load_image(self, path):\n", "        \"\"\"Load image from path based on dimensionality with proper 16-bit TIF handling\"\"\"\n", "        if self.is_3d:\n", "            # 3D handling remains the same\n", "            try:\n", "                return np.load(path)\n", "            except:\n", "                import nibabel as nib\n", "                return nib.load(path).get_fdata()\n", "        else:\n", "            # For 2D images, check if it's a TIF/TIFF\n", "            if path.lower().endswith(('.tif', '.tiff')):\n", "                # Try multiple methods for loading TIFF files\n", "                try:\n", "                    # Method 1: Try tifffile with imagecodecs\n", "                    try:\n", "                        import tifffile\n", "                        import imagecodecs  # Check if imagecodecs is available\n", "                        image = tifffile.imread(path)\n", "                        return image\n", "                    except ImportError:\n", "                        raise  # Let it go to next method\n", "                    \n", "                except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValueE<PERSON>r):\n", "                    # Method 2: Try PIL which may handle some LZW compressed files\n", "                    try:\n", "                        from PIL import Image\n", "                        with Image.open(path) as img:\n", "                            return np.array(img)\n", "                    except:\n", "                        pass\n", "                    \n", "                    # Method 3: Try OpenCV's TIFF reader\n", "                    try:\n", "                        image = cv2.imread(path, cv2.IMREAD_ANYDEPTH)\n", "                        if image is None:\n", "                            raise ValueError(f\"OpenCV returned None for {path}\")\n", "                        return image\n", "                    except:\n", "                        pass\n", "                    \n", "                    # Method 4: Try libtiff directly\n", "                    try:\n", "                        import libtiff\n", "                        tif = libtiff.TIFF.open(path, 'r')\n", "                        image = tif.read_image()\n", "                        tif.close()\n", "                        return image\n", "                    except ImportError:\n", "                        pass\n", "                    \n", "                    # If we get here, all methods failed\n", "                    raise ValueError(f\"Failed to load TIFF file: {path}. Try installing 'pip install imagecodecs'\")\n", "            else:\n", "                # Use OpenCV for other formats\n", "                image = cv2.imread(path, cv2.IMREAD_ANYDEPTH)\n", "                if image is None:\n", "                    raise ValueError(f\"Failed to load image: {path}\")\n", "                return image\n", "    \n", "    def __len__(self):\n", "        return len(self.images)\n", "    \n", "    def _get_patch(self, image, mask, center=None):\n", "        \"\"\"Extract a patch centered at specified location\"\"\"\n", "        h, w = image.shape[-2:]  # Last two dims regardless of 2D/3D\n", "        \n", "        if self.is_3d:\n", "            d = image.shape[0]\n", "            ps = min(self.patch_size, min(d, h, w))\n", "            \n", "            # If center is not specified, choose randomly\n", "            if center is None:\n", "                d_start = np.random.randint(0, max(1, d - ps + 1))\n", "                h_start = np.random.randint(0, max(1, h - ps + 1))\n", "                w_start = np.random.randint(0, max(1, w - ps + 1))\n", "            else:\n", "                # Center patch on the given point\n", "                d_start = max(0, min(center[0] - ps // 2, d - ps))\n", "                h_start = max(0, min(center[1] - ps // 2, h - ps))\n", "                w_start = max(0, min(center[2] - ps // 2, w - ps))\n", "            \n", "            # Extract patch\n", "            img_patch = image[d_start:d_start+ps, h_start:h_start+ps, w_start:w_start+ps]\n", "            mask_patch = mask[d_start:d_start+ps, h_start:h_start+ps, w_start:w_start+ps]\n", "            \n", "        else:\n", "            ps = min(self.patch_size, min(h, w))\n", "            \n", "            # If center is not specified, choose randomly\n", "            if center is None:\n", "                h_start = np.random.randint(0, max(1, h - ps + 1))\n", "                w_start = np.random.randint(0, max(1, w - ps + 1))\n", "            else:\n", "                # Center patch on the given point\n", "                h_start = max(0, min(center[0] - ps // 2, h - ps))\n", "                w_start = max(0, min(center[1] - ps // 2, w - ps))\n", "            \n", "            # Extract patch - FIXED INCORRECT INDEXING\n", "            img_patch = image[h_start:h_start+ps, w_start:w_start+ps]\n", "            mask_patch = mask[h_start:h_start+ps, w_start:w_start+ps]  # FIXED\n", "        \n", "        return img_patch, mask_patch\n", "        \n", "    def __getitem__(self, idx):\n", "        \"\"\"Get item with balanced sampling for training\"\"\"\n", "        # For training with patch extraction\n", "        if self.is_train and self.patch_size and self.masks is not None:\n", "            # 50% chance to sample from images with spots\n", "            if np.random.rand() > 0.5 and len(self.spot_indices) > 0:\n", "                # Randomly select an image with spots\n", "                spot_idx = np.random.choice(len(self.spot_indices))\n", "                idx = self.spot_indices[spot_idx]\n", "                \n", "                # Randomly select a spot location\n", "                spot_locs = self.spot_locations[spot_idx]\n", "                spot_center = spot_locs[np.random.choice(len(spot_locs))]\n", "            # Otherwise use the regular index but extract random patch\n", "        \n", "        # Load image and mask\n", "        if isinstance(self.images[idx], str):\n", "            image = self._load_image(self.images[idx])\n", "        else:\n", "            image = self.images[idx]\n", "        \n", "        # ADD THIS: Clip the image to remove outliers (common for microscopy)\n", "        if isinstance(image, np.ndarray) and image.dtype in [np.uint16, np.int16, np.float32, np.float64]:\n", "            # Calculate percentiles for robust clipping\n", "            p_low, p_high = np.percentile(image, (0.1, 99.9))\n", "            image = np.clip(image, p_low, p_high)\n", "            \n", "            # Then normalize to [0,1]\n", "            if p_high > p_low:\n", "                image = (image - p_low) / (p_high - p_low)\n", "        \n", "        if self.masks is not None:\n", "            if isinstance(self.masks[idx], str):\n", "                mask = self._load_image(self.masks[idx])\n", "            else:\n", "                mask = self.masks[idx]\n", "                \n", "            # CRITICAL FIX: Store original instance mask for reference\n", "            original_instance_mask = mask.copy()\n", "            \n", "            # After creating binary mask\n", "            if mask.max() > 1:  # Only convert if not already binary\n", "                binary_mask = (mask > 0).astype(mask.dtype)\n", "                \n", "                # Add hole filling - critical for accurate boundary and flow calculations\n", "                try:\n", "                    from scipy import ndimage\n", "                    if len(binary_mask.shape) == 3:  # 3D case\n", "                        for z in range(binary_mask.shape[0]):\n", "                            binary_mask[z] = ndimage.binary_fill_holes(binary_mask[z]).astype(binary_mask.dtype)\n", "                    else:  # 2D case\n", "                        binary_mask = ndimage.binary_fill_holes(binary_mask).astype(binary_mask.dtype)\n", "                except ImportError:\n", "                    print(\"Warning: scipy.ndimage not available. Hole filling skipped.\")\n", "                \n", "                mask = binary_mask\n", "                self.binary_conversion_count += 1\n", "                \n", "                # # Debug info (print occasionally)\n", "                # if self.binary_conversion_count % 100 == 0:\n", "                #     print(f\"Binary conversion count: {self.binary_conversion_count}\")\n", "                #     print(f\"Max mask value seen so far: {self.max_mask_value_seen}\")\n", "        else:\n", "            mask = np.zeros_like(image)\n", "        \n", "        # Extract patch if needed\n", "        if self.is_train and self.patch_size:\n", "            # If we selected a spot-centered patch earlier\n", "            if 'spot_center' in locals():\n", "                image, mask = self._get_patch(image, mask, center=spot_center)\n", "            else:\n", "                image, mask = self._get_patch(image, mask)\n", "        \n", "        # Apply augmentations\n", "        if self.is_3d:\n", "            # Use the 3D transform from our augmenter\n", "            if self.transform:\n", "                # For MONAI transforms, we need to create a dictionary\n", "                data = {\n", "                    \"image\": image,\n", "                    \"mask\": mask\n", "                }\n", "                transformed = self.transform(data)\n", "                image = transformed[\"image\"]\n", "                mask = transformed[\"mask\"]\n", "        else:\n", "            # Use albumentations for 2D\n", "            if self.transform:\n", "                transformed = self.transform(image=image, mask=mask)\n", "                image = transformed['image']  # Now a tensor with albumentations.pytorch.ToTensorV2\n", "                mask = transformed['mask'] \n", "                \n", "                # Convert to float if not already\n", "                if isinstance(mask, np.ndarray):\n", "                    mask = torch.from_numpy(mask).float()\n", "                elif not mask.is_floating_point():\n", "                    mask = mask.float()\n", "        \n", "        # Add channel dimension if needed (should be handled by transforms, but just in case)\n", "        if not isinstance(image, torch.Tensor):\n", "            image = torch.from_numpy(image.astype(np.float32))\n", "            \n", "        if not isinstance(mask, torch.Tensor):\n", "            mask = torch.from_numpy(mask.astype(np.float32))\n", "            \n", "        if not self.is_3d and image.ndim == 2:\n", "            image = image.unsqueeze(0)\n", "        if not self.is_3d and mask.ndim == 2:\n", "            mask = mask.unsqueeze(0)\n", "        \n", "        # CRITICAL: Ensure mask is float for BCE loss, not long/int\n", "        # The model handles instance separation through heatmap + flow/distance\n", "        if self.masks is not None:\n", "            mask = mask.float()  # Always use float for binary masks\n", "        \n", "        return image, mask"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["#nstanceSpotDetector Class\n", "# Weighted BCE loss for class imbalance\n", "# Dice loss for overlap-based similarity\n", "# Flow loss for instance separation\n", "# Boundary loss for instance boundaries\n", "# Multi-scale loss for multi-resolution consistency"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["class SparseSpotLoss(nn.Module):\n", "    \"\"\"\n", "    Loss function designed for sparse spot annotations where not all spots are labeled.\n", "    Reduces penalty for potential false positives by introducing an ignore region.\n", "    \"\"\"\n", "    def __init__(self, \n", "                pos_weight=10.0, \n", "                ignore_threshold=0.3,\n", "                focal_gamma=2.0):\n", "        super().__init__()\n", "        self.pos_weight = pos_weight\n", "        self.ignore_threshold = ignore_threshold\n", "        self.focal_gamma = focal_gamma\n", "    \n", "    def forward(self, pred, target):\n", "        # Apply sigmoid if input is logits\n", "        if pred.min() < 0 or pred.max() > 1:\n", "            pred_sigmoid = torch.sigmoid(pred)\n", "        else:\n", "            pred_sigmoid = pred\n", "            pred = torch.log(pred_sigmoid / (1 - pred_sigmoid + 1e-7))  # Convert to logits for BCE\n", "        \n", "        # Basic BCE loss\n", "        bce_loss = F.binary_cross_entropy_with_logits(\n", "            pred, target, reduction='none')\n", "        \n", "        # Create ignore mask - where target=0 but prediction is low\n", "        # These are regions where we don't penalize potential false positives\n", "        ignore_mask = (target < 0.5) & (pred_sigmoid < self.ignore_threshold)\n", "        bce_loss = bce_loss * (~ignore_mask).float()\n", "        \n", "        # Add focal term for hard examples\n", "        if self.focal_gamma > 0:\n", "            pt = torch.exp(-bce_loss)\n", "            focal_term = (1 - pt) ** self.focal_gamma\n", "            bce_loss = bce_loss * focal_term\n", "        \n", "        # Balance positive and negative examples\n", "        pos_mask = target > 0.5\n", "        weighted_bce = bce_loss * (1 + (pos_mask.float() * (self.pos_weight - 1)))\n", "        \n", "        return weighted_bce.mean()\n", "\n", "class AdaptiveSpotInstanceLoss(nn.Module):\n", "    \"\"\"Loss function for spot instance segmentation with guaranteed float32 operations\"\"\"\n", "    \n", "    def __init__(self, \n", "         heatmap_weight=0.6, \n", "         boundary_weight=0.1, \n", "         flow_weight=0.1, \n", "         distance_weight=0.2,\n", "         pos_weight=None,\n", "         initial_pos_weight=None,\n", "         learn_weights=True,\n", "         dice_weight=1.0,\n", "         focal_gamma=1.5,\n", "         stability_eps=1e-6,\n", "         device=None):\n", "\n", "        super().__init__()\n", "        \n", "        self.learn_weights = learn_weights\n", "        \n", "        # Store device\n", "        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "        \n", "        # Store as Python floats\n", "        self.heatmap_weight_val = float(heatmap_weight)\n", "        self.boundary_weight_val = float(boundary_weight)\n", "        self.flow_weight_val = float(flow_weight)\n", "        self.distance_weight_val = float(distance_weight)\n", "        \n", "        # Get pos_weight\n", "        actual_pos_weight = pos_weight if pos_weight is not None else initial_pos_weight\n", "        if actual_pos_weight is None:\n", "            actual_pos_weight = 20.0\n", "        \n", "        # Store other weights as Python floats in a dict\n", "        self.weight_values = {\n", "            'pos_weight': float(actual_pos_weight),\n", "            'dice_weight': float(dice_weight),\n", "            'focal_gamma': float(focal_gamma)\n", "        }\n", "        \n", "        # CRITICAL: Register the learnable parameters correctly\n", "        if self.learn_weights:\n", "            # IMPORTANT: Use register_parameter instead of nn.Parameter directly\n", "            self.register_parameter('log_heatmap_weight', \n", "                nn.Parameter(torch.tensor(np.log(self.heatmap_weight_val), dtype=torch.float32)))\n", "            self.register_parameter('log_boundary_weight', \n", "                nn.Parameter(torch.tensor(np.log(self.boundary_weight_val), dtype=torch.float32)))\n", "            self.register_parameter('log_flow_weight', \n", "                nn.Parameter(torch.tensor(np.log(self.flow_weight_val), dtype=torch.float32)))\n", "            self.register_parameter('log_distance_weight', \n", "                nn.Parameter(torch.tensor(np.log(self.distance_weight_val), dtype=torch.float32)))\n", "        \n", "        self.stability_eps = float(stability_eps)\n", "        self.negative_loss_count = 0\n", "        self.total_calls = 0\n", "    \n", "    def get_current_weights(self):\n", "        \"\"\"Get current weight values with type safety\"\"\"\n", "        if self.learn_weights:\n", "            # Extract from tensors\n", "            heatmap = torch.exp(self.log_heatmap_weight)\n", "            boundary = torch.exp(self.log_boundary_weight)\n", "            flow = torch.exp(self.log_flow_weight)\n", "            distance = torch.exp(self.log_distance_weight)\n", "            \n", "            # Normalize to sum to 1.0 using PyTorch tensors\n", "            total = heatmap + boundary + flow + distance\n", "            \n", "            return {\n", "                'heatmap': heatmap / total,\n", "                'boundary': boundary / total,\n", "                'flow': flow / total,\n", "                'distance': distance / total,\n", "                'pos_weight': self.weight_values['pos_weight'],\n", "                'dice_weight': self.weight_values['dice_weight'],\n", "                'focal_gamma': self.weight_values['focal_gamma']\n", "            }\n", "        else:\n", "            # Use initial values\n", "            return {\n", "                'heatmap': self.heatmap_weight_val,\n", "                'boundary': self.boundary_weight_val,\n", "                'flow': self.flow_weight_val,\n", "                'distance': self.distance_weight_val,\n", "                'pos_weight': self.weight_values['pos_weight'],\n", "                'dice_weight': self.weight_values['dice_weight'],\n", "                'focal_gamma': self.weight_values['focal_gamma']\n", "            }\n", "    \n", "    def forward(self, pred, target):\n", "        \"\"\"Forward pass with numerical stability improvements\"\"\"\n", "        # Get current weight values as Python floats\n", "        weights = self.get_current_weights()\n", "        \n", "        self.total_calls += 1\n", "        if self.total_calls % 100 == 0:\n", "            print(f\"Learned weights: heatmap={weights['heatmap']:.3f}, \"\n", "                f\"boundary={weights['boundary']:.3f}, \"\n", "                f\"flow={weights['flow']:.3f}, \"\n", "                f\"distance={weights['distance']:.3f}\")\n", "        \n", "        # Ensure all tensors are float32\n", "        for key in pred:\n", "            if isinstance(pred[key], torch.Tensor):\n", "                pred[key] = pred[key].float()\n", "\n", "        if isinstance(target, dict):\n", "            target_mask = target['masks'].float()\n", "        else:\n", "            target_mask = target.float()\n", "        \n", "        # Compute losses with explicit float creation\n", "        losses = {}\n", "        \n", "        # 1. Heatmap loss\n", "        losses['heatmap'] = self.compute_heatmap_loss(pred['heatmap'], target_mask)\n", "        \n", "        # Handle empty target case\n", "        if target_mask.sum() < 1:\n", "            return losses['heatmap'], losses\n", "        \n", "        try:\n", "            # 2. Boundary loss\n", "            boundary_loss = self.compute_boundary_loss(pred['boundary'], target_mask)\n", "            if torch.isfinite(boundary_loss):\n", "                losses['boundary'] = boundary_loss\n", "            else:\n", "                losses['boundary'] = torch.tensor(0.0, dtype=torch.float32, device=self.device)\n", "                \n", "            # 3. Flow loss\n", "            flow_loss = self.compute_flow_loss(pred['flow'], target_mask)\n", "            if torch.isfinite(flow_loss):\n", "                losses['flow'] = flow_loss\n", "            else:\n", "                losses['flow'] = torch.tensor(0.0, dtype=torch.float32, device=self.device)\n", "                \n", "            # 4. Distance loss\n", "            distance_loss = self.compute_distance_loss(pred['distance'], target_mask)\n", "            if torch.isfinite(distance_loss):\n", "                losses['distance'] = distance_loss\n", "            else:\n", "                losses['distance'] = torch.tensor(0.0, dtype=torch.float32, device=self.device)\n", "                \n", "            # CRITICAL: Compute total loss using Python floats for weights\n", "            # This avoids implicit casting to double\n", "            total_loss = weights['heatmap'] * losses['heatmap'].float()\n", "            \n", "            if 'boundary' in losses:\n", "                total_loss = total_loss + weights['boundary'] * losses['boundary'].float()\n", "            if 'flow' in losses:\n", "                total_loss = total_loss + weights['flow'] * losses['flow'].float()\n", "            if 'distance' in losses:\n", "                total_loss = total_loss + weights['distance'] * losses['distance'].float()\n", "                \n", "            # Final safety checks\n", "            if not torch.isfinite(total_loss):\n", "                print(\"Warning: non-finite loss value, using heatmap loss only\")\n", "                total_loss = losses['heatmap'].float()\n", "                \n", "        except Exception as e:\n", "            print(f\"Loss computation error: {str(e)}, falling back to heatmap_loss only\")\n", "            total_loss = losses['heatmap'].float()\n", "            \n", "        return total_loss, losses\n", "    \n", "    def compute_heatmap_loss(self, pred, target):\n", "        \"\"\"Compute heatmap loss with sparse annotation awareness\"\"\"\n", "        # Handle list outputs if needed\n", "        if isinstance(pred, list):\n", "            pred = pred[0]  # Use first tensor if we have a list\n", "            \n", "        pred = pred.float()\n", "        target = target.float()\n", "        \n", "        # Get weights\n", "        weights = self.get_current_weights()\n", "        \n", "        # Create pos_weight tensor\n", "        pos_weight = torch.ones_like(target)\n", "        pos_weight[target > 0.5] = weights['pos_weight']\n", "        \n", "        # Apply BCE loss\n", "        bce_loss = F.binary_cross_entropy_with_logits(\n", "            pred,\n", "            target,\n", "            pos_weight=pos_weight\n", "        )\n", "        \n", "        # Create ignore mask - where target=0 but prediction is low\n", "        # This helps with sparse annotation\n", "        ignore_threshold = 0.3  # Add this as a parameter if needed\n", "        pred_sigmoid = torch.sigmoid(pred)\n", "        ignore_mask = (target < 0.5) & (pred_sigmoid < ignore_threshold)\n", "        \n", "        # Apply focal term if specified\n", "        if weights['focal_gamma'] > 0:\n", "            with torch.no_grad():\n", "                pt = torch.sigmoid(pred)\n", "                focal_weight = ((1 - pt) ** weights['focal_gamma'] * target + \n", "                            pt ** weights['focal_gamma'] * (1 - target))\n", "                # Don't penalize low confidence predictions where target is 0\n", "                focal_weight = focal_weight * (~ignore_mask).float()\n", "            \n", "            focal_loss = F.binary_cross_entropy_with_logits(\n", "                pred, \n", "                target,\n", "                pos_weight=pos_weight,\n", "                reduction='none'\n", "            ) * focal_weight\n", "            \n", "            return focal_loss.mean()\n", "        else:\n", "            # Apply ignore mask to BCE loss\n", "            bce_loss_per_pixel = F.binary_cross_entropy_with_logits(\n", "                pred, \n", "                target,\n", "                pos_weight=pos_weight,\n", "                reduction='none'\n", "            )\n", "            bce_loss_per_pixel = bce_loss_per_pixel * (~ignore_mask).float()\n", "            return bce_loss_per_pixel.mean()\n", "    \n", "    def compute_boundary_loss(self, pred, target):\n", "        \"\"\"Compute boundary loss with stability improvements\"\"\"\n", "        # Get weights using the correct method\n", "        weights = self.get_current_weights()\n", "        \n", "        # Get boundaries from target mask\n", "        target_boundaries = self._get_boundaries(target).float()\n", "        \n", "        # FIXED: Use BCE with logits for AMP safety\n", "        pos_weight = torch.ones_like(target_boundaries,dtype=torch.float32)\n", "        pos_weight[target_boundaries > 0.5] = weights['pos_weight']\n", "        \n", "        bce_loss = F.binary_cross_entropy_with_logits(\n", "            pred.float(),\n", "            target_boundaries,\n", "            pos_weight=pos_weight\n", "        )\n", "        \n", "        return bce_loss\n", "    \n", "    def _get_boundaries(self, masks):\n", "        \"\"\"Get boundaries from masks with robust detection\"\"\"\n", "        from scipy import ndimage\n", "        import numpy as np\n", "        \n", "        boundaries = []\n", "        for mask in masks:\n", "            if mask.sum() < 1:  # Handle empty mask case\n", "                boundaries.append(torch.zeros_like(mask, dtype=torch.float32))  # Ensure float32\n", "                continue\n", "                \n", "            # Convert to numpy for morphological operations\n", "            mask_np = mask.detach().cpu().numpy().astype(np.float32)  # Ensure float32\n", "            \n", "            # Use erosion to find boundaries \n", "            if len(mask_np.shape) == 3:  # 3D case\n", "                # Handle 3D masks\n", "                struct = ndimage.generate_binary_structure(3, 1)\n", "                eroded = ndimage.binary_erosion(mask_np > 0.5, struct)\n", "                boundary = ((mask_np > 0.5) & ~eroded).astype(np.float32)  # Ensure float32\n", "            else:\n", "                # Handle 2D masks\n", "                struct = ndimage.generate_binary_structure(2, 1)\n", "                eroded = ndimage.binary_erosion(mask_np > 0.5, struct)\n", "                boundary = ((mask_np > 0.5) & ~eroded).astype(np.float32)  # Ensure float32\n", "            \n", "            # Convert back to tensor\n", "            boundary_tensor = torch.from_numpy(boundary).to(mask.device, dtype=torch.float32)  # Ensure float32\n", "            boundaries.append(boundary_tensor)\n", "            \n", "        return torch.stack(boundaries).float()  # Ensure final tensor is float32\n", "    \n", "    def compute_flow_loss(self, pred, target, normalize_vectors=True):\n", "        \"\"\"Compute flow loss with AMP compatibility\"\"\"\n", "        # Handle list outputs\n", "        if isinstance(pred, list):\n", "            pred = pred[0]\n", "            \n", "        # Generate flow vectors\n", "        flow_vectors = self._get_flow_vectors(target).float()  # Ensure float32\n", "        \n", "        # Create mask of valid flow vectors (only where target > 0)\n", "        valid_mask = (target > 0.5).float()  # Ensure float32\n", "        \n", "        # Convert all tensors to float32\n", "        pred = pred.float()\n", "        \n", "        \n", "        \n", "        # Match dimensions if needed\n", "        if flow_vectors.shape != pred.shape:\n", "            if flow_vectors.dim() > pred.dim():\n", "                flow_vectors = flow_vectors.view(pred.shape)\n", "                \n", "                \n", "            elif pred.shape[1] != flow_vectors.shape[1]:\n", "                if pred.shape[1] > flow_vectors.shape[1]:\n", "                    # Use only needed channels\n", "                    pred = pred[:, :flow_vectors.shape[1]]\n", "                else:\n", "                    #print(f\"ERROR: Model outputs {pred.shape[1]} channels, need {flow_vectors.shape[1]}\")\n", "                    return torch.tensor(0.0, device=target.device, dtype=torch.float32)  # Ensure float32\n", "        \n", "        # Normalize predicted flow vectors\n", "        if normalize_vectors:\n", "            pred_magnitude = torch.sqrt(torch.sum(pred**2, dim=1, keepdim=True) + self.stability_eps)\n", "            normalized_pred = pred / pred_magnitude\n", "            pred_vectors = torch.where(pred_magnitude > self.stability_eps, normalized_pred, pred)\n", "        else:\n", "            pred_vectors = pred\n", "        \n", "        # Calculate MSE loss only on valid flow vectors\n", "        valid_pixels = valid_mask.sum()\n", "        if valid_pixels < 1:\n", "            return torch.tensor(0.0, device=target.device, dtype=torch.float32)  # Ensure float32\n", "            \n", "        # Apply mask to squared error\n", "        valid_mask_expanded = valid_mask.unsqueeze(1)\n", "        squared_error = ((pred_vectors - flow_vectors)**2) * valid_mask_expanded\n", "        flow_loss = torch.sum(squared_error) / (valid_pixels + self.stability_eps)\n", "        \n", "        return flow_loss.float()  # Ensure float32\n", "    \n", "    def _get_flow_vectors(self, masks):\n", "        \"\"\"Universal flow vector calculation with explicit float32 conversion\"\"\"\n", "        from scipy import ndimage\n", "        import numpy as np\n", "        import torch\n", "\n", "        flow = []\n", "        for mask in masks:\n", "            # Handle empty mask\n", "            if mask.sum() < 1:\n", "                zeros = torch.zeros((2,) + mask.shape[-2:], device=mask.device, dtype=torch.float32)\n", "                flow.append(zeros)\n", "                continue\n", "\n", "            # Convert to numpy\n", "            mask_np = mask.detach().cpu().numpy().astype(np.float32)  # Ensure float32\n", "\n", "            # Check dimensionality\n", "            original_ndim = mask_np.ndim\n", "            \n", "\n", "            # For 2D data with extra dimensions, squeeze or select first channel\n", "            if original_ndim > 2:\n", "                if mask_np.shape[0] == 1:\n", "                    mask_np = mask_np[0]  # Take first and only channel\n", "                else:\n", "                    mask_np = mask_np[0]\n", "\n", "            # Create binary mask\n", "            mask_bin = (mask_np > 0.5).astype(np.float32)  # Ensure float32\n", "\n", "            # Process 2D image data\n", "            \n", "            flow_y = np.zeros_like(mask_bin, dtype=np.float32)  # Ensure float32\n", "            flow_x = np.zeros_like(mask_bin, dtype=np.float32)  # Ensure float32\n", "\n", "            # Get foreground pixel coordinates\n", "            y_indices, x_indices = np.nonzero(mask_bin)\n", "\n", "            if len(y_indices) > 0:\n", "                # Calculate centroid\n", "                center_y = np.mean(y_indices).astype(np.float32)  # Ensure float32\n", "                center_x = np.mean(x_indices).astype(np.float32)  # Ensure float32\n", "\n", "                # Calculate flow vectors\n", "                for i in range(len(y_indices)):\n", "                    y, x = y_indices[i], x_indices[i]\n", "                    dy = center_y - y\n", "                    dx = center_x - x\n", "\n", "                    # Normalize\n", "                    dist = np.sqrt(dy * dy + dx * dx).astype(np.float32)  # Ensure float32\n", "                    if dist > 1e-6:\n", "                        dy = dy / dist\n", "                        dx = dx / dist\n", "                    else:\n", "                        dy, dx = 0.0, 0.0\n", "\n", "                    # Assign to flow maps\n", "                    flow_y[y, x] = dy\n", "                    flow_x[y, x] = dx\n", "\n", "                # Create 2-channel flow tensor\n", "                flow_tensor = torch.stack([\n", "                    torch.from_numpy(flow_y).to(dtype=torch.float32, device=mask.device),\n", "                    torch.from_numpy(flow_x).to(dtype=torch.float32, device=mask.device)\n", "                ])\n", "            else:\n", "                # Empty mask case\n", "                flow_tensor = torch.zeros((2,) + mask_bin.shape, device=mask.device, dtype=torch.float32)\n", "\n", "            flow.append(flow_tensor)\n", "\n", "        # Stack and return\n", "        stacked = torch.stack(flow).float()  # Ensure final tensor is float32\n", "        #print(f\"Final flow tensor shape: {stacked.shape}\")\n", "        #print(f\"Final flow tensor dtype: {stacked.dtype}\")  # Debug line\n", "        return stacked\n", "    \n", "    def compute_distance_loss(self, pred, target):\n", "        \"\"\"Compute distance transform loss with stability\"\"\"\n", "        # Generate distance transforms\n", "        target_dt = self._get_distance_transform(target).float()  # Ensure float32\n", "        \n", "        # Apply sigmoid to predictions to get [0,1] range\n", "        pred_sigmoid = torch.sigmoid(pred.float())  # Ensure float32\n", "        \n", "        # Use MSE loss\n", "        mse_loss = torch.nn.functional.mse_loss(pred_sigmoid, target_dt)\n", "        return mse_loss\n", "    \n", "    def _get_distance_transform(self, masks, normalize=True):\n", "        \"\"\"Get distance transform with stability improvements\"\"\"\n", "        from scipy import ndimage\n", "        import numpy as np\n", "\n", "        distances = []\n", "        for mask in masks:\n", "            if mask.sum() < 1:  # Handle empty mask\n", "                distances.append(torch.zeros_like(mask, dtype=torch.float32))  # Ensure float32\n", "                continue\n", "\n", "            # Convert to numpy for processing\n", "            mask_np = mask.detach().cpu().numpy().astype(np.float32)  # Ensure float32\n", "\n", "            # Compute distance transform\n", "            dt = ndimage.distance_transform_edt(mask_np > 0.5).astype(np.float32)  # Ensure float32\n", "            if normalize:\n", "                max_dist = dt.max()\n", "                if max_dist > 0:\n", "                    dt = dt / max_dist\n", "\n", "            # Convert back to tensor\n", "            dt_tensor = torch.from_numpy(dt).to(mask.device, dtype=torch.float32)  # Ensure float32\n", "            distances.append(dt_tensor)\n", "\n", "        return torch.stack(distances).float()  # Ensure final tensor is float32"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def _create_tensor(self, value, dtype=torch.float32):\n", "    \"\"\"Helper to create tensors with consistent dtype\"\"\"\n", "    return torch.tensor(value, device=self.device, dtype=dtype)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["class ConvBlock(nn.Module):\n", "    \"\"\"Convolutional block with batch norm and activation\"\"\"\n", "\n", "    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, \n", "                 is_3d=False, use_batchnorm=True):\n", "        super().__init__()\n", "        \n", "        Conv = nn.Conv3d if is_3d else nn.Conv2d\n", "        BatchNorm = nn.BatchNorm3d if is_3d else nn.BatchNorm2d\n", "        \n", "        layers = [\n", "            Conv(in_channels, out_channels, kernel_size, stride, padding, bias=not use_batchnorm)\n", "        ]\n", "        \n", "        if use_batchnorm:\n", "            layers.append(BatchNorm(out_channels))\n", "        \n", "        layers.append(nn.ReLU(inplace=True))\n", "        \n", "        self.conv_block = nn.Sequential(*layers)\n", "        \n", "    def forward(self, x):\n", "        return self.conv_block(x)\n", "\n", "\n", "class ResidualBlock(nn.Module):\n", "    \"\"\"Residual block for better gradient flow\"\"\"\n", "    \n", "    def __init__(self, channels, is_3d=False):\n", "        super().__init__()\n", "        \n", "        self.conv1 = ConvBlock(channels, channels, is_3d=is_3d)\n", "        self.conv2 = ConvBlock(channels, channels, is_3d=is_3d)\n", "        \n", "    def forward(self, x):\n", "        residual = x\n", "        x = self.conv1(x)\n", "        x = self.conv2(x) + residual\n", "        return x\n", "\n", "\n", "class DownBlock(nn.Module):\n", "    \"\"\"Downsampling block for encoder\"\"\"\n", "    \n", "    def __init__(self, in_channels, out_channels, is_3d=False):\n", "        super().__init__()\n", "        \n", "        Pool = nn.MaxPool3d if is_3d else nn.MaxPool2d\n", "        \n", "        self.pool = Pool(kernel_size=2, stride=2)\n", "        self.conv = ConvBlock(in_channels, out_channels, is_3d=is_3d)\n", "        self.residual = ResidualBlock(out_channels, is_3d=is_3d)\n", "        \n", "    def forward(self, x):\n", "        x = self.pool(x)\n", "        x = self.conv(x)\n", "        x = self.residual(x)\n", "        return x\n", "\n", "\n", "class UpBlock(nn.Module):\n", "    \"\"\"Upsampling block for decoder\"\"\"\n", "    \n", "    def __init__(self, in_channels, out_channels, is_3d=False):\n", "        super().__init__()\n", "        \n", "        ConvTranspose = nn.ConvTranspose3d if is_3d else nn.ConvTranspose2d\n", "        \n", "        self.up = ConvTranspose(\n", "            in_channels, out_channels, kernel_size=2, stride=2\n", "        )\n", "        \n", "        self.conv = ConvBlock(out_channels * 2, out_channels, is_3d=is_3d)\n", "        self.residual = ResidualBlock(out_channels, is_3d=is_3d)\n", "        \n", "    def forward(self, x, skip):\n", "        x = self.up(x)\n", "        \n", "        # Handle case where dimensions don't exactly match\n", "        diffY = skip.size()[2] - x.size()[2]\n", "        diffX = skip.size()[3] - x.size()[3]\n", "        \n", "        x = F.pad(x, [diffX // 2, diffX - diffX // 2,\n", "                       diffY // 2, diffY - diffY // 2])\n", "        \n", "        # Concatenate with skip connection\n", "        x = torch.cat([x, skip], dim=1)\n", "        \n", "        x = self.conv(x)\n", "        x = self.residual(x)\n", "        \n", "        return x\n", "\n", "\n", "class SpotInstanceModel(nn.Module):\n", "    \"\"\"Model for spot instance segmentation with flow prediction\"\"\"\n", "    \n", "    def __init__(self, in_channels=1, base_filters=32, is_3d=False):\n", "        super().__init__()\n", "        \n", "        self.is_3d = is_3d\n", "        Conv = nn.Conv3d if is_3d else nn.Conv2d\n", "        ConvTranspose = nn.ConvTranspose3d if is_3d else nn.ConvTranspose2d\n", "        \n", "        # Number of flow channels depends on dimensionality\n", "        flow_channels = 3 if is_3d else 2\n", "        \n", "        # Initial convolution\n", "        self.input_conv = ConvBlock(in_channels, base_filters, is_3d=is_3d)\n", "        self.input_res = ResidualBlock(base_filters, is_3d=is_3d)\n", "        \n", "        # Encoder\n", "        self.down1 = DownBlock(base_filters, base_filters*2, is_3d=is_3d)\n", "        self.down2 = DownBlock(base_filters*2, base_filters*4, is_3d=is_3d)\n", "        self.down3 = DownBlock(base_filters*4, base_filters*8, is_3d=is_3d)\n", "        \n", "        # Center\n", "        self.center = ResidualBlock(base_filters*8, is_3d=is_3d)\n", "        \n", "        # Decoder with skip connections\n", "        self.up3 = UpBlock(base_filters*8, base_filters*4, is_3d=is_3d)\n", "        self.up2 = UpBlock(base_filters*4, base_filters*2, is_3d=is_3d)\n", "        self.up1 = UpBlock(base_filters*2, base_filters, is_3d=is_3d)\n", "        \n", "        # Multi-level prediction heads for heatmaps\n", "        self.heatmap_head_level3 = Conv(base_filters*4, 1, kernel_size=1)\n", "        self.heatmap_head_level2 = Conv(base_filters*2, 1, kernel_size=1)\n", "        self.heatmap_head_level1 = Conv(base_filters, 1, kernel_size=1)\n", "        \n", "        # Flow prediction head\n", "        self.flow_head = Conv(base_filters, flow_channels, kernel_size=3, padding=1)\n", "        \n", "        # Instance embedding head (helps separate nearby spots)\n", "        self.embedding_head = Conv(base_filters, 8, kernel_size=3, padding=1)\n", "        \n", "        # Initialize weights with special care for output layers\n", "        self._initialize_weights()\n", "    \n", "    def _initialize_weights(self):\n", "        \"\"\"Initialize weights with special care for output layers\"\"\"\n", "        for m in self.modules():\n", "            if isinstance(m, (nn.Conv2d, nn.Conv3d)):\n", "                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None:\n", "                    nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, (nn.<PERSON>chNorm2d, nn.BatchNorm3d)):\n", "                nn.init.constant_(m.weight, 1)\n", "                nn.init.constant_(m.bias, 0)\n", "        \n", "        # Initialize output heads with strong negative bias for extreme imbalance\n", "        for m in [self.heatmap_head_level1, self.heatmap_head_level2, self.heatmap_head_level3]:\n", "            nn.init.normal_(m.weight, mean=0, std=0.01)\n", "            nn.init.constant_(m.bias, -4.0)  # Strong negative bias to predict mostly background\n", "    \n", "    def forward(self, x):\n", "        # Initial feature extraction\n", "        x0 = self.input_conv(x)\n", "        x0 = self.input_res(x0)\n", "        \n", "        # Encoder path\n", "        x1 = self.down1(x0)\n", "        x2 = self.down2(x1)\n", "        x3 = self.down3(x2)\n", "        \n", "        # Center\n", "        x3 = self.center(x3)\n", "        \n", "        # Decoder path with skip connections\n", "        x2_up = self.up3(x3, x2)\n", "        x1_up = self.up2(x2_up, x1)\n", "        x0_up = self.up1(x1_up, x0)\n", "        \n", "        # Multi-level heatmap predictions\n", "        heatmap_level3 = self.heatmap_head_level3(x2_up)\n", "        heatmap_level2 = self.heatmap_head_level2(x1_up)\n", "        heatmap_level1 = self.heatmap_head_level1(x0_up)\n", "        \n", "        # Upsample lower resolution heatmaps to match input size\n", "        target_size = x.shape[2:]\n", "        \n", "        # Only perform upsampling if sizes don't match\n", "        if heatmap_level3.shape[2:] != target_size:\n", "            heatmap_level3 = F.interpolate(\n", "                heatmap_level3, size=target_size, \n", "                mode='trilinear' if self.is_3d else 'bilinear', \n", "                align_corners=False\n", "            )\n", "        \n", "        if heatmap_level2.shape[2:] != target_size:\n", "            heatmap_level2 = F.interpolate(\n", "                heatmap_level2, size=target_size, \n", "                mode='trilinear' if self.is_3d else 'bilinear', \n", "                align_corners=False\n", "            )\n", "        \n", "        if heatmap_level1.shape[2:] != target_size:\n", "            heatmap_level1 = F.interpolate(\n", "                heatmap_level1, size=target_size, \n", "                mode='trilinear' if self.is_3d else 'bilinear', \n", "                align_corners=False\n", "            )\n", "        \n", "        # Flow field prediction\n", "        flow = self.flow_head(x0_up)\n", "        \n", "        # Instance embedding\n", "        embedding = self.embedding_head(x0_up)\n", "        \n", "        return {\n", "            'heatmaps': heatmap_level1,  # Main output\n", "            'multi_heatmaps': [heatmap_level1, heatmap_level2, heatmap_level3],  # Multi-scale outputs\n", "            'flow': flow,  # Flow field\n", "            'embedding': embedding,  # Instance embedding\n", "        }"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def train_spot_instance_model(model, criterion, train_loader, val_loader, device, \n", "                             num_epochs=100, log_dir='spot_logs', checkpoint_dir='spot_checkpoints',\n", "                             optimizer=None, scheduler=None, accumulation_steps=1):\n", "    \"\"\"\n", "    Train spot instance segmentation model with adaptive parameters\n", "    \n", "    Args:\n", "        model: The model with learnable instance parameters\n", "        criterion: Loss function with learnable weights\n", "        train_loader: Training data loader\n", "        val_loader: Validation data loader\n", "        device: Device to train on\n", "        num_epochs: Number of epochs to train\n", "        log_dir: Directory for tensorboard logs\n", "        checkpoint_dir: Directory for model checkpoints\n", "        optimizer: Optional pre-configured optimizer (if None, will create one)\n", "        scheduler: Optional learning rate scheduler\n", "        accumulation_steps: Number of batches to accumulate gradients over\n", "        \n", "    Returns:\n", "        Trained model, criterion, and training history\n", "    \"\"\"\n", "    # ADD THIS: Monkey-patch PyTorch's autograd backward function to prevent dtype issues\n", "    original_backward = torch.autograd.backward\n", "    \n", "    def safe_backward(tensors, grad_tensors=None, retain_graph=None, create_graph=False, inputs=None):\n", "        \"\"\"Force tensors to float32 before backward to prevent dtype mismatches\"\"\"\n", "        if isinstance(tensors, torch.Tensor) and tensors.dtype == torch.float64:\n", "            print(\"WARNING: Converting double tensor to float32 in backward\")\n", "            tensors = tensors.float()\n", "            \n", "        if grad_tensors is not None:\n", "            if isinstance(grad_tensors, torch.Tensor) and grad_tensors.dtype == torch.float64:\n", "                grad_tensors = grad_tensors.float()\n", "            elif isinstance(grad_tensors, (list, tuple)):\n", "                grad_tensors = [(g.float() if isinstance(g, torch.Tensor) and g.dtype == torch.float64 else g) \n", "                              for g in grad_tensors]\n", "        \n", "        return original_backward(\n", "            tensors, \n", "            grad_tensors=grad_tensors,\n", "            retain_graph=retain_graph,\n", "            create_graph=create_graph,\n", "            inputs=inputs\n", "        )\n", "        \n", "    # Replace PyT<PERSON><PERSON>'s backward with our safe version\n", "    torch.autograd.backward = safe_backward\n", "    \n", "    def debug_tensor_types(name, tensor):\n", "        \"\"\"Debug tensor types and detect any double precision values\"\"\"\n", "        if isinstance(tensor, torch.Tensor):\n", "            #print(f\"TENSOR CHECK - {name}: dtype={tensor.dtype}, shape={tensor.shape}\")\n", "            if tensor.dtype == torch.float64:\n", "                #print(f\"WARNING: Double precision detected in {name}\")\n", "                # Force to float32\n", "                return tensor.float()\n", "        return tensor\n", "\n", "    # Early in the function, add:\n", "    torch.set_default_dtype(torch.float32)\n", "    \n", "     # Create directories\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    \n", "    # Initialize tensorboard logger\n", "    from torch.utils.tensorboard import SummaryWriter\n", "    writer = SummaryWriter(log_dir=log_dir)\n", "    \n", "    # Initialize model and criterion on device - FORCE FLOAT32\n", "    model = model.float().to(device)  # MODIFIED: Explicitly call .float() first\n", "    criterion = criterion.float().to(device)  # MODIFIED: Explicitly call .float() first\n", "    \n", "    # Initialize optimizer if not provided\n", "    if optimizer is None:\n", "        optimizer = torch.optim.AdamW([\n", "            {'params': model.parameters(), 'lr': 1e-4},\n", "            {'params': criterion.parameters(), 'lr': 1e-4}  # Lower LR for loss parameters\n", "        ], weight_decay=1e-5)\n", "    \n", "    # Initialize learning rate scheduler if not provided\n", "    if scheduler is None:\n", "        scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "            optimizer, \n", "            max_lr=[5e-4, 5e-5],  # Different peak LR for model and criterion\n", "            total_steps=num_epochs * len(train_loader) // accumulation_steps,\n", "            pct_start=0.1,  # Warm up for 10% of training\n", "            div_factor=25.0,\n", "            final_div_factor=1e4,\n", "            anneal_strategy='cos'\n", "        )\n", "    \n", "    # Initialize early stopping\n", "    patience = 25\n", "    best_val_loss = float('inf')\n", "    best_val_f1 = 0.0\n", "    no_improve_epochs = 0\n", "    best_epoch = 0\n", "    \n", "    # Enable mixed precision training - VERSION COMPATIBLE\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    from torch.cuda.amp import GradScaler\n", "    scaler = GradScaler()\n", "    \n", "    # For proper mixed precision with device type\n", "    from torch.amp import autocast\n", "    \n", "    # Training history\n", "    history = {\n", "        'train_loss': [], 'val_loss': [],\n", "        'train_f1': [], 'val_f1': [],\n", "        'train_dice': [], 'val_dice': [],\n", "        'lr': [], 'loss_weights': [],\n", "        'instance_params': []\n", "    }\n", "    \n", "    # Track speed metrics\n", "    import time\n", "    from collections import deque\n", "    speed_history = deque(maxlen=10)\n", "    \n", "    # Print header with explanation of metrics\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"TRAINING PROGRESS\")\n", "    print(\"=\"*80)\n", "    print(\"Metrics explanation:\")\n", "    print(\"- Loss: Binary Cross Entropy + Dice Loss (lower is better)\")\n", "    print(\"- F1: F1 Score for spot detection (higher is better)\")\n", "    print(\"- Dice: Dice coefficient for segmentation overlap (higher is better)\")\n", "    print(\"- mIoU: Mean Intersection over Union (higher is better)\")\n", "    print(\"- LR: Current learning rate\")\n", "    print(\"- Speed: Images processed per second\")\n", "    print(\"=\"*80)\n", "    print(f\"Training for {num_epochs} epochs with gradient accumulation over {accumulation_steps} steps\")\n", "    print(\"=\"*80 + \"\\n\")\n", "    \n", "    # Main training loop with a better progress bar\n", "    from tqdm.notebook import tqdm\n", "    progress_bar = tqdm(total=num_epochs, desc=\"Training\", position=0, leave=True)\n", "    \n", "    for epoch in range(num_epochs):\n", "        # =====================\n", "        # TRAINING PHASE\n", "        # =====================\n", "        model.train()\n", "        criterion.train()\n", "        train_start = time.time()\n", "        \n", "        # Reset metrics\n", "        train_loss = 0.0\n", "        train_f1 = 0.0\n", "        train_dice = 0.0\n", "        batch_count = 0\n", "        train_miou = 0.0\n", "        \n", "        # Create a separate progress bar for batches within this epoch\n", "        batch_progress = tqdm(\n", "            total=len(train_loader), \n", "            desc=f\"Epoch {epoch+1}/{num_epochs} [Train]\",\n", "            position=1, \n", "            leave=False\n", "        )\n", "        \n", "        # Loop through batches with timing\n", "        optimizer.zero_grad()  # Zero gradients at start of epoch\n", "        \n", "        for i, (images, targets) in enumerate(train_loader):\n", "            batch_start = time.time()\n", "            \n", "            # MODIFY: Explicitly force float32\n", "            images = images.to(device, non_blocking=True).float()  # Added .float()\n", "            if isinstance(targets, torch.Tensor):\n", "                targets = targets.to(device, non_blocking=True).float()  # Added .float()\n", "            else:\n", "                targets = {k: v.to(device, non_blocking=True).float() for k, v in targets.items()}  # Added .float()\n", "            \n", "            # Forward pass with mixed precision - MODIFY THE CONTEXT MANAGER\n", "            with autocast(device_type=device.type, dtype=torch.float32):  # Force float32 in autocast\n", "                outputs = model(images)\n", "                \n", "                # Prepare target dictionary\n", "                if isinstance(targets, torch.Tensor):\n", "                    target_dict = {'masks': targets}\n", "                else:\n", "                    target_dict = targets\n", "                \n", "                # Calculate loss\n", "                loss, loss_components = criterion(outputs, target_dict)\n", "                # Scale loss for gradient accumulation\n", "                loss = loss / accumulation_steps\n", "            \n", "            # MODIFY: Force float32 for loss\n", "            loss = loss.float()\n", "            \n", "            # Check model for double parameters\n", "            for name, param in model.named_parameters():\n", "                if param.dtype == torch.float64:\n", "                    #print(f\"Double parameter found: {name}, converting to float32\")\n", "                    param.data = param.data.float()\n", "            \n", "            # Debug critical tensors in backward pass\n", "            loss = debug_tensor_types(\"loss\", loss)\n", "            \n", "            # For each key in outputs\n", "            for key in outputs:\n", "                outputs[key] = debug_tensor_types(f\"outputs[{key}]\", outputs[key])\n", "            \n", "            # Check optimizer state\n", "            for group in optimizer.param_groups:\n", "                for p in group['params']:\n", "                    if p.grad is not None and p.grad.dtype == torch.float64:\n", "                        #print(f\"Double precision gradient detected: {p.shape}\")\n", "                        p.grad = p.grad.float()\n", "            \n", "            try:\n", "                # Try with scaled loss first (uses our safe_backward internally now)\n", "                scaler.scale(loss).backward()\n", "            except Exception as e:\n", "                print(f\"Error in backward: {e}\")\n", "                \n", "                # If that fails, try a different approach\n", "                try:\n", "                    # Start fresh with zero_grad\n", "                    optimizer.zero_grad()\n", "                    \n", "                    # Create a fresh computation path with explicitly detached float32 tensors\n", "                    detached_outputs = {}\n", "                    for key, val in outputs.items():\n", "                        if isinstance(val, torch.Tensor):\n", "                            detached_outputs[key] = val.detach().float().requires_grad_(True)\n", "                        else:\n", "                            detached_outputs[key] = val\n", "                    \n", "                    # Recalculate loss with detached tensors\n", "                    with torch.enable_grad():\n", "                        fresh_loss, _ = criterion(detached_outputs, target_dict)\n", "                        fresh_loss = fresh_loss.float() / accumulation_steps\n", "                        fresh_loss.backward()\n", "                \n", "                except Exception as nested_e:\n", "                    #print(f\"Both backward attempts failed: {nested_e}\")\n", "                    # Just continue without updating this batch\n", "                    continue\n", "            \n", "            # Only update weights after accumulation_steps\n", "            if (i + 1) % accumulation_steps == 0 or (i + 1 == len(train_loader)):\n", "                # Unscale for gradient clipping\n", "                scaler.unscale_(optimizer)\n", "                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "                \n", "                # Update weights\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "                optimizer.zero_grad()\n", "                \n", "                # Update LR scheduler AFTER optimizer step\n", "                scheduler.step()\n", "            \n", "            # Calculate metrics on CPU to save GPU memory\n", "            with torch.no_grad():\n", "                # Get main heatmap for metrics\n", "                heatmap = outputs['heatmap']\n", "                if isinstance(targets, dict) and 'masks' in targets:\n", "                    mask = targets['masks']\n", "                else:\n", "                    mask = targets\n", "                \n", "                # Calculate F1 score\n", "                batch_f1 = calculate_f1_score(heatmap, mask)\n", "                \n", "                # Calculate Dice coefficient\n", "                batch_dice = calculate_dice_coefficient(heatmap, mask)\n", "                \n", "                # Calculate mean IoU\n", "                batch_miou = calculate_mean_iou(heatmap, mask)\n", "            \n", "            # Update statistics (use original loss value * accumulation_steps)\n", "            train_loss += loss.item() * accumulation_steps\n", "            train_f1 += batch_f1\n", "            train_dice += batch_dice\n", "            train_miou += batch_miou\n", "            batch_count += 1\n", "            \n", "            # Calculate speed\n", "            batch_duration = time.time() - batch_start\n", "            imgs_per_sec = images.size(0) / batch_duration\n", "            speed_history.append(imgs_per_sec)\n", "            avg_speed = sum(speed_history) / len(speed_history)\n", "            \n", "            # Update batch progress bar\n", "            batch_progress.set_postfix({\n", "                'loss': f\"{loss.item() * accumulation_steps:.4f}\",\n", "                'f1': f\"{batch_f1:.4f}\",\n", "                'dice': f\"{batch_dice:.4f}\",\n", "                'img/s': f\"{avg_speed:.1f}\"\n", "            })\n", "            batch_progress.update(1)\n", "        \n", "        # Close batch progress bar\n", "        batch_progress.close()\n", "        \n", "        # Calculate average training metrics\n", "        avg_train_loss = train_loss / batch_count\n", "        avg_train_f1 = train_f1 / batch_count\n", "        avg_train_dice = train_dice / batch_count\n", "        avg_train_miou = train_miou / batch_count\n", "        train_duration = time.time() - train_start\n", "        \n", "        # =====================\n", "        # VALIDATION PHASE\n", "        # =====================\n", "        \n", "        model.eval()\n", "        criterion.eval()  # Still track gradients for loss parameters in eval mode\n", "        val_start = time.time()\n", "        \n", "        # Before validation starts:\n", "        if epoch == 0 or (epoch + 1) % 10 == 0:\n", "            # Find optimal threshold for evaluation every 10 epochs\n", "            print(\"Finding optimal threshold for sparse annotations...\")\n", "            optimal_threshold = find_optimal_threshold(\n", "                model, val_loader, device, \n", "                metric_fn=calculate_f1_score,\n", "                threshold_range=np.arange(0.1, 0.9, 0.05)\n", "            )\n", "        else:\n", "            # Use last known optimal threshold\n", "            optimal_threshold = getattr(model, 'optimal_threshold', 0.5)\n", "        # Store optimal threshold in model for future use\n", "        model.optimal_threshold = optimal_threshold\n", "        \n", "        # Reset validation metrics\n", "        val_loss = 0.0\n", "        val_f1 = 0.0\n", "        val_dice = 0.0\n", "        val_miou = 0.0\n", "        val_batch_count = 0\n", "        \n", "        # Create a separate progress bar for validation\n", "        val_progress = tqdm(\n", "            total=len(val_loader), \n", "            desc=f\"Epoch {epoch+1}/{num_epochs} [Valid]\",\n", "            position=1, \n", "            leave=False\n", "        )\n", "        \n", "        with torch.no_grad():\n", "            for images, targets in val_loader:\n", "                # Move data to device\n", "                images = images.to(device, non_blocking=True).float()  # Ensure float32\n", "                if isinstance(targets, torch.Tensor):\n", "                    targets = targets.to(device, non_blocking=True).float()  # Add .float()\n", "                else:\n", "                    targets = {k: v.to(device, non_blocking=True).float() for k, v in targets.items()}  # Add .float()\n", "                \n", "                # Forward pass with mixed precision\n", "                with autocast(device_type=device.type, dtype=torch.float32):  # Use autocast for validation\n", "                    outputs = model(images)\n", "                \n", "                # Prepare target dictionary\n", "                if isinstance(targets, torch.Tensor):\n", "                    target_dict = {'masks': targets}\n", "                else:\n", "                    target_dict = targets\n", "                \n", "                # Calculate loss\n", "                loss, _ = criterion(outputs, target_dict)\n", "                \n", "                # Calculate metrics\n", "                heatmap = outputs['heatmap']\n", "                if isinstance(targets, dict) and 'masks' in targets:\n", "                    mask = targets['masks']\n", "                else:\n", "                    mask = targets\n", "                \n", "                # Calculate metrics with optimal threshold\n", "                batch_f1 = calculate_f1_score(heatmap, mask, threshold=optimal_threshold)\n", "                batch_dice = calculate_dice_coefficient(heatmap, mask, threshold=optimal_threshold)\n", "                batch_miou = calculate_mean_iou(heatmap, mask, threshold=optimal_threshold)\n", "                \n", "                # Update statistics\n", "                val_loss += loss.item()\n", "                val_f1 += batch_f1\n", "                val_dice += batch_dice\n", "                val_miou += batch_miou\n", "                val_batch_count += 1\n", "                \n", "                # Update validation progress bar\n", "                val_progress.set_postfix({\n", "                    'loss': f\"{loss.item():.4f}\", \n", "                    'f1': f\"{batch_f1:.4f}\",\n", "                    'dice': f\"{batch_dice:.4f}\"\n", "                })\n", "                val_progress.update(1)\n", "        \n", "        # Close validation progress bar\n", "        val_progress.close()\n", "        \n", "        # Calculate average validation metrics\n", "        avg_val_loss = val_loss / val_batch_count\n", "        avg_val_f1 = val_f1 / val_batch_count\n", "        avg_val_dice = val_dice / val_batch_count\n", "        avg_val_miou = val_miou / val_batch_count\n", "        val_duration = time.time() - val_start\n", "        \n", "        # =====================\n", "        # DEBUGGING: Log learned weights and gradients\n", "        # =====================\n", "        if hasattr(criterion, 'get_current_weights'):\n", "            weights = criterion.get_current_weights()\n", "            print(f\"Epoch {epoch+1}: Learned weights - heatmap={weights['heatmap']:.4f}, \"\n", "                  f\"boundary={weights['boundary']:.4f}, flow={weights['flow']:.4f}, \"\n", "                  f\"distance={weights['distance']:.4f}\")\n", "        \n", "        for name, param in criterion.named_parameters():\n", "            if param.requires_grad:\n", "                print(f\"{name}: grad={param.grad}\")\n", "        \n", "        \n", "        # Update history\n", "        history['train_loss'].append(avg_train_loss)\n", "        history['val_loss'].append(avg_val_loss)\n", "        history['train_f1'].append(avg_train_f1)\n", "        history['val_f1'].append(avg_val_f1)\n", "        history['train_dice'].append(avg_train_dice)\n", "        history['val_dice'].append(avg_val_dice)\n", "        history['lr'].append(scheduler.get_last_lr()[0])\n", "        \n", "        # Get loss weights and instance parameters for history\n", "        if hasattr(criterion, 'get_weights'):\n", "            weights = criterion.get_weights()\n", "            history['loss_weights'].append(weights)\n", "        \n", "        if hasattr(model, 'learn_instance_params') and model.learn_instance_params:\n", "            instance_params = {\n", "                'boundary_factor': torch.sigmoid(model.boundary_factor).item(),\n", "                'flow_div_factor': torch.sigmoid(model.flow_div_factor).item(),\n", "                'distance_factor': torch.sigmoid(model.distance_factor).item(),\n", "                'min_distance': max(1, min(10, model.min_distance.item()))\n", "            }\n", "            history['instance_params'].append(instance_params)\n", "        \n", "        # Print epoch summary\n", "        epoch_summary = (\n", "            f\"\\n{'='*80}\\n\"\n", "            f\"EPOCH {epoch+1}/{num_epochs} SUMMARY\\n\"\n", "            f\"{'='*80}\\n\"\n", "            f\"TRAINING:\\n\"\n", "            f\"  Loss:    {avg_train_loss:.4f}\\n\"\n", "            f\"  F1:      {avg_train_f1:.4f}\\n\"\n", "            f\"  Dice:    {avg_train_dice:.4f}\\n\"\n", "            f\"  mIoU:    {avg_train_miou:.4f}\\n\"\n", "            f\"  Time:    {train_duration:.1f}s\\n\\n\"\n", "            f\"VALIDATION:\\n\"\n", "            f\"  Loss:    {avg_val_loss:.4f}\\n\"\n", "            f\"  F1:      {avg_val_f1:.4f}\\n\"\n", "            f\"  Dice:    {avg_val_dice:.4f}\\n\"\n", "            f\"  mIoU:    {avg_val_miou:.4f}\\n\"\n", "            f\"  Time:    {val_duration:.1f}s\\n\\n\"\n", "            f\"LEARNING:\\n\"\n", "            f\"  Rate:    {scheduler.get_last_lr()[0]:.6f}\\n\"\n", "            f\"  Speed:   {avg_speed:.1f} img/s\\n\"\n", "            f\"{'='*80}\"\n", "        )\n", "        print(epoch_summary)\n", "        \n", "        # Log to tensorboard\n", "        writer.add_scalar('Loss/train', avg_train_loss, epoch)\n", "        writer.add_scalar('Loss/val', avg_val_loss, epoch)\n", "        writer.add_scalar('F1/train', avg_train_f1, epoch)\n", "        writer.add_scalar('F1/val', avg_val_f1, epoch)\n", "        writer.add_scalar('Dice/train', avg_train_dice, epoch)\n", "        writer.add_scalar('Dice/val', avg_val_dice, epoch)\n", "        writer.add_scalar('IoU/train', avg_train_miou, epoch)\n", "        writer.add_scalar('IoU/val', avg_val_miou, epoch)\n", "        writer.add_scalar('LearningRate', scheduler.get_last_lr()[0], epoch)\n", "        writer.add_scalar('Speed/imgs_per_sec', avg_speed, epoch)\n", "        \n", "        # Update main progress bar\n", "        progress_bar.set_postfix({\n", "            'val_loss': f\"{avg_val_loss:.4f}\",\n", "            'val_f1': f\"{avg_val_f1:.4f}\",\n", "            'val_dice': f\"{avg_val_dice:.4f}\",\n", "            'lr': f\"{scheduler.get_last_lr()[0]:.6f}\"\n", "        })\n", "        progress_bar.update(1)\n", "        \n", "        # Check for improvement (based on validation loss)\n", "        improved_loss = avg_val_loss < best_val_loss\n", "        improved_f1 = avg_val_f1 > best_val_f1\n", "        \n", "        if improved_loss or improved_f1:\n", "            # Save what improved\n", "            improvements = []\n", "            if improved_loss:\n", "                best_val_loss = avg_val_loss\n", "                improvements.append(\"loss\")\n", "            \n", "            if improved_f1:\n", "                best_val_f1 = avg_val_f1\n", "                improvements.append(\"F1\")\n", "            \n", "            best_epoch = epoch\n", "            no_improve_epochs = 0\n", "            \n", "            # Save best model\n", "            torch.save({\n", "                'epoch': epoch + 1,\n", "                'model_state_dict': model.state_dict(),\n", "                'criterion_state_dict': criterion.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'val_loss': avg_val_loss,\n", "                'val_f1': avg_val_f1,\n", "                'history': history\n", "            }, os.path.join(checkpoint_dir, 'best_model.pth'))\n", "            \n", "            # Print improvement message\n", "            print(f\"\\n[Epoch {epoch+1}] New best model saved! Improved {', '.join(improvements)}\")\n", "            print(f\"  Val Loss: {avg_val_loss:.4f}  |  Val F1: {avg_val_f1:.4f}  |  Val Dice: {avg_val_dice:.4f}\")\n", "        else:\n", "            no_improve_epochs += 1\n", "        \n", "        # Save periodic checkpoints\n", "        if (epoch + 1) % 10 == 0 or epoch == num_epochs - 1:\n", "            torch.save({\n", "                'epoch': epoch + 1,\n", "                'model_state_dict': model.state_dict(),\n", "                'criterion_state_dict': criterion.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'val_loss': avg_val_loss,\n", "                'val_f1': avg_val_f1,\n", "                'history': history\n", "            }, os.path.join(checkpoint_dir, f'checkpoint_epoch{epoch+1}.pth'))\n", "        \n", "        # Check early stopping\n", "        if no_improve_epochs >= patience and epoch >= 30:\n", "            print(f\"\\nEarly stopping triggered after {epoch+1} epochs without improvement\")\n", "            print(f\"Best model was at epoch {best_epoch+1} with val_loss={best_val_loss:.4f}, val_f1={best_val_f1:.4f}\")\n", "            break\n", "    \n", "    # Close tensorboard writer and progress bar\n", "    writer.close()\n", "    progress_bar.close()\n", "    \n", "    # Load best model\n", "    checkpoint = torch.load(os.path.join(checkpoint_dir, 'best_model.pth'))\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "    \n", "    print(f\"\\nTraining complete! Best model from epoch {best_epoch+1}:\")\n", "    print(f\"  Val Loss: {best_val_loss:.4f}  |  Val F1: {best_val_f1:.4f}\")\n", "    \n", "    # At the end of the function, before returning:\n", "    torch.autograd.backward = original_backward\n", "    \n", "    return model, criterion, history"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from scipy.spatial import cKDTree\n", "\n", "def calculate_spot_f1(outputs, targets, threshold=0.5, max_dist=10):\n", "    \"\"\"\n", "    Optimized F1 score calculation for spot instance segmentation.\n", "    \n", "    Args:\n", "        outputs: Model outputs (dictionary with 'heatmaps')\n", "        targets: Target masks (instance labels)\n", "        threshold: <PERSON><PERSON><PERSON><PERSON> for prediction\n", "        max_dist: Maximum distance to consider a match\n", "        \n", "    Returns:\n", "        F1 score based on instance matching\n", "    \"\"\"\n", "    # Extract heatmap from outputs\n", "    if isinstance(outputs, dict):\n", "        heatmap = outputs['heatmap']\n", "    else:\n", "        heatmap = outputs\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = torch.sigmoid(heatmap)\n", "    \n", "    # Convert to numpy for instance processing\n", "    batch_size = heatmap.shape[0]\n", "    batch_f1 = 0.0\n", "    \n", "    for i in range(batch_size):\n", "        # Get prediction and target for this batch item\n", "        pred = heatmap[i, 0].detach().cpu().numpy()\n", "        target = targets[i, 0].detach().cpu().numpy()\n", "        \n", "        # Threshold prediction\n", "        pred_binary = pred > threshold\n", "        \n", "        # Get instance labels\n", "        pred_labels = label(pred_binary)\n", "        target_labels = label(target > 0.5)\n", "        \n", "        # Extract centroids using cKDTree for faster matching\n", "        pred_centroids = np.array([prop.centroid for prop in regionprops(pred_labels)])\n", "        target_centroids = np.array([prop.centroid for prop in regionprops(target_labels)])\n", "        \n", "        if len(pred_centroids) > 0 and len(target_centroids) > 0:\n", "            # Use cKDTree for efficient distance computation\n", "            tree = cKDTree(target_centroids)\n", "            distances, indices = tree.query(pred_centroids, distance_upper_bound=max_dist)\n", "            \n", "            # Count true positives\n", "            true_positives = np.sum(distances <= max_dist)\n", "            \n", "            # Calculate precision and recall\n", "            precision = true_positives / len(pred_centroids) if len(pred_centroids) > 0 else 0\n", "            recall = true_positives / len(target_centroids) if len(target_centroids) > 0 else 0\n", "            \n", "            # Calculate F1 score\n", "            f1 = 2 * precision * recall / (precision + recall + 1e-10) if (precision + recall) > 0 else 0\n", "        else:\n", "            # Handle case with no instances in prediction or target\n", "            f1 = 1.0 if len(pred_centroids) == 0 and len(target_centroids) == 0 else 0.0\n", "        \n", "        batch_f1 += f1\n", "    \n", "    return batch_f1 / batch_size\n", "\n", "def calculate_f1_score(pred, target, threshold=0.5, ignore_threshold=0.3):\n", "    \"\"\"Calculate F1 score for spot detection that's robust to sparse annotations\"\"\"\n", "    # Apply sigmoid if needed\n", "    pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "    pred_binary = (pred > threshold).float()\n", "    target_binary = (target > threshold).float()\n", "    \n", "    # Create ignore mask - we don't penalize potential false positives that have low confidence\n", "    ignore_mask = (pred < ignore_threshold).float() * (1 - target_binary)\n", "    \n", "    # Calculate true positives (no change)\n", "    true_pos = (pred_binary * target_binary).sum()\n", "    \n", "    # Calculate false positives - don't count low confidence predictions in ignore mask\n", "    false_pos = (pred_binary * (1 - target_binary) * (1 - ignore_mask)).sum()\n", "    \n", "    # Calculate false negatives (no change)\n", "    false_neg = ((1 - pred_binary) * target_binary).sum()\n", "    \n", "    # Avoid division by zero\n", "    precision = true_pos / (true_pos + false_pos + 1e-10)\n", "    recall = true_pos / (true_pos + false_neg + 1e-10)\n", "    f1 = 2 * precision * recall / (precision + recall + 1e-10)\n", "    \n", "    return f1.item()\n", "\n", "\n", "def calculate_dice_coefficient(pred, target, threshold=0.5, ignore_threshold=0.3):\n", "    \"\"\"Calculate Dice coefficient with sparse annotation handling\"\"\"\n", "    # Apply sigmoid if needed\n", "    pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "    pred_binary = (pred > threshold).float()\n", "    target_binary = (target > threshold).float()\n", "    \n", "    # Create ignore mask for low confidence predictions where target=0\n", "    ignore_mask = (pred < ignore_threshold).float() * (1 - target_binary)\n", "    \n", "    # Modified intersection that doesn't penalize potential false positives in ignore regions\n", "    intersection = (pred_binary * target_binary).sum(dim=(1, 2, 3))\n", "    \n", "    # Modified union calculation\n", "    pred_area = pred_binary.sum(dim=(1, 2, 3))\n", "    true_area = target_binary.sum(dim=(1, 2, 3))\n", "    \n", "    # Reduce the impact of false positives in ignore regions\n", "    fp_area = (pred_binary * (1 - target_binary) * (1 - ignore_mask)).sum(dim=(1, 2, 3))\n", "    adjusted_pred_area = pred_area - (pred_binary * (1 - target_binary) * ignore_mask).sum(dim=(1, 2, 3))\n", "    \n", "    # Calculate Dice using adjusted areas\n", "    smooth = 1e-5  # Prevent division by zero\n", "    dice = (2. * intersection + smooth) / (adjusted_pred_area + true_area + smooth)\n", "    \n", "    # Return average Dice across batch\n", "    return dice.mean().item()\n", "\n", "\n", "def calculate_mean_iou(pred, target, threshold=0.5, ignore_threshold=0.3):\n", "    \"\"\"Calculate IoU that's robust to sparse annotations\"\"\"\n", "    # Apply sigmoid if needed\n", "    pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "    pred_binary = (pred > threshold).float()\n", "    target_binary = (target > threshold).float()\n", "    \n", "    # Create ignore mask for potential false positives\n", "    ignore_mask = (pred < ignore_threshold).float() * (1 - target_binary)\n", "    \n", "    # Calculate intersection (no change)\n", "    intersection = (pred_binary * target_binary).sum(dim=(1, 2, 3))\n", "    \n", "    # Calculate union with adjustment for ignore regions\n", "    pred_area = pred_binary.sum(dim=(1, 2, 3))\n", "    adjusted_pred_area = pred_area - (pred_binary * (1 - target_binary) * ignore_mask).sum(dim=(1, 2, 3))\n", "    \n", "    target_area = target_binary.sum(dim=(1, 2, 3))\n", "    union = adjusted_pred_area + target_area - intersection\n", "    \n", "    # Calculate IoU\n", "    smooth = 1e-5  # Prevent division by zero\n", "    iou = (intersection + smooth) / (union + smooth)\n", "    \n", "    # Return average IoU across batch\n", "    return iou.mean().item()\n", "\n", "# def calculate_spot_f1(outputs, targets, threshold=0.5):\n", "#     \"\"\"\n", "#     Calculate F1 score for spot instance segmentation\n", "    \n", "#     Args:\n", "#         outputs: Model outputs (dictionary with 'heatmaps')\n", "#         targets: Target masks (instance labels)\n", "#         threshold: <PERSON><PERSON><PERSON><PERSON> for prediction\n", "        \n", "#     Returns:\n", "#         F1 score based on instance matching\n", "#     \"\"\"\n", "#     # Extract heatmap from outputs\n", "#     if isinstance(outputs, dict):\n", "#         heatmap = outputs['heatmap']\n", "#     else:\n", "#         heatmap = outputs\n", "    \n", "#     # Apply sigmoid if needed\n", "#     if heatmap.min() < 0 or heatmap.max() > 1:\n", "#         heatmap = torch.sigmoid(heatmap)\n", "    \n", "#     # Convert to numpy for instance processing\n", "#     batch_size = heatmap.shape[0]\n", "#     batch_f1 = 0.0\n", "    \n", "#     for i in range(batch_size):\n", "#         # Get prediction and target for this batch item\n", "#         pred = heatmap[i, 0].detach().cpu().numpy()\n", "#         target = targets[i, 0].detach().cpu().numpy()\n", "        \n", "#         # Threshold prediction\n", "#         pred_binary = pred > threshold\n", "        \n", "#         # Get instance labels\n", "        \n", "        \n", "#         pred_labels = label(pred_binary)\n", "#         target_labels = label(target > 0.5)\n", "        \n", "#         # Get instance properties\n", "       \n", "        \n", "#         pred_props = regionprops(pred_labels)\n", "#         target_props = regionprops(target_labels)\n", "        \n", "#         # Extract centroids\n", "#         pred_centroids = [prop.centroid for prop in pred_props]\n", "#         target_centroids = [prop.centroid for prop in target_props]\n", "        \n", "#         # Match instances based on distance\n", "#         if len(pred_centroids) > 0 and len(target_centroids) > 0:\n", "#             # Calculate distances between all centroids\n", "#             distances = cdist(np.array(pred_centroids), np.array(target_centroids))\n", "            \n", "#             # Maximum distance to consider a match (adjust based on spot size)\n", "#             max_dist = 10\n", "            \n", "#             # Find matches\n", "#             true_positives = 0\n", "#             matched_targets = set()\n", "            \n", "#             for p_idx in range(len(pred_centroids)):\n", "#                 for t_idx in range(len(target_centroids)):\n", "#                     if distances[p_idx, t_idx] <= max_dist and t_idx not in matched_targets:\n", "#                         true_positives += 1\n", "#                         matched_targets.add(t_idx)\n", "#                         break\n", "            \n", "#             # Calculate precision and recall\n", "#             precision = true_positives / len(pred_centroids) if len(pred_centroids) > 0 else 0\n", "#             recall = true_positives / len(target_centroids) if len(target_centroids) > 0 else 0\n", "            \n", "#             # Calculate F1 score\n", "#             f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "#         else:\n", "#             # Handle case with no instances in prediction or target\n", "#             if len(pred_centroids) == 0 and len(target_centroids) == 0:\n", "#                 f1 = 1.0  # Both are empty - perfect match\n", "#             else:\n", "#                 f1 = 0.0  # One has instances, the other doesn't\n", "        \n", "#         batch_f1 += f1\n", "    \n", "#     return batch_f1 / batch_size\n", "\n", "\n", "# def calculate_f1_score(pred, target, threshold=0.5):\n", "#     \"\"\"Calculate F1 score for spot detection\"\"\"\n", "#     # Move to CPU for numpy operations\n", "#     if isinstance(pred, dict) and 'heatmaps' in pred:\n", "#         pred = pred['heatmaps']\n", "        \n", "#     pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "#     pred_binary = (pred > threshold).float()\n", "#     target_binary = (target > threshold).float()\n", "    \n", "#     # Calculate true positives, false positives, false negatives\n", "#     true_pos = (pred_binary * target_binary).sum().item()\n", "#     false_pos = (pred_binary * (1 - target_binary)).sum().item()\n", "#     false_neg = ((1 - pred_binary) * target_binary).sum().item()\n", "    \n", "#     # Avoid division by zero\n", "#     if true_pos + false_pos + false_neg == 0:\n", "#         return 0.0\n", "    \n", "#     # Calculate F1 score\n", "#     precision = true_pos / (true_pos + false_pos + 1e-10)\n", "#     recall = true_pos / (true_pos + false_neg + 1e-10)\n", "#     f1 = 2 * precision * recall / (precision + recall + 1e-10)\n", "    \n", "#     return f1\n", "\n", "# def calculate_dice_coefficient(pred, target, threshold=0.5):\n", "#     \"\"\"Calculate Dice coefficient (F1 score for segmentation)\"\"\"\n", "#     # Move to CPU for numpy operations\n", "#     if isinstance(pred, dict) and 'heatmaps' in pred:\n", "#         pred = pred['heatmaps']\n", "        \n", "#     pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "#     pred_binary = (pred > threshold).float()\n", "#     target_binary = (target > threshold).float()\n", "    \n", "#     # Reshape tensors for proper batch-wise calculation\n", "#     b = pred.size(0)\n", "#     pred_flat = pred_binary.view(b, -1)\n", "#     target_flat = target_binary.view(b, -1)\n", "    \n", "#     # Calculate intersection and union areas\n", "#     intersection = (pred_flat * target_flat).sum(dim=1)\n", "#     pred_sum = pred_flat.sum(dim=1)\n", "#     target_sum = target_flat.sum(dim=1)\n", "    \n", "#     # Calculate Dice for each image in batch\n", "#     smooth = 1e-5  # Prevent division by zero\n", "#     dice = (2. * intersection + smooth) / (pred_sum + target_sum + smooth)\n", "    \n", "#     # Return average Dice across batch\n", "#     return dice.mean().item()\n", "\n", "# def calculate_mean_iou(pred, target, threshold=0.5):\n", "#     \"\"\"Calculate mean Intersection over Union (IoU)\"\"\"\n", "#     # Move to CPU for numpy operations\n", "#     if isinstance(pred, dict) and 'heatmaps' in pred:\n", "#         pred = pred['heatmaps']\n", "        \n", "#     pred = torch.sigmoid(pred) if pred.min() < 0 else pred\n", "#     pred_binary = (pred > threshold).float()\n", "#     target_binary = (target > threshold).float()\n", "    \n", "#     # Reshape tensors for proper batch-wise calculation\n", "#     b = pred.size(0)\n", "#     pred_flat = pred_binary.view(b, -1)\n", "#     target_flat = target_binary.view(b, -1)\n", "    \n", "#     # Calculate intersection and union\n", "#     intersection = (pred_flat * target_flat).sum(dim=1)\n", "#     union = pred_flat.sum(dim=1) + target_flat.sum(dim=1) - intersection\n", "    \n", "#     # Calculate IoU for each image in batch\n", "#     smooth = 1e-5  # Prevent division by zero\n", "#     iou = (intersection + smooth) / (union + smooth)\n", "    \n", "#     # Return average IoU across batch\n", "#     return iou.mean().item()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import DataLoader, WeightedRandomSampler\n", "\n", "def find_optimal_threshold(model, val_loader, device, metric_fn=calculate_f1_score, \n", "                          threshold_range=np.arange(0.1, 0.9, 0.05)):\n", "    \"\"\"Find optimal prediction threshold for sparse annotations\"\"\"\n", "    best_metric = 0\n", "    best_threshold = 0.5\n", "    \n", "    model.eval()\n", "    with torch.no_grad():\n", "        for threshold in threshold_range:\n", "            total_metric = 0\n", "            samples = 0\n", "            \n", "            for images, targets in val_loader:\n", "                images = images.to(device)\n", "                if isinstance(targets, dict):\n", "                    targets = {k: v.to(device) for k, v in targets.items()}\n", "                    target_mask = targets['masks']\n", "                else:\n", "                    targets = targets.to(device)\n", "                    target_mask = targets\n", "                \n", "                outputs = model(images)\n", "                if isinstance(outputs, dict):\n", "                    pred = outputs['heatmap']\n", "                else:\n", "                    pred = outputs\n", "                \n", "                # Calculate metric with current threshold\n", "                metric = metric_fn(pred, target_mask, threshold=threshold)\n", "                total_metric += metric * images.size(0)\n", "                samples += images.size(0)\n", "            \n", "            avg_metric = total_metric / samples\n", "            print(f\"Threshold: {threshold:.2f}, Metric: {avg_metric:.4f}\")\n", "            \n", "            if avg_metric > best_metric:\n", "                best_metric = avg_metric\n", "                best_threshold = threshold\n", "                \n", "    print(f\"Found optimal threshold: {best_threshold:.2f} with metric value: {best_metric:.4f}\")\n", "    return best_threshold\n", "\n", "def visualize_spot_predictions(model, val_loader, device, num_samples=5, \n", "                              save_dir='debug/spot_predictions', threshold=0.5):\n", "    \"\"\"Visualize model predictions against sparse ground truth\"\"\"\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    \n", "    model.eval()\n", "    sample_idx = 0\n", "    \n", "    with torch.no_grad():\n", "        for images, targets in val_loader:\n", "            if sample_idx >= num_samples:\n", "                break\n", "                \n", "            images = images.to(device)\n", "            \n", "            # Get ground truth\n", "            if isinstance(targets, dict):\n", "                target_mask = targets['masks']\n", "            else:\n", "                target_mask = targets\n", "            \n", "            # Get predictions\n", "            outputs = model(images)\n", "            if isinstance(outputs, dict):\n", "                pred = outputs['heatmap']\n", "            else:\n", "                pred = outputs\n", "                \n", "            pred_sigmoid = torch.sigmoid(pred)\n", "            \n", "            # Process each image in batch\n", "            for b in range(min(images.shape[0], 3)):  # Limit to 3 images per batch\n", "                if sample_idx >= num_samples:\n", "                    break\n", "                    \n", "                # Get single image, prediction and target\n", "                img = images[b].cpu().detach()\n", "                pred_single = pred_sigmoid[b, 0].cpu().detach().numpy()\n", "                target_single = target_mask[b, 0].cpu().detach().numpy()\n", "                \n", "                # Create visualization\n", "                fig, axs = plt.subplots(1, 3, figsize=(15, 5))\n", "                \n", "                # Show original image (denormalize if needed)\n", "                if img.shape[0] == 1:  # Gray<PERSON>le\n", "                    img_display = img[0].numpy()\n", "                else:  # RGB\n", "                    img_display = img.permute(1, 2, 0).numpy()\n", "                    img_display = (img_display * 0.5 + 0.5)  # Denormalize if [-0.5, 0.5]\n", "                \n", "                axs[0].imshow(img_display, cmap='gray' if img.shape[0] == 1 else None)\n", "                axs[0].set_title('Original Image')\n", "                axs[0].axis('off')\n", "                \n", "                # Show ground truth\n", "                axs[1].imshow(target_single, cmap='viridis')\n", "                axs[1].set_title('Ground Truth (Sparse)')\n", "                axs[1].axis('off')\n", "                \n", "                # Show prediction with potential false positives highlighted\n", "                axs[2].imshow(pred_single, cmap='viridis')\n", "                \n", "                # Highlight potential unannotated spots\n", "                # (high confidence predictions where target is 0)\n", "                potential_spots = (pred_single > threshold) & (target_single == 0)\n", "                if np.any(potential_spots):\n", "                    y, x = np.where(potential_spots)\n", "                    axs[2].scatter(x, y, c='red', s=10, alpha=0.5, \n", "                                  label='Potential unannotated spots')\n", "                    axs[2].legend(loc='upper right')\n", "                    \n", "                axs[2].set_title(f'Prediction (Threshold: {threshold:.2f})')\n", "                axs[2].axis('off')\n", "                \n", "                plt.tight_layout()\n", "                plt.savefig(os.path.join(save_dir, f'spot_prediction_{sample_idx}.png'))\n", "                plt.close(fig)\n", "                \n", "                sample_idx += 1\n", "                \n", "    print(f\"Saved {min(sample_idx, num_samples)} spot prediction visualizations to {save_dir}\")\n", "\n", "\n", "def create_weighted_sampler(dataset):\n", "    \"\"\"\n", "    Create a weighted sampler to prioritize images with spots.\n", "    \n", "    Args:\n", "        dataset: The SpotInstanceDataset object.\n", "    \n", "    Returns:\n", "        WeightedRandomSampler for the dataset.\n", "    \"\"\"\n", "    # Assign higher weights to images with spots\n", "    weights = [10.0 if i in dataset.spot_indices else 1.0 for i in range(len(dataset))]\n", "    sampler = WeightedRandomSampler(weights, num_samples=len(dataset), replacement=True)\n", "    return sampler\n", "\n", "def run_spot_detection():\n", "    \"\"\"GPU-optimized implementation of spot instance detection with auto-tuned parameters\"\"\"\n", "    import multiprocessing\n", "    import time\n", "    \n", "    # Start with type enforcement\n", "    torch.set_default_dtype(torch.float32)\n", "    torch.set_default_tensor_type(torch.FloatTensor)\n", "    \n", "    # Ensure numpy is using float32 as default\n", "    np.seterr(all='warn')  # Show warnings for numerical issues\n", "    \n", "    # Set environment variables for performance\n", "    os.environ[\"OMP_NUM_THREADS\"] = str(min(8, multiprocessing.cpu_count()))\n", "    os.environ[\"OPENCV_IO_ENABLE_OPENEXR\"] = \"1\"\n", "    \n", "    # Configure PyTorch performance settings\n", "    torch.backends.cudnn.benchmark = True  # Optimize for fixed input sizes\n", "    if hasattr(torch.backends.cuda, 'matmul'):\n", "        torch.backends.cuda.matmul.allow_tf32 = True  # Allow TF32 for faster computation on Ampere+ GPUs\n", "    torch.backends.cudnn.allow_tf32 = True\n", "    \n", "    # Set device\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "    \n", "    # Print GPU information if available\n", "    if torch.cuda.is_available():\n", "        print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "        print(f\"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "        print(f\"CUDA Version: {torch.version.cuda}\")\n", "        \n", "        # Find optimal batch size based on GPU memory\n", "        gpu_mem = torch.cuda.get_device_properties(0).total_memory\n", "        # Use a memory-based heuristic for batch size (adjust if needed)\n", "        optimal_batch_size = max(4, min(32, int(gpu_mem / (1024**3 * 3))))\n", "        print(f\"Auto-selected batch size: {optimal_batch_size}\")\n", "    else:\n", "        optimal_batch_size = 8\n", "        print(\"No GPU available, using CPU\")\n", "    \n", "    # Set seeds for reproducibility\n", "    torch.manual_seed(42)\n", "    if torch.cuda.is_available():\n", "        torch.cuda.manual_seed(42)\n", "        torch.cuda.manual_seed_all(42)\n", "    np.random.seed(42)\n", "    random.seed(42)\n", "    \n", "    # Parameters\n", "    image_path = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large images/*.tif\"\n", "    mask_path = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large mask filtered eccencicity 0.8/*.tif\"\n", "    batch_size = optimal_batch_size  # Use optimal batch size from GPU detection\n", "    is_3d = False  # Set to True for 3D data\n", "    num_epochs = 800\n", "    learning_rate = 1e-3  # Lower learning rate for stability\n", "    initial_pos_weight = 20.0  # Initial weight for positive class (will be tuned during training)\n", "    \n", "    # Load all image and mask files\n", "    \n", "    print(\"Loading image and mask files...\")\n", "    image_files = sorted(glob.glob(image_path))\n", "    mask_files = sorted(glob.glob(mask_path))\n", "    \n", "    if len(image_files) == 0 or len(mask_files) == 0:\n", "        raise ValueError(f\"No files found. Image path: {image_path}, Mask path: {mask_path}\")\n", "    \n", "    print(f\"Found {len(image_files)} images and {len(mask_files)} masks\")\n", "    \n", "    # Ensure equal number of images and masks\n", "    if len(image_files) != len(mask_files):\n", "        min_len = min(len(image_files), len(mask_files))\n", "        print(f\"Warning: Number of images ({len(image_files)}) doesn't match masks ({len(mask_files)})\")\n", "        print(f\"Using first {min_len} files from each\")\n", "        image_files = image_files[:min_len]\n", "        mask_files = mask_files[:min_len]\n", "    \n", "    # Split into training (80%) and validation (20%) sets\n", "    from sklearn.model_selection import train_test_split\n", "    train_images, val_images, train_masks, val_masks = train_test_split(\n", "        image_files, mask_files, test_size=0.2, random_state=42\n", "    )\n", "    \n", "    print(f\"Training set: {len(train_images)} images\")\n", "    print(f\"Validation set: {len(val_images)} images\")\n", "    \n", "    # Create optimized augmenter with GPU-friendly transformations\n", "    augmenter = SpotAugmenter(\n", "        is_3d=is_3d,\n", "        rotation=True,\n", "        flip=True,\n", "        elastic=False,  # Careful with elastic transforms for spots\n", "        gaussian_noise=True,\n", "        brightness_contrast=True\n", "    )\n", "\n", "    \n", "    # Create datasets\n", "    train_dataset = SpotInstanceDataset(\n", "        images=train_images,\n", "        masks=train_masks,\n", "        is_train=True,\n", "        is_3d=is_3d,\n", "        patch_size=256,  # Use patches for training\n", "        augmentation_params={\n", "            'rotation': True,\n", "            'flip': True,\n", "            'elastic': <PERSON><PERSON><PERSON>,\n", "            'gaussian_noise': True,\n", "            'brightness_contrast': True\n", "        }\n", "    )\n", "    \n", "    val_dataset = SpotInstanceDataset(\n", "        images=val_images,\n", "        masks=val_masks,\n", "        is_train=False,\n", "        is_3d=is_3d,\n", "        patch_size=256  \n", "    )\n", "    \n", "    # Create the weighted sampler for the training dataset\n", "    sampler = create_weighted_sampler(train_dataset)\n", "    \n", "    # Calculate optimal number of workers\n", "    num_workers = min(8, multiprocessing.cpu_count())\n", "    print(f\"Using {num_workers} dataloader workers\")\n", "    \n", "    # Create data loaders\n", "    train_loader = DataLoader(\n", "        train_dataset, \n", "        batch_size=batch_size,\n", "        sampler=sampler,  # Use the weighted sampler\n", "        num_workers=num_workers,\n", "        pin_memory=True,\n", "        persistent_workers=True if num_workers > 0 else False,\n", "        prefetch_factor=2 if num_workers > 0 else None,\n", "        drop_last=True,  # Avoid smaller last batch for better optimization\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=batch_size * 4,  # Can use larger batch size for validation\n", "        shuffle=False,\n", "        num_workers=num_workers,\n", "        pin_memory=False,\n", "        persistent_workers=True if num_workers > 0 else False,\n", "    )\n", "    \n", "    # Create model with gradient checkpointing for memory efficiency\n", "    model = InstanceSpotDetector(\n", "        in_channels=1,\n", "        feature_channels=[16, 32, 64, 128, 256],\n", "        is_3d=is_3d,\n", "        use_instance_flow=True,\n", "        use_attention=True,\n", "        learn_instance_params=True,  # Enable learnable parameters for instance segmentation\n", "        use_gradient_checkpointing=True  # Add gradient checkpointing for memory efficiency\n", "    )\n", "\n", "    # Calculate model size\n", "    model_size = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)\n", "    print(f\"Model size: {model_size:.2f} MB\")\n", "    # OPTIMIZATION 4: Optimized Loss Function\n", "    # Create adaptive loss function with optimized GPU implementation\n", "    criterion = AdaptiveSpotInstanceLoss(\n", "        initial_pos_weight=initial_pos_weight,\n", "        learn_weights=True,  # Enable learnable loss weights\n", "        device=device  # Pass device for optimized tensor operations\n", "    )\n", "\n", "    # Add explicit float32 conversion to the model and criterion\n", "    model = model.float()\n", "\n", "    # Check criterion parameters if applicable\n", "    if hasattr(criterion, 'parameters'):\n", "        for p in criterion.parameters():\n", "            if p.dtype != torch.float32:\n", "                print(f\"Converting criterion parameter from {p.dtype} to float32\")\n", "                p.data = p.data.float()\n", "\n", "    # Create timestamp for logging\n", "    from datetime import datetime\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    checkpoint_dir = f\"checkpoints/spots_adaptive_{timestamp}\"\n", "    log_dir = f\"logs/spots_adaptive_{timestamp}\"\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    os.makedirs(log_dir, exist_ok=True)\n", "\n", "    # Create optimizer including both model and loss parameters\n", "    optimizer = torch.optim.AdamW([\n", "        {'params': model.parameters()},\n", "        {'params': criterion.parameters(), 'lr': learning_rate * 0.1}  # Lower LR for loss weights\n", "    ], weight_decay=1e-5)\n", "\n", "    # Create learning rate scheduler\n", "    scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "        optimizer,\n", "        max_lr=[learning_rate, learning_rate * 0.1],  # Different peak LR for model and criterion\n", "        total_steps=num_epochs * len(train_loader),\n", "        pct_start=0.1,\n", "        div_factor=1.3,\n", "        final_div_factor=1000.0,\n", "        anneal_strategy='cos'\n", "    )\n", "\n", "    # OPTIMIZATION 5: GPU-Optimized Training Loop\n", "    # Determine if we should use gradient accumulation based on batch size and GPU memory\n", "    accumulation_steps = 1\n", "    if torch.cuda.is_available():\n", "        # If we have a small GPU or large model, use gradient accumulation\n", "        gpu_mem_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)\n", "        if gpu_mem_gb < 8 or model_size > 100:\n", "            accumulation_steps = 4\n", "            print(f\"Using gradient accumulation with {accumulation_steps} steps\")\n", "\n", "    # Profile GPU memory before training\n", "    if torch.cuda.is_available():\n", "        torch.cuda.reset_peak_memory_stats()\n", "        print(f\"Initial GPU memory: {torch.cuda.memory_allocated() / (1024**2):.2f} MB\")\n", "\n", "    # Train the model with adaptive parameters and GPU optimizations\n", "    model, criterion, history = train_spot_instance_model(\n", "        model=model,\n", "        criterion=criterion,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        device=device,\n", "        num_epochs=num_epochs,\n", "        log_dir=log_dir,\n", "        checkpoint_dir=checkpoint_dir,\n", "        optimizer=optimizer,\n", "        scheduler=scheduler,\n", "        accumulation_steps=accumulation_steps  # Add accumulation steps\n", "    )\n", "    \n", "    # Report GPU stats after training\n", "    if torch.cuda.is_available():\n", "        print(f\"Peak GPU memory during training: {torch.cuda.max_memory_allocated() / (1024**2):.2f} MB\")\n", "\n", "    \n", "    # OPTIMIZATION 6: Inference Optimizations\n", "    # Apply model fusing for faster inference\n", "    if hasattr(model, 'fuse_model') and not is_3d:\n", "        try:\n", "            model.eval()  # Set to eval mode first\n", "            model = model.fuse_model()\n", "            print(\"Model layers fused for faster inference\")\n", "        except Exception as e:\n", "            print(f\"Could not fuse model: {e}\")\n", "    \n", "    # Save optimized model for inference (TorchScript)\n", "    try:\n", "        model.eval()\n", "        # Create a sample input tensor\n", "        sample_input = torch.randn(1, 1, 256, 256, device=device)\n", "        # Export to TorchScript\n", "        scripted_model = torch.jit.trace(model, sample_input)\n", "        scripted_model.save(os.path.join(checkpoint_dir, \"optimized_inference_model.pt\"))\n", "        print(f\"Optimized TorchScript model saved for faster inference\")\n", "    except Exception as e:\n", "        print(f\"Could not export TorchScript model: {e}\")\n", "    \n", "    # Save final model and learned parameters (standard PyTorch format)\n", "    torch.save({\n", "        'model_state_dict': model.state_dict(),\n", "        'criterion_state_dict': criterion.state_dict(),\n", "        'history': history,\n", "        # Save learned parameters for reference\n", "        'learned_loss_weights': criterion.get_weights() if hasattr(criterion, 'get_weights') else None,\n", "        'learned_instance_params': {\n", "            'boundary_factor': torch.sigmoid(model.boundary_factor).item() if model.learn_instance_params else model.boundary_factor,\n", "            'flow_div_factor': torch.sigmoid(model.flow_div_factor).item() if model.learn_instance_params else model.flow_div_factor,\n", "            'distance_factor': torch.sigmoid(model.distance_factor).item() if model.learn_instance_params else model.distance_factor,\n", "            'min_distance': max(1, min(10, model.min_distance.item())) if model.learn_instance_params else model.min_distance\n", "        } if hasattr(model, 'learn_instance_params') else None\n", "    }, os.path.join(checkpoint_dir, \"final_model_with_params.pth\"))\n", "    \n", "    print(f\"Final model and learned parameters saved to {os.path.join(checkpoint_dir, 'final_model_with_params.pth')}\")\n", "    \n", "    # Print learned parameters\n", "    if hasattr(criterion, 'get_weights'):\n", "        print(\"\\nLearned Loss Weights:\")\n", "        for name, value in criterion.get_weights().items():\n", "            print(f\"  {name}: {value:.4f}\")\n", "    \n", "    if hasattr(model, 'learn_instance_params') and model.learn_instance_params:\n", "        print(\"\\nLearned Instance Segmentation Parameters:\")\n", "        print(f\"  Boundary Factor: {torch.sigmoid(model.boundary_factor).item():.4f}\")\n", "        print(f\"  Flow Divergence Factor: {torch.sigmoid(model.flow_div_factor).item():.4f}\")\n", "        print(f\"  Distance Factor: {torch.sigmoid(model.distance_factor).item():.4f}\")\n", "        print(f\"  Minimum Distance: {max(1, min(10, model.min_distance.item())):.4f}\")\n", "        if hasattr(model, 'peak_threshold'):\n", "            print(f\"  Peak Threshold: {torch.sigmoid(model.peak_threshold).item() * 0.95 + 0.05:.4f}\")\n", "        if hasattr(model, 'spot_min_size'):\n", "            print(f\"  Spot Min Size: {max(1, int(model.spot_min_size.item()))}\")\n", "    \n", "    # Test on a few validation samples with optimized inference\n", "    print(\"\\nTesting model on validation samples with optimized inference...\")\n", "    test_model_on_samples(model, val_dataset, device, log_dir, num_samples=min(5, len(val_dataset)))\n", "    \n", "    # Visualize learned parameters\n", "    visualize_learned_parameters(model, criterion, os.path.join(log_dir, 'learned_params'))\n", "    \n", "    # Report total training time\n", "    total_time = time.time() - start_time\n", "    hours, remainder = divmod(total_time, 3600)\n", "    minutes, seconds = divmod(remainder, 60)\n", "    print(f\"\\nTotal training time: {int(hours)}h {int(minutes)}m {seconds:.2f}s\")\n", "    \n", "    # Check for images in log_dir for tensorboard viewing\n", "    print(f\"\\nTraining logs and visualizations saved to: {log_dir}\")\n", "    print(f\"To view with TensorBoard, run: tensorboard --logdir={log_dir}\")\n", "    \n", "    return model, criterion, history"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def visualize_image_mask_pairs(self, num_samples=5, threshold=0.5, save_path=None):\n", "    \"\"\"\n", "    Visualize image-mask pairs for quality control with threshold applied\n", "    \n", "    Args:\n", "        num_samples: Number of samples to visualize\n", "        threshold: Thresh<PERSON> for binary mask conversion (default: 0.5)\n", "        save_path: Optional path to save the visualization (if None, only displays)\n", "    \"\"\"\n", "    import matplotlib.pyplot as plt\n", "    import numpy as np\n", "    import os\n", "    from matplotlib.colors import LinearSegmentedColormap\n", "    \n", "    # Create output directory if needed for saving (if path provided)\n", "    if save_path:\n", "        os.makedirs(os.path.dirname(save_path), exist_ok=True)\n", "    \n", "    # Select random indices, preferring images with spots if available\n", "    if hasattr(self, 'spot_indices') and self.spot_indices and len(self.spot_indices) >= num_samples:\n", "        indices = np.random.choice(self.spot_indices, num_samples, replace=False)\n", "    else:\n", "        indices = np.random.choice(len(self), num_samples, replace=False)\n", "    \n", "    # Create figure with 3 rows (original, mask, overlay) and num_samples columns\n", "    fig, axes = plt.subplots(3, num_samples, figsize=(4*num_samples, 12))\n", "    \n", "    # Handle case of single sample\n", "    if num_samples == 1:\n", "        axes = axes.reshape(3, 1)\n", "    \n", "    # Create custom colormap for mask (red for spots)\n", "    spot_cmap = LinearSegmentedColormap.from_list('spot_mask', [(0,0,0,0), (1,0,0,0.7)])\n", "    \n", "    for i, idx in enumerate(indices):\n", "        # Get image and mask\n", "        if isinstance(self.images[idx], str):\n", "            image = self._load_image(self.images[idx])\n", "            image_name = os.path.basename(self.images[idx])\n", "        else:\n", "            image = self.images[idx].copy()\n", "            image_name = f\"Array {idx}\"\n", "        \n", "        if self.masks is not None:\n", "            if isinstance(self.masks[idx], str):\n", "                mask = self._load_image(self.masks[idx])\n", "                mask_name = os.path.basename(self.masks[idx])\n", "            else:\n", "                mask = self.masks[idx].copy()\n", "                mask_name = f\"Array {idx}\"\n", "                \n", "            # Convert to binary with threshold 0.5\n", "            binary_mask = (mask > threshold).astype(np.float32)\n", "            \n", "            # Calculate mask statistics\n", "            num_spots = np.max(mask) if mask.max() > 1 else np.sum(binary_mask > 0)\n", "            mask_coverage = np.mean(binary_mask) * 100  # percentage of positive pixels\n", "        else:\n", "            mask = np.zeros_like(image)\n", "            binary_mask = np.zeros_like(image)\n", "            mask_name = \"None\"\n", "            num_spots = 0\n", "            mask_coverage = 0\n", "        \n", "        # Normalize image for display\n", "        if image.max() > image.min():\n", "            norm_image = (image - image.min()) / (image.max() - image.min())\n", "        else:\n", "            norm_image = image\n", "            \n", "        # Select the 2D image/slice to display if 3D\n", "        if self.is_3d:\n", "            if len(image.shape) == 3:  # 3D image\n", "                middle_slice = image.shape[0] // 2\n", "                display_image = norm_image[middle_slice]\n", "                display_mask = binary_mask[middle_slice]\n", "            else:\n", "                display_image = norm_image\n", "                display_mask = binary_mask\n", "        else:\n", "            display_image = norm_image\n", "            display_mask = binary_mask\n", "            \n", "        # Ensure display mask is 2D\n", "        if len(display_mask.shape) > 2:\n", "            display_mask = display_mask[0]  # Take first channel\n", "        \n", "        # Row 1: Show original image\n", "        axes[0, i].imshow(display_image, cmap='gray')\n", "        axes[0, i].set_title(f\"Image {idx}\\n{image_name}\")\n", "        axes[0, i].axis('off')\n", "        \n", "        # Row 2: Show binary mask\n", "        mask_img = axes[1, i].imshow(display_mask, cmap='hot')\n", "        axes[1, i].set_title(f\"Binary Mask (t={threshold})\\nSpots: {num_spots}, Coverage: {mask_coverage:.2f}%\")\n", "        axes[1, i].axis('off')\n", "        plt.colorbar(mask_img, ax=axes[1, i], fraction=0.046, pad=0.04)\n", "        \n", "        # Row 3: Show overlay\n", "        axes[2, i].imshow(display_image, cmap='gray')\n", "        axes[2, i].imshow(display_mask, cmap=spot_cmap)\n", "        axes[2, i].set_title(\"Overlay\")\n", "        axes[2, i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save if path provided\n", "    if save_path:\n", "        plt.savefig(save_path)\n", "        print(f\"Image-mask QC visualization saved to {save_path}\")\n", "    \n", "    # Display in notebook\n", "    plt.show()\n", "    \n", "    # Return statistics about the masks\n", "    mask_stats = {}\n", "    total_masks = len(self.masks) if self.masks is not None else 0\n", "    positive_masks = len(self.spot_indices) if hasattr(self, 'spot_indices') and self.spot_indices else \"Unknown\"\n", "    \n", "    mask_stats[\"total_masks\"] = total_masks\n", "    mask_stats[\"positive_masks\"] = positive_masks\n", "    if isinstance(positive_masks, (int, float)) and total_masks > 0:\n", "        mask_stats[\"percent_positive\"] = f\"{100 * positive_masks / total_masks:.1f}%\"\n", "    else:\n", "        mask_stats[\"percent_positive\"] = \"Unknown\"\n", "    mask_stats[\"max_value_seen\"] = self.max_mask_value_seen\n", "    mask_stats[\"binary_conversion_count\"] = self.binary_conversion_count\n", "    \n", "    print(f\"\\nMask Statistics:\")\n", "    print(f\"  - Total masks: {mask_stats['total_masks']}\")\n", "    print(f\"  - Masks with spots: {mask_stats['positive_masks']} ({mask_stats['percent_positive']})\")\n", "    print(f\"  - Max value encountered: {mask_stats['max_value_seen']}\")\n", "    print(f\"  - Binary conversions: {mask_stats['binary_conversion_count']}\")\n", "    \n", "    return mask_stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Default tensor type set to torch.float32\n", "Starting spot instance detection training with learnable parameters...\n", "Using device: cuda\n", "GPU: NVIDIA RTX A5000\n", "GPU Memory: 24.0 GB\n", "CUDA Version: 12.4\n", "Auto-selected batch size: 7\n", "Loading image and mask files...\n", "Found 2064 images and 2064 masks\n", "Training set: 1651 images\n", "Validation set: 413 images\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3359340/3425906264.py:116: UserWarning: Argument(s) 'var_limit' are not valid for transform GaussNoise\n", "  <PERSON><PERSON>(var_limit=(5, 20), p=0.7),\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15f183812c3c4bbeb11e41807cbeb75a", "version_major": 2, "version_minor": 0}, "text/plain": ["Finding spots:   0%|          | 0/1651 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Filtered dataset: 1602 images with spots out of 1602 total masks.\n", "Sample masks comparison saved to 'debug/sample_masks_comparison.png'\n", "Image-mask QC visualization saved to debug/sample_image_mask_qc.png\n", "\n", "Mask Statistics:\n", "  - Total masks: 1602\n", "  - Masks with spots: 1602 (100.0%)\n", "  - Max value encountered: 0\n", "Using 8 dataloader workers\n", "Model size: 7.50 MB\n", "Initial GPU memory: 0.00 MB\n", "PyTorch version: 2.6.0+cu124\n", "\n", "================================================================================\n", "TRAINING PROGRESS\n", "================================================================================\n", "Metrics explanation:\n", "- Loss: Binary Cross Entropy + Dice Loss (lower is better)\n", "- F1: F1 Score for spot detection (higher is better)\n", "- Dice: Dice coefficient for segmentation overlap (higher is better)\n", "- mIoU: Mean Intersection over Union (higher is better)\n", "- LR: Current learning rate\n", "- Speed: Images processed per second\n", "================================================================================\n", "Training for 800 epochs with gradient accumulation over 1 steps\n", "================================================================================\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_3359340/2671233114.py:104: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.\n", "  scaler = GradScaler()\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "00a818f5c33745f8a58752b5901f8438", "version_major": 2, "version_minor": 0}, "text/plain": ["Training:   0%|          | 0/800 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c9f60e77fea4ecc8d7cb46e7a2cd61c", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 1/800 [Train]:   0%|          | 0/228 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Learned weights: heatmap=0.601, boundary=0.100, flow=0.099, distance=0.200\n", "Learned weights: heatmap=0.602, boundary=0.100, flow=0.097, distance=0.201\n", "Finding optimal threshold for sparse annotations...\n", "Threshold: 0.10, Metric: 0.0078\n", "Threshold: 0.15, Metric: 0.0077\n", "Threshold: 0.20, Metric: 0.0077\n", "Threshold: 0.25, Metric: 0.0076\n", "Threshold: 0.30, Metric: 0.0075\n", "Threshold: 0.35, Metric: 0.0078\n", "Threshold: 0.40, Metric: 0.0083\n", "Threshold: 0.45, Metric: 0.0102\n", "Threshold: 0.50, Metric: 0.0225\n", "Threshold: 0.55, Metric: 0.0465\n", "Threshold: 0.60, Metric: 0.1081\n", "Threshold: 0.65, Metric: 0.1522\n", "Threshold: 0.70, Metric: 0.1238\n", "Threshold: 0.75, Metric: 0.0717\n", "Threshold: 0.80, Metric: 0.0221\n", "Threshold: 0.85, Metric: 0.0027\n", "Found optimal threshold: 0.65 with metric value: 0.1522\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e559879465944647b7acb335b9d0687f", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 1/800 [Valid]:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1: Learned weights - heatmap=0.6020, boundary=0.1003, flow=0.0970, distance=0.2007\n", "log_heatmap_weight: grad=None\n", "log_boundary_weight: grad=None\n", "log_flow_weight: grad=None\n", "log_distance_weight: grad=None\n", "\n", "================================================================================\n", "EPOCH 1/800 SUMMARY\n", "================================================================================\n", "TRAINING:\n", "  Loss:    0.7700\n", "  F1:      0.1243\n", "  Dice:    0.0961\n", "  mIoU:    0.0602\n", "  Time:    31.6s\n", "\n", "VALIDATION:\n", "  Loss:    2.8247\n", "  F1:      0.1536\n", "  Dice:    0.1522\n", "  mIoU:    0.0942\n", "  Time:    975.6s\n", "\n", "LEARNING:\n", "  Rate:    0.000769\n", "  Speed:   57.8 img/s\n", "================================================================================\n", "\n", "[Epoch 1] New best model saved! Improved loss, F1\n", "  Val Loss: 2.8247  |  Val F1: 0.1536  |  <PERSON>ce: 0.1522\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd2c8918da874c36a8ee20f3a4e08d60", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 2/800 [Train]:   0%|          | 0/228 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Learned weights: heatmap=0.602, boundary=0.100, flow=0.096, distance=0.201\n", "Learned weights: heatmap=0.603, boundary=0.101, flow=0.095, distance=0.201\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "910a5971758e41d5bfc894fedbd2f647", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 2/800 [Valid]:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2: Learned weights - heatmap=0.6039, boundary=0.1007, flow=0.0941, distance=0.2013\n", "log_heatmap_weight: grad=None\n", "log_boundary_weight: grad=None\n", "log_flow_weight: grad=None\n", "log_distance_weight: grad=None\n", "\n", "================================================================================\n", "EPOCH 2/800 SUMMARY\n", "================================================================================\n", "TRAINING:\n", "  Loss:    0.7005\n", "  F1:      0.2166\n", "  Dice:    0.1731\n", "  mIoU:    0.1168\n", "  Time:    29.0s\n", "\n", "VALIDATION:\n", "  Loss:    2.6934\n", "  F1:      0.0001\n", "  Dice:    0.0240\n", "  mIoU:    0.0239\n", "  Time:    27.2s\n", "\n", "LEARNING:\n", "  Rate:    0.000770\n", "  Speed:   59.5 img/s\n", "================================================================================\n", "\n", "[Epoch 2] New best model saved! Improved loss\n", "  Val Loss: 2.6934  |  Val F1: 0.0001  |  Val Dice: 0.0240\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c4f990910e14899976ef10bd372d68c", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 3/800 [Train]:   0%|          | 0/228 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Learned weights: heatmap=0.604, boundary=0.101, flow=0.094, distance=0.201\n", "Learned weights: heatmap=0.605, boundary=0.101, flow=0.093, distance=0.202\n", "Learned weights: heatmap=0.606, boundary=0.101, flow=0.091, distance=0.202\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1ce8c2b2d1ec48c6a773143ad7031fff", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 3/800 [Valid]:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3: Learned weights - heatmap=0.6058, boundary=0.1010, flow=0.0913, distance=0.2019\n", "log_heatmap_weight: grad=None\n", "log_boundary_weight: grad=None\n", "log_flow_weight: grad=None\n", "log_distance_weight: grad=None\n", "\n", "================================================================================\n", "EPOCH 3/800 SUMMARY\n", "================================================================================\n", "TRAINING:\n", "  Loss:    0.6784\n", "  F1:      0.2220\n", "  Dice:    0.1718\n", "  mIoU:    0.1173\n", "  Time:    29.1s\n", "\n", "VALIDATION:\n", "  Loss:    2.5970\n", "  F1:      0.2123\n", "  Dice:    0.2217\n", "  mIoU:    0.1554\n", "  Time:    42.9s\n", "\n", "LEARNING:\n", "  Rate:    0.000770\n", "  Speed:   58.4 img/s\n", "================================================================================\n", "\n", "[Epoch 3] New best model saved! Improved loss, F1\n", "  Val Loss: 2.5970  |  Val F1: 0.2123  |  Val Dice: 0.2217\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "61a44e6a766542e8bd5201b9b0bc9f9e", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 4/800 [Train]:   0%|          | 0/228 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Learned weights: heatmap=0.606, boundary=0.101, flow=0.090, distance=0.202\n", "Learned weights: heatmap=0.607, boundary=0.101, flow=0.089, distance=0.202\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f4a8e97233264f7297679ca35adc87b7", "version_major": 2, "version_minor": 0}, "text/plain": ["Epoch 4/800 [Valid]:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if __name__ == \"__main__\":\n", "    # Print version information for debugging\n", "    import torch\n", "    import cv2\n", "    import numpy as np\n", "    import os\n", "    import sys\n", "    \n", "    #print(f\"Python version: {sys.version}\")\n", "    #print(f\"PyTorch version: {torch.__version__}\")\n", "    #print(f\"OpenCV version: {cv2.__version__}\")\n", "    #print(f\"NumPy version: {np.__version__}\")\n", "    \n", "    # Set default tensor type\n", "    torch.set_default_dtype(torch.float32)\n", "    print(f\"Default tensor type set to {torch.get_default_dtype()}\")\n", "    \n", "    # # Try importing required packages\n", "    # try:\n", "    #     import tifffile\n", "    #     print(\"tifffile available for TIFF support\")\n", "    # except ImportError:\n", "    #     print(\"WARNING: tifffile not available, will fall back to PIL\")\n", "        \n", "    # try:\n", "    #     import imagecodecs\n", "    #     print(\"imagecodecs available for LZW compression\")\n", "    # except ImportError:\n", "    #     print(\"WARNING: imagecodecs not available - trying to install now\")\n", "    #     try:\n", "    #         import subprocess\n", "    #         subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", \"imagecodecs\"])\n", "    #         import imagecodecs\n", "    #         print(\"Successfully installed imagecodecs\")\n", "    #     except:\n", "    #         print(\"CRITICAL: Failed to install imagecodecs - will try alternative loading methods\")\n", "    \n", "    # Create directories for outputs\n", "    os.makedirs('checkpoints', exist_ok=True)\n", "    os.makedirs('logs', exist_ok=True)\n", "    os.makedirs('results', exist_ok=True)\n", "    os.makedirs('debug', exist_ok=True)\n", "    \n", "    # Fix for OpenCV errors: check and set environment variable\n", "    os.environ[\"OPENCV_IO_ENABLE_OPENEXR\"] = \"1\"  # Enable OpenEXR support\n", "    \n", "    print(\"Starting spot instance detection training with learnable parameters...\")\n", "    \n", "    # Run the main training function with try-except for better error handling\n", "    try:\n", "        model, criterion, history = run_spot_detection()\n", "        \n", "        # Visualize learned parameters\n", "        visualize_learned_parameters(model, criterion, 'results/learned_parameters')\n", "        \n", "        # NEW: Visualize spot predictions vs sparse ground truth\n", "        print(\"Visualizing spot predictions...\")\n", "        visualize_spot_predictions(\n", "            model, \n", "            val_loader,  # You'll need to make this accessible\n", "            device, \n", "            num_samples=10, \n", "            save_dir='results/spot_predictions',\n", "            threshold=getattr(model, 'optimal_threshold', 0.5)\n", "        )\n", "        \n", "        print(\"Training completed successfully\")\n", "    except Exception as e:\n", "        print(f\"Error during training: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aaaaaa"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Resume training from best model (on disk)\n", "\n", "### Reuse a best model : \n", "\n", "if __name__ == \"__main__\":\n", "    import torch\n", "    import os\n", "\n", "    # Set default tensor type\n", "    torch.set_default_dtype(torch.float32)\n", "    print(f\"Default tensor type set to {torch.get_default_dtype()}\")\n", "\n", "    # Create directories for outputs\n", "    os.makedirs('checkpoints', exist_ok=True)\n", "    os.makedirs('logs', exist_ok=True)\n", "    os.makedirs('results', exist_ok=True)\n", "    os.makedirs('debug', exist_ok=True)\n", "\n", "    # Fix for OpenCV errors: check and set environment variable\n", "    os.environ[\"OPENCV_IO_ENABLE_OPENEXR\"] = \"1\"  # Enable OpenEXR support\n", "\n", "    print(\"Starting spot instance detection training with learnable parameters...\")\n", "\n", "    # Path to the best checkpoint\n", "    checkpoint_path = os.path.join('checkpoints', 'spots_adaptive_20250320_123724', 'best_model.pth')\n", "\n", "    # Set device\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "    # Initialize model\n", "    model = InstanceSpotDetector(\n", "        in_channels=1,\n", "        feature_channels=[16, 32, 64, 128, 256],\n", "        is_3d=False,  # Set to True for 3D data\n", "        use_instance_flow=True,\n", "        use_attention=True,\n", "        learn_instance_params=True,\n", "        use_gradient_checkpointing=True\n", "    ).to(device)\n", "\n", "    # Initialize criterion\n", "    criterion = AdaptiveSpotInstanceLoss(\n", "        initial_pos_weight=20.0,  # Adjust based on dataset\n", "        learn_weights=True,\n", "        device=device\n", "    )\n", "\n", "    # Initialize optimizer\n", "    optimizer = torch.optim.AdamW([\n", "        {'params': model.parameters()},\n", "        {'params': criterion.parameters(), 'lr': 1e-4}  # Lower LR for loss weights\n", "    ], weight_decay=1e-5)\n", "\n", "    # Initialize scheduler\n", "    scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "        optimizer,\n", "        max_lr=[1e-3, 1e-4],  # Different peak LR for model and criterion\n", "        total_steps=400 * len(train_loader),  # Adjust based on total epochs and train_loader size\n", "        pct_start=0.1,\n", "        div_factor=25.0,\n", "        final_div_factor=1e4,\n", "        anneal_strategy='cos'\n", "    )\n", "\n", "    # Check if a checkpoint exists\n", "    if os.path.exists(checkpoint_path):\n", "        print(f\"Loading checkpoint from {checkpoint_path}...\")\n", "        checkpoint = torch.load(checkpoint_path)\n", "\n", "        # Load model state\n", "        model.load_state_dict(checkpoint['model_state_dict'])\n", "\n", "        # Load criterion state (if applicable)\n", "        if 'criterion_state_dict' in checkpoint:\n", "            criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "\n", "        # Load optimizer state\n", "        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "\n", "        # Load scheduler state (if applicable)\n", "        if 'scheduler_state_dict' in checkpoint and scheduler is not None:\n", "            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])\n", "\n", "        # Resume from the saved epoch\n", "        start_epoch = checkpoint['epoch']\n", "        print(f\"Resuming training from epoch {start_epoch}...\")\n", "    else:\n", "        print(\"No checkpoint found. Starting training from scratch.\")\n", "        start_epoch = 0\n", "\n", "    # Continue training\n", "    model, criterion, history = train_spot_instance_model(\n", "        model=model,\n", "        criterion=criterion,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        device=device,\n", "        num_epochs=400,  # Total epochs\n", "        log_dir='logs',\n", "        checkpoint_dir='checkpoints',\n", "        optimizer=optimizer,\n", "        scheduler=scheduler,\n", "        accumulation_steps=1\n", "    )\n", "\n", "    # Visualize learned parameters\n", "    visualize_learned_parameters(model, criterion, 'results/learned_parameters')\n", "    print(\"Training completed successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gc\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def resume_training(checkpoint_path, run_name=None):\n", "    \"\"\"\n", "    Resume training from a saved checkpoint\n", "    \n", "    Args:\n", "        checkpoint_path: Path to the checkpoint file\n", "        run_name: Optional new name for the resumed run (for logs)\n", "        \n", "    Returns:\n", "        Trained model, criterion, and history\n", "    \"\"\"\n", "    # Load checkpoint\n", "    print(f\"Resuming training from checkpoint: {checkpoint_path}\")\n", "    if not os.path.exists(checkpoint_path):\n", "        raise FileNotFoundError(f\"Checkpoint not found: {checkpoint_path}\")\n", "    \n", "    checkpoint = torch.load(checkpoint_path)\n", "    \n", "    # Set device\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    print(f\"Using device: {device}\")\n", "    if device.type == \"cuda\":\n", "        print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "        print(f\"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "    \n", "    # Retrieve checkpoint metadata\n", "    last_epoch = checkpoint['epoch']\n", "    print(f\"Checkpoint from epoch {last_epoch}\")\n", "    \n", "    # Create new log directory with timestamp for the resumed training\n", "    from datetime import datetime\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    if run_name:\n", "        log_dir = f'logs/resume_{run_name}_{timestamp}'\n", "        checkpoint_dir = f'checkpoints/resume_{run_name}_{timestamp}'\n", "    else:\n", "        log_dir = f'logs/resume_{timestamp}'\n", "        checkpoint_dir = f'checkpoints/resume_{timestamp}'\n", "    \n", "    os.makedirs(log_dir, exist_ok=True)\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    \n", "    # Prepare datasets and loaders\n", "    print(\"Loading datasets...\")\n", "    train_dataset = SpotInstanceDataset(images=train_images, masks=train_masks, is_train=True)\n", "    val_dataset = SpotInstanceDataset(images=val_images, masks=val_masks, is_train=False)\n", "    \n", "    # Optimize batch size and workers\n", "    import multiprocessing\n", "    num_workers = min(8, multiprocessing.cpu_count())\n", "    \n", "    # Select batch size based on GPU memory\n", "    if device.type == \"cuda\":\n", "        gpu_mem = torch.cuda.get_device_properties(0).total_memory / (1024**3)\n", "        batch_size = max(1, int(gpu_mem / 6))  # ~6GB per batch as a conservative estimate\n", "        batch_size = min(batch_size, 8)  # Cap at 8\n", "    else:\n", "        batch_size = 4\n", "    \n", "    print(f\"Using batch size: {batch_size}, workers: {num_workers}\")\n", "    \n", "    # Create data loaders\n", "    train_loader = DataLoader(\n", "        train_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=True,\n", "        num_workers=num_workers,\n", "        pin_memory=True,\n", "        prefetch_factor=2,\n", "        persistent_workers=True if num_workers > 0 else False\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=batch_size * 2,  # Can use larger batch size for validation\n", "        shuffle=False,\n", "        num_workers=num_workers,\n", "        pin_memory=False  # Avoid pin memory issues\n", "    )\n", "    \n", "    # Initialize model and criterion\n", "    print(\"Initializing model and criterion...\")\n", "    model = InstanceSpotDetector(in_channels=1, feature_channels=[16, 32, 64, 128, 256])\n", "    criterion = AdaptiveSpotInstanceLoss(initial_pos_weight=10.0)\n", "    \n", "    # Load model and criterion states\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "    \n", "    # Move to device\n", "    model = model.to(device)\n", "    criterion = criterion.to(device)\n", "    \n", "    # Initialize optimizer\n", "    optimizer = torch.optim.AdamW([\n", "        {'params': model.parameters(), 'lr': 5e-5},\n", "        {'params': criterion.parameters(), 'lr': 5e-6}\n", "    ], weight_decay=1e-5)\n", "    \n", "    # Load optimizer state\n", "    if 'optimizer_state_dict' in checkpoint:\n", "        print(\"Restoring optimizer state...\")\n", "        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "        # Move optimizer state to the correct device\n", "        for state in optimizer.state.values():\n", "            for k, v in state.items():\n", "                if isinstance(v, torch.Tensor):\n", "                    state[k] = v.to(device)\n", "    \n", "    # Initialize scheduler\n", "    total_epochs = 100  # Or however many additional epochs you want\n", "    scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "        optimizer,\n", "        max_lr=[5e-4, 5e-5],\n", "        total_steps=total_epochs * len(train_loader),\n", "        pct_start=0.1,\n", "        div_factor=25.0,\n", "        final_div_factor=1e4,\n", "        anneal_strategy='cos'\n", "    )\n", "    \n", "    # Load scheduler state if available\n", "    if 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:\n", "        try:\n", "            print(\"Restoring scheduler state...\")\n", "            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])\n", "        except Exception as e:\n", "            print(f\"Warning: Could not restore scheduler state: {e}\")\n", "            print(\"Creating new scheduler instead.\")\n", "    \n", "    # Load history if available and continue from there\n", "    history = checkpoint.get('history', {\n", "        'train_loss': [], 'val_loss': [],\n", "        'train_f1': [], 'val_f1': [],\n", "        'train_dice': [], 'val_dice': [],\n", "        'lr': [], 'loss_weights': [],\n", "        'instance_params': []\n", "    })\n", "    \n", "    print(f\"Previous training: {len(history['train_loss'])} epochs completed\")\n", "    print(f\"Best validation metrics from previous run: Loss={checkpoint.get('val_loss', 'N/A')}, F1={checkpoint.get('val_f1', 'N/A')}\")\n", "    \n", "    # Continue training from the saved point\n", "    print(f\"\\nResuming training for {total_epochs} more epochs...\")\n", "    model, criterion, updated_history = train_spot_instance_model(\n", "        model=model,\n", "        criterion=criterion,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        device=device,\n", "        num_epochs=total_epochs,\n", "        log_dir=log_dir,\n", "        checkpoint_dir=checkpoint_dir,\n", "        optimizer=optimizer,\n", "        scheduler=scheduler,\n", "        accumulation_steps=1  # Use gradient accumulation for stability\n", "    )\n", "    \n", "    # Merge histories - old + new\n", "    for key in updated_history:\n", "        if key in history:\n", "            history[key].extend(updated_history[key])\n", "    \n", "    print(\"Training resumed and completed successfully!\")\n", "    return model, criterion, history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if __name__ == \"__main__\":\n", "    import torch\n", "    import cv2\n", "    import numpy as np\n", "    import os\n", "    import sys\n", "    import argparse  # For command-line arguments\n", "    \n", "    # Parse command-line arguments\n", "    parser = argparse.ArgumentParser(description=\"Spot Instance Detection Training\")\n", "    parser.add_argument(\"--resume\", type=str, help=\"Path to checkpoint file for resuming training\")\n", "    parser.add_argument(\"--name\", type=str, help=\"Name for this training run\")\n", "    parser.add_argument(\"--epochs\", type=int, default=100, help=\"Number of training epochs\")\n", "    args = parser.parse_args()\n", "    \n", "    print(f\"Python version: {sys.version}\")\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    print(f\"OpenCV version: {cv2.__version__}\")\n", "    print(f\"NumPy version: {np.__version__}\")\n", "    \n", "    # Set default tensor type\n", "    torch.set_default_dtype(torch.float32)\n", "    print(f\"Default tensor type set to {torch.get_default_dtype()}\")\n", "    \n", "    \n", "    # Create directories for outputs\n", "    os.makedirs('checkpoints', exist_ok=True)\n", "    os.makedirs('logs', exist_ok=True)\n", "    os.makedirs('results', exist_ok=True)\n", "    os.makedirs('debug', exist_ok=True)\n", "    \n", "    # Fix for OpenCV errors: check and set environment variable\n", "    os.environ[\"OPENCV_IO_ENABLE_OPENEXR\"] = \"1\"  # Enable OpenEXR support\n", "    \n", "    # Run the main training function with try-except for better error handling\n", "    try:\n", "        if args.resume:\n", "            # Resume training from a checkpoint\n", "            print(f\"Resuming training from checkpoint: {args.resume}\")\n", "            model, criterion, history = resume_training(\n", "                checkpoint_path=args.resume, \n", "                run_name=args.name\n", "            )\n", "        else:\n", "            # Start a new training run\n", "            print(\"Starting spot instance detection training with learnable parameters...\")\n", "            model, criterion, history = run_spot_detection(num_epochs=args.epochs)\n", "        \n", "        # Visualize learned parameters\n", "        visualize_learned_parameters(model, criterion, 'results/learned_parameters')\n", "        print(\"Training completed successfully\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error during training: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For resuming training in a notebook\n", "resume_checkpoint = None  # Change this to the path to resume from, e.g., 'checkpoints/best_model.pth'\n", "run_name = \"continued_training\"  # Give a name to this continued training run\n", "num_epochs = 100  # How many more epochs to train\n", "\n", "if resume_checkpoint:\n", "    print(f\"Resuming training from checkpoint: {resume_checkpoint}\")\n", "    model, criterion, history = resume_training(\n", "        checkpoint_path=resume_checkpoint, \n", "        run_name=run_name\n", "    )\n", "else:\n", "    print(\"Starting new training run...\")\n", "    model, criterion, history = run_spot_detection(num_epochs=num_epochs)\n", "\n", "# Visualize learned parameters\n", "visualize_learned_parameters(model, criterion, 'results/learned_parameters')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def finetune_model(checkpoint_path, new_train_dataset, new_val_dataset, run_name=\"finetuned\"):\n", "    \"\"\"Fine-tune a pretrained model on a new dataset with reduced learning rate\"\"\"\n", "    # Load checkpoint\n", "    checkpoint = torch.load(checkpoint_path)\n", "    \n", "    # Initialize model and load weights\n", "    model = InstanceSpotDetector(in_channels=1, feature_channels=[16, 32, 64, 128, 256])\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    \n", "    # Initialize criterion and load weights (optional, can start fresh)\n", "    criterion = AdaptiveSpotInstanceLoss()\n", "    criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "    \n", "    # Create data loaders for new dataset\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    batch_size = 4  # Adjust based on your GPU\n", "    \n", "    train_loader = DataLoader(\n", "        new_train_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=True,\n", "        num_workers=4,\n", "        pin_memory=True\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        new_val_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=False,\n", "        num_workers=4,\n", "        pin_memory=False\n", "    )\n", "    \n", "    # Use reduced learning rate for fine-tuning\n", "    optimizer = torch.optim.AdamW([\n", "        {'params': model.parameters(), 'lr': 1e-5},  # 10x lower than initial training\n", "        {'params': criterion.parameters(), 'lr': 1e-6}\n", "    ], weight_decay=1e-5)\n", "    \n", "    # Train for fewer epochs\n", "    finetune_epochs = 50\n", "    \n", "    # Create new directories for fine-tuning run\n", "    from datetime import datetime\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    log_dir = f'logs/finetune_{run_name}_{timestamp}'\n", "    checkpoint_dir = f'checkpoints/finetune_{run_name}_{timestamp}'\n", "    \n", "    os.makedirs(log_dir, exist_ok=True)\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    \n", "    # Start fine-tuning\n", "    model, criterion, history = train_spot_instance_model(\n", "        model=model,\n", "        criterion=criterion,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        device=device,\n", "        num_epochs=finetune_epochs,\n", "        log_dir=log_dir,\n", "        checkpoint_dir=checkpoint_dir,\n", "        optimizer=optimizer,\n", "        accumulation_steps=2\n", "    )\n", "    \n", "    return model, criterion, history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_predictions(model, val_loader, device, epoch, save_dir='results/visualizations'):\n", "    \"\"\"Visualize model predictions on validation data\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    \n", "    # Create directory for this epoch\n", "    epoch_dir = os.path.join(save_dir, f'epoch_{epoch}')\n", "    os.makedirs(epoch_dir, exist_ok=True)\n", "    \n", "    # Set model to evaluation mode\n", "    model.eval()\n", "    \n", "    # Get a batch of validation data\n", "    images, targets = next(iter(val_loader))\n", "    images = images.to(device)\n", "    \n", "    # Generate predictions\n", "    with torch.no_grad():\n", "        outputs = model(images)\n", "    \n", "    # Visualize up to 4 examples\n", "    for i in range(min(4, images.size(0))):\n", "        plt.figure(figsize=(15, 5))\n", "        \n", "        # Show original image\n", "        plt.subplot(1, 3, 1)\n", "        img = images[i, 0].cpu().numpy()\n", "        plt.imshow(img, cmap='gray')\n", "        plt.title(f'Original Image')\n", "        plt.axis('off')\n", "        \n", "        # Show ground truth mask\n", "        plt.subplot(1, 3, 2)\n", "        if isinstance(targets, dict):\n", "            mask = targets['masks'][i, 0].cpu().numpy()\n", "        else:\n", "            mask = targets[i, 0].cpu().numpy()\n", "        plt.imshow(mask, cmap='hot')\n", "        plt.title('Ground Truth')\n", "        plt.axis('off')\n", "        \n", "        # Show model prediction\n", "        plt.subplot(1, 3, 3)\n", "        pred = torch.sigmoid(outputs['heatmaps'][i, 0]).cpu().numpy()\n", "        plt.imshow(pred, cmap='hot')\n", "        plt.title(f'Prediction (Epoch {epoch})')\n", "        plt.axis('off')\n", "        \n", "        # Save figure\n", "        plt.tight_layout()\n", "        plt.savefig(os.path.join(epoch_dir, f'sample_{i}.png'), dpi=150)\n", "        plt.close()\n", "    \n", "    # Set model back to training mode\n", "    model.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def inference_optimized(model, image, device='cuda'):\n", "    \"\"\"Optimized inference with TorchScript for better GPU utilization\"\"\"\n", "    # Move model to eval mode\n", "    model.eval()\n", "    \n", "    # Check GPU availability\n", "    if device == 'cuda' and not torch.cuda.is_available():\n", "        device = 'cpu'\n", "        print(\"CUDA not available, using CPU\")\n", "    \n", "    # Use TorchScript for faster inference\n", "    try:\n", "        # Try to optimize the model with TorchScript\n", "        scripted_model = torch.jit.script(model)\n", "        print(\"Using TorchScript for faster inference\")\n", "        model_for_inference = scripted_model\n", "    except Exception as e:\n", "        print(f\"TorchScript conversion failed: {str(e)}. Using regular model.\")\n", "        model_for_inference = model\n", "    \n", "    # Pre-process image\n", "    if isinstance(image, str):  # Path provided\n", "        if image.lower().endswith(('.tif', '.tiff')):\n", "            import tifffile\n", "            image = tifffile.imread(image)\n", "        else:\n", "            import cv2\n", "            image = cv2.imread(image, cv2.IMREAD_ANYDEPTH)\n", "    \n", "    # Convert to tensor if needed\n", "    if not isinstance(image, torch.Tensor):\n", "        image = torch.from_numpy(image).float()\n", "    \n", "    # Add batch and channel dimensions if needed\n", "    if image.dim() == 2:  # H, W\n", "        image = image.unsqueeze(0).unsqueeze(0)  # 1, 1, H, W\n", "    elif image.dim() == 3 and image.size(0) == 1:  # 1, H, W (already has channel)\n", "        image = image.unsqueeze(0)  # 1, 1, H, W\n", "    elif image.dim() == 3 and image.size(0) > 3:  # H, W, C\n", "        image = image.permute(2, 0, 1).unsqueeze(0)  # 1, C, H, W\n", "    \n", "    # Normalize if needed (0-1 range)\n", "    if image.max() > 1.0:\n", "        image = image / 65535.0 if image.max() > 255 else image / 255.0\n", "    \n", "    # Move to device\n", "    image = image.to(device)\n", "    model_for_inference = model_for_inference.to(device)\n", "    \n", "    # Inference with mixed precision\n", "    with torch.no_grad(), torch.cuda.amp.autocast() if device == 'cuda' else nullcontext():\n", "        output = model_for_inference(image)\n", "    \n", "    return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Reuse a best model : \n", "\n", "if __name__ == \"__main__\":\n", "    import torch\n", "    import os\n", "    import sys\n", "\n", "    # Set default tensor type\n", "    torch.set_default_dtype(torch.float32)\n", "    print(f\"Default tensor type set to {torch.get_default_dtype()}\")\n", "\n", "    # Create directories for outputs\n", "    os.makedirs('checkpoints', exist_ok=True)\n", "    os.makedirs('logs', exist_ok=True)\n", "    os.makedirs('results', exist_ok=True)\n", "    os.makedirs('debug', exist_ok=True)\n", "\n", "    # Fix for OpenCV errors: check and set environment variable\n", "    os.environ[\"OPENCV_IO_ENABLE_OPENEXR\"] = \"1\"  # Enable OpenEXR support\n", "\n", "    print(\"Starting spot instance detection training with learnable parameters...\")\n", "\n", "    # Path to the best checkpoint\n", "    checkpoint_path = os.path.join('checkpoints','spots_adaptive_20250320_123724', 'best_model.pth')\n", "\n", "    # Run the main training function with try-except for better error handling\n", "    try:\n", "        # Initialize model, criterion, optimizer, etc.\n", "        model, criterion, optimizer, scheduler = initialize_training_components()\n", "\n", "        # Check if a checkpoint exists\n", "        if os.path.exists(checkpoint_path):\n", "            print(f\"Loading checkpoint from {checkpoint_path}...\")\n", "            checkpoint = torch.load(checkpoint_path)\n", "\n", "            # Load model state\n", "            model.load_state_dict(checkpoint['model_state_dict'])\n", "\n", "            # Load criterion state (if applicable)\n", "            if 'criterion_state_dict' in checkpoint:\n", "                criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "\n", "            # Load optimizer state\n", "            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "\n", "            # Load scheduler state (if applicable)\n", "            if 'scheduler_state_dict' in checkpoint and scheduler is not None:\n", "                scheduler.load_state_dict(checkpoint['scheduler_state_dict'])\n", "\n", "            # Resume from the saved epoch\n", "            start_epoch = checkpoint['epoch']\n", "            print(f\"Resuming training from epoch {start_epoch}...\")\n", "        else:\n", "            print(\"No checkpoint found. Starting training from scratch.\")\n", "            start_epoch = 0\n", "\n", "        # Continue training\n", "        model, criterion, history = train_spot_instance_model(\n", "            model=model,\n", "            criterion=criterion,\n", "            train_loader=train_loader,\n", "            val_loader=val_loader,\n", "            device=device,\n", "            num_epochs=400,  # Total epochs\n", "            log_dir='logs',\n", "            checkpoint_dir='checkpoints',\n", "            optimizer=optimizer,\n", "            scheduler=scheduler,\n", "            accumulation_steps=1,\n", "            start_epoch=start_epoch  # Pass the starting epoch\n", "        )\n", "\n", "        # Visualize learned parameters\n", "        visualize_learned_parameters(model, criterion, 'results/learned_parameters')\n", "        print(\"Training completed successfully\")\n", "    except Exception as e:\n", "        print(f\"Error during training: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_spot_detection():\n", "    \"\"\"Main function to run spot instance detection training\"\"\"\n", "    \n", "    # Start with type enforcement\n", "    torch.set_default_dtype(torch.float32)\n", "    print(f\"Default dtype set to: {torch.get_default_dtype()}\")\n", "    torch.set_default_tensor_type(torch.FloatTensor)\n", "    \n", "    # Ensure numpy is using float32 as default\n", "    np.seterr(all='warn')  # Show warnings for numerical issues\n", "    \n", "    # Set device\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Using device: {device}\")\n", "    \n", "    # Set seeds for reproducibility\n", "    torch.manual_seed(42)\n", "    if torch.cuda.is_available():\n", "        torch.cuda.manual_seed(42)\n", "    np.random.seed(42)\n", "    \n", "   # Parameters\n", "    image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large images/*.tif\"\n", "    mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/spots large mask filtered eccencicity 0.8/*.tif\"\n", "    batch_size = 8\n", "    is_3d = False  # Set to True for 3D data\n", "    num_epochs = 100\n", "    learning_rate = 5e-5  # Lower learning rate for stability\n", "    pos_weight = 400.0  # High weight for extreme class imbalance\n", "    \n", "    # Create augmenter\n", "    augmenter = SpotAugmenter(is_3d=is_3d)\n", "    \n", "    # Create datasets\n", "    train_dataset = SpotInstanceDataset(\n", "        data_dir=os.path.join(image_dir,'train'),\n", "        transform=augmenter.get_transform(train=True),\n", "        is_3d=is_3d\n", "    )\n", "    \n", "    val_dataset = SpotInstanceDataset(\n", "        data_dir=os.path.join(mask_dir,'val'),\n", "        transform=augmenter.get_transform(train=False),\n", "        is_3d=is_3d\n", "    )\n", "    \n", "    # Create data loaders\n", "    train_loader = DataLoader(\n", "        train_dataset, \n", "        batch_size=batch_size,\n", "        shuffle=True,\n", "        num_workers=4,\n", "        pin_memory=True\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=False,\n", "        num_workers=4,\n", "        pin_memory=True\n", "    )\n", "    \n", "    # Create model\n", "    model = InstanceSpotDetector(\n", "        in_channels=1,\n", "        feature_channels=[16, 32, 64, 128,256],\n", "        is_3d=is_3d,\n", "        use_instance_flow=True,\n", "        use_attention=True\n", "    )\n", "    \n", "    # Create timestamp for logging\n", "    timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    checkpoint_dir = f\"checkpoints/spots_{timestamp}\"\n", "    log_dir = f\"logs/spots_{timestamp}\"\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    \n", "    # Train the model\n", "    model, history = train_spot_instance_model(\n", "        model=model,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        device=device,\n", "        num_epochs=num_epochs,\n", "        log_dir=log_dir,\n", "        checkpoint_dir=checkpoint_dir,\n", "        learning_rate=learning_rate,\n", "        pos_weight=pos_weight\n", "    )\n", "    \n", "    # Save final model\n", "    torch.save(model.state_dict(), os.path.join(checkpoint_dir, \"final_model.pth\"))\n", "    print(f\"Final model saved to {os.path.join(checkpoint_dir, 'final_model.pth')}\")\n", "    \n", "    # Test on a few validation samples\n", "    test_model_on_samples(model, val_dataset, device, log_dir, num_samples=5)\n", "    \n", "    return model, history\n", "\n", "if __name__ == \"__main__\":\n", "    model, history = run_spot_detection()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_spot_instance_model(model, criterion, train_loader, val_loader, device, \n", "                             num_epochs=100, log_dir='spot_logs', checkpoint_dir='spot_checkpoints',\n", "                             optimizer=None, scheduler=None, accumulation_steps=1):\n", "    \"\"\"\n", "    Train spot instance segmentation model with adaptive parameters\n", "    \n", "    Args:\n", "        model: The model with learnable instance parameters\n", "        criterion: Loss function with learnable weights\n", "        train_loader: Training data loader\n", "        val_loader: Validation data loader\n", "        device: Device to train on\n", "        num_epochs: Number of epochs to train\n", "        log_dir: Directory for tensorboard logs\n", "        checkpoint_dir: Directory for model checkpoints\n", "        optimizer: Optional pre-configured optimizer (if None, will create one)\n", "        scheduler: Optional learning rate scheduler\n", "        accumulation_steps: Number of batches to accumulate gradients over\n", "        \n", "    Returns:\n", "        Trained model, criterion, and training history\n", "    \"\"\"\n", "    # At the start of the function\n", "    torch.set_default_tensor_type(torch.FloatTensor)  # Force default to float32\n", "    \n", "    # Create directories\n", "    os.makedirs(log_dir, exist_ok=True)\n", "    os.makedirs(checkpoint_dir, exist_ok=True)\n", "    \n", "    # Initialize tensorboard logger\n", "    from torch.utils.tensorboard import SummaryWriter\n", "    writer = SummaryWriter(log_dir=log_dir)\n", "    \n", "    # Enable cuDNN benchmarking for optimized performance\n", "    torch.backends.cudnn.benchmark = True\n", "    \n", "    # Track CUDA memory usage\n", "    if device.type == 'cuda':\n", "        initial_memory = torch.cuda.memory_allocated(device) / 1024**2\n", "        print(f\"Initial GPU memory: {initial_memory:.2f} MB\")\n", "        torch.cuda.reset_peak_memory_stats(device)\n", "    \n", "    # Initialize model and criterion on device\n", "    model = model.to(device)\n", "    criterion = criterion.to(device)\n", "    \n", "    # Initialize optimizer if not provided\n", "    if optimizer is None:\n", "        optimizer = torch.optim.AdamW([\n", "            {'params': model.parameters(), 'lr': 5e-5},\n", "            {'params': criterion.parameters(), 'lr': 5e-6}  # Lower LR for loss parameters\n", "        ], weight_decay=1e-5)\n", "    \n", "    # Initialize learning rate scheduler if not provided\n", "    if scheduler is None:\n", "        scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "            optimizer, \n", "            max_lr=[5e-4, 5e-5],  # Different peak LR for model and criterion\n", "            total_steps=num_epochs * len(train_loader) // accumulation_steps,\n", "            pct_start=0.1,  # Warm up for 10% of training\n", "            div_factor=25.0,\n", "            final_div_factor=1e4,\n", "            anneal_strategy='cos'\n", "        )\n", "    \n", "    # Initialize early stopping\n", "    from collections import deque\n", "    patience = 10\n", "    best_val_score = float('inf')\n", "    no_improve_epochs = 0\n", "    best_epoch = 0\n", "    \n", "    # Performance tracking\n", "    speed_history = deque(maxlen=50)  # Track iterations/sec\n", "    \n", "    # Enable mixed precision training\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    \n", "    scaler = GradScaler()\n", "    \n", "    # Get autocast function\n", "    from torch.cuda.amp import autocast\n", "    \n", "    # Training history\n", "    history = {\n", "        'train_loss': [], 'val_loss': [],\n", "        'train_f1': [], 'val_f1': [],\n", "        'lr': [], 'loss_weights': [],\n", "        'instance_params': [],\n", "        'gpu_memory': [],\n", "        'speed': []\n", "    }\n", "    \n", "    # Main training loop\n", "    print(f\"Training for {num_epochs} epochs with gradient accumulation={accumulation_steps}:\")\n", "    progress_bar = tqdm(total=num_epochs, desc=\"Training progress\")\n", "    \n", "    import time\n", "    for epoch in range(num_epochs):\n", "        epoch_start = time.time()\n", "        batch_start = epoch_start\n", "        \n", "        # Training phase\n", "        model.train()\n", "        criterion.train()  # Enable gradient flow through criterion parameters\n", "        train_loss = 0.0\n", "        train_f1 = 0.0\n", "        batch_count = 0\n", "        \n", "        # Zero gradients at beginning of epoch\n", "        optimizer.zero_grad()\n", "        \n", "        \n", "            # Move data to device with non_blocking for async transfer\n", "            images = images.to(device, non_blocking=True)\n", "            \n", "            if isinstance(targets, torch.Tensor):\n", "                targets = targets.to(device, non_blocking=True)\n", "            else:\n", "                # Handle dictionary case\n", "                targets = {k: v.to(device, non_blocking=True) for k, v in targets.items()}\n", "            \n", "            # Forward pass with mixed precision\n", "            with autocast():\n", "                outputs = model(images)\n", "                \n", "                # Create target dictionary\n", "                if isinstance(targets, torch.Tensor):\n", "                    target_dict = {'masks': targets}\n", "                else:for i, (images, targets) in enumerate(train_loader):\n", "                    target_dict = targets\n", "                \n", "                # Calculate loss with adaptive weighting\n", "                loss, loss_components = criterion(outputs, target_dict)\n", "                # Normalize loss for gradient accumulation\n", "                loss = loss / accumulation_steps\n", "            \n", "            # Backward pass with gradient scaling\n", "            scaler.scale(loss).backward()\n", "            \n", "            # Only update weights after accumulation_steps or at the end of epoch\n", "            if (i + 1) % accumulation_steps == 0 or (i + 1 == len(train_loader)):\n", "                # Unscale optimizer for gradient clipping\n", "                scaler.unscale_(optimizer)\n", "                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "                \n", "                # Update weights\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "                optimizer.zero_grad()\n", "                \n", "                # Step scheduler\n", "                scheduler.step()\n", "            \n", "            # Calculate metrics\n", "            with torch.no_grad():\n", "                batch_f1 = calculate_spot_f1(\n", "                    outputs, targets,\n", "                    threshold=0.3 if epoch < 10 else 0.5\n", "                )\n", "            \n", "            # Update statistics (multiply by accumulation_steps to get correct loss)\n", "            train_loss += loss.item() * accumulation_steps\n", "            train_f1 += batch_f1\n", "            batch_count += 1\n", "            \n", "            # Speed monitoring (every 10 batches)\n", "            if i > 0 and i % 10 == 0:\n", "                batch_end = time.time()\n", "                imgs_per_sec = (10 * train_loader.batch_size) / (batch_end - batch_start)\n", "                speed_history.append(imgs_per_sec)\n", "                batch_start = batch_end\n", "        \n", "        # Calculate average training metrics\n", "        avg_train_loss = train_loss / batch_count\n", "        avg_train_f1 = train_f1 / batch_count\n", "        \n", "        # Validation phase\n", "        model.eval()\n", "        criterion.eval()\n", "        val_loss = 0.0\n", "        val_f1 = 0.0\n", "        val_batch_count = 0\n", "        \n", "        with torch.no_grad():\n", "            for images, targets in val_loader:\n", "                # Move data to device\n", "                images = images.to(device, non_blocking=True)\n", "                \n", "                if isinstance(targets, torch.Tensor):\n", "                    targets = targets.to(device, non_blocking=True)\n", "                else:\n", "                    targets = {k: v.to(device, non_blocking=True) for k, v in targets.items()}\n", "                \n", "                # Forward pass (no autocast needed for validation)\n", "                outputs = model(images)\n", "                \n", "                # Create proper target dict\n", "                if isinstance(targets, torch.Tensor):\n", "                    target_dict = {'masks': targets}\n", "                else:\n", "                    target_dict = targets\n", "                \n", "                # Calculate loss with adaptive weighting\n", "                loss, _ = criterion(outputs, target_dict)\n", "                \n", "                # Calculate metrics\n", "                batch_f1 = calculate_spot_f1(\n", "                    outputs, targets,\n", "                    threshold=0.5\n", "                )\n", "                \n", "                # Update statistics\n", "                val_loss += loss.item()\n", "                val_f1 += batch_f1\n", "                val_batch_count += 1\n", "        \n", "        # Calculate average validation metrics\n", "        avg_val_loss = val_loss / max(val_batch_count, 1)\n", "        avg_val_f1 = val_f1 / max(val_batch_count, 1)\n", "        \n", "        # Update history\n", "        current_lr = scheduler.get_last_lr()[0]\n", "        history['train_loss'].append(avg_train_loss)\n", "        history['val_loss'].append(avg_val_loss)\n", "        history['train_f1'].append(avg_train_f1)\n", "        history['val_f1'].append(avg_val_f1)\n", "        history['lr'].append(current_lr)\n", "        \n", "        # Memory tracking\n", "        if device.type == 'cuda':\n", "            current_memory = torch.cuda.memory_allocated(device) / 1024**2\n", "            peak_memory = torch.cuda.max_memory_allocated(device) / 1024**2\n", "            history['gpu_memory'].append(peak_memory)\n", "            \n", "            # Clear cache if memory gets too high\n", "            if current_memory > 0.8 * torch.cuda.get_device_properties(device).total_memory / 1024**2:\n", "                torch.cuda.empty_cache()\n", "        \n", "        # Log adaptive parameters\n", "        if hasattr(criterion, 'get_weights'):\n", "            weights = criterion.get_weights()\n", "            history['loss_weights'].append(weights)\n", "            \n", "            # Log weights to tensorboard\n", "            for name, value in weights.items():\n", "                writer.add_scalar(f'LossWeights/{name}', value, epoch)\n", "        \n", "        if hasattr(model, 'learn_instance_params') and model.learn_instance_params:\n", "            instance_params = {\n", "                'boundary_factor': torch.sigmoid(model.boundary_factor).item(),\n", "                'flow_div_factor': torch.sigmoid(model.flow_div_factor).item(),\n", "                'distance_factor': torch.sigmoid(model.distance_factor).item(),\n", "                'min_distance': max(1, min(10, model.min_distance.item()))\n", "            }\n", "            history['instance_params'].append(instance_params)\n", "            \n", "            # Log instance parameters to tensorboard\n", "            for name, value in instance_params.items():\n", "                writer.add_scalar(f'InstanceParams/{name}', value, epoch)\n", "        \n", "        # Compute speed\n", "        epoch_time = time.time() - epoch_start\n", "        imgs_per_sec = len(train_loader) * train_loader.batch_size / epoch_time\n", "        history['speed'].append(imgs_per_sec)\n", "        \n", "        # Log epoch metrics\n", "        writer.add_scalar('Loss/train', avg_train_loss, epoch)\n", "        writer.add_scalar('Loss/val', avg_val_loss, epoch)\n", "        writer.add_scalar('F1/train', avg_train_f1, epoch)\n", "        writer.add_scalar('F1/val', avg_val_f1, epoch)\n", "        writer.add_scalar('LearningRate', current_lr, epoch)\n", "        writer.add_scalar('Performance/images_per_sec', imgs_per_sec, epoch)\n", "        if device.type == 'cuda':\n", "            writer.add_scalar('Memory/peak_mb', peak_memory, epoch)\n", "        \n", "        # Update progress bar\n", "        progress_bar.update(1)\n", "        progress_bar.set_description(\n", "            f\"Epoch {epoch+1}/{num_epochs}: \"\n", "            f\"Loss: {avg_train_loss:.4f}/{avg_val_loss:.4f}, \"\n", "            f\"F1: {avg_train_f1:.4f}/{avg_val_f1:.4f}, \"\n", "            f\"LR: {current_lr:.6f}, \"\n", "            f\"Speed: {imgs_per_sec:.1f} img/s\"\n", "        )\n", "        \n", "        # Check early stopping\n", "        val_score = avg_val_loss  # Can use F1 instead: -avg_val_f1\n", "        if val_score < best_val_score:\n", "            best_val_score = val_score\n", "            best_epoch = epoch\n", "            no_improve_epochs = 0\n", "            \n", "            # Save best model\n", "            torch.save({\n", "                'epoch': epoch + 1,\n", "                'model_state_dict': model.state_dict(),\n", "                'criterion_state_dict': criterion.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'val_loss': avg_val_loss,\n", "                'val_f1': avg_val_f1,\n", "                'history': history\n", "            }, os.path.join(checkpoint_dir, 'best_model.pth'))\n", "            \n", "            print(f\"\\n[Epoch {epoch+1}] New best model saved: val_loss={avg_val_loss:.4f}, val_f1={avg_val_f1:.4f}\")\n", "        else:\n", "            no_improve_epochs += 1\n", "            \n", "        # Save periodic checkpoints\n", "        if (epoch + 1) % 10 == 0 or epoch == num_epochs - 1:\n", "            torch.save({\n", "                'epoch': epoch + 1,\n", "                'model_state_dict': model.state_dict(),\n", "                'criterion_state_dict': criterion.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'val_loss': avg_val_loss,\n", "                'val_f1': avg_val_f1,\n", "                'history': history\n", "            }, os.path.join(checkpoint_dir, f'checkpoint_epoch{epoch+1}.pth'))\n", "        \n", "        # Check early stopping\n", "        if no_improve_epochs >= patience and epoch >= 30:\n", "            print(f\"\\nEarly stopping triggered after {epoch+1} epochs without improvement\")\n", "            break\n", "    \n", "    # Final memory report\n", "    if device.type == 'cuda':\n", "        final_memory = torch.cuda.memory_allocated(device) / 1024**2\n", "        peak_memory = torch.cuda.max_memory_allocated(device) / 1024**2\n", "        print(f\"Final GPU memory: {final_memory:.2f} MB\")\n", "        print(f\"Peak GPU memory: {peak_memory:.2f} MB\")\n", "        print(f\"Average speed: {sum(history['speed'])/len(history['speed']):.1f} images/sec\")\n", "    \n", "    # Disable benchmarking when done\n", "    torch.backends.cudnn.benchmark = False\n", "    \n", "    # Close tensorboard writer\n", "    writer.close()\n", "    \n", "    # Load best model\n", "    checkpoint = torch.load(os.path.join(checkpoint_dir, 'best_model.pth'))\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "    criterion.load_state_dict(checkpoint['criterion_state_dict'])\n", "    \n", "    print(f\"\\nTraining complete. Best model from epoch {best_epoch+1} with val_loss={best_val_score:.4f}\")\n", "    \n", "    return model, criterion, history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_instances(model, image, device, threshold=0.5):\n", "    \"\"\"\n", "    Predict instance segmentation for an image\n", "    \n", "    Args:\n", "        model: Trained spot instance model\n", "        image: Input image (numpy array)\n", "        device: Device to run inference on\n", "        threshold: <PERSON><PERSON><PERSON><PERSON> for heatmap\n", "        \n", "    Returns:\n", "        Dictionary with instance map and detected spots\n", "    \"\"\"\n", "    # Prepare image tensor\n", "    if len(image.shape) == 2:\n", "        # Add batch and channel dimensions for single image\n", "        image_tensor = torch.from_numpy(image).float().unsqueeze(0).unsqueeze(0)\n", "    else:\n", "        # Add batch dimension for image with channel\n", "        image_tensor = torch.from_numpy(image).float().unsqueeze(0)\n", "    \n", "    # Normalize image if needed\n", "    if image_tensor.max() > 1.0:\n", "        image_tensor = image_tensor / 255.0\n", "    \n", "    # Move to device\n", "    image_tensor = image_tensor.to(device)\n", "    \n", "    # Predict\n", "    model.eval()\n", "    with torch.no_grad():\n", "        outputs = model(image_tensor)\n", "    \n", "    # Get heatmap\n", "    if isinstance(outputs, dict):\n", "        heatmap = outputs['heatmap'][0, 0].detach().cpu().numpy()\n", "    else:\n", "        heatmap = outputs[0, 0].detach().cpu().numpy()\n", "    \n", "    # Apply sigmoid if needed\n", "    if heatmap.min() < 0 or heatmap.max() > 1:\n", "        heatmap = 1 / (1 + np.exp(-heatmap))\n", "    \n", "    # T<PERSON><PERSON><PERSON> heatmap\n", "    binary_mask = heatmap > threshold\n", "    \n", "    \n", "    \n", "    instance_map = label(binary_mask)\n", "    \n", "    # Get spot properties\n", "    props = regionprops(instance_map, intensity_image=image)\n", "    \n", "    # Extract spot information\n", "    spots = []\n", "    for prop in props:\n", "        # Filter out very small objects\n", "        if prop.area >= 3:  # Minimum size threshold\n", "            spots.append({\n", "                'id': prop.label,\n", "                'centroid': prop.centroid,\n", "                'area': prop.area,\n", "                'intensity': prop.mean_intensity,\n", "                'bbox': prop.bbox\n", "            })\n", "    \n", "    return {\n", "        'heatmap': heatmap,\n", "        'binary_mask': binary_mask,\n", "        'instance_map': instance_map,\n", "        'spots': spots\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Model_spot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}