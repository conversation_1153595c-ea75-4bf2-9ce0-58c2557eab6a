import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import Normalize
import tifffile
import os
import argparse

def create_overlay_comparison(moving, transformed, reference, output_path=None):
    """
    Create overlay visualizations comparing before and after registration.
    
    Args:
        moving: Original moving image before registration
        transformed: Registered image after transformation
        reference: Reference image used for registration
        output_path: Path to save the comparison image (optional)
        
    Returns:
        Tuple of (before_overlay, after_overlay) images
    """
    # Normalize all images to 0-1 range for consistent visualization
    norm = Normalize(vmin=0, vmax=max(np.max(moving), np.max(transformed), np.max(reference)))
    
    moving_norm = norm(moving)
    transformed_norm = norm(transformed)
    reference_norm = norm(reference)
    
    # Create RGB overlays (reference=red, moving/transformed=green)
    before_overlay = np.zeros((*moving.shape, 3), dtype=np.float32)
    after_overlay = np.zeros((*moving.shape, 3), dtype=np.float32)
    
    # Red channel = reference
    before_overlay[:,:,0] = reference_norm
    after_overlay[:,:,0] = reference_norm
    
    # Green channel = moving/transformed
    before_overlay[:,:,1] = moving_norm
    after_overlay[:,:,1] = transformed_norm
    
    # Create figure with side-by-side comparison
    if output_path is not None:
        plt.figure(figsize=(16, 8))
        
        plt.subplot(1, 3, 1)
        plt.title("Before Registration\n(Red=Reference, Green=Moving)")
        plt.imshow(before_overlay)
        plt.axis('off')
        
        plt.subplot(1, 3, 2)
        plt.title("After Registration\n(Red=Reference, Green=Transformed)")
        plt.imshow(after_overlay)
        plt.axis('off')
        
        plt.subplot(1, 3, 3)
        plt.title("Difference (After - Before)")
        diff = np.abs(after_overlay - before_overlay)
        plt.imshow(diff, cmap='viridis')
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=150)
        plt.close()
        
    return before_overlay, after_overlay

def main():
    parser = argparse.ArgumentParser(description='Create overlay comparison of registration results')
    parser.add_argument('--reference', required=True, help='Path to reference image')
    parser.add_argument('--moving', required=True, help='Path to moving image (before registration)')
    parser.add_argument('--transformed', required=True, help='Path to transformed image (after registration)')
    parser.add_argument('--output', required=True, help='Path to save output comparison image')
    
    args = parser.parse_args()
    
    # Load images
    reference = tifffile.imread(args.reference)
    moving = tifffile.imread(args.moving)
    transformed = tifffile.imread(args.transformed)
    
    # Make sure all images are 2D
    if reference.ndim > 2:
        reference = reference.squeeze()
    if moving.ndim > 2:
        moving = moving.squeeze()
    if transformed.ndim > 2:
        transformed = transformed.squeeze()
    
    # Create overlay comparison
    create_overlay_comparison(moving, transformed, reference, args.output)
    print(f"Comparison image saved to {args.output}")

if __name__ == "__main__":
    main()