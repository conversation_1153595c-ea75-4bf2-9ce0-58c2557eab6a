{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from skimage import io\n", "from skimage.color import rgb2hed, hed2rgb\n", "\n", "# Load your TIFF image\n", "image_path = 'path/to/your/image.tiff'\n", "ihc_rgb = io.imread(image_path)\n", "\n", "# Get the shape for QC\n", "print(f\"Image shape: {ihc_rgb.shape}\")\n", "\n", "# Check if the image is RGB (3 channels)\n", "if ihc_rgb.shape[-1] != 3:\n", "    raise ValueError(\"The image should have 3 channels (RGB)\")\n", "\n", "# Separate the stains from the IHC image\n", "ihc_hed = rgb2hed(ihc_rgb)\n", "\n", "# Create an RGB image for each of the stains\n", "null = np.zeros_like(ihc_hed[:, :, 0])\n", "ihc_h = hed2rgb(np.stack((ihc_hed[:, :, 0], null, null), axis=-1))\n", "ihc_e = hed2rgb(np.stack((null, ihc_hed[:, :, 1], null), axis=-1))\n", "ihc_d = hed2rgb(np.stack((null, null, ihc_hed[:, :, 2]), axis=-1))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display\n", "fig, axes = plt.subplots(2, 2, figsize=(7, 6), sharex=True, sharey=True)\n", "ax = axes.ravel()\n", "\n", "ax[0].imshow(ihc_rgb)\n", "ax[0].set_title(\"Original image\")\n", "\n", "ax[1].imshow(ihc_h)\n", "ax[1].set_title(\"Hematoxylin\")\n", "\n", "ax[2].imshow(ihc_e)\n", "ax[2].set_title(\"<PERSON><PERSON><PERSON>\")\n", "----\n", "ax[3].imshow(ihc_d)\n", "ax[3].set_title(\"DAB\")\n", "\n", "for a in ax.ravel():\n", "    a.axis('off')\n", "\n", "fig.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from skimage import io\n", "from skimage.color import rgb2hed\n", "from skimage.exposure import rescale_intensity\n", "import os\n", "\n", "\n", "# Rescale intensity for each channel\n", "h = rescale_intensity(\n", "    ihc_hed[:, :, 0],\n", "    out_range=(0, 1),\n", "    in_range=(0, np.percentile(ihc_hed[:, :, 0], 99)),\n", ")\n", "e = rescale_intensity(\n", "    ihc_hed[:, :, 1],\n", "    out_range=(0, 1),\n", "    in_range=(0, np.percentile(ihc_hed[:, :, 1], 99)),\n", ")\n", "d = rescale_intensity(\n", "    ihc_hed[:, :, 2],\n", "    out_range=(0, 1),\n", "    in_range=(0, np.percentile(ihc_hed[:, :, 2], 99)),\n", ")\n", "\n", "# Get the directory and base filename\n", "output_dir = os.path.dirname(image_path)\n", "base_filename = os.path.splitext(os.path.basename(image_path))[0]\n", "\n", "# Save each channel as a separate 8-bit TIFF file\n", "io.imsave(os.path.join(output_dir, f\"{base_filename}_merged_ch00.tif\"), (h * 255).astype(np.uint8))\n", "io.imsave(os.path.join(output_dir, f\"{base_filename}_merged_ch01.tif\"), (e * 255).astype(np.uint8))\n", "io.imsave(os.path.join(output_dir, f\"{base_filename}_merged_ch02.tif\"), (d * 255).astype(np.uint8))\n", "\n", "print(f\"8-bit grayscale channels saved successfully in: {output_dir}\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Display the channels\n", "fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "axes[0].imshow(h, cmap='gray')\n", "axes[0].set_title(\"Hematoxylin\")\n", "axes[1].imshow(e, cmap='gray')\n", "axes[1].set_title(\"<PERSON><PERSON><PERSON>\")\n", "axes[2].imshow(d, cmap='gray')\n", "axes[2].set_title(\"DAB\")\n", "\n", "for ax in axes:\n", "    ax.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}