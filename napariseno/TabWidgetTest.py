import os
import sys

import napari
from qtpy.QtWidgets import QApplication

if __name__ == "__main__":
    viewer = napari.Viewer()

    # app = QApplication(sys.argv)
    from src.napari_spot_detector.SenoquantSpotDetectorTabWidget import SenoquantSpotDetectorTabWidget

    dWidget = SenoquantSpotDetectorTabWidget(viewer=viewer)

    viewer.window.add_dock_widget(dWidget)
    # dWidget.setCurrentIndex(1)
    # u: UDWTWaveletDetector = dWidget.currentWidget()
    # u.displayBinaryImageCheckBox.setChecked(True)
    # u.computeWATwithROICheckBox.setChecked(True)
    # u.cropImageCheckBox.setChecked(True)
    # u.filterDropDownMenu.setCurrentText('SizeFiltering')
    # f: SizeFiltering = u.filter
    # f.maxValueTextField.setText('1500')
    # t = os.path.splitext(u.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit.text())
    # u.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit.setText(t[0] + "_" +
    #                                                                             str(random.randint(100, 1000)) +
    #                                                                             "." + t[1])
    # u.output_widget.xml_file_naming.setChecked(True)


    napari.run()
