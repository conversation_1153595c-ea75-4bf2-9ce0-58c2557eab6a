#!/usr/bin/env python
# coding: utf-8

# In[1]:


import datetime
import traceback
from typing import List

import napari
import numpy as np
from IPython import get_ipython

from src.napari_spot_detector.UDWTWaveletDetector import UDWTWaveletDetector
from src.napari_spot_detector.tokens import *
from src.napari_spot_detector.images import *
from src.napari_spot_detector.UDWT.B3SplineUDWT import *
from src.napari_spot_detector.UDWT.UDWTWaveletCore import UDWTWaveletCore

import copy
import cv2


# In[2]:

#
# get_ipython().run_line_magic('load_ext', 'autoreload')
#
#
# # In[3]:
#
#
# get_ipython().run_line_magic('autoreload', '2')


# In[4]:


viewer = napari.Viewer()
dWidget = UDWTWaveletDetector(viewer)
viewer.window.add_dock_widget(dWidget)
napari.run()


# In[5]:


dWidget.gdt = GlobalDetectionToken()
dWidget.gdt.inputSequence = \
    Sequence(imageName=dWidget.input_box.currentText(), viewer=dWidget.viewer)
dWidget.gdt.inputComputationSequence = dWidget.gdt.inputSequence


# In[6]:


sequence = dWidget.gdt.inputComputationSequence
useROIWat = dWidget.computeWATwithROICheckBox.isChecked()
UDWTScaleArrayList = dWidget.UDWTScaleArrayList
dWidget.waveletCore.UDWTScaleArrayList = UDWTScaleArrayList
numScales = dWidget.waveletCore.getNumberOfMaxEnabledScale()
frame = 0
negative = dWidget.detectRadio()


# In[7]:


# Call
detectionList: DetectionSpot = DetectionSpot(image=sequence)  # napari points
rois: List[ROI] = list()  # Threshold and Rectangles Boundaries TODO: Create ROI class
bounds = [(0, 0), (sequence.getSizeX(),
                   sequence.getSizeY())]  # Returns 2D bounds of sequence {0, 0, sizeX, sizeY}
binaryDetectionResultCopy: np.ndarray
if useROIWat:
    # rois.extend(self.sequence.getROIs())
    if len(rois) == 0:
        rois.append(ROI(bounds))  # TODO: Initialized for 3D

# Updated Wavelet Transform algorithm
image: np.ndarray = sequence.getAllImage()
dataIn = image.reshape(image.shape[0], -1).astype(Sequence.data_type_np)

try:
    scales: np.ndarray = b3WaveletScales3D_C(dataIn, sequence.getSizeX(),
                                           sequence.getSizeY(),
                                           sequence.getSizeZ(),
                                           numScales)
except WaveletConfigException as el:
    print(el)
    traceback.print_exc()
    # return detectionList
except Exception:
    traceback.print_exc()
    # return detectionList

coefficients = b3WaveletCoefficients3D(scales, dataIn, numScales,
                                       sequence.getSizeX() *
                                       sequence.getSizeY(),
                                       sequence.getSizeZ())

'''
    Apply threshold to coefficients but not last one ( residual )
'''
masks = UDWTWaveletCore.buildBinaryMask(bounds, rois, frame, sequence.getSizeZ())

for scale in range(coefficients.shape[0] - 1):
    if negative:
        coefficients[scale] = -1 * coefficients[scale]

    dWidget.waveletCore.filter_wat_matrix(coefficients[scale], scale,
                                    sequence.getSizeX(),
                                    sequence.getSizeY(), masks)


#  fill of 0 the residual image
# c: np.ndarray = coefficients[len(coefficients) - 1]
coefficients[len(coefficients) - 1][:] = np.zeros(shape=coefficients[len(coefficients) - 1].shape[1])

reconstruction: np.ndarray = b3SpotConstruction3DMatrix(coefficients, numScales,
                                                        sequence.getSizeX() * sequence.getSizeY(),
                                                        sequence.getSizeZ(),
                                                        dWidget.UDWTScaleArrayList)
detectionList.reconstruction = np.copy(reconstruction).reshape((sequence.getSizeZ(),
                                                                sequence.getSizeY(),
                                                                sequence.getSizeX()))
dWidget.waveletCore.reconstruction = detectionList.reconstruction

# Binarisation de la reconstruction.
binaryDetectionResult: np.ndarray = reconstruction.astype(np.uint8)
binaryDetectionResult[binaryDetectionResult != 0] = 1

binaryDetectionResult3D: np.ndarray = binaryDetectionResult.reshape((sequence.getSizeZ(), sequence.getSizeY(),
                                                                     sequence.getSizeX()))


# In[8]:


viewer.add_labels(binaryDetectionResult3D, scale=sequence.scale)


# In[9]:


detectionListCopy = copy.copy(detectionList)


# In[ ]:


st = datetime.datetime.now()
print('Starting Connected Component')

dWidget.waveletCore.UDWTWaveletCoreProcessors().extract_cc3d(binaryDetectionResult3D, image, detectionListCopy)

et = datetime.datetime.now()
elapsed_time = et - st
print('Connected Component Finish:', elapsed_time, 'Seconds')


# In[ ]:


detectionListCopy.components.max()


# In[ ]:


detectionListCopy2 = copy.copy(detectionList)


# In[ ]:


print(binaryDetectionResult3D.max())
print(binaryDetectionResult3D.min())


# In[ ]:


st = datetime.datetime.now()
print('Starting Connected Component')

dWidget.waveletCore.UDWTWaveletCoreProcessors().extractConnectedComponents(binaryDetectionResult3D,
                                                                           image, detectionListCopy2, sequence)


et = datetime.datetime.now()
elapsed_time = et - st
print('Connected Component Finish:', elapsed_time, 'Seconds')


# In[ ]:


print(detectionListCopy2.components.max())


# In[ ]:


g = np.unique(detectionListCopy2.components)


# In[ ]:


detectionListCopy3 = copy.copy(detectionList)


# In[ ]:


def extract(img: np.ndarray, detectionList: DetectionSpot, originalImage: np.ndarray):
            detectionList.components = np.zeros(img.shape)
            results = []
            average_intensity = []
            total_intensity = []
            min_intensity = []
            max_intensity = []
            volumes = []
            centroids = np.empty(shape=(0, 3))
            counts = np.empty(shape=(0, 1))
            intensity = np.empty(shape=(0, 4))

            detectionList.components = np.zeros(img.shape, dtype="uint8")
            for i in range(img.shape[0]):
                slice = img[i]
                num_labels, label_ids, stats, centroids_array = \
                    cv2.connectedComponentsWithStatsWithAlgorithm(slice.astype(np.uint8), 8,
                                                                  cv2.CV_32S, ccltype=cv2.CCL_WU)




                results.append((num_labels, label_ids, stats, centroids_array))
            first = True
            for i, analyze in enumerate(results):
                num_label, label_id, stat, centroid = analyze
                centroid_fix = np.zeros(shape=(num_label, 3), dtype=Sequence.data_type_np)
                voxel_counts = np.zeros(shape=(num_label, 1), dtype=np.uint8)
                num = 0
                check = False

                for j in range(1, num_label):
                    check = True
                    mask = label_id == j

                    mask_img = originalImage[i] * mask

                    mean_ = np.mean(mask_img)
                    average_intensity.append(mean_)

                    sum_ = np.sum(mask_img)
                    total_intensity.append(sum_)

                    min_ = np.min(mask_img)
                    min_intensity.append(min_)

                    max_ = np.max(mask_img)
                    max_intensity.append(max_)

                    intensity_stats = np.array([[min_, max_, mean_, sum_]])
                    intensity = np.concatenate((intensity, intensity_stats), axis=0)

                    volumes.append(stat[j][cv2.CC_STAT_AREA])
                    voxel_counts[j] = np.count_nonzero(mask)
                    num = num + 1

                # componentMask = (~(label_id == 0)) * (detectionList.N - 1)



                if first:
                    first = False
                    componentMask = (label_id != 0) * 0
                else:
                    componentMask = (label_id != 0) * (detectionList.N - 1)

                detectionList.components[i] = (label_id + componentMask)

                if check:
                    centroid_fix[:, 0] = i
                    centroid_fix[:, 1] = centroid[:, 1]
                    centroid_fix[:, 2] = centroid[:, 0]
                    detectionList.N = detectionList.N + num_label


                    centroids = np.concatenate((centroids, centroid_fix), axis=0)
                    counts = np.concatenate((counts, voxel_counts), axis=0)



            detectionList.stats = {'centroids': centroids, 'voxel_counts': counts}
            detectionList.volumes = np.array(volumes)
            detectionList.intensity = intensity

st = datetime.datetime.now()
print('Starting Connected Component')

# dWidget.waveletCore.UDWTWaveletCoreProcessors().extract(binaryDetectionResult3D, detectionListCopy3, image)
extract(binaryDetectionResult3D, detectionListCopy3, image)


et = datetime.datetime.now()
elapsed_time = et - st
print('Connected Component Finish:', elapsed_time, 'Seconds')


# In[ ]:


print(detectionListCopy3.components.max())


# In[ ]:


# detectionListCopy3


# In[ ]:





# In[ ]:




