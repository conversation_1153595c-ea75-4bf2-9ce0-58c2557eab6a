# For more information about tox, see https://tox.readthedocs.io/en/latest/
[tox]
envlist = py{38,39,310}-{linux,macos,windows}
isolated_build=true

[gh-actions]
python =
    3.8: py38
    3.9: py39
    3.10: py310

[gh-actions:env]
PLATFORM =
    ubuntu-latest: linux
    macos-latest: macos
    windows-latest: windows

[testenv]
platform =
    macos: darwin
    linux: linux
    windows: win32
passenv =
    CI
    GITHUB_ACTIONS
    DISPLAY XAUTHORITY
    NUMPY_EXPERIMENTAL_ARRAY_FUNCTION
    PYVISTA_OFF_SCREEN
extras =
    testing
commands = pytest -v --color=yes --cov=napari_senoquant --cov-report=xml
