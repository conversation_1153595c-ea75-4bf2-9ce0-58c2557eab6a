[metadata]
name = napari-senoquant
version = 0.0.1
description = Plugin for TAF.
long_description = file: README.md
long_description_content_type = text/markdown

author = <PERSON>
author_email = <EMAIL>
license = MPL-2.0
license_files = LICENSE
classifiers =
    Development Status :: 2 - Pre-Alpha
    Framework :: napari
    Intended Audience :: Developers
    License :: OSI Approved :: Mozilla Public License 2.0 (MPL 2.0)
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Topic :: Scientific/Engineering :: Image Processing


[options]
packages = find:
install_requires =
    numpy
    magicgui
    qtpy

python_requires = >=3.8
include_package_data = True
package_dir =
    =src

# add your package requirements here

[options.packages.find]
where = src

[options.entry_points]
napari.manifest =
    napari-senoquant = napari_senoquant:napari.yaml

[options.extras_require]
testing =
    tox
    pytest  # https://docs.pytest.org/en/latest/contents.html
    pytest-cov  # https://pytest-cov.readthedocs.io/en/latest/
    pytest-qt  # https://pytest-qt.readthedocs.io/en/latest/
    napari
    pyqt5


[options.package_data]
* = *.yaml
