name: starGPU
channels:
  - conda-forge
  - defaults
dependencies:
  - alabaster=0.7.12=pyhd3eb1b0_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py38h2bbff1b_0
  - arrow=1.2.2=pyhd3eb1b0_0
  - astroid=2.11.7=py38haa95532_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - atomicwrites=1.4.0=py_0
  - attrs=21.4.0=pyhd3eb1b0_0
  - autopep8=1.6.0=pyhd3eb1b0_1
  - babel=2.9.1=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - bcrypt=3.2.0=py38h2bbff1b_1
  - beautifulsoup4=4.11.1=py38haa95532_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - black=22.6.0=py38haa95532_0
  - bleach=4.1.0=pyhd3eb1b0_0
  - brotlipy=0.7.0=py38h2bbff1b_1003
  - ca-certificates=2022.07.19=haa95532_0
  - certifi=2022.9.24=py38haa95532_0
  - cffi=1.15.1=py38h2bbff1b_0
  - chardet=4.0.0=py38haa95532_1003
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py38haa95532_0
  - cloudpickle=2.0.0=pyhd3eb1b0_0
  - colorama=0.4.5=py38haa95532_0
  - console_shortcut=0.1.1=4
  - cookiecutter=1.7.3=pyhd3eb1b0_0
  - cryptography=37.0.1=py38h21b164f_0
  - debugpy=1.5.1=py38hd77b12b_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.4=pyhd3eb1b0_0
  - docutils=0.18.1=py38haa95532_3
  - entrypoints=0.4=py38haa95532_0
  - executing=0.8.3=pyhd3eb1b0_0
  - flake8=4.0.1=pyhd3eb1b0_1
  - glib=2.69.1=h5dc1a3c_1
  - gst-plugins-base=1.18.5=h9e645db_0
  - gstreamer=1.18.5=hd78058f_0
  - icu=58.2=ha925a31_3
  - idna=3.4=py38haa95532_0
  - imagesize=1.4.1=py38haa95532_0
  - importlib-metadata=4.11.3=py38haa95532_0
  - importlib_metadata=4.11.3=hd3eb1b0_0
  - importlib_resources=5.2.0=pyhd3eb1b0_1
  - inflection=0.5.1=py38haa95532_0
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.15.2=py38haa95532_0
  - ipython=7.31.1=py38haa95532_1
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - isort=5.9.3=pyhd3eb1b0_0
  - jedi=0.18.1=py38haa95532_1
  - jellyfish=0.9.0=py38h2bbff1b_0
  - jinja2=3.0.3=pyhd3eb1b0_0
  - jinja2-time=0.2.0=pyhd3eb1b0_3
  - jpeg=9e=h2bbff1b_0
  - jsonschema=4.16.0=py38haa95532_0
  - jupyter_client=7.3.5=py38haa95532_0
  - jupyter_core=4.11.1=py38haa95532_0
  - jupyterlab_pygments=0.1.2=py_0
  - keyring=23.4.0=py38haa95532_0
  - lazy-object-proxy=1.6.0=py38h2bbff1b_0
  - libffi=3.4.2=hd77b12b_4
  - libiconv=1.16=h2bbff1b_2
  - libogg=1.3.5=h2bbff1b_1
  - libpng=1.6.37=h2a8f88b_0
  - libsodium=1.0.18=h62dcd97_0
  - libspatialindex=1.9.3=h6c2663c_0
  - libvorbis=1.3.7=he774522_0
  - libwebp=1.2.4=h2bbff1b_0
  - libwebp-base=1.2.4=h2bbff1b_0
  - libxml2=2.9.14=h0ad7f3c_0
  - libxslt=1.1.35=h2bbff1b_0
  - lz4-c=1.9.3=h2bbff1b_1
  - markupsafe=2.1.1=py38h2bbff1b_0
  - matplotlib-inline=0.1.6=py38haa95532_0
  - mccabe=0.7.0=pyhd3eb1b0_0
  - mistune=0.8.4=py38he774522_1000
  - mypy_extensions=0.4.3=py38haa95532_1
  - nbclient=0.5.13=py38haa95532_0
  - nbconvert=6.4.4=py38haa95532_0
  - nbformat=5.5.0=py38haa95532_0
  - nest-asyncio=1.5.5=py38haa95532_0
  - notebook=6.4.12=py38haa95532_0
  - numpydoc=1.4.0=py38haa95532_0
  - openssl=1.1.1q=h2bbff1b_0
  - packaging=21.3=pyhd3eb1b0_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - paramiko=2.8.1=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pathspec=0.9.0=py38haa95532_0
  - pcre=8.45=hd77b12b_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pkgutil-resolve-name=1.3.10=py38haa95532_0
  - platformdirs=2.5.2=py38haa95532_0
  - pluggy=1.0.0=py38haa95532_1
  - ply=3.11=py38_0
  - poyo=0.5.0=pyhd3eb1b0_0
  - prometheus_client=0.14.1=py38haa95532_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - psutil=5.9.0=py38h2bbff1b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycodestyle=2.8.0=pyhd3eb1b0_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pydocstyle=6.1.1=pyhd3eb1b0_0
  - pyflakes=2.4.0=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pylint=2.14.5=py38haa95532_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pynacl=1.5.0=py38h8cc25b3_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pyparsing=3.0.9=py38haa95532_0
  - pyqt=5.15.7=py38hd77b12b_0
  - pyqt5-sip=12.11.0=py38hd77b12b_0
  - pyqtwebengine=5.15.7=py38hd77b12b_0
  - pyrsistent=0.18.0=py38h196d8e1_0
  - pysocks=1.7.1=py38haa95532_0
  - python=3.8.13=h6244533_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py38haa95532_0
  - python-lsp-black=1.2.1=py38haa95532_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.5.0=py38haa95532_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - pytz=2022.1=py38haa95532_0
  - pywin32=302=py38h2bbff1b_2
  - pywin32-ctypes=0.2.0=py38_1000
  - pywinpty=2.0.2=py38h5da7b33_0
  - pyyaml=6.0=py38h2bbff1b_1
  - pyzmq=23.2.0=py38hd77b12b_0
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.1.10=pyhd3eb1b0_0
  - qt-main=5.15.2=he8e5bd7_7
  - qt-webengine=5.15.9=hb9a9bb5_4
  - qtawesome=1.0.3=pyhd3eb1b0_0
  - qtconsole=5.3.2=py38haa95532_0
  - qtpy=2.2.0=py38haa95532_0
  - qtwebkit=5.212=h3ad3cdb_4
  - requests=2.28.1=py38haa95532_0
  - rope=0.22.0=pyhd3eb1b0_0
  - rtree=0.9.7=py38h2eaa2aa_1
  - send2trash=1.8.0=pyhd3eb1b0_1
  - setuptools=63.4.1=py38haa95532_0
  - sip=6.6.2=py38hd77b12b_0
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.3.2.post1=py38haa95532_0
  - sphinx=5.0.2=py38haa95532_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - spyder=5.3.3=py38haa95532_0
  - spyder-kernels=2.3.3=py38haa95532_0
  - sqlite=3.39.3=h2bbff1b_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - terminado=0.13.1=py38haa95532_0
  - testpath=0.6.0=py38haa95532_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tinycss=0.4=pyhd3eb1b0_1002
  - toml=0.10.2=pyhd3eb1b0_0
  - tomli=2.0.1=py38haa95532_0
  - tomlkit=0.11.1=py38haa95532_0
  - tornado=6.2=py38h2bbff1b_0
  - traitlets=5.1.1=pyhd3eb1b0_0
  - ujson=5.4.0=py38hd77b12b_0
  - unidecode=1.2.0=pyhd3eb1b0_0
  - urllib3=1.26.12=py38haa95532_0
  - vc=14.2=h21ff451_1
  - vs2015_runtime=14.27.29016=h5e58377_2
  - watchdog=2.1.6=py38haa95532_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py38_1
  - whatthepatch=1.0.2=py38haa95532_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - win_inet_pton=1.1.0=py38haa95532_0
  - wincertstore=0.2=py38haa95532_2
  - winpty=0.4.3=4
  - yaml=0.2.5=he774522_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - zeromq=4.3.4=hd77b12b_0
  - zipp=3.8.0=py38haa95532_0
  - zlib=1.2.12=h8cc25b3_3
  - zstd=1.5.2=h19a0ad4_0
  - pip:
    - absl-py==1.3.0
    - aicsimageio==4.9.2
    - aicspylibczi==3.0.5
    - aiohttp==3.8.3
    - aiosignal==1.2.0
    - annotator==0.14.2
    - appdirs==1.4.4
    - asciitree==0.3.3
    - astunparse==1.6.3
    - async-timeout==4.0.2
    - bfio==2.3.0
    - bioformats-jar==2020.5.27
    - build==0.8.0
    - cachetools==5.2.0
    - cachey==0.2.1
    - cellpose-vollseg==0.0.3
    - commonmark==0.9.1
    - connected-components-3d==3.10.3
    - contourpy==1.0.5
    - csbdeep==0.7.2
    - cycler==0.11.0
    - dask==2022.10.0
    - dnspython==2.2.1
    - docstring-parser==0.15
    - elasticsearch==1.9.0
    - elementpath==2.5.3
    - email-validator==1.3.0
    - fasteners==0.18
    - fastremap==1.13.3
    - flatbuffers==22.9.24
    - fonttools==4.37.4
    - freetype-py==2.3.0
    - frozenlist==1.3.1
    - fsspec==2022.10.0
    - gast==0.3.3
    - google-api-core==2.11.0
    - google-api-python-client==2.73.0
    - google-api-python-client-helpers==1.2.6
    - google-auth==2.16.0
    - google-auth-httplib2==0.1.0
    - google-auth-oauthlib==0.4.6
    - google-pasta==0.2.0
    - googleapis-common-protos==1.58.0
    - greenlet==1.1.3.post0
    - grpcio==1.32.0
    - h5py==2.10.0
    - heapdict==1.0.1
    - hsluv==5.0.3
    - httplib2==0.21.0
    - imagecodecs==2022.9.26
    - imageio==2.10.5
    - imageio-ffmpeg==0.4.7
    - ipywidgets==8.0.2
    - iso8601==1.1.0
    - jgo==1.0.4
    - jpype1==1.4.0
    - jupyterlab-widgets==3.0.3
    - keras==2.10.0
    - keras-preprocessing==1.1.2
    - kiwisolver==1.4.4
    - libclang==14.0.6
    - llvmlite==0.39.1
    - locket==1.0.0
    - lxml==4.9.1
    - magicgui==0.5.1
    - markdown==3.4.1
    - matplotlib==3.6.1
    - micromanager==0.1.13
    - mrc==0.2.1
    - multidict==6.0.2
    - napari==0.4.16
    - napari-aicsimageio==0.7.2
    - napari-console==0.0.6
    - napari-plugin-engine==0.2.0
    - napari_senoquant==0.0.1
    - napari-spot-detector==0.0.1
    - napari-svg==0.1.6
    - natsort==8.2.0
    - nd2==0.4.6
    - networkx==2.8.7
    - npe2==0.6.1
    - numba==0.56.3
    - numcodecs==0.10.2
    - numpy==1.23.4
    - oauthlib==3.2.2
    - ome-types==0.3.1
    - opencv-python-headless==********
    - opt-einsum==3.3.0
    - pandas==1.5.1
    - partd==1.3.0
    - pbr==5.11.1
    - pep517==0.13.0
    - pillow==8.4.0
    - pint==0.19.2
    - pip==22.3
    - pooch==1.6.0
    - positional==1.2.1
    - protobuf==3.19.6
    - psygnal==0.5.0
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pydantic==1.10.2
    - pydicom==2.3.0
    - pygit2==1.10.1
    - pyjwt==2.6.0
    - pymysql==1.0.2
    - pyopengl==3.1.6
    - pytomlpp==1.0.11
    - pywavelets==1.4.1
    - readlif==0.6.5
    - requests-oauthlib==1.3.1
    - resource-backed-dask-array==0.1.0
    - rich==12.6.0
    - rsa==4.9
    - scikit-image==0.19.3
    - scipy==1.9.3
    - scyjava==1.7.0
    - six==1.15.0
    - sqlalchemy==1.4.42
    - stardist==0.8.3
    - stardist-napari==2022.7.5
    - superqt==0.3.8
    - tenacity==8.1.0
    - tensorboard==2.10.1
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - tensorflow==2.10.0
    - tensorflow-estimator==2.10.0
    - tensorflow-gpu==2.10.0
    - tensorflow-io-gcs-filesystem==0.27.0
    - termcolor==1.1.0
    - tifffile==2022.4.8
    - toolz==0.12.0
    - torch==1.13.1
    - tqdm==4.64.1
    - typer==0.6.1
    - typing-extensions==4.4.0
    - uritemplate==4.1.1
    - vispy==0.10.0
    - vollseg==6.2.3
    - vollseg-napari==2.3.7
    - werkzeug==2.2.2
    - widgetsnbextension==4.0.3
    - wrapt==1.12.1
    - wurlitzer==3.0.2
    - xarray==2022.10.0
    - xmlschema==1.11.3
    - yarl==1.8.1
    - zarr==2.13.3
prefix: C:\ProgramData\Anaconda3\envs\starGPU
