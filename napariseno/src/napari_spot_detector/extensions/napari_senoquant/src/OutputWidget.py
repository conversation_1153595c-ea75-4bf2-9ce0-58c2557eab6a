from qtpy.QtWidgets import Q<PERSON>roup<PERSON>ox, QVBoxLayout, QGridLayout, \
    QPushButton, QLineEdit, QLabel, QFileDialog, QComboBox

import os


class FileSelector(QGroupBox):
    def __init__(self, call, group_name='Enable Automatic', button_name='Browse', label_name='Directory:',
                 checked=True, *args):
        super(FileSelector, self).__init__(*args)

        self.setTitle(group_name)
        self.setCheckable(True)
        self.setChecked(checked)
        self.setLayout(QGridLayout())

        # directory selection
        dir_btn = QPushButton(button_name)
        dir_btn.clicked.connect(call)
        self.dir_name_edit = QLineEdit()  # TODO: Have a default path

        self.layout().addWidget(QLabel(label_name), 1, 0)
        self.layout().addWidget(self.dir_name_edit, 1, 1)
        self.layout().addWidget(dir_btn, 1, 2)


class OutputWidget(QGroupBox):
    def __init__(self, input_box: QComboBox, *args):
        super(OutputWidget, self).__init__(*args)

        # InputBox
        self.input_box: QComboBox = input_box

        # Output
        self.setTitle('Output')
        # self.output_widget = QGroupBox('Output')
        # self.output_widget.setLayout(QVBoxLayout())
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        self.excel_output_settings = QGroupBox('Excel output settings:')
        self.excel_output_settings.setLayout(QVBoxLayout())
        # self.excel_output_settings.layout().addWidget(self.excel_output_settings)
        self.layout.addWidget(self.excel_output_settings)

        self.automatic_xls_file_naming = QGroupBox('Automatic XLSX file naming')
        self.automatic_xls_file_naming.setCheckable(True)
        self.automatic_xls_file_naming.setChecked(True)
        self.automatic_xls_file_naming.setLayout(QVBoxLayout())
        self.automatic_xls_file_naming_file_dialog = FileSelector(self.open_dir_dialog, group_name='Enable Automatic',
                                                                  button_name='Browse',
                                                                  label_name='Directory:', checked=True)
        self.automatic_xls_file_naming.layout().addWidget(self.automatic_xls_file_naming_file_dialog)

        self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)
        self.automatic_xls_file_naming_file_dialog_append = FileSelector(self.open_file_dialog,
                                                                         group_name='Append data to existing files',
                                                                         button_name='Browse', label_name='File:',
                                                                         checked=False)
        self.automatic_xls_file_naming.layout().addWidget(self.automatic_xls_file_naming_file_dialog_append)

        self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)

        # self.textDescription = QLabel("The XLS file will be saved in the 'save' folder of the original image. The "
        #                               "file name will be 'originalfilename.xls'. If an XLS file already exists, "
        #                               "it will be replaced.")
        self.xml_file_naming = FileSelector(self.save_file_dialog, group_name='Choose XML File',
                                            button_name='Browse', label_name='File:', checked=False)

        self.layout.addWidget(self.xml_file_naming)

    def open_dir_dialog(self):
        dir_name = QFileDialog.getExistingDirectory(self, "Select a Directory")
        if dir_name:
            import re
            pattern = '[-:._\\s]+'
            file_name = re.sub(pattern, "_", self.input_box.currentText())
            path = os.path.realpath(dir_name)
            self.automatic_xls_file_naming_file_dialog.dir_name_edit.setText(os.path.join(str(path),
                                                                                          file_name + ".xlsx"))

    def open_file_dialog(self):
        file_name, _ = QFileDialog.getOpenFileName(self, "Select a File", directory="", filter="Excel (*.xlsx)")
        if file_name and file_name != '':
            # path = Path(dir_name)
            path = os.path.realpath(file_name)
            self.automatic_xls_file_naming_file_dialog_append.dir_name_edit.setText(str(path))

    def save_file_dialog(self):
        file_name, _ = QFileDialog.getSaveFileName(self, "Select a File", directory="", filter="Excel (*.xlsx)")
        if file_name and file_name != '':
            # path = Path(dir_name)
            path = os.path.realpath(file_name)
            self.xml_file_naming.dir_name_edit.setText(str(path))
