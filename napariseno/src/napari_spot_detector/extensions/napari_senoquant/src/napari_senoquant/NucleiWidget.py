import os
import traceback
from typing import Final

from csbdeep.utils import normalize
from napari._qt.qthreading import thread_worker
from qtpy.QtGui import QDoubleValidator
from qtpy.QtWidgets import (
    QHBoxLayout,
    QPushButton,
    QWidget,
    QComboBox,
    QLabel,
    QVBoxLayout,
    QScrollArea,
    QTabWidget,
    QCheckBox,
    QSpinBox,
    QDoubleSpinBox,
    QGroupBox,
)
from qtpy.QtWidgets import QLineEdit
from stardist.models import StarDist3D, StarDist2D
import warnings
warnings.filterwarnings('ignore')
from ._widget import ProcessWidget

##modified to use the delanay models by antho
#modified version of config 2d including the convexity threshold and delaunay method

from stardist.models import Config2D # XXX
from scipy.spatial import Delaunay
class MyConfig(Config2D):
    def __init__(
        self,
        convexity_threshold=0.75,
        use_delaunay=True,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.convexity_threshold = convexity_threshold
        self.use_delaunay = use_delaunay

    def get_startdist(self, points):
        # Compute the convex hull of the points
        hull = ConvexHull(points)
        vertices = hull.vertices
        
        # Compute the convexity of the hull
        area_hull = hull.volume
        area_points = Delaunay(points).convex_hull.volume
        convexity = area_points / area_hull
        
        # Determine whether to use the Delaunay method or not based on the convexity threshold
        if convexity >= self.convexity_threshold:
            use_delaunay = self.use_delaunay
            if use_delaunay:
                # Use Delaunay triangulation to compute the distance map
                #tri = Delaunay(points)
                # Use Delaunay triangulation to compute the distance map more precise use that isntead : 
                tri = Delaunay(points, qhull_options='QJ')
                dist = tri.vertex_to_simplex
                return dist
            else:
                # Use the classic startdist algorithm to compute the distance map
                points = np.round(points).astype(int)
                n_pts = points.shape[0]
                dist = np.zeros((n_pts, n_pts))
                for i in range(n_pts):
                    for j in range(i+1, n_pts):
                        dist[i,j] = np.linalg.norm(points[i,:] - points[j,:])
                        dist[j,i] = dist[i,j]
                return dist
        else:
            # Compute the distance map using the convex hull vertices
            n_pts = len(vertices)
            dist = np.zeros((n_pts, n_pts))
            for i in range(n_pts):
                for j in range(i+1, n_pts):
                    dist[i,j] = np.linalg.norm(points[vertices[i],:] - points[vertices[j],:])
                    dist[j,i] = dist[i,j]
            return dist

TESTBUTTON = False
MODELS_DIR = os.path.join(
    os.path.join(
        os.path.join(os.path.dirname(__file__), os.pardir), os.pardir
    ),
    "models",
)
seno = dict()

class NucleiWidget(QScrollArea):
    TAB_NAME: Final = "Napari Senoquant: Nuclei"
    initList = True
    # enableAutoscale = False
    # global seno

    def __init__(self, napari_viewer, tab: QTabWidget):
        # self.seno['NucleiWidget'] = self
        self.tab = tab

        super().__init__()
        self.viewer = napari_viewer
        # self.seno = dict()

        if TESTBUTTON:
            TestButton = QPushButton("TEST")
            TestButton.clicked.connect(self.Test)
            TestBox = QHBoxLayout()
            TestBox.addWidget(TestButton)
        # Nuclei
        # Nuclei Image
        self.NucImageLabel = QLabel("Nuclei Image", self)
        self.NucImage = QComboBox()
        self.initListWidget(self.NucImage)
        # self.NucImage.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        self.NucImage.currentTextChanged.connect(self.nucleiSelected)

        NucImageBox = QHBoxLayout()
        NucImageBox.addWidget(self.NucImageLabel)
        NucImageBox.addWidget(self.NucImage)

        # Model
        self.modelList2D = [ # XXX
            "2D_versatile_fluo",
            "versatile2dcellsallantho",
            "versatile2dcellsallantho2",
            "versatile2dcellsallanthoMULIPLEGPUnolargenuclei05182022",
            "versatile2dcellsallanthoMULIPLEGPUnolargenucleiDELAUNAY0732rays",
        ]
        self.modelListDefault2D = (
            "versatile2dcellsallanthoMULIPLEGPUnolargenuclei05182022"
        )
        self.modelList3D = [
            "stardist3dhumanliversanthogpatch48160160",
            "stardistliver3d63xobjective",
            "stardist3dMOUSEliversanthogpatch64160160",
            "stardist3dcellsvariousshapes63xobjective3D",
            "stardist3dUNIVERSALliverspatch64160160",
            "stardist3dhumanliversanthogpatch48160160retrainedcuratedmousedata",
            "stardist3dhumanliversanthogpatch48160160curatedmousedatafromsratch",
        ]
        self.modelListDefault3D = "stardist3dhumanliversanthogpatch48160160"
        self.ModelLabel = QLabel("Model", self)
        self.ModelName = QComboBox()
        self.ModelName.currentTextChanged.connect(self.loadModel)

        # Normalization
        self.NormalizationPanel = QGroupBox("Image Normalization")
        self.NormalizationPanel.setLayout(QVBoxLayout())
        self.normLowLabel = QLabel("Percentile low", self)
        self.normLow = QDoubleSpinBox()
        self.normLow.setRange(1.0, 100.0)
        self.normLow.setDecimals(2)
        self.normLow.setSingleStep(0.1)
        self.normLow.setValue(1.0)
        NormalLowBox = QHBoxLayout()
        NormalLowBox.addWidget(self.normLowLabel)
        NormalLowBox.addWidget(self.normLow)
        self.normHighLabel = QLabel("Percentile High", self)
        self.normHigh = QDoubleSpinBox()
        self.normHigh.setRange(1.0, 100.0)
        self.normHigh.setDecimals(2)
        self.normHigh.setSingleStep(0.1)
        self.normHigh.setValue(99.8)
        NormalHighBox = QHBoxLayout()
        NormalHighBox.addWidget(self.normHighLabel)
        NormalHighBox.addWidget(self.normHigh)
        self.NormalizationPanel.layout().addLayout(NormalLowBox)
        self.NormalizationPanel.layout().addLayout(NormalHighBox)

        # Autoscale
        self.enableAutoscale = QCheckBox()
        self.enableAutoscale.setToolTip(
            "If enabled, input images are scaled relative to the training image pixel sizes."
        )
        self.enableAutoscale.setText("Enable autoscale")
        self.enableAutoscale.setChecked(False)
        self.enableAutoscale.clicked.connect(self.setModelScale)

        EnableAutoscaleBox = QHBoxLayout()
        EnableAutoscaleBox.addWidget(self.enableAutoscale)
        self.scaleSentToModelLabel = QLabel("Scale sent to model")
        self.scaleSentToModel = QLabel("")
        ScaleSentToModelBox = QHBoxLayout()
        ScaleSentToModelBox.addWidget(self.scaleSentToModelLabel)
        ScaleSentToModelBox.addWidget(self.scaleSentToModel)
        # Normalization min/ max

        # Post Processing probability and overlap thresholds
        self.PostProcessingPanel = QGroupBox("PostProcessing")
        self.PostProcessingPanel.setLayout(QVBoxLayout())
        self.ProbabilityThresholdLabel = QLabel("Probability threshold", self)
        self.ProbThresh = QDoubleSpinBox()
        self.ProbThresh.setRange(0, 1.0)
        self.ProbThresh.setDecimals(2)
        self.ProbThresh.setSingleStep(0.05)
        self.ProbThresh.setValue(0.5)
        ProbThresBox = QHBoxLayout()
        ProbThresBox.addWidget(self.ProbabilityThresholdLabel)
        ProbThresBox.addWidget(self.ProbThresh)
        self.OverlapThreshLabel = QLabel("Overlap Threshold", self)
        self.OverlapThresh = QDoubleSpinBox()
        self.OverlapThresh.setRange(0, 1.0)
        self.OverlapThresh.setDecimals(2)
        self.OverlapThresh.setSingleStep(0.05)
        self.OverlapThresh.setValue(0.4)
        OverlayThreshdBox = QHBoxLayout()
        OverlayThreshdBox.addWidget(self.OverlapThreshLabel)
        OverlayThreshdBox.addWidget(self.OverlapThresh)
        self.PostProcessingPanel.layout().addLayout(ProbThresBox)
        self.PostProcessingPanel.layout().addLayout(OverlayThreshdBox)

        modelScaleLabel = QLabel("Scale", self)
        self.modelScale = QLineEdit("1")
        self.modelScale.setPlaceholderText("0.01 to 5.0")
        self.modelScale.setValidator(QDoubleValidator(0.01, 5.0, 2, self))
        self.modelScale.textChanged.connect(self.fixEntry)
        self.modelScale.textChanged.connect(self.setModelScale)

        self.modelScale.textChanged.emit(self.modelScale.text())

        # self.ModelName.setCurrentText('')
        self.ModelName.setCurrentIndex(-1)
        # if self.NucImage.currentIndex() != -1:
        #     self.nucleiSelected

        self.RunModel3DButton = QPushButton("Find Nuclei")
        self.RunModel3DButton.setEnabled(False)
        # self.RunModel3DButton.clicked.connect(self.runModel3D)

        ModelSelectorBox = QHBoxLayout()
        ModelSelectorBox.addWidget(self.ModelLabel)
        ModelSelectorBox.addWidget(self.ModelName)

        modelScaleBox = QHBoxLayout()
        modelScaleBox.addWidget(modelScaleLabel)
        modelScaleBox.addWidget(self.modelScale)

        ModelBox = QVBoxLayout()
        ModelBox.addLayout(ModelSelectorBox)
        ModelBox.addLayout(EnableAutoscaleBox)
        ModelBox.addLayout(ScaleSentToModelBox)
        ModelBox.addLayout(modelScaleBox)

        # ModelBox.addWidget(self.RunModel3DButton)

        layout = QVBoxLayout()
        self.setWidgetResizable(True)
        self.setGeometry(10, 10, 80, 160)
        if TESTBUTTON:
            layout.addLayout(TestBox)

        NucImageBoxWidget = QWidget()
        NucImageBoxWidget.setLayout(NucImageBox)
        layout.addWidget(NucImageBoxWidget)
        ModelBoxWidget = QWidget()
        ModelBoxWidget.setLayout(ModelBox)
        layout.addWidget(self.NormalizationPanel)
        layout.addWidget(ModelBoxWidget)
        layout.addWidget(self.PostProcessingPanel)
        layout.addWidget(self.RunModel3DButton)

        # layout.addLayout(NucImageBox)
        # layout.addLayout(ModelBox)
        self.setLayout(layout)

        self.viewer.layers.events.inserted.connect(self.refreshImageSelectors)
        self.viewer.layers.events.removed.connect(self.refreshImageSelectors)
        self.viewer.layers.events.moved.connect(self.refreshImageSelectors)
        self.viewer.layers.events.reordered.connect(self.refreshImageSelectors)
        try:
            self.refreshImageSelectors()
        except:
            pass

    def refreshImageSelectors(self):
        nucleiPrefferences = ['DAPI', 'Blue']
        layerNameList = [layer.name for layer in self.viewer.layers]
        self.initListWidget(self.NucImage)
        if self.NucImage.currentIndex() == -1:
            AllItems = [
                self.NucImage.itemText(i) for i in range(self.NucImage.count())
            ]
            # for text in AllItems:
            #     if "DAPI" in text:
            #         self.NucImage.setCurrentText(text)
            #         # print('refreshImageSelectors',text)
            #         break
            preferredLayers = [name for name in layerNameList if [pref for pref in nucleiPrefferences if pref in name] ] 
            if(len(preferredLayers) )  > 0:
                self.NucImage.setCurrentText(preferredLayers[0])
            
    def initListWidget(
        self, widget, imageOrLabels="image"
    ):  # Also works with comboBox
        self.initList = True
        # print('initListWidget',self.sender(),widget.currentText())

        # currentIndex = widget.currentIndex()
        currentText = widget.currentText()
        try:
            # widget.currentTextChanged.disconnect(self.nucleiSelected)
            # print('disconnected')
            reconnect = True
        except Exception as e:
            print(e)
            reconnect = True
            pass
        widget.clear()
        for index in range(len(self.viewer.layers)):
            if imageOrLabels == "image":
                if (
                    self.viewer.layers[index].as_layer_data_tuple()[2]
                    == "image"
                ):
                    widget.insertItem(index, self.viewer.layers[index].name)
            elif imageOrLabels == "labels":
                if (
                    self.viewer.layers[index].as_layer_data_tuple()[2]
                    == "labels"
                ):
                    widget.insertItem(index, self.viewer.layers[index].name)
        # widget.setCurrentIndex(currentIndex)
        AllItems = [widget.itemText(i) for i in range(widget.count())]
        if currentText in AllItems:
            widget.setCurrentText(currentText)
        else:
            # widget.setCurrentText('')
            widget.setCurrentIndex(-1)
            widget.setCurrentIndex(-1)
        if reconnect:
            # widget.currentTextChanged.connect(self.nucleiSelected)
            # print('reconnected')
            self.initList = False

    DEBUG_nucleiSelected = False

    def nucleiSelected(self):
        trainingVoxelSize3D = [0.2131, 0.1029, 0.1029]  # ZYX microns
        trainingVoxelSize2D = [0.3225, 0.3225]  # YX microns
        if self.DEBUG_nucleiSelected:
            print(
                "nucleiSelected",
                self.NucImage.currentText(),
                self.NucImage.currentIndex(),
                self.sender(),
                self.initList,
            )
        if self.initList:
            if self.DEBUG_nucleiSelected:
                print("No Action")
            return ()
        if self.NucImage.currentIndex() == -1:
            if self.DEBUG_nucleiSelected:
                print("reset")
            self.ModelLabel.setText("Model")
            self.ModelName.clear()
            self.RunModel3DButton.setEnabled(False)
            return ()

        try:
            currentIndex = self.ModelName.currentIndex()
            currentText = self.ModelName.currentText()
            if self.DEBUG_nucleiSelected:
                print("\nCurrentIndex ", currentIndex, self.ModelLabel.text())
            currentLabelText = self.ModelLabel.text()
            if self.DEBUG_nucleiSelected:
                print(
                    "nucleiSelected",
                    currentIndex,
                    currentText,
                    currentLabelText,
                )
            if (
                len(self.viewer.layers[self.NucImage.currentText()].data.shape)
                == 2
            ):
                if currentLabelText == "2D Model":
                    return ()
                if self.DEBUG_nucleiSelected:
                    print("2D")
                if (
                    self.viewer.layers[self.NucImage.currentText()].scale
                    == [1, 1]
                ).all():
                    print(
                        "WARNING: selected image scale = 1.  Autoscale disabled."
                    )
                    self.enableAutoscale.setChecked(False)
                    self.Scale2D = abs((
                        self.viewer.layers[self.NucImage.currentText()].scale
                        / self.viewer.layers[self.NucImage.currentText()].scale
                    ))
                else:
                    self.Scale2D = abs((
                        self.viewer.layers[self.NucImage.currentText()].scale
                        / trainingVoxelSize2D
                    ))
                # print("Autoscale 2D", self.Scale2D)

                # nucleiImageDimension = '2D'
                modelList = self.modelList2D
                default = self.modelListDefault2D
                self.ModelLabel.setText("2D Model")
                if currentLabelText != "Model":
                    self.RunModel3DButton.clicked.disconnect(
                        self.runModel3D_action
                    )
                self.RunModel3DButton.clicked.connect(self.runModel2D_action)
                if self.DEBUG_nucleiSelected:
                    print(
                        "self.RunModel3DButton.clicked.connect(self.runModel2D_action)"
                    )

            else:
                if self.DEBUG_nucleiSelected:
                    print("3D")
                # print(self.viewer.layers[self.NucImage.currentText()].scale)
                # self.Scale3D = self.viewer.layers[self.NucImage.currentText()].scale / self.trainingVoxelSize3D
                # print(
                #     "Selectedlayer scale: ",
                #     self.viewer.layers[self.NucImage.currentText()].scale,
                # )
                if (
                    self.viewer.layers[self.NucImage.currentText()].scale
                    == [1, 1, 1]
                ).all():
                    print(
                        "WARNING: selected image scale = 1.  Autoscale disabled."
                    )
                    self.enableAutoscale.setChecked(False)
                    self.Scale3D = abs((
                        self.viewer.layers[self.NucImage.currentText()].scale
                        / self.viewer.layers[self.NucImage.currentText()].scale
                    ))
                else:
                    self.Scale3D = abs((
                        self.viewer.layers[self.NucImage.currentText()].scale
                        / trainingVoxelSize3D
                    ))
                # print("Autoscale 3D", self.Scale3D)

                # nucleiImageDimension = '3D'
                if currentLabelText == "3D Model":
                    return ()
                modelList = self.modelList3D
                default = self.modelListDefault3D
                self.ModelLabel.setText("3D Model")
                if currentLabelText != "Model":
                    self.RunModel3DButton.clicked.disconnect(
                        self.runModel2D_action
                    )
                self.RunModel3DButton.clicked.connect(self.runModel3D_action)
                if self.DEBUG_nucleiSelected:
                    print(
                        "self.RunModel3DButton.clicked.connect(self.runModel3D)_action"
                    )
                # if self.NucImage.currentText() != '':

            self.ModelName.clear()
            for index in range(len(modelList)):
                if self.DEBUG_nucleiSelected:
                    print("insert", modelList[index])
                self.ModelName.insertItem(index, modelList[index])
            self.ModelName.setCurrentText(default)
            self.RunModel3DButton.setEnabled(True)
            self.setModelScale()
            # self.loadModel()

        except Exception as e:
            if self.DEBUG_nucleiSelected:
                print("nucleiSelected exception", e)
            pass

    def loadModel(self): # XXX
        if self.NucImage.currentText() == "":
            return ()
        # Access the selected model name
        selected_model_name = self.ModelName.currentText()

        # Apply conditional configuration
        if "delaunay" in selected_model_name.lower():
            config = MyConfig(
                n_rays=32,
                grid=(2,2),
                use_gpu=False,
                use_delaunay=True,
                convexity_threshold=0.7,
            )
            config_specified = True
        else:
            config_specified = False

        try:
            if self.ModelLabel.text() == "3D Model":
                 if self.running3D:
                      return ()           
                 self.model = StarDist3D(
                     None, name=self.ModelName.currentText(), basedir=MODELS_DIR
                     )
            else:
                 if self.running2D:
                      return ()
                 if config_specified:
                     # self.model = StarDist2D(
                     # None, name=self.ModelName.currentText(), basedir=MODELS_DIR, config=config
                     #     )
                     self.model = StarDist2D(
                         config, name=self.ModelName.currentText(), basedir=MODELS_DIR
                             )
                     self.model.load_weights(os.path.join(MODELS_DIR,self.ModelName.currentText(),'weights_best.h5'))
                 else:
                     self.model = StarDist2D(
                     None, name=self.ModelName.currentText(), basedir=MODELS_DIR
                         )
                 # print(self.model.predict_instances.__doc__)
                 # self.model = StarDist2D(
                 #     None, name=self.ModelName.currentText(), basedir=MODELS_DIR
                 # )
            # print(dir(self.model))
            # print(self.model.thresholds)
            # print(self.model.thresholds.prob)
            self.ProbThresh.setValue(self.model.thresholds.prob)
            self.OverlapThresh.setValue(self.model.thresholds.nms)
        except Exception as e:
             print('Error loading model.\n',e)
             return() 
                
        
    running2D = False
    running3D = False

    def runModel2D_action(self):
        self.RunModel3DButton.setEnabled(False)
        if self.running2D:
            return ()
        if self.ModelLabel.text() != "2D Model":
            return ()
        if self.NucImage.currentText() == "":
            return ()

        # print('runModel2D_action')
        # modelParams = {'source': '2DModel', 'filePath': self.viewer.layers[self.NucImage.currentText()].source.path,
        #                'channel': self.NucImage.currentText(),
        #                'model': self.ModelName.currentText()}
        worker = self.runModel2D()

        # worker.returned.connect(lambda labels: self.viewer.add_labels(labels,
        #                                                               name='nucleiLabels',
        #                                                               scale=self.viewer.layers[
        #                                                                   self.NucImage.currentText()].scale,
        #                                                               metadata=modelParams))
        worker.returned.connect(
            lambda labels: self.insertLabelsLayer2D(self, labels)
        )
        worker.finished.connect(self._run)
        self.running2D = True
        worker.start()

    def _run(self):
        self.RunModel3DButton.setEnabled(True)
        # global seno
        self.running2D = False
        self.running3D = False
        # print(self.seno)
        # print('_run')
        # self.NucLabels = QWidget()
        # for i in range(self.tab.count()):
        #     if self.tab.tabText(i) == ProcessWidget.TAB_NAME:
        #         # if isinstance(self.tab.widget(i), ProcessWidget):
        #         # print("Found", ProcessWidget.TAB_NAME, "Tab")
        #         self.NucLabels = self.tab.widget(i).NucLabels
        #         break
        # self.initListWidget(self.NucLabels)
        # self.NucLabels.setCurrentIndex(len(self.viewer.layers) - 1)

        # Additional Change to Flush Memory from System taken up by Model
        from keras.backend import clear_session
        print("Clear Keras Session and Remove Model")
        # Clear the session
        clear_session()
        # delete variable if it exists
        if hasattr(self, 'model'):
            print("Delete Model")
            del self.model
            print("Reload Model")
            self.loadModel()

    def insertLabelsLayer2D(self, widget, labels):
        if labels is None:
            return()
        # print(self,'\n',widget)
        modelParams = {
            "source": "2DModel",
            "filePath": self.viewer.layers[
                self.NucImage.currentText()
            ].source.path,
            "channel": self.NucImage.currentText(),
            "model": self.ModelName.currentText(),
        }
        newLayer = self.viewer.add_labels(
            labels,
            name="nucleiLabels",
            scale=self.viewer.layers[self.NucImage.currentText()].scale,
            metadata=modelParams,
        )
        newLayer.contour = 2
        newLayerName = newLayer.name
        # newLayerName = self.viewer.layers[len(self.viewer.layers) - 1].name
        self.tab.markerWidget.Marker1NucMask.setCurrentText(newLayerName)
        self.tab.processWidget.NucLabels.setCurrentText(newLayerName)

    def insertLabelsLayer3D(self, widget, labels):
        if labels is None:
            return()
        # print(self,'\n',widget)
        modelParams = {
            "source": "3DModel",
            "filePath": self.viewer.layers[
                self.NucImage.currentText()
            ].source.path,
            "channel": self.NucImage.currentText(),
            "model": self.ModelName.currentText(),
        }
        newLayer = self.viewer.add_labels(
            labels,
            name="nucleiLabels",
            scale=self.viewer.layers[self.NucImage.currentText()].scale,
            metadata=modelParams,
        )
        newLayer.contour = 2
        newLayerName = newLayer.name
        # newLayerName = self.viewer.layers[len(self.viewer.layers) - 1].name
        self.tab.markerWidget.Marker1NucMask.setCurrentText(newLayerName)
        self.tab.processWidget.NucLabels.setCurrentText(newLayerName)

    def runModel3D_action(self):
        self.RunModel3DButton.setEnabled(False)
        if self.running3D:
            return ()
        if self.ModelLabel.text() != "3D Model":
            return ()
        if self.NucImage.currentText() == "":
            return ()
        # print('runModel3D_action')
        # modelParams = {
        #     "source": "3DModel",
        #     "filePath": self.viewer.layers[
        #         self.NucImage.currentText()
        #     ].source.path,
        #     "channel": self.NucImage.currentText(),
        #     "model": self.ModelName.currentText(),
        # }

        worker = self.runModel3D()
        # worker.returned.connect(lambda labels:
        #                         self.viewer.add_labels(labels, name='nucleiLabels',
        #                                                scale=self.viewer.layers[self.NucImage.currentText()].scale,
        #                                                metadata=modelParams))
        worker.returned.connect(
            lambda labels: self.insertLabelsLayer3D(self, labels)
        )

        worker.finished.connect(self._run)
        self.running3D = True
        worker.start()

    DEBUG_runModel2D = False

    @thread_worker
    def runModel2D(self): # XXX
        if self.DEBUG_runModel2D:
            print("runModel2D", self.ModelName.currentText())
        xMax = yMax = 512
        # scale = 1.0
        scale = float(self.modelScale.text())
        print(self.modelScale.text(), scale)
        if scale < 0.01:
            scale = 0.01
        elif scale > 5.0:
            scale = 5.0
        print(self.modelScale.text(), scale)
        self.modelScale.setText(str(scale))

        axis_norm = (0, 1)
        # This is incorrect - all 2D models were trained with full-size data.
        # if self.ModelName.currentText() != 'stardist3dhumanliversanthofullrespatch48160160':  # Size down input
        #     scale *= 0.5
        # else:
        #     scale = scale
        # nTilesZ = 1
        if self.enableAutoscale.isChecked():
            scale = self.Scale2D * scale
        # print("scale sent to model", scale)
        self.scaleSentToModel.setText(str(scale))

        normPercentHigh = self.normHigh.value()
        normPercentLow = self.normLow.value()
        img = normalize(
            self.viewer.layers[self.NucImage.currentText()].data,
            normPercentLow,
            normPercentHigh,
            axis=axis_norm,
        )
        # try: 
        #     model = StarDist2D(
        #         None, name=self.ModelName.currentText(), basedir=MODELS_DIR
        #     )
        # except Exception as e:
        #     print('Error loading model.\n',e)
        #     return(None) 

        if len(img.shape) == 2:
            nTilesY = int(img.shape[0] / yMax) + 1
            nTilesX = int(img.shape[1] / xMax) + 1

            # nTilesZYX = (nTilesZ, nTilesY, nTilesX)
            nTilesYX = (nTilesY, nTilesX)
        # labels, details = model.predict_instances(img,scale=scale,n_tiles=nTilesZYX)
        try:
            # prob = 0.5
            # nms = 0.4
            # labels, details = model.predict_instances(img, scale=scale, n_tiles=nTilesYX)
            labels, details = self.model.predict_instances( 
                img,
                prob_thresh=self.ProbThresh.value(),
                nms_thresh=self.OverlapThresh.value(),
                scale=scale,
                n_tiles=nTilesYX,
            )
        except Exception as e:
            print("Inference failed", e)
            return(None)

        return labels

    def setModelScale(self):
        try:
            scale = scale = float(self.modelScale.text())
        except ValueError:
            self.scaleSentToModel.setText("")
            return
        if self.NucImage.currentText() == "":
            self.scaleSentToModel.setText("")
            return
        # print('setModelScale',scale)
        if (
            len(self.viewer.layers[self.NucImage.currentText()].data.shape)
            == 3
        ):
            # print('setModelScale','3D',self.Scale3D)
            if self.enableAutoscale.isChecked():
                if (
                    self.ModelName.currentText()
                    != "stardist3dhumanliversanthofullrespatch48160160"
                ):  # Size down input
                    # scale = 0.5*self.Scale3D*scale # Assumes stack was also reduced.
                    scale = (
                        [1.0, 0.5, 0.5] * self.Scale3D * scale
                    )  # Stack was not reduced.
                else:
                    scale = self.Scale3D * scale
        else:
            # print('setModelScale','2D',self.Scale2D)
            if self.enableAutoscale.isChecked():
                scale = self.Scale2D * scale
        self.scaleSentToModel.setText(str(scale))

    @thread_worker
    def runModel3D(self):
        xMax = yMax = 512
        # scale = 1.0
        scale = float(self.modelScale.text())
        # print(self.modelScale.text(),scale)
        if scale < 0.01:
            scale = 0.01
        elif scale > 5.0:
            scale = 5.0
        # print(self.modelScale.text(),scale)
        self.modelScale.setText(str(scale))

        axis_norm = (0, 1, 2)
        # The following is correct relative to training regimen.  only 1 3D model was trained with full-scale data.Training pixel sizes unknown.
        # if self.ModelName.currentText() != 'stardist3dhumanliversanthofullrespatch48160160':  # Size down input
        #     scale *= 0.5
        # else:
        #     scale = scale
        if self.enableAutoscale.isChecked():
            if (
                self.ModelName.currentText()
                != "stardist3dhumanliversanthofullrespatch48160160"
            ):  # Size down input
                # scale = 0.5*self.Scale3D*scale # Assumes stack was also reduced.
                scale = (
                    [1.0, 0.5, 0.5] * self.Scale3D * scale
                )  # Stack was not reduced.
            else:
                scale = self.Scale3D * scale
        # print("scale sent to model", scale)
        self.scaleSentToModel.setText(str(scale))
        nTilesZ = 1
        # normPercentHigh = 99.8
        normPercentHigh = self.normHigh.value()
        normPercentLow = self.normLow.value()

        img = normalize(
            self.viewer.layers[self.NucImage.currentText()].data,
            normPercentLow,
            normPercentHigh,
            axis=axis_norm,
        )
        # try: 
        #     model = StarDist3D(
        #         None, name=self.ModelName.currentText(), basedir=MODELS_DIR
        #     )
        # except Exception as e:
        #     print('Error loading model.\n',e)
        #     return(None) 
        if len(img.shape) == 3:
            nTilesY = int(img.shape[1] / yMax) + 1
            nTilesX = int(img.shape[2] / xMax) + 1
            nTilesZYX = (nTilesZ, nTilesY, nTilesX)
        try:
            # prob = 0.5
            # nms = 0.4
            # labels, details = model.predict_instances(img, scale=scale, n_tiles=nTilesZYX)
            labels, details = self.model.predict_instances( 
                img,
                prob_thresh=self.ProbThresh.value(),
                nms_thresh=self.OverlapThresh.value(),
                scale=scale,
                n_tiles=nTilesZYX,
            )
        except Exception as e:
            print("Inference failed", e)
            return(None)
        # return Information moved
        return labels

    def fixEntry(self, *args, **kwargs):
        sender = self.sender()
        validator = sender.validator()
        # state = validator.validate(sender.text(), 0)[0]
        # print(validator.validate(sender.text(), 0))
        # print(dir())
        if (
            sender.text() == "0."
            or sender.text() == "."
            or sender.text() == "0"
            or sender.text() == ""
        ):
            return ()
        val = float(sender.text())
        # print(val)
        if val < validator.bottom():
            val = validator.bottom()
            self.modelScale.setText(str(val))
        elif val > validator.top():
            val = validator.top()
            self.modelScale.setText(str(val))
