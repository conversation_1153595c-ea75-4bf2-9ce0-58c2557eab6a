"""
This module is an example of a barebones QWidget plugin for napari

It implements the Widget specification.
see: https://napari.org/stable/plugins/guides.html?#widgets

Replace code below according to your needs.
"""
import traceback
from typing import TYPE_CHECKING, Final, List

from napari.layers import Image
# from magicgui import magic_factory
# from qtpy.QtWidgets import QHBoxLayout, QPushButton, QWidget, QListWidget,QComboBox,QLabel,QVBoxLayout,QRadioButton
from qtpy.QtWidgets import QH<PERSON><PERSON>Layout, QPushButton, QWidget, QComboBox, QLabel, QVBoxLayout
from qtpy.QtWidgets import QSlider, QGroupBox, QScrollArea, QLineEdit
from qtpy.QtCore import Qt
from qtpy.QtGui import QDoubleValidator

from skimage.measure import label, regionprops_table
from skimage.filters import unsharp_mask
from skimage.morphology import disk, ball, dilation
from tifffile import imwrite

from IPython.display import display
import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
from sqlalchemy.engine import URL

import os
import xarray
from superqt.utils import thread_worker
from napari_segment_blobs_and_things_with_membranes import voronoi_otsu_labeling, \
    seeded_watershed, \
    seeded_watershed_with_mask

MODELS_DIR = os.path.join(os.path.join(os.path.join(os.path.dirname(__file__), os.pardir), os.pardir), 'models')

# for root, dirs, files in os.walk(os.environ['USERPROFILE']):
#     if 'napari_senoquant' in dirs:  # napari_senoquant
#         MODELS_DIR = os.path.join(root,'napari_senoquant','models')
# print(MODELS_DIR)

# os.chdir('C:\\Users\\<USER>\\Python\\napari_senoquant')
import platform

node = platform.uname()[1]

import datetime

import time

if TYPE_CHECKING:
    import napari
# from napari.utils.progress import progrange
from csbdeep.utils import normalize
# import stardist
# from stardist import random_label_cmap
from stardist.models import StarDist3D, StarDist2D
# MODEL_FOLDER = '\\\\Users\\jjc\\Python\\NIH006471700\\models'
# seno = dict()

from .output.OutputWidget import OutputWidget

TESTBUTTON = False
ENABLE_FIND_TELOMERES = False
ENABLE_FIND_FOCI = False


# %%
class ProcessWidget(QWidget):
    global seno
    TAB_NAME: Final = "Napari Senoquant: Process"

    def __init__(self, napari_viewer, nucImage: QComboBox):
        # global seno
        # self.seno['ProcessWidget'] = self
        # print(seno)
        super().__init__()
        self.viewer = napari_viewer
        self.seno = dict()
        # self.markerList = markerList
        self.markerList = list()
        self.markerParamsList = list()
        self.markerPropsList = list()
        self.fixAllColors()

        # From NucleiWidet
        self.NucImageMaster = nucImage

        # TESTBUTTON
        if TESTBUTTON:
            TestButton = QPushButton("TEST")
            TestButton.clicked.connect(self.Test)
            TestBox = QHBoxLayout()
            TestBox.addWidget(TestButton)

        # Nuclei
        # Nuclei Image
        self.NucImageLabel = QLabel('Nuclei Image', self)
        self.NucImage = QComboBox()
        self.initListWidget(self.NucImage)
        # self.NucImage.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        # self.NucImage.currentTextChanged.connect(self.nucleiSelected)
        self.NucImageMaster.currentIndexChanged.connect(self.setNucImage)
        self.NucImageMaster.currentIndexChanged.connect(self.enableProcess)

        try:
            self.NucImage.setCurrentIndex(self.getLayerIndexByName('image', 'DAPI'))
        except:
            pass
        NucImageBox = QHBoxLayout()
        NucImageBox.addWidget(self.NucImageLabel)
        NucImageBox.addWidget(self.NucImage)
        #     # Model
        #         modelList2D  = [  '2D_versatile_fluo',  'versatile2dcellsallantho', 'versatile2dcellsallantho2', 'versatile2dcellsallanthoMULIPLEGPUnolargenuclei05182022' ]
        #         modelList3D  = ['stardist3dhumanliversanthogpatch48160160',  'stardistliver3d63xobjective', 'stardist3dMOUSEliversanthogpatch64160160', 'stardist3dcellsvariousshapes63xobjective3D', 'stardist3dUNIVERSALliverspatch64160160' ,'stardist3dhumanliversanthogpatch48160160retrainedcuratedmousedata' ,'stardist3dhumanliversanthogpatch48160160curatedmousedatafromsratch']
        #         self.ModelLabel = QLabel('Model', self)
        #         self.ModelName = QComboBox()
        #         # for index in range(len(modelList3D)):
        #         #     self.ModelName.insertItem(index, modelList3D[index])
        #
        #         modelScaleLabel = QLabel('Scale', self)
        #         self.modelScale = QLineEdit('1')
        #         self.modelScale.setPlaceholderText("0.1 to 1.5")
        #         self.modelScale.setValidator(QDoubleValidator(0.1, 1.5, 1,self))
        #         self.modelScale.textChanged.connect(self.fixEntry)
        #         self.modelScale.textChanged.emit(self.modelScale.text())
        #
        #         # self.ModelName.setCurrentText('')
        #         self.ModelName.setCurrentIndex(-1)
        #         if self.NucImage.currentIndex() != -1:
        #             self.nucleiSelected
        #
        #         self.RunModel3DButton = QPushButton("Find Nuclei")
        #         self.RunModel3DButton.clicked.connect(self.runModel3D)
        #
        #         ModelSelectorBox = QHBoxLayout()
        #         ModelSelectorBox.addWidget(self.ModelLabel)
        #         ModelSelectorBox.addWidget(self.ModelName)
        #
        #
        #         modelScaleBox = QHBoxLayout()
        #         modelScaleBox.addWidget(modelScaleLabel)
        #         modelScaleBox.addWidget(self.modelScale)
        #
        #         ModelBox = QVBoxLayout()
        #         ModelBox.addLayout(ModelSelectorBox)
        #
        #         ModelBox.addLayout(modelScaleBox)
        #
        #         ModelBox.addWidget(self.RunModel3DButton)

        # Nuclei Labels
        self.NucLabelsLabel = QLabel('Nuclei Labels', self)
        self.NucLabels = QComboBox()
        self.seno['NucleiLabelComboBox'] = self.NucLabels
        self.initListWidget(self.NucLabels, 'labels')
        self.NucLabels.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        self.NucLabels.currentIndexChanged.connect(self.enableProcess)
        try:
            self.NucLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'StarDist'))
        except:
            try:
                self.NucLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'Nuclei'))
            except:
                pass
        NucLabelsBox = QHBoxLayout()
        NucLabelsBox.addWidget(self.NucLabelsLabel)
        NucLabelsBox.addWidget(self.NucLabels)
        # Telomeres
        # Telomere Image
        self.TelImageLabel = QLabel('Telomere/Spot 1 Image', self)
        self.TelImage = QComboBox()
        self.initListWidget(self.TelImage, 'image')
        self.TelImage.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        try:
            self.TelImage.setCurrentIndex(self.getLayerIndexByName('image', 'TXR'))
        except:
            pass
        TelImageBox = QHBoxLayout()
        TelImageBox.addWidget(self.TelImageLabel)
        TelImageBox.addWidget(self.TelImage)
        # Filter size Slider
        if ENABLE_FIND_TELOMERES:
            self.TelFilterSizeSliderLabel = QLabel('FilterSize', self)
            self.TelFilterSizeSliderReadout = QLabel('5', self)
            self.TelFilterSizeSlider = QSlider(1)
            self.TelFilterSizeSlider.setMinimum(1)
            self.TelFilterSizeSlider.setMaximum(11)
            self.TelFilterSizeSlider.setValue(5)
            self.TelFilterSizeSlider.setTickPosition(QSlider.TicksBelow)
            self.TelFilterSizeSlider.setTickInterval(2)
            self.TelFilterSizeSlider.valueChanged.connect(self.updateTelFilterSizeSliderReadout)
            TelFilterSizeSliderBox = QHBoxLayout()
            TelFilterSizeSliderBox.addWidget(self.TelFilterSizeSliderLabel)
            TelFilterSizeSliderBox.addWidget(self.TelFilterSizeSliderReadout)
            TelFilterSizeSliderBox.addWidget(self.TelFilterSizeSlider)
            # Threshold Slider
            self.TelThreshSliderLabel = QLabel('Threshold', self)
            self.TelThreshSliderReadout = QLabel('0.3', self)
            self.TelThreshSlider = QSlider(1)  # horizontal
            self.TelThreshSlider.setMinimum(1)
            self.TelThreshSlider.setValue(30)
            self.TelThreshSlider.valueChanged.connect(self.updateTelThreshSliderReadout)
            TelThreshSliderBox = QHBoxLayout()
            TelThreshSliderBox.addWidget(self.TelThreshSliderLabel)
            TelThreshSliderBox.addWidget(self.TelThreshSliderReadout)
            TelThreshSliderBox.addWidget(self.TelThreshSlider)
            # Find Telomeres Button
            findTelomeresButton = QPushButton("Find Telomeres")
            findTelomeresButton.clicked.connect(self.findTelomeres)
            findTelomeresButtonBox = QHBoxLayout()
            findTelomeresButtonBox.addWidget(findTelomeresButton)
        # Telomere Labels
        self.TelLabelsLabel = QLabel('Telomere/Spot 1 Labels', self)
        self.TelLabels = QComboBox()
        self.initListWidget(self.TelLabels, 'labels')
        self.TelLabels.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        try:
            self.TelLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'telomere'))
        except:
            pass
        TelLabelsBox = QHBoxLayout()
        TelLabelsBox.addWidget(self.TelLabelsLabel)
        TelLabelsBox.addWidget(self.TelLabels)
        # Foci
        # Foci Image
        self.FociImageLabel = QLabel('Foci/Spot 2 Image', self)
        self.FociImage = QComboBox()
        self.initListWidget(self.FociImage, 'image')
        self.FociImage.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        try:
            self.FociImage.setCurrentIndex(self.getLayerIndexByName('image', 'FITC'))
        except:
            pass
        FociImageBox = QHBoxLayout()
        FociImageBox.addWidget(self.FociImageLabel)
        FociImageBox.addWidget(self.FociImage)
        # Filter size Slider
        if ENABLE_FIND_FOCI:
            self.FocFilterSizeSliderLabel = QLabel('FilterSize', self)
            self.FocFilterSizeSliderReadout = QLabel('5', self)
            self.FocFilterSizeSlider = QSlider(1)
            self.FocFilterSizeSlider.setMinimum(1)
            self.FocFilterSizeSlider.setMaximum(11)
            self.FocFilterSizeSlider.setValue(5)
            self.FocFilterSizeSlider.setTickPosition(QSlider.TicksBelow)
            self.FocFilterSizeSlider.setTickInterval(2)
            self.FocFilterSizeSlider.valueChanged.connect(self.updateFocFilterSizeSliderReadout)
            FocFilterSizeSliderBox = QHBoxLayout()
            FocFilterSizeSliderBox.addWidget(self.FocFilterSizeSliderLabel)
            FocFilterSizeSliderBox.addWidget(self.FocFilterSizeSliderReadout)
            FocFilterSizeSliderBox.addWidget(self.FocFilterSizeSlider)
            # Threshold Slider
            self.FocThreshSliderLabel = QLabel('Threshold', self)
            self.FocThreshSliderReadout = QLabel('0.3', self)
            self.FocThreshSlider = QSlider(1)  # horizontal
            self.FocThreshSlider.setMinimum(1)
            self.FocThreshSlider.setMaximum(100)
            self.FocThreshSlider.setValue(30)
            self.FocThreshSlider.valueChanged.connect(self.updateFocThreshSliderReadout)
            FocThreshSliderBox = QHBoxLayout()
            FocThreshSliderBox.addWidget(self.FocThreshSliderLabel)
            FocThreshSliderBox.addWidget(self.FocThreshSliderReadout)
            FocThreshSliderBox.addWidget(self.FocThreshSlider)
            # Minimum Focus Size Slider
            self.FocMinSizeSliderLabel = QLabel('Min. Size', self)
            self.FocMinSizeSliderReadout = QLabel('10', self)
            self.FocMinSizeSlider = QSlider(1)  # horizontal
            self.FocMinSizeSlider.setMinimum(1)
            self.FocMinSizeSlider.setMaximum(30)
            self.FocMinSizeSlider.setValue(10)
            self.FocMinSizeSlider.valueChanged.connect(self.updateFocMinSizeSliderReadout)
            FocMinSizeSliderBox = QHBoxLayout()
            FocMinSizeSliderBox.addWidget(self.FocMinSizeSliderLabel)
            FocMinSizeSliderBox.addWidget(self.FocMinSizeSliderReadout)
            FocMinSizeSliderBox.addWidget(self.FocMinSizeSlider)
            # Maximum Focus Size Slider
            self.FocMaxSizeSliderLabel = QLabel('Max. Size', self)
            self.FocMaxSizeSliderReadout = QLabel('100', self)
            self.FocMaxSizeSlider = QSlider(1)  # horizontal
            self.FocMaxSizeSlider.setMinimum(50)
            self.FocMaxSizeSlider.setMaximum(250)
            self.FocMaxSizeSlider.setValue(100)
            self.FocMaxSizeSlider.valueChanged.connect(self.updateFocMaxSizeSliderReadout)
            FocMaxSizeSliderBox = QHBoxLayout()
            FocMaxSizeSliderBox.addWidget(self.FocMaxSizeSliderLabel)
            FocMaxSizeSliderBox.addWidget(self.FocMaxSizeSliderReadout)
            FocMaxSizeSliderBox.addWidget(self.FocMaxSizeSlider)
            # Find Foci Button
            findFociButton = QPushButton("Find Foci")
            findFociButton.clicked.connect(self.findFoci)
            findFociButtonBox = QHBoxLayout()
            findFociButtonBox.addWidget(findFociButton)
        # Foci Labels
        self.FociLabelsLabel = QLabel('Foci/Spot 2 Labels', self)
        self.FociLabels = QComboBox()
        self.initListWidget(self.FociLabels, 'labels')
        self.FociLabels.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        try:
            self.FociLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'foci'))
        except:
            pass
        FociLabelsBox = QHBoxLayout()
        FociLabelsBox.addWidget(self.FociLabelsLabel)
        FociLabelsBox.addWidget(self.FociLabels)

        # Multiple Markers panel
        self.scalePanel = QGroupBox("Markers")
        self.scalePanel.setLayout(QVBoxLayout())
        self.scrollPane = QScrollArea(self)
        self.scrollWidget = QWidget()
        self.scalePanel.layout().addWidget(self.scrollPane)
        # Scroll Area Properties
        self.scrollPane.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.scrollPane.setWidgetResizable(True)
        self.scrollPane.setGeometry(5, 5, 100, 100)
        self.scrollPane.setWidget(self.scrollWidget)
        self.scrollWidget.setLayout(QVBoxLayout())
        MultipleMarkerBox = QVBoxLayout()
        MultipleMarkerBox.addWidget(self.scalePanel)

        self.configurationPanel = QWidget()
        self.configurationPanel.setLayout(QHBoxLayout())

        self.addScaleButton = QPushButton()
        self.addScaleButton.setText("Add marker")
        self.addScaleButton.clicked.connect(self.addMarkerPanel)
        self.configurationPanel.layout().addWidget(self.addScaleButton)
        self.addMarkerPanel()
        self.scalePanel.layout().addWidget(self.configurationPanel)

        # Output
        self.output_widget = OutputWidget(self.NucImage)
        # self.output_widget.set_save_process(self.saveToExcel)
        self.output_widget.set_save_process(self.save)

        # self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)

        # self.automatic_xls_file_naming.layout().addWidget(self.automatic_xls_file_naming_file_dialog_append)

        # self.excel_output_settings.layout().addWidget(self.automatic_xls_file_naming)

        # Process button
        self.processButton = QPushButton("Process")
        # self.processButton.clicked.connect(self.process_action)
        self.processButton.clicked.connect(self.process)
        self.processButton.setEnabled(False)

        # saveButton = QPushButton("Save to Database")
        # saveButton.clicked.connect(self.saveToDataBase)
        processButtonBox = QHBoxLayout()
        processButtonBox.addWidget(self.processButton)
        # processButtonBox.addWidget(saveButton)
        # Layout
        layout = QVBoxLayout()
        if TESTBUTTON:
            layout.addLayout(TestBox)

        layout.addLayout(NucImageBox)
        # layout.addLayout(ModelBox)
        layout.addLayout(NucLabelsBox)
        layout.addLayout(TelImageBox)
        if ENABLE_FIND_TELOMERES:
            layout.addLayout(TelFilterSizeSliderBox)
            layout.addLayout(TelThreshSliderBox)
            layout.addLayout(findTelomeresButtonBox)
        layout.addLayout(TelLabelsBox)
        layout.addLayout(FociImageBox)
        if ENABLE_FIND_FOCI:
            layout.addLayout(FocFilterSizeSliderBox)
            layout.addLayout(FocThreshSliderBox)
            layout.addLayout(FocMinSizeSliderBox)
            layout.addLayout(FocMaxSizeSliderBox)
            layout.addLayout(findFociButtonBox)
        layout.addLayout(FociLabelsBox)
        # layout.addLayout(Marker1ImageBox)
        # layout.addLayout(Marker1LabelBox)   
        layout.addLayout(MultipleMarkerBox)
        layout.addLayout(processButtonBox)
        layout.addWidget(self.output_widget)

        self.setLayout(layout)
        self.viewer.layers.events.inserted.connect(self.refreshImageSelectors)
        self.viewer.layers.events.inserted.connect(self.fixColors)
        self.viewer.layers.events.removed.connect(self.refreshImageSelectors)
        self.viewer.layers.events.moved.connect(self.refreshImageSelectors)
        self.viewer.layers.events.reordered.connect(self.refreshImageSelectors)

    def setNucImage(self, index):
        # print(index)
        self.NucImage.setCurrentIndex(index)

    def addMarkerPanel(self):
        markerIndex = len(self.markerList)
        markerNumber = markerIndex + 1
        markerNameLabel = QLabel('Marker ' + str(markerNumber) + ' Name', self)
        markerName = QLineEdit('Marker' + str(markerNumber))
        markerNameBox = QHBoxLayout()
        markerNameBox.addWidget(markerNameLabel)
        markerNameBox.addWidget(markerName)

        markerLabel = QLabel('Marker ' + str(markerNumber) + ' Image', self)
        markerImage = QComboBox()
        self.initListWidget(markerImage, 'image')
        markerImage.insertItem(len(self.viewer.layers), 'None')
        markerImage.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        markerImageBox = QHBoxLayout()
        markerImageBox.addWidget(markerLabel)
        markerImageBox.addWidget(markerImage)

        markerLabelLabel = QLabel('Marker ' + str(markerNumber) + ' Labels', self)
        markerLabel = QComboBox()
        self.initListWidget(markerLabel, 'labels')
        markerLabel.currentIndexChanged.connect(self.setSelectedWidgetIndex)
        MarkerLabelBox = QHBoxLayout()
        MarkerLabelBox.addWidget(markerLabelLabel)
        MarkerLabelBox.addWidget(markerLabel)

        markerBox = QVBoxLayout()
        markerBox.addLayout(markerNameBox)
        markerBox.addLayout(markerImageBox)
        markerBox.addLayout(MarkerLabelBox)

        markerDict = {'nameWidget': markerName, 'imageWidget': markerImage, 'labelsWidget': markerLabel,
                      'markerNumber': markerNumber}
        self.markerList.append(markerDict)
        # print(self.markerList)

        self.scrollWidget.layout().addLayout(markerBox)

    def enableProcess(self):
        if self.NucLabels.currentIndex() == -1 or self.NucImage.currentIndex() == -1:
            self.processButton.setEnabled(False)
        else:
            self.processButton.setEnabled(True)

    def fixAllColors(self):
        for layer in self.viewer.layers:
            # print(layer.name)
            try:
                if isinstance(self.viewer.layers[len(self.viewer.layers) - 1], Image):
                    if 'Red' in layer.name:
                        layer.colormap = 'red'
                    if 'Green' in layer.name:
                        layer.colormap = 'green'
                    if 'Blue' in layer.name:
                        layer.colormap = 'blue'
                    if 'Cyan' in layer.name:
                        layer.colormap = 'cyan'
            except AttributeError as e:
                print(e)
                traceback.print_stack()

    def fixColors(self):
        try:
            if isinstance(self.viewer.layers[len(self.viewer.layers) - 1], Image):
                if 'Red' in self.viewer.layers[len(self.viewer.layers) - 1].name:
                    self.viewer.layers[len(self.viewer.layers) - 1].colormap = 'red'
                elif 'Green' in self.viewer.layers[len(self.viewer.layers) - 1].name:
                    self.viewer.layers[len(self.viewer.layers) - 1].colormap = 'green'
                elif 'Blue' in self.viewer.layers[len(self.viewer.layers) - 1].name:
                    self.viewer.layers[len(self.viewer.layers) - 1].colormap = 'blue'
                elif 'Cyan' in self.viewer.layers[len(self.viewer.layers) - 1].name:
                    self.viewer.layers[len(self.viewer.layers) - 1].colormap = 'cyan'
        except AttributeError as e:
            print(e)
            traceback.print_stack()

    # def fixEntry(self, *args, **kwargs):
    #     sender = self.sender()
    #     validator = sender.validator()
    #     # state = validator.validate(sender.text(), 0)[0]
    #     # print(validator.validate(sender.text(), 0))
    #     # print(dir())
    #     if sender.text() == '0.' or sender.text() == '.' or sender.text() == '0' or sender.text() == '':
    #         return()
    #     val = float(sender.text())
    #     # print(val)
    #     if val < validator.bottom():
    #         val = validator.bottom()
    #         self.modelScale.setText(str(val))
    #     elif val > validator.top():
    #         val = validator.top()
    #         self.modelScale.setText(str(val))

    def setSelectedWidgetIndex(self, ix):
        self.seno[self.sender()] = ix

    def refreshImageSelectors(self):
        layerNameList = [layer.name for layer in self.viewer.layers]

        nucleiPrefferences = ['DAPI', 'Blue']
        self.initListWidget(self.NucImage, 'image')
        if self.NucImage.currentIndex() == -1:
            # try:
            #     self.NucImage.setCurrentIndex(self.getLayerIndexByName('image','DAPI'))                
            # except:
            #     pass 
            preferredLayers = [name for name in layerNameList if [pref for pref in nucleiPrefferences if pref in name]]
            if (len(preferredLayers)) > 0:
                self.NucImage.setCurrentText(preferredLayers[0])

        self.initListWidget(self.NucLabels, 'labels')
        if self.NucLabels.currentIndex() == -1:
            try:
                self.NucLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'StarDist'))
            except:
                try:
                    self.NucLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'nucleiLabels'))
                except:
                    pass

        telomerePrefferences = ['TXR', 'Y3', 'Red', 'Orange']
        self.initListWidget(self.TelImage, 'image')
        if self.TelImage.currentIndex() == -1:
            # try:
            #     self.TelImage.setCurrentIndex(self.getLayerIndexByName('image','TXR'))
            # except:
            #     pass
            preferredLayers = [name for name in layerNameList if
                               [pref for pref in telomerePrefferences if pref in name]]
            if (len(preferredLayers)) > 0:
                self.TelImage.setCurrentText(preferredLayers[0])

        self.initListWidget(self.TelLabels, 'labels')
        if self.TelLabels.currentIndex() == -1:
            try:
                self.TelLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'telomere'))
            except:
                pass

        fociPrefferences = ['FITC', 'GFP', 'Green']
        self.initListWidget(self.FociImage, 'image')
        if self.FociImage.currentIndex() == -1:
            # try:
            #     self.FociImage.setCurrentIndex(self.getLayerIndexByName('image','FITC'))
            # except:
            #     pass            
            preferredLayers = [name for name in layerNameList if [pref for pref in fociPrefferences if pref in name]]
            if (len(preferredLayers)) > 0:
                self.FociImage.setCurrentText(preferredLayers[0])

        self.initListWidget(self.FociLabels, 'labels')
        if self.FociImage.currentIndex() == -1:
            try:
                self.FociLabels.setCurrentIndex(self.getLayerIndexByName('labels', 'foci'))
            except:
                pass

        for markerDict in self.markerList:
            self.initListWidget(markerDict['imageWidget'], 'image')
            markerDict['imageWidget'].insertItem(len(self.viewer.layers), 'None')
            self.initListWidget(markerDict['labelsWidget'], 'labels')
            try:
                markerDict['imageWidget'].setCurrentIndex(self.getLayerIndexByName('image', 'Y5'))
            except:
                pass

    def getLayerIndexByName(self, layer, name):
        # print('getLayerDataByName',name)	
        for index in range(len(self.viewer.layers)):
            if self.viewer.layers[index].as_layer_data_tuple()[2] == layer and name in self.viewer.layers[index].name:
                if layer == 'image':
                    # print(self.viewer.layers[index].name,index)
                    return (index)
                elif layer == 'labels':
                    # print(self.viewer.layers[index].name,index)
                    return (index)

    # def nucleiSelected(self):
    #     try:
    #         currentIndex =  self.ModelName.currentIndex()
    #         currentText = self.ModelName.currentText()
    #         # print('\nCurrentIndex ',currentIndex,self.ModelLabel.text())
    #         currentLabelText = self.ModelLabel.text()
    #         # print('nucleiSelected',currentIndex,currentText,currentLabelText)
    #         if len(self.viewer.layers[self.NucImage.currentText()].data.shape) == 2:
    #             if currentLabelText == '2D Model':
    #                 return()
    #             # print('2D')
    #             modelList = self.modelList2D
    #             default = self.modelListDefault2D
    #             self.ModelLabel.setText('2D Model')
    #             if self.ModelLabel.text() != 'Model':
    #                 self.RunModel3DButton.clicked.disconnect(self.runModel3D)
    #             self.RunModel3DButton.clicked.connect(self.runModel2D)
    #         else:
    #             # print('3D')
    #             if currentLabelText == '3D Model':
    #                 return()
    #             modelList = self.modelList3D
    #             default = self.modelListDefault3D
    #             self.ModelLabel.setText('3D Model')
    #             if currentLabelText != 'Model':
    #                 self.RunModel3DButton.clicked.disconnect(self.runModel2D)
    #             self.RunModel3DButton.clicked.connect(self.runModel3D)
    #
    #         self.ModelName.clear()
    #         for index in range(len(modelList)):
    #             # print('insert',modelList[index])
    #             self.ModelName.insertItem(index, modelList[index])
    #         self.ModelName.setCurrentText(default)
    #     except Exception as e:
    #         # print('nucleiSelected exception',e)
    #         pass
    #
    # def runModel2D(self):
    #     # print('runModel2D',self.ModelName.currentText())
    #     xMax = yMax = 512
    #     # scale = 1.0
    #     scale =float(self.modelScale.text())
    #     # print(self.modelScale.text(),scale)
    #     if scale < 0.1:
    #         scale = 0.1
    #     elif scale > 1.5:
    #         scale = 1.5
    #     # print(self.modelScale.text(),scale)
    #     self.modelScale.setText(str(scale))
    #
    #     axis_norm = (0,1)
    #     if self.ModelName.currentText() != 'stardist3dhumanliversanthofullrespatch48160160':# Size down input
    #         scale *= 0.5
    #     else:
    #         scale = scale
    #     # nTilesZ = 1
    #     normPercent = 99.8
    #     img = normalize(self.viewer.layers[self.NucImage.currentText()].data, 1,normPercent, axis=axis_norm)
    #     model = StarDist2D(None, name=self.ModelName.currentText(), basedir=MODELS_DIR)
    #     if len(img.shape) == 2:
    #         nTilesY = int(img.shape[0]/yMax) + 1
    #         nTilesX = int(img.shape[1]/xMax) + 1
    #
    #         # nTilesZYX = (nTilesZ, nTilesY, nTilesX)
    #         nTilesYX = (nTilesY, nTilesX)
    #     # labels, details = model.predict_instances(img,scale=scale,n_tiles=nTilesZYX)
    #     try:
    #         labels, details = model.predict_instances(img,scale=scale,n_tiles=nTilesYX)
    #     except Exception as e:
    #         print('Inference failed',e)
    #         pass
    #
    #     modelParams = {'source':'2DModel','filePath':self.viewer.layers[self.NucImage.currentText()].source.path,
    #                     'channel':self.NucImage.currentText(),
    #                     'model':self.ModelName.currentText()}
    #     self.viewer.add_labels(labels, name='nucleiLabels',scale=self.viewer.layers[self.NucImage.currentText()].scale,metadata=modelParams)
    #     self.initListWidget(self.NucLabels,'labels')
    #     self.NucLabels.setCurrentIndex(len(self.viewer.layers) - 1)
    #
    #
    # def runModel3D(self):
    #     xMax = yMax = 512
    #     # scale = 1.0
    #     scale =float(self.modelScale.text())
    #     # print(self.modelScale.text(),scale)
    #     if scale < 0.1:
    #         scale = 0.1
    #     elif scale > 1.5:
    #         scale = 1.5
    #     # print(self.modelScale.text(),scale)
    #     self.modelScale.setText(str(scale))
    #
    #     axis_norm = (0,1,2)
    #     if self.ModelName.currentText() != 'stardist3dhumanliversanthofullrespatch48160160':# Size down input
    #         scale *= 0.5
    #     else:
    #         scale = scale
    #     nTilesZ = 1
    #     normPercent = 99.8
    #     img = normalize(self.viewer.layers[self.NucImage.currentText()].data, 1,normPercent, axis=axis_norm)
    #     model = StarDist3D(None, name=self.ModelName.currentText(), basedir=MODELS_DIR)
    #     if len(img.shape) == 3:
    #         nTilesY = int(img.shape[1]/yMax) + 1
    #         nTilesX = int(img.shape[2]/xMax) + 1
    #         nTilesZYX = (nTilesZ, nTilesY, nTilesX)
    #     try:
    #         labels, details = model.predict_instances(img,scale=scale,n_tiles=nTilesZYX)
    #     except Exception as e:
    #         print('Inference failed',e)
    #         pass
    #     modelParams = {'source':'3DModel','filePath':self.viewer.layers[self.NucImage.currentText()].source.path,
    #                     'channel':self.NucImage.currentText(),
    #                     'model':self.ModelName.currentText()}
    #     self.viewer.add_labels(labels, name='nucleiLabels',scale=self.viewer.layers[self.NucImage.currentText()].scale,metadata=modelParams)
    #     self.initListWidget(self.NucLabels,'labels')
    #     self.NucLabels.setCurrentIndex(len(self.viewer.layers) - 1)

    # def initListWidget(self,widget): # Also works with comboBox
    #     currentText = widget.currentText()
    #     widget.clear()
    #     for index in range(len(self.viewer.layers)):
    #         widget.insertItem(index, self.viewer.layers[index].name)
    #     AllItems = [widget.itemText(i) for i in range(widget.count())]
    #     if currentText in AllItems:
    #         widget.setCurrentText(currentText)
    #     else:
    #         widget.setCurrentIndex(-1)

    # def initListWidget(self,widget): # Also works with comboBox
    #     currentIndex = widget.currentIndex()
    #     widget.clear()
    #     for index in range(len(self.viewer.layers)):
    #         widget.insertItem(index, self.viewer.layers[index].name)
    #     widget.setCurrentIndex(currentIndex)

    def initListWidget(self, widget, imageOrLabels='image'):  # Also works with comboBox
        # currentIndex = widget.currentIndex()
        currentText = widget.currentText()
        widget.clear()
        for index in range(len(self.viewer.layers)):
            if imageOrLabels == 'image':
                if self.viewer.layers[index].as_layer_data_tuple()[2] == 'image':
                    widget.insertItem(index, self.viewer.layers[index].name)
            elif imageOrLabels == 'labels':
                if self.viewer.layers[index].as_layer_data_tuple()[2] == 'labels':
                    widget.insertItem(index, self.viewer.layers[index].name)
        # widget.setCurrentIndex(currentIndex)
        AllItems = [widget.itemText(i) for i in range(widget.count())]
        if currentText in AllItems:
            widget.setCurrentText(currentText)
        else:
            # widget.setCurrentText('')
            widget.setCurrentIndex(-1)

    def updateTelFilterSizeSliderReadout(self, value):
        self.TelFilterSizeSliderReadout.setText(str(value))

    def updateTelThreshSliderReadout(self, value):
        self.TelThreshSliderReadout.setText(str(value / 100))

    # @thread_worker
    def findTelomeres(self):
        threshold = self.TelThreshSlider.value() / 100
        telomeres = self.viewer.layers[self.TelImage.currentText()].data
        unsharp = unsharp_mask(telomeres, radius=self.TelFilterSizeSlider.value(), amount=1)
        telomereMask = unsharp > threshold
        # telomereLabels,count = label(telomereMask,connectivity=3, return_num=True)
        try:
            path = self.viewer.layers[self.TelImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.TelImage.currentText()].source.path
        telomereLabels, count = label(telomereMask, connectivity=len(telomereMask.shape), return_num=True)
        telomereParams = {'source': 'findTelomeres',
                          'filePath': path,
                          # 'filePath': self.viewer.layers[self.TelImage.currentText()].source.path,
                          # 'channel':self.viewer.layers[self.TelImage.currentIndex()].name,
                          'channel': self.TelImage.currentText(),
                          'threshold': self.TelThreshSlider.value() / 100,
                          'unsharpRadius': self.TelFilterSizeSlider.value()}
        self.viewer.add_labels(telomereLabels, name='telomereLabels',
                               scale=self.viewer.layers[self.TelImage.currentText()].scale, metadata=telomereParams)
        self.initListWidget(self.TelLabels, 'labels')
        self.TelLabels.setCurrentIndex(len(self.viewer.layers) - 1)

    def updateFocFilterSizeSliderReadout(self, value):
        self.FocFilterSizeSliderReadout.setText(str(value))

    def updateFocThreshSliderReadout(self, value):
        self.FocThreshSliderReadout.setText(str(value / 100))

    def updateFocMinSizeSliderReadout(self, value):
        self.FocMinSizeSliderReadout.setText(str(value))

    def updateFocMaxSizeSliderReadout(self, value):
        self.FocMaxSizeSliderReadout.setText(str(value))

    showSteps = False

    # @thread_worker
    def findFoci(self):
        threshold = self.FocThreshSlider.value() / 100
        foci = self.viewer.layers[self.FociImage.currentText()].data
        unsharp = unsharp_mask(foci, radius=self.FocFilterSizeSlider.value(), amount=1)
        if self.showSteps:
            self.viewer.add_image(unsharp, name='unsharp', scale=self.viewer.layers[0].scale)
        fociMask = unsharp > threshold
        # fociLabels,count = label(fociMask,connectivity=3, return_num=True) 
        fociLabels, count = label(fociMask, connectivity=len(fociMask.shape), return_num=True)
        if self.showSteps:
            self.viewer.add_labels(fociLabels.copy(), name='fociLabelsRaw',
                                   scale=self.viewer.layers[self.FociImage.currentText()].scale)
        props = regionprops_table(fociLabels, intensity_image=fociLabels,
                                  properties=('centroid', 'area', 'EquivDiameter', 'Label', 'MeanIntensity'))
        props = pd.DataFrame(props)
        minSize = self.FocMinSizeSlider.value()
        maxSize = self.FocMaxSizeSlider.value()
        labelList = list(props.query("`area` > @maxSize  or `area` < @minSize")['Label'].values)
        fociLabels[np.isin(fociLabels, labelList)] = 0
        try:
            path = self.viewer.layers[self.FociImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.FociImage.currentText()].source.path
        fociParams = {'source': 'findFoci',
                      'filePath': path,
                      # 'filePath': self.viewer.layers[self.FociImage.currentText()].source.path,
                      # 'channel':self.viewer.layers[self.FociImage.currentText()].name,
                      'channel': self.FociImage.currentText(),
                      'threshold': self.FocThreshSlider.value() / 100,
                      'unsharpRadius': self.FocFilterSizeSlider.value(),
                      'maxSize': maxSize, 'minSize': minSize}
        self.viewer.add_labels(fociLabels, name='fociLabels',
                               scale=self.viewer.layers[self.FociImage.currentText()].scale, metadata=fociParams)
        self.initListWidget(self.FociLabels, 'labels')
        self.FociLabels.setCurrentIndex(len(self.viewer.layers) - 1)

    def processTelomeres(self):
        # print('TelImage  data type',type(self.viewer.layers[self.TelImage.currentText()].data))
        # print('processTelomeres',self.TelImage.currentText())
        scale = self.viewer.layers[self.TelImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]
        if isinstance(self.viewer.layers[self.TelImage.currentText()].data, np.ndarray):
            telomereImage = self.viewer.layers[self.TelImage.currentText()].data
        else:
            telomereImage = self.viewer.layers[self.TelImage.currentText()].data.values
        telomereLabels = self.viewer.layers[self.TelLabels.currentText()].data
        if isinstance(self.viewer.layers[self.TelLabels.currentText()].data, xarray.DataArray):
            data = self.viewer.layers[self.TelLabels.currentText()].data.values
        else:
            data = self.viewer.layers[self.TelLabels.currentText()].data
        if len(telomereLabels.shape) != len(telomereImage.shape):
            raise ValueError("The telomere labels layer is %d dimensional, but the telomere image is %d dimensional" % (
                len(telomereLabels.shape), len(telomereImage.shape)))
        props = regionprops_table(data, intensity_image=telomereImage,
                                  properties=('centroid', 'area', 'EquivDiameter', 'Label', 'MeanIntensity'))
        props = pd.DataFrame(props)

        p2 = regionprops_table(data,
                               intensity_image=self.viewer.layers[self.NucLabels.currentText()].data,
                               properties=('centroid', 'MeanIntensity'))
        p2 = pd.DataFrame(p2)
        props['nucleus'] = (p2['MeanIntensity'] + 0.5).astype(int)

        try:
            path = self.viewer.layers[self.TelImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.TelImage.currentText()].source.path

        props['filePath'] = path

        # props['filePath'] = self.viewer.layers[self.TelImage.currentText()].source.path
        props['Channel'] = self.viewer.layers[self.TelImage.currentText()].name
        if len(telomereImage.shape) == 3:
            props.rename(columns={"centroid-0": "Z", "centroid-1": "Y", "centroid-2": "X"}, inplace=True)
        elif len(telomereImage.shape) == 2:
            props.rename(columns={"centroid-0": "Y", "centroid-1": "X"}, inplace=True)
            props['Z'] = 0.0
        props['EquivDiameter(um)'] = props['EquivDiameter'] * np.sqrt(np.sum(scale ** 2))
        props['area(um)'] = props['area'] * abs(np.prod(scale))
        props['Z(um)'] = props['Z'] * scaleZ
        props['X(um)'] = props['X'] * scaleX
        props['Y(um)'] = props['Y'] * scaleY

        return (props)

    def processFoci(self):
        # print('FociImage data type',type(self.viewer.layers[self.FociImage.currentText()].data))
        scale = self.viewer.layers[self.TelImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]
        if isinstance(self.viewer.layers[self.FociImage.currentText()].data, np.ndarray):
            fociImage = self.viewer.layers[self.FociImage.currentText()].data
        else:
            fociImage = self.viewer.layers[self.FociImage.currentText()].data.values

        # print(self.viewer.layers[self.FociLabels.currentText()].data,fociImage)
        forciLabels = self.viewer.layers[self.FociLabels.currentText()].data
        if len(forciLabels.shape) != len(fociImage.shape):
            raise ValueError("The telomere labels layer is %d dimensional, but the telomere image is %d dimensional" % (
                len(forciLabels.shape), len(fociImage.shape)))

        if isinstance(self.viewer.layers[self.FociLabels.currentText()].data, xarray.DataArray):
            data = self.viewer.layers[self.FociLabels.currentText()].data.values
        else:
            data = self.viewer.layers[self.FociLabels.currentText()].data

        # self.viewer.layers[self.FociLabels.currentText()].data

        props = regionprops_table(data, intensity_image=fociImage,
                                  properties=('centroid', 'area', 'EquivDiameter', 'Label', 'MeanIntensity'))
        props = pd.DataFrame(props)
        # self.viewer.layers[self.FociLabels.currentText()].data
        p2 = regionprops_table(data,
                               intensity_image=self.viewer.layers[self.NucLabels.currentText()].data,
                               properties=('centroid', 'MeanIntensity'))
        p2 = pd.DataFrame(p2)
        props['nucleus'] = (p2['MeanIntensity'] + 0.5).astype(int)
        try:
            path = self.viewer.layers[self.FociImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.FociImage.currentText()].source.path

        props['filePath'] = path
        # props['filePath'] = self.viewer.layers[self.FociImage.currentText()].source.path
        props['Channel'] = self.viewer.layers[self.FociImage.currentText()].name
        # Correct Centroid Labels
        if len(fociImage.shape) == 3:
            props.rename(columns={"centroid-0": "Z", "centroid-1": "Y", "centroid-2": "X"}, inplace=True)
        elif len(fociImage.shape) == 2:
            props.rename(columns={"centroid-0": "Y", "centroid-1": "X"}, inplace=True)
            props['Z'] = 0.0
        props['EquivDiameter(um)'] = props['EquivDiameter'] * np.sqrt(np.sum(scale ** 2))
        props['area(um)'] = props['area'] * abs(np.prod(scale))
        props['Z(um)'] = props['Z'] * scaleZ
        props['X(um)'] = props['X'] * scaleX
        props['Y(um)'] = props['Y'] * scaleY
        return (props)

    def processTAF(self):
        scale = self.viewer.layers[self.TelImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]
        t = self.viewer.layers[self.TelLabels.currentText()].data > 0
        f = self.viewer.layers[self.FociLabels.currentText()].data > 0
        taf = t * f
        tafLabels, count = label(taf, connectivity=len(t.shape), return_num=True)
        # print('processTAF',tafLabels,count,self.viewer.layers[self.FociImage.currentText()].scale)
        self.viewer.add_labels(taf, name='tafLabels', scale=self.viewer.layers[self.FociImage.currentText()].scale)
        props = regionprops_table(tafLabels, intensity_image=self.viewer.layers[self.NucLabels.currentText()].data,
                                  properties=('centroid', 'area', 'EquivDiameter', 'Label', 'MeanIntensity'))
        props = pd.DataFrame(props)
        props['nucleus'] = (props['MeanIntensity'] + 0.5).astype(int)
        props.drop(['MeanIntensity'], axis=1, inplace=True)
        if isinstance(self.viewer.layers[self.FociLabels.currentText()].data, xarray.DataArray):
            data_FociLabels = self.viewer.layers[self.FociLabels.currentText()].data.values
        else:
            data_FociLabels = self.viewer.layers[self.FociLabels.currentText()].data

        if isinstance(self.viewer.layers[self.TelLabels.currentText()].data, xarray.DataArray):
            data_TelLabels = self.viewer.layers[self.TelLabels.currentText()].data.values
        else:
            data_TelLabels = self.viewer.layers[self.TelLabels.currentText()].data
        telomere = pd.DataFrame(
            regionprops_table(tafLabels,
                              # intensity_image=self.viewer.layers[self.TelLabels.currentText()].data,
                              intensity_image=data_TelLabels,
                              properties=('centroid', 'MeanIntensity')))
        props['telomere'] = (telomere['MeanIntensity'] + 0.5).astype(int)
        focus = pd.DataFrame(
            regionprops_table(tafLabels,
                              # intensity_image=self.viewer.layers[self.FociLabels.currentText()].data,
                              intensity_image=data_FociLabels,
                              properties=('centroid', 'MeanIntensity')))
        props['focus'] = (focus['MeanIntensity'] + 0.5).astype(int)
        # Correct Centroid Labels
        if len(t.shape) == 3:
            props.rename(columns={"centroid-0": "Z", "centroid-1": "Y", "centroid-2": "X"}, inplace=True)
        elif len(t.shape) == 2:
            props.rename(columns={"centroid-0": "Y", "centroid-1": "X"}, inplace=True)
            props['Z'] = 0.0
        props['EquivDiameter(um)'] = props['EquivDiameter'] * np.sqrt(np.sum(scale ** 2))
        props['area(um)'] = props['area'] * abs(np.prod(scale))
        props['Z(um)'] = props['Z'] * scaleZ
        props['X(um)'] = props['X'] * scaleX
        props['Y(um)'] = props['Y'] * scaleY

        if len(t.shape) == 2:
            tafArray = props[['Y', 'X']].values
        else:
            tafArray = props[['Z', 'Y', 'X']].values

        self.viewer.add_points(tafArray, size=30, name='tafPoints', symbol='ring', face_color='yellow',
                               edge_color='yellow', scale=self.viewer.layers[self.FociImage.currentText()].scale)

        return (props)

    def processNuclei(self):
        # print('processNuclei')
        scale = self.viewer.layers[self.NucImage.currentIndex()].scale
        if isinstance(self.viewer.layers[self.NucImage.currentText()].data, np.ndarray):
            nucleusImage = self.viewer.layers[self.NucImage.currentText()].data
        else:
            nucleusImage = self.viewer.layers[self.NucImage.currentText()].data.values
        nucLabels = self.viewer.layers[self.NucLabels.currentText()].data
        if len(nucLabels.shape) != len(nucleusImage.shape):
            # print('The nuclei labels layer is',len(nucLabels.shape),'-dimensional, but the nuclei image is ',len(nucleusImage.shape),'-D.')
            # print(nucleusImage.shape,nucleusImage.dtype)
            # print(nucLabels.shape,nucLabels.dtype)
            raise ValueError("The nuclei labels layer is %d dimensional, but the nuclei image is %d dimensional" % (
                len(nucLabels.shape), len(nucleusImage.shape)))

        nucleiProps = regionprops_table(nucLabels, intensity_image=nucleusImage,
                                        properties=('centroid', 'area', 'EquivDiameter', 'Label', 'MeanIntensity'))
        nucleiProps = pd.DataFrame(nucleiProps)
        nucleiProps.style.set_caption("Nuclei")
        try:
            path = self.viewer.layers[self.NucImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.NucImage.currentText()].source.path

        nucleiProps['filePath'] = path
        # nucleiProps['filePath'] = self.viewer.layers[self.NucImage.currentText()].source.path
        nucleiProps['Channel'] = self.viewer.layers[self.NucImage.currentText()].name
        # nucleiProps['Marker1'] = 0.0
        # Correct Centroid Labels
        scale = self.viewer.layers[self.NucImage.currentText()].scale
        if len(nucleusImage.shape) == 3:
            nucleiProps.rename(columns={"centroid-0": "Z", "centroid-1": "Y", "centroid-2": "X"}, inplace=True)
            nucleiProps['Z(um)'] = nucleiProps['Z'] * scale[0]
            nucleiProps['X(um)'] = nucleiProps['X'] * scale[1]
            nucleiProps['Y(um)'] = nucleiProps['Y'] * scale[2]

        elif len(nucleusImage.shape) == 2:
            nucleiProps.rename(columns={"centroid-0": "Y", "centroid-1": "X"}, inplace=True)
            nucleiProps['Z'] = 0.0
            nucleiProps['Z(um)'] = 0.0
            nucleiProps['X(um)'] = nucleiProps['X'] * scale[0]
            nucleiProps['Y(um)'] = nucleiProps['Y'] * scale[1]

        nucleiProps['area(um)'] = nucleiProps['area'] * abs(np.prod(scale))
        nucleiProps['EquivDiameter(um)'] = nucleiProps['EquivDiameter'] * np.sqrt(np.sum(scale ** 2))
        # print(nucleiProps.keys())
        return (nucleiProps)

    def testForMarkers(self):
        if len(self.markerList) < 1:
            return (False)
        for markerDict in self.markerList:
            if markerDict['imageWidget'].currentText() != 'None' and markerDict['imageWidget'].currentText() != '' and \
                    markerDict['labelsWidget'].currentText() != '':
                return (True)
        return (False)

    def fakeprocess(self):
        for item in dir(self.viewer.layers[2]):
            try:
                if eval('self.viewer.layers[3].' + item) != eval('self.viewer.layers[2].' + item):
                    print(item, eval('self.viewer.layers[3].' + item), eval('self.viewer.layers[2].' + item))
            except Exception as e:
                print(e)
                # if (eval('self.viewer.layers[3].'+item) != eval('self.viewer.layers[2].'+item)).any().any():
                print(item, eval('self.viewer.layers[3].' + item), eval('self.viewer.layers[2].' + item))

                # for subitem in dir(eval('self.viewer.layers[2].'+item)):
                #     if eval('self.viewer.layers[2].'+item+'['+subitem+']') != eval('self.viewer.layers[3].'+item)[subitem]:
                #         print(item,subitem,eval('self.viewer.layers[3].'+item)[subitem],eval('self.viewer.layers[2].'+item)[subitem])

    def processMarkers(self):
        # print('processMarkers')
        self.markerParamsList = []
        self.markerPropsList = []
        scale = self.viewer.layers[self.NucImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]

        for markerDict in self.markerList:
            # print(markerDict)
            # print(markerDict['imageWidget'].currentText())
            # print(markerDict['labelsWidget'].currentText())
            if markerDict['imageWidget'].currentText() == '' or markerDict['imageWidget'].currentText() == 'None' or \
                    markerDict['labelsWidget'].currentText() == '':
                continue
            # print(markerDict['nameWidget'].text())
            # print(markerDict['imageWidget'].currentText())
            # print(markerDict['labelsWidget'].currentText())
            if isinstance(self.viewer.layers[markerDict['imageWidget'].currentText()].data, np.ndarray):
                MarkerImage = self.viewer.layers[markerDict['imageWidget'].currentText()].data
            else:
                MarkerImage = self.viewer.layers[markerDict['imageWidget'].currentText()].data.values
            markerLabels = self.viewer.layers[markerDict['labelsWidget'].currentText()].data
            if len(markerLabels.shape) != len(MarkerImage.shape):
                print('The ', markerDict['nameWidget'].text(), ' labels layer is', len(markerLabels.shape),
                      '-dimensional, but the ', markerDict['nameWidget'].text(), ' image is ', len(MarkerImage.shape),
                      '-D.')
                print(MarkerImage.shape, MarkerImage.dtype)
                print(markerLabels.shape, markerLabels.dtype)
                raise ValueError("The %s labels layer is %d dimensional, but the %s image is %d dimensional." % (
                    markerDict['nameWidget'].text(), len(markerLabels.shape), markerDict['nameWidget'].text(),
                    len(MarkerImage.shape)))

            histogram, bin_edges = np.histogram(MarkerImage, bins=256, range=(0, 255))
            scale = self.viewer.layers[markerDict['imageWidget'].currentText()].scale
            # print(scale)
            # print(self.viewer.layers[markerDict['labelsWidget'].currentText()].metadata)
            # if self.viewer.layers[markerDict['labelsWidget'].currentText()].metadata == {}:
            path = ""
            try:
                path = self.viewer.layers[markerDict['labelsWidget'].currentText()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[markerDict['labelsWidget'].currentText()].source.path
            params = {'source': self.viewer.layers[markerDict['labelsWidget'].currentText()].source.path}
            self.viewer.layers[markerDict['labelsWidget'].currentText()].metadata = params
            markerParamsDict = {
                'filePath': path,
                # 'filePath': self.viewer.layers[markerDict['imageWidget'].currentText()].source.path,
                'channel': markerDict['imageWidget'].currentText(),
                'labelChannel': markerDict['labelsWidget'].currentText(),
                # 'scaleZ':scale[0],
                # 'scaleY':scale[1],
                # 'scaleX':scale[2],
                'histogram': [histogram],
                'name': markerDict['nameWidget'].text(),
                'params': str(self.viewer.layers[markerDict['labelsWidget'].currentText()].metadata),
                'markerNumber': markerDict['markerNumber']
            }
            # print(markerParamsDict)
            # if len(scale) == 2:
            #     markerParamsDict['scaleZ'] = 1 #?
            #     markerParamsDict['scaleY'] = scale[0]
            #     markerParamsDict['scaleX'] = scale[1]

            # else:
            #     markerParamsDict['scaleZ'] = scale[0]
            #     markerParamsDict['scaleY'] = scale[1]
            #     markerParamsDict['scaleX'] = scale[2]
            markerParamsDict['scaleZ'] = scaleZ
            markerParamsDict['scaleY'] = scaleY
            markerParamsDict['scaleX'] = scaleX

            # print(markerParamsDict)
            markerParamsFrame = pd.DataFrame(markerParamsDict)
            self.markerParamsList.append(markerParamsFrame)
            # print(markerParamsDict)
            # print(MarkerImage.shape,MarkerImage.dtype)
            # print(markerLabels.shape,markerLabels.dtype)
            # print(self.markerParamsList)
            # markerProps = regionprops_table(markerLabels,intensity_image=MarkerImage, properties=('centroid','area','Label','MeanIntensity'))
            try:
                markerProps = regionprops_table(markerLabels, intensity_image=MarkerImage,
                                                properties=('centroid', 'area', 'Label', 'MeanIntensity'))
            except Exception as e:
                print(e)
                return ()
            # print(markerProps)
            markerProps = pd.DataFrame(markerProps)
            # print(markerProps)
            # Correct Centroid Labels
            if len(MarkerImage.shape) == 3:
                markerProps.rename(columns={"centroid-0": "Z", "centroid-1": "Y", "centroid-2": "X"}, inplace=True)
            elif len(MarkerImage.shape) == 2:
                markerProps.rename(columns={"centroid-0": "Y", "centroid-1": "X"}, inplace=True)
                markerProps['Z'] = 0.0
            # print(markerProps)
            markerProps['filePath'] = self.viewer.layers[markerDict['imageWidget'].currentText()].source.path
            markerProps['Channel'] = self.viewer.layers[markerDict['imageWidget'].currentText()].name
            markerProps['labelChannel'] = self.viewer.layers[markerDict['labelsWidget'].currentText()].name
            markerProps['markerNumber'] = markerDict['markerNumber']
            markerProps['name'] = markerDict['nameWidget'].text()

            markerProps['area(um)'] = markerProps['area'] * abs(np.prod(scale))
            markerProps['Z(um)'] = markerProps['Z'] * scaleZ
            markerProps['X(um)'] = markerProps['X'] * scaleX
            markerProps['Y(um)'] = markerProps['Y'] * scaleY

            # print(markerProps)

            self.markerPropsList.append(markerProps)

    # At this point nuclei,telomeres, foci and marker maps are independent and self-referential.
    # Those calculated by senoquant or a compatible pligin should have metadata.
    # If missing, features will be calculated and attahed to the layers.
    #
    def process_action(self):
        worker = self.process()
        # worker.returned.connect()

        # worker.finished.connect(self.processButton.setEnabled(True))
        # worker.started.connect(self.processButton.setEnabled(False))
        # worker.errored.connect(self.processButton.setEnabled(True))

        worker.start()

    # @thread_worker
    def process(self):
        # print('process')
        self.processButton.setEnabled(False)

        noTelomeres = False
        noFoci = False
        noNuclei = False
        # noMarker = False
        if self.NucImage.currentText() == '' or self.NucLabels.currentText() == '':
            print('You must select both a Nuclei image and Nuclei labels.')
            self.processButton.setEnabled(True)
            return ()
        if self.TelImage.currentText() == '' or self.TelLabels.currentText() == '':
            noTelomeres = True
        if self.FociImage.currentText() == '' or self.FociLabels.currentText() == '':
            noFoci = True

        noMarker = not self.testForMarkers()

        nucleiDimension = len(self.viewer.layers[self.NucLabels.currentText()].data.shape)

        if not noMarker:
            for markerDict in self.markerList:
                if markerDict['imageWidget'].currentText() == '' or markerDict['imageWidget'].currentText() == 'None' or \
                        markerDict['labelsWidget'].currentText() == '':
                    continue
                markerDimension = len(self.viewer.layers[markerDict['labelsWidget'].currentText()].data.shape)
                if nucleiDimension != markerDimension:
                    print('All dimensions must be consistent for marker analysis.')
                    print('Nuclei mask dimension: ', nucleiDimension)
                    print(markerDict['nameWidget'].text(), 'mask dimension: ', markerDimension)
                    self.processButton.setEnabled(True)
                    return ()
        if not noTelomeres:
            telomereDimension = len(self.viewer.layers[self.TelLabels.currentText()].data.shape)

        if not noFoci:
            fociDimension = len(self.viewer.layers[self.FociLabels.currentText()].data.shape)

        if not noTelomeres and not noFoci:
            if (nucleiDimension != telomereDimension) or (nucleiDimension != fociDimension):
                print('All dimensions must be consistent for TAF analysis.')
                print('Nuclei mask dimension: ', nucleiDimension)
                print('Telomere mask dimension: ', telomereDimension)
                print('Foci mask dimension: ', fociDimension)
                self.processButton.setEnabled(True)
                return ()

        self.modelParams = pd.DataFrame.from_dict([self.viewer.layers[self.NucLabels.currentText()].metadata])
        try:
            nucleiProps = self.processNuclei()
        except Exception as e:
            print('process() encountered an exception:', e)
            return ()
        self.nucleiProps = nucleiProps
        # display(self.nucleiProps)

        try:
            self.processMarkers()
        except Exception as e:

            print('processMarkers() encountered an exception:', e)
            return ()

        if not noTelomeres:
            self.telomereParams = pd.DataFrame.from_dict([self.viewer.layers[self.TelLabels.currentText()].metadata])
            try:
                telomereProps = self.processTelomeres()
                numTel = pd.DataFrame()
                numTel['numTelomeres'] = [len(telomereProps.query("nucleus == @x")) for x in
                                          nucleiProps['Label'].values]
                nucleiProps['numTelomeres'] = numTel['numTelomeres']
                self.telomereProps = telomereProps

                # display(self.telomereProps)
            except Exception as e:
                print('process() encountered an exception:', e)
                self.processButton.setEnabled(True)
        else:
            nucleiProps['numTelomeres'] = 0

        if not noFoci:
            try:
                fociProps = self.processFoci()
                self.fociParams = pd.DataFrame.from_dict([self.viewer.layers[self.FociLabels.currentText()].metadata])
                numFoci = pd.DataFrame()
                numFoci['numFoci'] = [len(fociProps.query("nucleus == @x")) for x in nucleiProps['Label'].values]
                nucleiProps['numFoci'] = numFoci['numFoci']
                self.fociProps = fociProps

                # display(self.fociProps)

            except Exception as e:
                print('process() encountered an exception:', e)
        else:
            nucleiProps['numFoci'] = 0

        if not noTelomeres and not noFoci:
            try:
                TAFProps = self.processTAF()
                numTAF = pd.DataFrame()
                numTAF['numTAF'] = [len(TAFProps.query("nucleus == @x")) for x in nucleiProps['Label'].values]
                nucleiProps['numTAF'] = numTAF['numTAF']
                self.TAFProps = TAFProps
                # display(self.TAFProps)
            except Exception as e:
                print('process() encountered an exception:', e)
                # self.processButton.setEnabled(True)
        else:
            nucleiProps['numTAF'] = 0

        # Construct filename and enable save #
        # print(self.viewer.layers[self.NucImage.currentText()].source.path)
        # if self.viewer.layers[self.NucImage.currentText()].source.path == '':
        #     imageFileBaseName = 'derived'
        # else:
        #     imageFileBaseName = os.path.basename( self.viewer.layers[self.NucImage.currentText()].source.path)
        # try:
        #     # print("Process:",self,self.saveDir)
        #     excelFilePath = os.path.join(self.saveDir,imageFileBaseName) + '.xlsx'           
        # # except Exception as e:          
        #     # print('Process: Exception \n',e)
        # except:          
        #     if self.viewer.layers[self.NucImage.currentText()].source.path == '':
        #         self.saveDir = os.getcwd()
        #     else:
        #         self.saveDir = os.path.dirname(self.viewer.layers[self.NucImage.currentText()].source.path)
        #     # print("Process:",self,self.saveDir)
        #     excelFilePath = os.path.join(self.saveDir,imageFileBaseName) + '.xlsx'
        # print("Process:",self.saveDir,imageFileBaseName,excelFilePath)

        # .metadata['aicsimage'].reader._path
        try:
            path = self.viewer.layers[self.NucImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.NucImage.currentText()].source.path

        try:
            # imageFileBaseName = os.path.basename(self.viewer.layers[self.NucImage.currentText()].source.path)
            imageFileBaseName = os.path.basename(path)
        except:
            imageFileBaseName = 'derived'
        try:
            excelFilePath = os.path.join(self.saveDir, imageFileBaseName) + '.xlsx'
        except:
            try:

                self.saveDir = os.path.dirname()
            except:
                self.saveDir = os.getcwd()
            try:
                excelFilePath = os.path.join(self.saveDir, imageFileBaseName) + '.xlsx'
            except Exception as e:
                raise (e)

        self.output_widget.file_naming.dir_name_edit.setText(str(excelFilePath))
        self.output_widget.saveButton.setEnabled(True)
        self.output_widget.file_naming.setEnabled(True)
        self.output_widget.file_dialog_append.dir_name_edit.setText(str(excelFilePath))
        self.output_widget.file_dialog_append.setEnabled(True)
        self.output_widget.file = os.path.basename(excelFilePath)
        self.output_widget.dir_name = self.saveDir
        self.processButton.setEnabled(True)

    '''
    Saving to Excel and Database as well as image data stored in layers 
    '''

    def save(self):
        self.output_widget.saveButton.setEnabled(False)
        self.output_widget.file_naming.setEnabled(False)
        self.saveToExcel()
        self.saveToDataBase()
        self.saveImages()

    def saveToExcel(self):
        mode = 'w'
        if (self.output_widget.file_dialog_append.isChecked() and
                self.output_widget.file_dialog_append.dir_name_edit.text() != ""):
            mode = 'a'
            file = self.output_widget.file_dialog_append.dir_name_edit.text()
        else:
            file = self.output_widget.file_naming.dir_name_edit.text()
        self.saveDir = os.path.dirname(file)
        print('saveToExcel:self.saveDir ', self.saveDir)
        noTelomeres = False
        noFoci = False
        noNuclei = False
        if self.NucImage.currentText() == '' or self.NucLabels.currentText() == '':
            print('You must select both a Nuclei image and Nuclei labels.')
            return ()
        if self.TelImage.currentText() == '' or self.TelLabels.currentText() == '':
            noTelomeres = True
        if self.FociImage.currentText() == '' or self.FociLabels.currentText() == '':
            noFoci = True

        noTAF = noTelomeres or noFoci
        noMarker = not self.testForMarkers()

        # print(noNuclei,noTelomeres,noFoci,noTAF,noMarker)

        # Analysis
        A = dict()
        A['user'] = os.getlogin()
        A['platform'] = platform.node()
        A['datetime'] = datetime.datetime.now()
        Analyses = pd.DataFrame.from_dict([A])
        # nucleiParams
        scale = self.viewer.layers[self.NucImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]
        try:
            path = self.viewer.layers[self.NucImage.currentText()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.NucImage.currentText()].source.path
        nucleiParams = {
            # 'filePath': self.viewer.layers[self.NucImage.currentIndex()].source.path,
            'filePath': path,
            'channel': self.viewer.layers[self.NucImage.currentIndex()].name,
            'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
            'params': {}}
        try:  # get metadata from nuclei mask
            nucleiParams['params'] = str(self.viewer.layers[self.NucLabels.currentText()].metadata)
        except Exception as e:
            print('nucleiParams', e)
            pass
        nucleiParams = pd.DataFrame.from_dict([nucleiParams])

        markerParamsList = self.markerParamsList
        markerPropsList = self.markerPropsList

        if not noTelomeres:
            scale = self.viewer.layers[self.TelImage.currentIndex()].scale
            if len(scale) == 3:
                scaleZ = scale[0]
                scaleY = scale[1]
                scaleX = scale[2]
            elif len(scale) == 2:
                scaleZ = 1
                scaleY = scale[0]
                scaleX = scale[1]
            try:
                path = self.viewer.layers[self.TelImage.currentIndex()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[self.TelImage.currentIndex()].source.path
            telomereParams = {
                # 'filePath': self.viewer.layers[self.TelImage.currentIndex()].source.path,
                'filePath': path,
                'channel': self.viewer.layers[self.TelImage.currentIndex()].name,
                'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
                'params': {}}

            try:  # get metadata from telomere mask
                telomereParams['params'] = str(self.viewer.layers[self.TelLabels.currentText()].metadata)
            except Exception as e:
                print('telomereParams', e)
                pass
            telomereParams = pd.DataFrame.from_dict([telomereParams])

        if not noFoci:
            scale = self.viewer.layers[self.FociImage.currentIndex()].scale
            if len(scale) == 3:
                scaleZ = scale[0]
                scaleY = scale[1]
                scaleX = scale[2]
            elif len(scale) == 2:
                scaleZ = 1
                scaleY = scale[0]
                scaleX = scale[1]
            try:
                path = self.viewer.layers[self.TelImage.currentIndex()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[self.TelImage.currentIndex()].source.path
            fociParams = {
                # 'filePath': self.viewer.layers[self.FociImage.currentText()].source.path,
                'filePath': path,
                'channel': self.viewer.layers[self.FociImage.currentText()].name,
                'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
                'params': {}}
            try:  # get metadata from foci mask
                fociParams['params'] = str(self.viewer.layers[self.FociLabels.currentText()].metadata)
            except Exception as e:
                print('fociParams', e)
                pass
            fociParams = pd.DataFrame.from_dict([fociParams])

            # Add marker means to nucleiProps
        nucleiProps = self.nucleiProps.copy()
        if not noMarker:
            for marker in markerPropsList:
                markerName = marker.loc[0]['name']
                markerColumnDropList = ['Y_' + markerName, 'X_' + markerName, 'area_' + markerName, 'Z_' + markerName,
                                        'Y(um)_' + markerName, 'X(um)_' + markerName, 'area(um)_' + markerName,
                                        'Z(um)_' + markerName,
                                        'filePath_' + markerName, 'Channel_' + markerName, 'labelChannel',
                                        'markerNumber', 'name']
                nucleiProps = pd.merge(nucleiProps, marker, how='left', on='Label',
                                       suffixes=('', '_' + markerName)).drop(markerColumnDropList, axis=1)
                # print(self.nucleiProps.keys())

        if mode == 'a':
            writer = pd.ExcelWriter(file, mode=mode, engine="openpyxl", if_sheet_exists='overlay')
        else:
            writer = pd.ExcelWriter(file, mode=mode, engine="openpyxl")

        def write(sheet: str, df: pd.DataFrame):
            row = writer.sheets[sheet].max_row if sheet in list(writer.sheets.keys()) else 0
            header = True
            print(row)

            if mode == 'a' and row != 0:
                header = False

                # Don't need break for each interation
                # pd.DataFrame('-', index=range(1),
                #              columns=range(len(df.columns))).to_excel(writer, sheet_name=sheet, startrow=row, header=True)

            df.to_excel(writer, sheet_name=sheet, startrow=row, header=header)

        write('Analyses', Analyses)

        # if mode == 'a':
        #     pd.DataFrame('-', index=range(1),
        #                  columns=range(len(Analyses.columns))).to_excel(writer, sheet_name='Analysis',
        #                                                                 startrow=writer.sheets['Analysis'].max_row)
        # Analyses.to_excel(writer, sheet_name='Analysis', startrow=writer.sheets['Analysis'].max_row)

        # if mode == 'a':
        #     pd.DataFrame('-', index=range(1),
        #                  columns=range(len(nucleiParams.columns))).to_excel(writer,
        #                                                                     sheet_name='nucleiParams',
        #                                                                     startrow=writer.sheets[
        #                                                                         'nucleiParams'].max_row)
        # nucleiParams.to_excel(writer, sheet_name='nucleiParams', startrow=writer.sheets['nucleiParams'].max_row)
        # self.nucleiProps.to_excel(writer,sheet_name='nucleiProps')

        write('nucleiParams', nucleiParams)

        # if mode == 'a':
        #     pd.DataFrame('-', index=range(1), columns=range(len(nucleiProps.columns))).to_excel(writer, sheet_name='nucleiProps',
        #                                                                    startrow=writer.sheets[
        #                                                                        'nucleiProps'].max_row)
        # nucleiProps.to_excel(writer, sheet_name='nucleiProps',
        #                      startrow=writer.sheets['nucleiProps'].max_row)

        write('nucleiProps', nucleiProps)

        # if  'marker1Params' in locals():
        #     print('marker1Params in locals()')
        #     marker1Params.to_excel(writer,sheet_name='markerParams')
        #     marker1Props.to_excel(writer,sheet_name='markerProps')
        if not noMarker:
            # print('saveToExcel',markerParamsList)
            markerParams = pd.concat(markerParamsList)
            markerProps = pd.concat(markerPropsList)
            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(markerParams.columns))).to_excel(writer, sheet_name='markerParams',
            #                                                                     startrow=writer.sheets[
            #                                                                         'markerParams'].max_row)
            # markerParams.to_excel(writer, sheet_name='markerParams', startrow=writer.sheets['markerParams'].max_row)

            write('markerParams', markerParams)

            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(markerProps.columns))).to_excel(writer, sheet_name='markerProps',
            #                                                                    startrow=writer.sheets[
            #                                                                        'markerProps'].max_row)
            # markerProps.to_excel(writer, sheet_name='markerProps', startrow=writer.sheets['markerProps'].max_row)

            write('markerProps', markerProps)

        if not noTelomeres:
            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(telomereParams.columns))).to_excel(writer, sheet_name='telomereParams',
            #                                                                       startrow=writer.sheets[
            #                                                                           'telomereParams'].max_row)
            # telomereParams.to_excel(writer, sheet_name='telomereParams',
            #                         startrow=writer.sheets['telomereParams'].max_row)

            write('telomereParams', telomereParams)

            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(self.telomereProps.columns))).to_excel(writer,
            #                                                                           sheet_name='telomereProps',
            #                                                                           startrow=writer.sheets[
            #                                                                               'telomereProps'].max_row)
            # self.telomereProps.to_excel(writer, sheet_name='telomereProps',
            #                             startrow=writer.sheets['telomereProps'].max_row)

            write('telomereProps', self.telomereProps)

        if not noFoci:
            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(fociParams.columns))).to_excel(writer, sheet_name='fociParams',
            #                                                                   startrow=writer.sheets[
            #                                                                       'fociParams'].max_row)
            # fociParams.to_excel(writer, sheet_name='fociParams', startrow=writer.sheets['fociParams'].max_row)

            write('fociParams', fociParams)

            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(self.fociProps.columns))).to_excel(writer, sheet_name='fociProps',
            #                                                                       startrow=writer.sheets[
            #                                                                           'fociProps'].max_row)
            # self.fociProps.to_excel(writer, sheet_name='fociProps', startrow=writer.sheets['fociProps'].max_row)

            write('fociProps', self.fociProps)

        if not noTAF:
            # if mode == 'a':
            #     pd.DataFrame('-', index=range(1),
            #                  columns=range(len(self.TAFProps.columns))).to_excel(writer, sheet_name='TAFProps',
            #                                                                      startrow=writer.sheets[
            #                                                                          'TAFProps'].max_row)
            # self.TAFProps.to_excel(writer, sheet_name='TAFProps', startrow=writer.sheets['TAFProps'].max_row)

            write('TAFProps', self.TAFProps)

        writer.close()

        # SaveToDatBase
        # self.output_widget.saveButton.setEnabled(False)
        # self.output_widget.file_naming.setEnabled(False)

        # self.saveToDataBase()

    def appendToTable(self, df, table, engine):
        try:
            df.to_sql(name=table, con=engine, if_exists='append', index=False)
        except:
            try:
                query = text("SHOW COLUMNS FROM `%s`" % table)
                with engine.connect() as conn:
                    columnsTable = pd.read_sql(query, conn)
                # print(columnsTable)   
                tableHasColumns = list(columnsTable.Field.values)
                # print('Table has columns:',tableHasColumns)
                dfHasColumns = df.keys().values
                # print('df has columns:',dfHasColumns)
                tableNeedsColumns = np.setdiff1d(dfHasColumns, tableHasColumns)
                # print('Table needs columns',tableNeedsColumns)                
                for column in tableNeedsColumns:
                    # print(column,df[column].dtype)
                    if df[column].dtype == 'float' or df.dtype == 'double':
                        query = text("ALTER TABLE `%s` ADD COLUMN `%s` double" % (table, column))
                    elif df[column].dtype == 'text':
                        query = text("ALTER TABLE `%s` ADD COLUMN `%s` text" % (table, column))
                    elif 'int' in df[column].dtype:
                        query = text("ALTER TABLE `%s` ADD COLUMN `%s` BIGINT" % (table, column))

                    # print(query)
                    with engine.connect() as conn:
                        conn.execute(query)
                df.to_sql(name=table, con=engine, if_exists='append', index=False)
            except Exception as e:
                print(e)
                return ()

    # @thread_worker
    def saveToDataBase(self):
        ENABLE_SAVE_NUCLEI = True  # XXX
        noTelomeres = False
        noFoci = False
        noNuclei = False
        if self.NucImage.currentText() == '' or self.NucLabels.currentText() == '':
            print('You must select both a Nuclei image and Nuclei labels.')
            return ()
        if self.TelImage.currentText() == '' or self.TelLabels.currentText() == '':
            noTelomeres = True
        if self.FociImage.currentText() == '' or self.FociLabels.currentText() == '':
            noFoci = True
        noTAF = noTelomeres or noFoci
        noMarker = not self.testForMarkers()

        if noNuclei:
            return ()

        if node == 'R5318238':
            engine = create_engine('mysql+pymysql://Senoquant:<EMAIL>/Senoquant_test')
            # localURL = URL.create(
            #         "postgresql",
            #         username="senoquant",
            #         password="senoquant",  # plain (unescaped) text
            #         host="localhost",
            #         database="senoquant_test",
            #     )
            # engine = create_engine(localURL)
            # nucSaveFile = "D:\\BiomarkerAnalysis\\Nuclei\\Nuclei.h5"
        else:
            engine = create_engine('mysql+pymysql://Senoquant:<EMAIL>/Senoquant')
            # nucSaveFile = "\\\\bir-external\\BiomarkerAnalysis\\Nuclei\\Nuclei.h5"

        try:
            engine.connect()
        except:
            print('Cannot connect to database server.')
            return ()
        print('Save to database.')

        try:
            with engine.connect() as conn:
                query = text('select ID from Analyses order by ID desc limit 1')
                # print(pd.read_sql(query, conn))
                newID = pd.read_sql(query, conn).values[0][0] + 1
            # print('newID',newID)
        except Exception as e:
            print('Caught Exception', e)
            print('New database.')
            newID = 1
        A = dict()
        A['user'] = os.getlogin()
        A['platform'] = platform.node()
        A['datetime'] = datetime.datetime.now()
        A['ID'] = newID
        Analyses = pd.DataFrame.from_dict([A])
        Analyses.to_sql(name='Analyses', con=engine, if_exists='append', index=False)
        # Nuclei
        self.nucleiProps['AnalysisID'] = newID
        self.appendToTable(self.nucleiProps, 'nucleiProps', engine)

        # The rest of it
        # try:
        #     self.nucleiProps.to_sql(name='nucleiProps', con=engine, if_exists = 'append', index=False)
        # except:
        #     # print(e)
        #     try:
        #         query = text("SHOW COLUMNS FROM `nucleiProps`") #
        #         with engine.connect() as conn:
        #             columnsTable = pd.read_sql(query,conn)
        #         print(columnsTable)
        #         tableHasColumns = list(columnsTable.Field.values)
        #         print('Table has columns:',tableHasColumns)
        #         dfHasColumns = self.nucleiProps.keys().values
        #         print('df has columns:',dfHasColumns)
        #         # tableNeedsColumns = set(dfHasColumns) - set(dfHasColumns)
        #         tableNeedsColumns = np.setdiff1d(dfHasColumns,tableHasColumns)
        #         print('Table needs columns',tableNeedsColumns)

        #         for column in tableNeedsColumns:
        #             print(column,self.nucleiProps[column].dtype)
        #             if self.nucleiProps[column].dtype == 'float' or self.nucleiProps[column].dtype == 'double':
        #                 query = text("ALTER TABLE `nucleiProps` ADD COLUMN `%s` double"%column)
        #             elif self.nucleiProps[column].dtype == 'text':
        #                 query = text("ALTER TABLE `nucleiProps` ADD COLUMN `%s` text"%column)
        #             elif 'int' in self.nucleiProps[column].dtype:
        #                 query = text("ALTER TABLE `nucleiProps` ADD COLUMN `%s` BIGINT"%column)

        #             print(query)
        #             with engine.connect() as conn:
        #                 conn.execute(query)

        #         self.nucleiProps.to_sql(name='nucleiProps', con=engine, if_exists = 'append', index=False)
        #     except Exception as e:
        #         print(e)
        #         return()

        #             if 'area(um)' not in columnsTable.Field.values:
        #                 query = text("ALTER TABLE `nucleiProps` ADD COLUMN `area(um)` double")
        #                 with engine.connect() as conn:
        #                     conn.execute(query)
        #                     print('area(um) added')
        #             if 'EquivDiameter(um)' not in columnsTable.Field.values:
        #                 query = text("ALTER TABLE `nucleiProps` ADD COLUMN `EquivDiameter(um)` double")
        #                 with engine.connect() as conn:
        #                     conn.execute(query)
        #                     print('EquivDiameter(um) added')

        # query = text("ALTER TABLE `nucleiProps` DROP COLUMN `area(um)`;ALTER TABLE nucleiProps DROP COLUMN `EquivDiameter(um)`")
        # with engine.connect() as conn:
        #     pd.read_sql_query(query,conn)

        # query = text("ALTER TABLE `nucleiProps` DROP COLUMN `area(um)`")
        # engine.connect().execute(query)

        # query = text("ALTER TABLE `nucleiProps` DROP COLUMN `EquivDiameter(um)`")
        # engine.connect().execute(query)

        del self.nucleiProps
        scale = self.viewer.layers[self.NucImage.currentIndex()].scale
        if len(scale) == 3:
            scaleZ = scale[0]
            scaleY = scale[1]
            scaleX = scale[2]
        elif len(scale) == 2:
            scaleZ = 1
            scaleY = scale[0]
            scaleX = scale[1]
        try:
            path = self.viewer.layers[self.NucImage.currentIndex()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.NucImage.currentIndex()].source.path
        nucleiParams = {'AnalysisID': newID,
                        'filePath': path,
                        # 'filePath': self.viewer.layers[self.NucImage.currentIndex()].source.path,
                        'channel': self.viewer.layers[self.NucImage.currentIndex()].name,
                        'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
                        'params': {}}
        try:  # get metadata from nuclei mask
            nucleiParams['params'] = str(self.viewer.layers[self.NucLabels.currentText()].metadata)
        except Exception as e:
            print('nucleiParams', e)
            pass
        nucleiParams = pd.DataFrame.from_dict([nucleiParams])
        # print(nucleiParams)

        nucleiParams.to_sql(name='nucleiParams', con=engine, if_exists='append', index=False)
        # Markers
        if not noMarker:
            try:

                markerParams = pd.concat(self.markerParamsList)
                markerProps = pd.concat(self.markerPropsList)

                markerParams['AnalysisID'] = newID
                markerProps['AnalysisID'] = newID

                markerParams.to_sql(name='markerParams', con=engine, if_exists='append', index=False)
                # markerProps.to_sql(name='markerProps', con=engine, if_exists = 'append', index=False)
                self.appendToTable(markerProps, 'markerProps', engine)
                self.markerParamsList = list()
                self.markerPropsList = list()
            except Exception as e:
                print(e)
                pass

        if not noTelomeres:
            # Telomeres
            self.telomereProps['AnalysisID'] = newID
            # self.telomereProps.to_sql(name='telomereProps', con=engine, if_exists = 'append', index=False)
            self.appendToTable(self.telomereProps, 'telomereProps', engine)
            del self.telomereProps
            scale = self.viewer.layers[self.TelImage.currentIndex()].scale
            if len(scale) == 3:
                scaleZ = scale[0]
                scaleY = scale[1]
                scaleX = scale[2]
            elif len(scale) == 2:
                scaleZ = 1
                scaleY = scale[0]
                scaleX = scale[1]
            try:
                path = self.viewer.layers[self.TelImage.currentIndex()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[self.TelImage.currentIndex()].source.path
            telomereParams = {'AnalysisID': newID,
                              'filePath': path,
                              # 'filePath': self.viewer.layers[self.TelImage.currentIndex()].source.path,
                              'channel': self.viewer.layers[self.TelImage.currentIndex()].name,
                              'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
                              'params': {}}
            try:  # get metadata from telomere mask
                telomereParams['params'] = str(self.viewer.layers[self.TelLabels.currentText()].metadata)
            except Exception as e:
                print('telomereParams', e)
                pass
            telomereParams = pd.DataFrame.from_dict([telomereParams])
            telomereParams.to_sql(name='telomereParams', con=engine, if_exists='append', index=False)

        if not noFoci:
            # Foci
            self.fociProps['AnalysisID'] = newID
            self.appendToTable(self.fociProps, 'fociProps', engine)
            # self.fociProps.to_sql(name='fociProps', con=engine, if_exists = 'append', index=False)
            del self.fociProps
            scale = self.viewer.layers[self.FociImage.currentIndex()].scale
            if len(scale) == 3:
                scaleZ = scale[0]
                scaleY = scale[1]
                scaleX = scale[2]
            elif len(scale) == 2:
                scaleZ = 1
                scaleY = scale[0]
                scaleX = scale[1]
            try:
                path = self.viewer.layers[self.FociImage.currentIndex()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[self.FociImage.currentIndex()].source.path
            fociParams = {'AnalysisID': newID,
                          'filePath': path,
                          # 'filePath': self.viewer.layers[self.FociImage.currentText()].source.path,
                          'channel': self.viewer.layers[self.FociImage.currentText()].name,
                          'scaleZ': scaleZ, 'scaleY': scaleY, 'scaleX': scaleX,
                          'params': {}}
            try:  # get metadata from foci mask
                fociParams['params'] = str(self.viewer.layers[self.FociLabels.currentText()].metadata)
            except Exception as e:
                print('fociParams', e)
                pass
            fociParams = pd.DataFrame.from_dict([fociParams])
            fociParams.to_sql(name='fociParams', con=engine, if_exists='append', index=False)

        if not noTAF:
            # TAF
            self.TAFProps['AnalysisID'] = newID
            # self.TAFProps.to_sql(name='TAFProps', con=engine, if_exists = 'append', index=False)
            self.appendToTable(self.TAFProps, 'TAFProps', engine)
            del self.TAFProps

        # self.saveImages()

    # def testSvetoExcelConnection(self):
    #     print('testSvetoExcelConnection',self.output_widget.file_naming.dir_name_edit.text())

    def saveImages(self):
        if node == 'R5318238':
            nucSaveFile = "D:\\BiomarkerAnalysis\\Nuclei\\Nuclei.h5"
        else:
            nucSaveFile = "\\\\bir-external\\BiomarkerAnalysis\\Nuclei\\Nuclei.h5"
        if self.output_widget.enable_save_images.isChecked():  # XXX
            import h5py
            print("Save images")
            if isinstance(self.viewer.layers[self.NucImage.currentText()].data, np.ndarray):
                nucleusImage = self.viewer.layers[self.NucImage.currentText()].data
            else:
                nucleusImage = self.viewer.layers[self.NucImage.currentText()].data.values
            # tag = self.viewer.layers[self.NucImage.currentText()].source.path + '/' + self.NucImage.currentText()
            tag = self.viewer.layers[
                      self.NucImage.currentText()].source.path + '/' + self.NucImage.currentText() + '/' + os.getlogin()
            nucLabels = self.viewer.layers[self.NucLabels.currentText()].data
            # imagesMeta = self.viewer.layers[self.NucImage.currentText()].metadata
            # labelsMeta = self.viewer.layers[self.NucLabels.currentText()].metadata
            # dfile = self.nucSaveFile
            start = time.time()
            if not os.path.exists(os.path.dirname(nucSaveFile)):
                os.makedirs(os.path.dirname(nucSaveFile))
            with h5py.File(nucSaveFile, 'a') as hf:
                try:
                    dataset = hf.create_dataset("/%s/image" % tag, data=nucleusImage)

                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucImage.currentText()].scale

                    # hf.create_dataset("/%s/image"%tag,data=nucleusImage)
                except:
                    del hf["%s/image" % tag]
                    hf.create_dataset("/%s/image" % tag, data=nucleusImage)

                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucImage.currentText()].scale
                try:
                    dataset = hf.create_dataset("/%s/labels" % tag, data=nucLabels)

                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucLabels.currentText()].scale
                except:
                    del hf["%s/labels" % tag]
                    hf.create_dataset("/%s/labels" % tag, data=nucLabels)

                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucLabels.currentText()].scale
                # TODO: Need to Look into it
                try:
                    dataset = hf.create_dataset("/%s/scale" % tag,
                                                data=self.viewer.layers[self.NucImage.currentText()].scale)
                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucImage.currentText()].scale
                except:
                    del hf["%s/scale" % tag]
                    hf.create_dataset("/%s/scale" % tag, data=self.viewer.layers[self.NucImage.currentText()].scale)

                    # Save the scale information as an attribute of the dataset
                    dataset.attrs['scale'] = self.viewer.layers[self.NucImage.currentText()].scale

                # try:
                #     dict_group = hf.create_group("/%s/imagesMeta"%tag)
                # except:
                #     del hf["%s/imagesMeta"%tag]
                #     dict_group = hf.create_group("/%s/imagesMeta"%tag)
                # for k, v in imagesMeta.items():
                #     dict_group[k] = v
                # try:
                #     dict_group = hf.create_group("/%s/labelsMeta"%tag)
                # except:
                #     del hf["%s/labelsMeta"%tag]
                #     dict_group = hf.create_group("/%s/labelsMeta"%tag)
                # for k, v in labelsMeta.items():
                #     dict_group[k] = v

                imageFilename = 'Image-' + \
                                os.path.basename(self.viewer.layers[self.NucImage.currentText()].source.path) + \
                                ':' + self.NucImage.currentText() + '-' + os.getlogin()
                imageFilename = imageFilename.replace('.', '').replace(' ', '').replace(':', '') + '.tif'
                imageScale = self.viewer.layers[self.NucImage.currentText()].scale
                print(imageScale)
                napari.save_layers(path=os.path.join(self.saveDir, imageFilename),
                                   layers=[self.viewer.layers[self.NucImage.currentText()]])
                # if len(imageScale) == 3:
                #     print(1.0 / imageScale[1], 1.0 / imageScale[2])
                #     imwrite(os.path.join(self.saveDir, imageFilename), nucleusImage,
                #             # imagej=True, \
                #             # resolution=(1.0/imageScale[1], 1.0/imageScale[2]),\
                #             metadata={'spacing': imageScale[0], 'unit': 'um', 'axes': 'ZYX'})
                # else:
                #     imwrite(os.path.join(self.saveDir, imageFilename), nucleusImage,
                #             # imagej=True, \
                #             # resolution=(1.0/imageScale[0], 1.0/imageScale[1]),\
                #             metadata={'unit': 'um', 'axes': 'YX'})
                labelsFilename = 'Labels-' + \
                                 os.path.basename(self.viewer.layers[self.NucImage.currentText()].source.path) + \
                                 ':' + self.NucImage.currentText() + '-' + os.getlogin()
                labelsFilename = labelsFilename.replace('.', '').replace(' ', '').replace(':', '') + '.tif'
                labelsScale = self.viewer.layers[self.NucLabels.currentText()].scale
                napari.save_layers(path=os.path.join(self.saveDir, labelsFilename),
                                   layers=[self.viewer.layers[self.NucLabels.currentText()]])
                # if len(labelsScale) == 3:
                #     imwrite(os.path.join(self.saveDir, labelsFilename), nucLabels,
                #             # imagej=True, \
                #             # resolution=(1.0/labelsScale[1], 1.0/labelsScale[2]),\
                #             metadata={'spacing': labelsScale[0], 'unit': 'um', 'axes': 'ZYX'})
                # else:
                #     imwrite(os.path.join(self.saveDir, labelsFilename), nucLabels,
                #             # imagej=True, \
                #             # resolution=(1.0/labelsScale[0], 1.0/labelsScale[1]),\
                #             metadata={'unit': 'um', 'axes': 'YX'})

            end = time.time()
            print('Image save time', end - start, ' seconds.')


class Marker1Widget(QWidget):
    TAB_NAME: Final = "Napari Senoquant: Cytoplasmic Marker Labels"

    def __init__(self, napari_viewer):
        self.ENABLE_WATERSHED = True
        super().__init__()
        self.viewer = napari_viewer
        # Dilations
        self.Marker1NucMaskLabel = QLabel('Nuclei Labels', self)
        self.Marker1NucMask = QComboBox()
        Marker1ImageBox = QHBoxLayout()
        Marker1ImageBox.addWidget(self.Marker1NucMaskLabel)
        Marker1ImageBox.addWidget(self.Marker1NucMask)

        # Dilation size Slider
        self.DilationSizeSliderLabel = QLabel('Dilation Size', self)
        self.DilationSizeSliderReadout = QLabel('11', self)
        self.DilationSizeSlider = QSlider(1)
        self.DilationSizeSlider.setMinimum(1)
        self.DilationSizeSlider.setMaximum(21)
        self.DilationSizeSlider.setValue(11)
        self.DilationSizeSlider.setTickPosition(QSlider.TicksBelow)
        self.DilationSizeSlider.setTickInterval(2)
        self.DilationSizeSlider.valueChanged.connect(self.updateDilationSizeSliderReadout)
        DilationSizeSliderBox = QHBoxLayout()
        DilationSizeSliderBox.addWidget(self.DilationSizeSliderLabel)
        DilationSizeSliderBox.addWidget(self.DilationSizeSliderReadout)
        DilationSizeSliderBox.addWidget(self.DilationSizeSlider)

        self.btn = QPushButton("Dilate Nuclei Labels")
        self.btn.clicked.connect(self._findMarker1Labels_action)
        Box = QHBoxLayout()
        Box.addWidget(self.btn)

        if self.ENABLE_WATERSHED:
            # Cytoplasmic Watershed
            self.CytoImageLabel = QLabel('Cytoplasmic Channel', self)
            self.CytoImage = QComboBox()
            self.initListWidget(self.CytoImage, imageOrLabels='image')

            CytoImageeBox = QHBoxLayout()
            CytoImageeBox.addWidget(self.CytoImageLabel)
            CytoImageeBox.addWidget(self.CytoImage)

            self.CytoButton = QPushButton("Find Cells From Nuclei")
            self.CytoButton.clicked.connect(self._watershed_action)
            CytoButtonBox = QHBoxLayout()
            CytoButtonBox.addWidget(self.CytoButton)

        layout = QVBoxLayout()
        layout.addLayout(Marker1ImageBox)
        layout.addLayout(DilationSizeSliderBox)
        layout.addLayout(Box)
        if self.ENABLE_WATERSHED:
            layout.addLayout(CytoImageeBox)
            layout.addLayout(CytoButtonBox)

        self.setLayout(layout)

        self.viewer.layers.events.inserted.connect(self.refreshImageSelectors)
        self.viewer.layers.events.removed.connect(self.refreshImageSelectors)
        self.viewer.layers.events.moved.connect(self.refreshImageSelectors)
        self.viewer.layers.events.reordered.connect(self.refreshImageSelectors)

    def initListWidget(self, widget, imageOrLabels='image'):  # Also works with comboBox
        # print('initListWidget')
        # currentIndex = widget.currentIndex()
        currentText = widget.currentText()
        widget.clear()
        for index in range(len(self.viewer.layers)):
            if imageOrLabels == 'image':
                # print('image')
                if self.viewer.layers[index].as_layer_data_tuple()[2] == 'image':
                    widget.insertItem(index, self.viewer.layers[index].name)
            elif imageOrLabels == 'labels':
                # print('Labels')
                if self.viewer.layers[index].as_layer_data_tuple()[2] == 'labels':
                    widget.insertItem(index, self.viewer.layers[index].name)
        # widget.setCurrentIndex(currentIndex)
        AllItems = [widget.itemText(i) for i in range(widget.count())]
        if currentText in AllItems:
            widget.setCurrentText(currentText)
        else:
            # widget.setCurrentText('')
            widget.setCurrentIndex(-1)

    def refreshImageSelectors(self):
        self.initListWidget(self.Marker1NucMask, 'labels')
        self.initListWidget(self.CytoImage, 'image')

    watershedActive = False

    def _watershed_action(self):
        if self.watershedActive:
            return ()
        worker = self._watershed()

        def run(parameters):
            cellLabels, cytoLabels, scale, watershedParams = parameters
            cellLabelLayer = self.viewer.add_labels(cellLabels, name="CellLabels from " + self.viewer.layers[
                self.Marker1NucMask.currentText()].name + ' via watershed from ' + self.viewer.layers[
                                                                         self.CytoImage.currentText()].name,
                                                    scale=scale,
                                                    metadata=watershedParams)
            cellLabelLayer.contour = 2
            cytoLabelLayer = self.viewer.add_labels(cytoLabels, name="CytoLabels from " + self.viewer.layers[
                self.Marker1NucMask.currentText()].name + ' via watershed from ' + self.viewer.layers[
                                                                         self.CytoImage.currentText()].name,
                                                    scale=scale,
                                                    metadata=watershedParams)
            cytoLabelLayer.contour = 2

        worker.returned.connect(run)

        worker.finished.connect(lambda: self.CytoButton.setEnabled(True))
        worker.started.connect(lambda: self.CytoButton.setEnabled(False))
        worker.errored.connect(lambda: self.CytoButton.setEnabled(True))
        self.watershedActive = True
        worker.start()

    @thread_worker
    def _watershed(self):
        if isinstance(self.viewer.layers[self.CytoImage.currentText()].data, np.ndarray):
            cytoImage = self.viewer.layers[self.CytoImage.currentText()].data
        else:
            cytoImage = self.viewer.layers[self.CytoImage.currentText()].data.values
        nucLabels = self.viewer.layers[self.Marker1NucMask.currentText()].data
        if len(nucLabels.shape) != len(cytoImage.shape):
            # print('The nuclei labels layer is',len(nucLabels.shape),'-dimensional, but the nuclei image is ',len(nucleusImage.shape),'-D.')
            # print(nucleusImage.shape,nucleusImage.dtype)
            # print(nucLabels.shape,nucLabels.dtype)
            raise ValueError(
                "The nuclei labels layer is %d dimensional, but the cytiplasmic image is %d dimensional" % (
                    len(nucLabels.shape), len(cytoImage.shape)))

        thresh = int(.05 * np.max(cytoImage))
        cytomask = cytoImage > thresh
        nucMask = nucLabels > 0
        mask = (cytomask + nucMask).astype(np.uint8)
        print('Starting watershed.')
        cellLabels = np.array(seeded_watershed_with_mask(cytoImage, nucLabels, mask))
        print("Watershed complete.")
        cytoLabels = cellLabels - nucLabels
        scale = self.viewer.layers[self.CytoImage.currentText()].scale
        try:
            path = self.viewer.layers[self.CytoImage.currentIndex()].metadata['aicsimage'].reader._path
        except Exception as e:
            print(e)
            path = self.viewer.layers[self.CytoImage.currentIndex()].source.path
        watershedParams = {'source': '_watershed',
                           'filePath': path,
                           # 'filePath': self.viewer.layers[self.CytoImage.currentText()].source.path,
                           'channel': self.CytoImage.currentText(),
                           'nucleiLabels': self.Marker1NucMask.currentText(),
                           'scale': scale,
                           'cytoThreshold': 10}
        # print()
        self.watershedActive = False
        return cellLabels, cytoLabels, scale, watershedParams

    def updateDilationSizeSliderReadout(self, value):
        self.DilationSizeSliderReadout.setText(str(value))

    def _findMarker1Labels_action(self):
        worker = self._findMarker1Labels()

        def generate_label_markers(parameters):
            marker1LabelsDisk, marker1LabelsTorus, dilationSize, scale, metadata, metadata2 = parameters
            if marker1LabelsDisk is None and marker1LabelsTorus is None:
                return
            self.viewer.add_labels(marker1LabelsDisk,
                                   name=self.Marker1NucMask.currentText() + 'Disk' + str(
                                       dilationSize), scale=scale, metadata=metadata)
            self.viewer.add_labels(marker1LabelsTorus,
                                   name=self.Marker1NucMask.currentText() + 'Torus' + str(
                                       dilationSize), scale=scale, metadata=metadata2)

        worker.returned.connect(generate_label_markers)
        worker.finished.connect(lambda: self.btn.setEnabled(True))
        worker.started.connect(lambda: self.btn.setEnabled(False))
        worker.errored.connect(lambda: self.btn.setEnabled(True))
        worker.start()

    @thread_worker
    def _findMarker1Labels(self):
        dilationSize = self.DilationSizeSlider.value()
        # print(self.viewer.layers[self.Marker1NucMask.currentIndex()].name)
        if len(self.viewer.layers[self.Marker1NucMask.currentText()].data.shape) == 3:
            # print("3D MarkerLabel")
            marker1LabelsDisk = dilation(self.viewer.layers[self.Marker1NucMask.currentText()].data,
                                         footprint=ball(dilationSize))
            # print("exit:", datetime.datetime.now())
            marker1LabelsTorus = marker1LabelsDisk - self.viewer.layers[self.Marker1NucMask.currentText()].data
            try:
                path = self.viewer.layers[self.Marker1NucMask.currentIndex()].metadata['aicsimage'].reader._path
            except Exception as e:
                print(e)
                path = self.viewer.layers[self.Marker1NucMask.currentIndex()].source.path
            marker1Params = {'source': '_findMarker1Labels',
                             'filePath': path,
                             # 'filePath': self.viewer.layers[self.Marker1NucMask.currentText()].source.path,
                             'channel': self.Marker1NucMask.currentText(),
                             'size': dilationSize}
            marker1Params['shape'] = 'disk'
            marker1ParamsCopy = marker1Params.copy()
            marker1Params['shape'] = 'torus'
            return marker1LabelsDisk, marker1LabelsTorus, dilationSize, self.viewer.layers[
                0].scale, marker1ParamsCopy, marker1Params

        elif len(self.viewer.layers[self.Marker1NucMask.currentText()].data.shape) == 2:
            if len(self.viewer.layers[0].scale) == 2:
                scale = self.viewer.layers[0].scale
            else:
                scale = self.viewer.layers[0].scale[1:]
            marker1LabelsDisk = dilation(self.viewer.layers[self.Marker1NucMask.currentText()].data,
                                         footprint=disk(dilationSize))
            marker1LabelsTorus = marker1LabelsDisk - self.viewer.layers[self.Marker1NucMask.currentText()].data
            return marker1LabelsDisk, marker1LabelsTorus, dilationSize, scale, None, None
        print("Something is wrong")
        return None, None, None, None, None, None
