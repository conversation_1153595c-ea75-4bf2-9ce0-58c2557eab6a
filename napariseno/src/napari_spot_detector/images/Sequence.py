from typing import List, Union, Tuple, Dict

import napari
import numpy as np
import openpyxl
import xarray
from aicsimageio import AICS<PERSON>mage
from napari.layers import Image, Layer, Shapes

from .ROI import ROI


class Sequence:
    """
    Images are assumed to be ndarray and not DataArray
    """
    data_type = 'float32'
    # data_type = 'uint8'
    data_type_np = np.float32
    data_type_label_np = np.uint16

    def __init__(self, imageName, viewer: napari.Viewer):
        self.imageName = imageName
        self.viewer = viewer
        self.image: Union[Layer, Image] = viewer.layers[imageName]  # dimension order - T(C)ZYX
        self.rois: List[ROI] = list()
        self.scale: Union[Tuple[float], List[float], np.ndarray] = self.image.scale
        self.path: str = 'None' if self.image.source.path is None else self.image.source.path
        self.current_scene: str = ""
        self.index = -1
        self.aicsimage = None
        self.channel = 'None'
        self.current_scene_index: int = -1
        self._cmin = 0
        self._cmax = self.image.data.shape[-1]
        self._rmin = 0
        self._rmax = self.image.data.shape[-2]
        self.crop = False  # Cropping Setting
        # Sequence.data_type_np = self.image.data.dtype
        print(Sequence.data_type_np)

        if 'aicsimage' in viewer.layers[imageName].metadata.keys() and \
                viewer.layers[imageName].metadata['aicsimage'] is not None:
            print('Metadata contains an aicsimage object')
            self.aicsimage: AICSImage = viewer.layers[imageName].metadata['aicsimage']
            self.current_scene = self.aicsimage.current_scene
            self.scene_index = self.aicsimage.current_scene_index
            self.check = False

            if self.path == 'None':
                self.path = self.aicsimage.reader._path

            for i in self.aicsimage.channel_names:
                self.check = i in imageName
                if self.check:
                    self.channel = i
                    self.index = self.aicsimage.channel_names.index(i)
                    break
        elif self.path == 'None':
            from qtpy.QtWidgets import QFileDialog
            import tifffile as tif
            import os
            import re
            pattern = '[-:._\\s]+'

            home_directory = os.path.expanduser('~')
            self.path = QFileDialog.getSaveFileName(viewer.window.qt_viewer, "Save Image",
                                                    os.path.join(home_directory, re.sub(pattern, "_", imageName)),
                                                    "Image (*.tif *.tiff)")[0]

            # tif.imsave
            tif.imwrite(self.path, self.image.data, bigtiff=True)

            self.aicsimage = AICSImage(self.path)
        for layer in viewer.layers:  # adding Region of Interest aka Labels w/ binary values
            if isinstance(layer, Shapes):
                if layer.visible:
                    self.rois.append(ROI(layer))

    def metadata(self) -> Dict:
        # Can add more info in the future (ROI might need to be removed in the future)
        metadata = {
            "filePath": self.path,
            "channel": self.channel,
            "channel_index": self.index,
            "scene": self.current_scene,
            "scene_index": self.current_scene_index,
            "imageName": self.imageName,
            "scale": self.scale,
            "series": self.current_scene,
            "boundary_box": [self._cmin, self._cmax, self._rmin, self._rmax],
            "ROI": [roi.layer.data for roi in self.rois]
        }

        return metadata

    def get_pixel_dims(self) -> List:
        z = 1 if self.aicsimage.physical_pixel_sizes.Z is None else abs(self.aicsimage.physical_pixel_sizes.Z)
        y = 1 if self.aicsimage.physical_pixel_sizes.Y is None else abs(self.aicsimage.physical_pixel_sizes.Y)
        x = 1 if self.aicsimage.physical_pixel_sizes.X is None else abs(self.aicsimage.physical_pixel_sizes.X)

        return [z, y, x]

    def calc_volume_from_pixel(self) -> float:
        return np.prod(self.get_pixel_dims())

    def getRmin(self):
        return self._rmin

    def getRmax(self):
        return self._rmax

    def getCmin(self):
        return self._cmin

    def getCmax(self):
        return self._cmax

    def bounding_box(self, img: np.ndarray):
        rows = np.any(img, axis=1)
        cols = np.any(img, axis=0)
        self._rmin, self._rmax = np.where(rows)[0][[0, -1]]
        self._cmin, self._cmax = np.where(cols)[0][[0, -1]]

    def getAllImage(self) -> np.ndarray:
        data: Union[np.ndarray, xarray.DataArray] = self.image.data.astype(Sequence.data_type_np)
        # Union[LayerDataProtocol, xarray.DataArray, np.ndarray]:
        # TODO: Check for pattern of TZYX, CZYX, TCZYX
        if type(data) is xarray.DataArray:
            if len(data.dims) == 3:
                return self.image.data.values.astype(Sequence.data_type_np) \
                    if not self.crop else (data.values[:, self._rmin:self._rmax + 1,
                                           self._cmin:self._cmax + 1]
                                           .astype(Sequence.data_type_np))
            if len(data.dims) == 2:
                return data.values.astype(Sequence.data_type_np) \
                    if not self.crop else (data.values[self._rmin:self._rmax + 1, self._cmin:self._cmax + 1]
                                           .astype(Sequence.data_type_np))

        if len(data.shape) == 2:
            return data.astype(Sequence.data_type_np) \
                if not self.crop \
                else data[self._rmin:self._rmax + 1, self._cmin:self._cmax + 1].astype(Sequence.data_type_np)

        return data.astype(Sequence.data_type_np) \
            if not self.crop else (data[:, self._rmin:self._rmax + 1, self._cmin:self._cmax + 1]
                                   .astype(Sequence.data_type_np))

    def getImage(self, t: int, z: int) -> np.ndarray:
        # Union[LayerDataProtocol, xarray.DataArray, np.ndarray]:
        data: np.ndarray = self.getAllImage()
        if len(data.shape) == 5:  # if channel is available
            return data[t][0][z][:][:]
        if len(data.shape) == 3:
            return data[z][:][:]  # Assume the shape is ZYX
        if len(data.shape) == 2:
            return data  # Assume the shape is YX
        return data[t][z][:][:]  # Assume the shape is TZYX

    def getROIs(self):
        return self.rois

    def getSizeZ(self):
        if len(self.image.data.shape) == 5:  # if channel is available
            return self.image.data.shape[2]
        if len(self.image.data.shape) == 3:
            return self.image.data.shape[0]  # Assume the shape is ZYX
        if len(self.image.data.shape) == 2:
            return 1

        return self.image.data.shape[1]

    def getSizeY(self):
        if self.crop:  # Assume ZYX
            if len(self.image.data.shape) == 2:
                d = self.image.data[self._rmin:self._rmax + 1, self._cmin:self._cmax + 1]
                return d.shape[0]
            return self.image.data[:, self._rmin:self._rmax + 1, self._cmin:self._cmax + 1].shape[1]
        if len(self.image.data.shape) == 5:  # if channel is available
            return self.image.data.shape[3]
        if len(self.image.data.shape) == 3:
            return self.image.data.shape[1]  # Assume the shape is ZYX
        if len(self.image.data.shape) == 2:
            return self.image.data.shape[0]

        return self.image.data.shape[2]

    def getSizeX(self):
        if self.crop:  # Assume ZYX
            if len(self.image.data.shape) == 2:
                d = self.image.data[self._rmin:self._rmax + 1, self._cmin:self._cmax + 1]
                return d.shape[1]
            d = self.image.data[:, self._rmin:self._rmax + 1, self._cmin:self._cmax + 1]
            print(d.shape)
            return d.shape[2]
        if len(self.image.data.shape) == 5:  # if channel is available
            return self.image.data.shape[4]
        if len(self.image.data.shape) == 3:
            return self.image.data.shape[2]  # Assume the shape is ZYX
        if len(self.image.data.shape) == 2:
            return self.image.data.shape[1]

        return self.image.data.shape[3]

    def getSizeC(self):
        if self.aicsimage is not None:
            return self.aicsimage.dims.C
        elif len(self.image.data.shape) == 5:
            return self.image.data.shape[1]
        else:
            return 1

    def getSizeT(self):
        if self.aicsimage is not None:
            return self.aicsimage.dims.T
        elif len(self.image.data.shape) == 5:
            return self.image.data.shape[0]
        else:
            return 1

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        page.append([self.image.name])

        page.append(['Metadata'])
        for key, value in self.metadata().items():
            if isinstance(value, np.ndarray):
                page.append([key, np.array2string(value)])
            elif isinstance(value, list):
                page.append((key, np.array2string(np.array(value))))
            else:
                page.append((key, value))
        # page.append(['Scene', self.current_scene])
        # page.append(['Channel', self.channel])
        # page.append(['Channel Index', self.index])
        # page.append([''])
