import traceback
from typing import List, <PERSON><PERSON>, Dict, Union, Any

import numpy as np
import openpyxl
import pandas as pd
from SimpleITK import ConnectedComponentImageFilter, LabelIntensityStatisticsImageFilter
from openpyxl.utils.dataframe import dataframe_to_rows
from ..images.Sequence import Sequence


class DetectionSpot:
    def __init__(self, image: Sequence = None):
        # mass center of detection
        # self.massCenter: np.ndarray = np.zeros(shape=(1, 3))

        # # Mean value of pixel in GrayLevel of the pixel in the group.
        # self.meanIntensity: float = 0
        # # Min value of pixel in GrayLevel of the pixel in the group.
        # self.minIntensity: float = 0
        # # Max value of pixel in GrayLevel of the pixel in the group.
        # self.maxIntensity: float = 0

        # self.points: Tuple[np.ndarray] = tuple()

        self.t: int = 0

        # TODO: Add import traceback & add a DetectionSpot 2D and add Image parameter

        # self.massCenter: List
        # if image.getSizeZ() > 1:
        # if image is None:
        self.stats: Dict[str, Union[np.ndarray, slice]] = {
            'centroids': np.zeros(shape=(1, 3), dtype=Sequence.data_type_np),
            'voxel_counts': np.zeros(shape=1, dtype=np.uint8)}
        self.components: np.ndarray = np.zeros(
            shape=(image.getSizeZ(), image.getSizeY(), image.getSizeX()), dtype=int) if image is not None else np.zeros(
            shape=(1, 1, 1), dtype=np.uint8)
        self.N: int = 1
        self.reconstruction: np.ndarray = np.zeros(
            shape=(image.getSizeZ(), image.getSizeY(), image.getSizeX()), dtype=Sequence.data_type_np) \
            if image is not None else np.zeros(
            shape=(1, 1, 1), dtype=Sequence.data_type_np)
        # self.reconstruction: np.ndarray = np.zeros(shape=(1, 1, 1), dtype=Sequence.data_type_np)
        self.intensity: np.ndarray = np.zeros(shape=(1, 4), dtype=Sequence.data_type_np)  # Form: Min,Max,Average,Sum
        self.volumes: np.ndarray = np.zeros(shape=(1, 1), dtype=Sequence.data_type_np)
        self.binaryDetectionResult: np.ndarray = np.zeros(
            shape=(image.getSizeZ(), image.getSizeY(), image.getSizeX()), dtype=np.uint8) \
            if image is not None else np.zeros(
            shape=(1, 1, 1), dtype=np.uint8)
        self.ROI2Detection = pd.DataFrame(columns=['Detection #', 'Surface', 'z', 'y', 'x', 't',
                                                   'Minimum Intensity',
                                                   'Maximum Intensity',
                                                   'Average Intensity', 'Sum Intensity', 'Volume',
                                                   'Shape Name', 'Shape Layer', 'Shape Type'])
        # Define a dictionary with column names as keys and dtypes as values
        self.dtypes = {
            'Detection #': 'float32',
            'Surface': 'float32',
            'z': 'float32',
            'y': 'float32',
            'x': 'float32',
            't': 'float32',
            'Minimum Intensity': 'float32',
            'Maximum Intensity': 'float32',
            'Average Intensity': 'float32',
            'Sum Intensity': 'float32',
            'Volume': 'float32',
            'Shape Name': 'str',
            'Shape Layer': 'str',
            'Shape Type': 'str',
        }

        # Use astype() function to set dtypes
        self.ROI2Detection = self.ROI2Detection.astype(self.dtypes)

        # self.volumes_test = None

        # self.cc: ConnectedComponentImageFilter = ConnectedComponentImageFilter()  # Connect Component Image Object
        # self.labels: LabelIntensityStatisticsImageFilter = LabelIntensityStatisticsImageFilter()  # LabelIntensityStatistics Object

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        try:
            nb: int = 0

            page.append(["ROI number", "ROI name", "Shape Layer", "Shape Type", "ROI surface", "ROI nb detection"])
            # u = np.unique(self.ROI2Detection[['Shape Name', 'Shape Layer', 'Shape Type']])
            for name, layer, type in (self.ROI2Detection[['Shape Name', 'Shape Layer', 'Shape Type']]
                    .drop_duplicates().itertuples(index=False, name=None)):
                num_detections = self.ROI2Detection[(self.ROI2Detection['Shape Name'] == name) &
                                                        (self.ROI2Detection['Shape Layer'] == layer)]
                num_detections = num_detections['Detection #']
                page.append((nb, name, layer, type, np.count_nonzero(np.isin(self.components, num_detections)),
                             len(num_detections)))
                nb += 1

            for r in dataframe_to_rows(self.ROI2Detection, index=False, header=True):
                page.append(r)

        except Exception as e:
            print(e)
            traceback.print_stack()
    # def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
    #     try:
    #         detection_num = np.arange(1, self.N + 1)[:, np.newaxis]
    #         t = np.zeros(shape=(self.N, 1)) * self.t
    #         voxel_counts = self.stats['voxel_counts'][:, np.newaxis]
    #         centroids = self.stats['centroids']
    #         intensity = self.intensity
    #         volumes = self.volumes
    #         data = np.concatenate((detection_num, voxel_counts, centroids, t, intensity, volumes), axis=1)
    #         columns = ['Detection #', 'Surface', 'z', 'y', 'x', 't', 'Minimum Intensity', 'Maximum Intensity',
    #                    'Average intensity', 'Sum intensity', 'Volume']
    #
    #         df = pd.DataFrame(data=data, columns=columns)
    #
    #         for r in dataframe_to_rows(df, index=False, header=True):
    #             page.append(r)
    #     except ValueError as e:
    #         print(e)
    #         traceback.print_stack()
