CC = gcc
CFLAGS = -Wall -Werror -fpic
LDFLAGS = -shared

clean:
	rm *.o *.dll *.so

libB3SplineUDWT.dll: libB3SplineUDWT.o
	gcc -shared -fPIC -o libB3SplineUDWT.dll libB3SplineUDWT.o -lgomp -Wl,--add-stdcall-alias

libB3SplineUDWT.o: libB3SplineUDWT.c
	gcc -c -Wall -g -O2 -fPIC -DBUILD_DLL libB3SplineUDWT.c
libB3SplineUDWT.so: libB3SplineUDWT.c
	mkdir -p build
	gcc -c  -Wall -g -O2 -fPIC -DBUILD_DLL -fopenmp  -o build/libB3SplineUDWT.o libB3SplineUDWT.c
	gcc -shared -Wignored-attributes -fms-extensions -o libB3SplineUDWT.so build/libB3SplineUDWT.o

#   -fPIC
#-fopenmp
# -Werror
#  -std=c11
#  -std=c99
#  /opt/homebrew/Cellar/gcc/13.1.0/bin/gcc-13
# -fopenmp

libB3SplineUDWT_base.dylib: libB3SplineUDWT.c
	gcc -shared -Wignored-attributes -fdeclspec -fms-extensions -o libB3SplineUDWT.dylib libB3SplineUDWT.c
	#gcc -c -shared -Wall -Werror -fpic -o Mac/libB3SplineUDWT.o libB3SplineUDWT.c

libB3SplineUDWT-arm64.dylib: libB3SplineUDWT.c
	clang -arch arm64 -dynamiclib -o libB3SplineUDWT-arm64.dylib libB3SplineUDWT.c
libB3SplineUDWT-x86_64.dylib: libB3SplineUDWT.c
	clang -arch x86_64 -dynamiclib -o libB3SplineUDWT-x86_64.dylib libB3SplineUDWT.c

libB3SplineUDWT.dylib:
	make libB3SplineUDWT-arm64.dylib
	make libB3SplineUDWT-x86_64.dylib
	lipo -create -output libB3SplineUDWT.dylib libB3SplineUDWT-arm64.dylib libB3SplineUDWT-x86_64.dylib

clean-windows:
	rm *.o *.dll

clean-mac:
	rm *.dylib

# -dynamiclib
#libB3SplineUDWT-mac.o: libB3SplineUDWT-mac.c
#	gcc -c -Wall -Werror -fpic libB3SplineUDWT-mac.c
#
#libexample.so: example.o
#	$(CC) $(LDFLAGS) -o $@ $^
#
#example.o: example.c
#	$(CC) $(CFLAGS) -c $<


#gcc -c -Wall -Werror -fpic -fdeclspec -DBUILD_DLL -lm libB3SplineUDWT.c



#all: libexample.so



#mingw32-make.exe libB3SplineUDWT.o
#mingw32-make.exe libB3SplineUDWT.dll
