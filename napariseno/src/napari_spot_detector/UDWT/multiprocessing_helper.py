import os

# import cv2
import numpy as np
from joblib import <PERSON><PERSON><PERSON>, delayed, wrap_non_picklable_objects
from numba import njit
import SimpleIT<PERSON> as sitk

from ..images.Sequence import Sequence


@njit
def getIntensityMeasurements(sequence_list: np.ndarray, image: np.ndarray, component: np.ndarray):
    labels = list(sequence_list)
    result = np.zeros((len(labels), 5), dtype=np.uint32)

    # print()

    for i, s in enumerate(labels):
        # print("Intensity")
        # print(i)
        # print(s)
        extracted_image: np.ndarray = image[component == s]#.astype(np.uint32)
        # print(extracted_image.size)
        result[i, 0] = extracted_image.min()
        result[i, 1] = extracted_image.max()
        result[i, 2] = extracted_image.mean()
        result[i, 3] = extracted_image.sum()
        result[i, 4] = s

    return result

# def runOpenCV3DParallel(slice: np.ndarray, i):
#     num_label, label_id, stat, centroid = \
#         cv2.connectedComponentsWithStatsWithAlgorithm(slice, 8,
#                                                       cv2.CV_32S, ccltype=cv2.CCL_WU)
#
#     # remove 0 Label centroid
#     centroid = np.delete(centroid, 0, 0)
#
#     centroid_fix = np.zeros(shape=(num_label - 1, 3), dtype=Sequence.data_type_np)
#
#     image = slice
#     labels_out = label_id
#
#     image_reduce = image.ravel()#.astype(np.float32)
#     labels_out_reduce = labels_out.ravel()#.astype(np.uint32)
#
#     # print(image_reduce.shape)
#     # print(labels_out_reduce.shape)
#
#     # print("num of labels")
#     # print(num_label)
#
#     # Get Intensity Measurements
#     seqids = np.arange(1, num_label)#.astype(np.uint32)
#
#     batches = np.array_split(seqids, 50)
#
#     # print("Here43")
#
#     # print(np.unique(labels_out_reduce))
#
#     # print(len(batches))
#     # print(num_label)
#
#     workers = os.cpu_count() - 1
#     results = np.concatenate(
#         Parallel(n_jobs=workers, batch_size=max(2, int(len(batches) / workers)), pre_dispatch='4*n_jobs')(
#             delayed(getIntensityMeasurements)(j, image_reduce, labels_out_reduce) for j in batches))
#     #
#     # results = []
#     # for f in batches:
#     #     # print("Next")
#     #     # print(f)
#     #     # print(max(f))
#     #     # print(labels_out_reduce.max())
#     #     r = getIntensityMeasurements(f, image_reduce, labels_out_reduce)
#     #     results.append(r)
#     #
#     # results = np.concatenate(results)
#
#
#     # print("Here55")
#     centroid_fix[:, 0] = i
#     centroid_fix[:, 1] = centroid[:, 1]
#     centroid_fix[:, 2] = centroid[:, 0]
#
#     # print("Here555")
#
#     voxel_counts = stat[1:, cv2.CC_STAT_AREA][:, np.newaxis]
#
#     # id_array = np.arange(1, num_label + 1)
#     num_label_array = np.ones(num_label - 1, 'uint16')[:, np.newaxis] * num_label
#
#     # TODO: Fix Label naming after everything is done
#
#     # print("Here5566")
#
#     return np.concatenate(
#         (results, centroid_fix, voxel_counts, num_label_array), axis=1), label_id


@wrap_non_picklable_objects
def getIntensityMeasurementsSITK(stats: sitk.LabelIntensityStatisticsImageFilter,
                                 binaryDetectionResult3DImage: sitk.Image, seq_list):
    # stats_array = np.array([(stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
    #                          stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i)
    #                         for i in range(1, stats.GetNumberOfLabels() + 1)])

    # range(1, stats.GetNumberOfLabels() + 1):
    stats_list = list()
    for i in seq_list:
        centroid = binaryDetectionResult3DImage.TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[::-1]
        stats_list.append([stats.GetMinimum(i), stats.GetMaximum(i), stats.GetMean(i), stats.GetSum(i),
                           centroid[0], centroid[1], centroid[2], stats.GetNumberOfPixels(i), stats.GetPhysicalSize(i), i])

    return np.ndarray(stats_list)
    # print('stats_array')
    # stats_dict = {'centroids':
    #     np.array(
    #         [binaryDetectionResult3DImage.
    #          TransformPhysicalPointToContinuousIndex(stats.GetCentroid(i))[::-1]
    #          for i in range(1, stats.GetNumberOfLabels() + 1)]),
    #     'voxel_counts': stats_array[:, 4], 'labels': stats_array[:, -1]}

# @njit
# def getIntensityMeasurementsCV(num_label, originalImage, label_id):
#     intensity = np.empty(shape=(0, 4))
#     for j in range(1, num_label):
#         mask_img = originalImage[(label_id == j)]
#
#         intensity = np.concatenate((intensity, np.array([[np.min(mask_img), np.max(mask_img),
#                                                           np.mean(mask_img), np.sum(mask_img)]])), axis=0)