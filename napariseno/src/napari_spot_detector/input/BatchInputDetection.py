from typing import Final

import napari

from napari.layers import Image
from qtpy.QtCore import Qt
from qtpy.QtWidgets import Q<PERSON>oxLayout, QPushButton, QGroupBox, QWidget, QListWidget, QListWidgetItem


# from src.napari_spot_detector.SenoquantSpotDetectorTabWidget import SenoquantSpotDetectorTabWidget


class BatchInputDetection(QWidget):
    TAB_NAME: Final[str] = "Batch Input Detection" #, tabWidget: SenoquantSpotDetectorTabWidget):
    def __init__(self, viewer: napari.Viewer, *args, **kwargs):
        super(BatchInputDetection, self).__init__(*args, **kwargs)
        self.viewer: napari.Viewer = viewer
        # self.tabWidget: SenoquantSpotDetectorTabWidget = tabWidget

        self.layout = QVBoxLayout()
        self.imageGroup = QGroupBox("List of Images")
        self.imageGroup.setLayout(QVBoxLayout())
        self.layout.addWidget(self.imageGroup)

        self.imageListWidget = QListWidget()
        self.imageListWidget.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.imageListWidget.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        # self.imageListWidget.setSelectionMode(QAbstractItemView)
        self.imageGroup.layout().addWidget(self.imageListWidget)

        self.button: QPushButton = QPushButton("Run Batch")
        self.button.clicked.connect(self.process)
        self.layout.addWidget(self.button)

        self.viewer.layers.events.inserted.connect(self._image_insert)
        self.viewer.layers.events.removed.connect(self._image_delete)

        self.setLayout(self.layout)

    def _image_insert(self, event):
        if isinstance(event.value, Image):
        # if isinstance(self.viewer.layers[event.value.name], Image):
            item = QListWidgetItem(event.value.name)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Checked)
            self.imageListWidget.addItem(item)

    def _image_delete(self, event):
        item = self.imageListWidget.findItems(event.value.name, Qt.MatchExactly)[0]
        self.imageListWidget.takeItem(self.imageListWidget.row(item))

    def process(self):
        for item in self.imageListWidget.selectedItems():
            self.tabWidget.nucleiWidget.NucImage.setCurrentText(item.text())
            self.tabWidget.nucleiWidget.RunModel3DButton.click()
            self.tabWidget.detector.startButton.click()
            self.tabWidget.detector2.startButton.click()
            self.tabWidget.markerWidget.btn.click()
            self.tabWidget.markerWidget.CytoButton.click()
            self.tabWidget.processWidget.processButton.click()

