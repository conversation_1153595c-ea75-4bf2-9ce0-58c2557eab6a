import os
import sys
import re
import traceback
import datetime
from copy import copy
from typing import Any, Final, Type, List, Dict, Union

import numpy as np
import openpyxl as openpyxl
import pandas as pd
import xarray
import xmltodict as xmltodict
from aicsimageio import AICSImage
from aicsimageio.dimensions import DimensionNames
from dicttoxml import dicttoxml
from openpyxl.utils.dataframe import dataframe_to_rows
from qtpy.QtCore import Qt, QObject
from qtpy import QtCore
from qtpy.QtWidgets import (QWidget, QRadioButton, QScrollArea, QComboBox, QGroupBox, QVBoxLayout,
                            QFileDialog, QTabWidget, QFormLayout, QLabel, QHBoxLayout, QPushButton, QCheckBox)

from napari import Viewer
from napari.layers import Image
from napari.qt.threading import thread_worker
from napari.utils.notifications import ErrorNotification

from .input.QDropDownGroupBox import QDropDownGroup<PERSON>ox
from .input.QHidePushButton import QHide<PERSON>ush<PERSON>utton

from .tokens import GlobalDetectionToken
from .images.Sequence import Sequence
from .UDWT.B3SplineUDWT import isNumberOfScaleOkForImage3D, getMinSize
from .UDWT.UDWTScale import UDWTScale
from .UDWT.UDWTWaveletCore import UDWTWaveletCore
from .images.DetectionSpot import DetectionSpot
from .output.OutputWidget import OutputWidget, FileSelector

from napari_roi._roi_widget import ROIWidget
from napari_roi.qt._roi_layer_accessor import ROILayerAccessor
from napari_roi._roi import ROIOrigin

from .processes.Process import Process
from .processes.filtering.Filtering import (PreFiltering, PostFiltering, NoFiltering,
                                            UFISHPreFiltering, SizeFiltering, DeClusterFiltering)
from .processes.roi.ROIDetection import ROIDetection, ROIFromSequence


def _calculate_chunk_sizes(image_shape, dtype):
    import psutil

    # Get the total available memory
    total_memory = psutil.virtual_memory().available

    # Set the memory limit to a minimum of 8GB and a maximum of 16GB
    memory_limit = max(min(round(total_memory * 0.5), 16 * 1024 ** 3), 3 * 1024 ** 3)

    # Calculate the size of each dimension in bytes
    dimension_sizes_bytes = [max(dim_size * np.dtype(dtype).itemsize, 1) for dim_size in image_shape]

    # Calculate the chunk size for each dimension
    chunk_sizes = [memory_limit // dim_size_bytes for dim_size_bytes in dimension_sizes_bytes]

    return tuple(chunk_sizes)


class UDWTWaveletDetector(QScrollArea):
    TAB_NAME: Final = "Napari Spot Detector"

    input_boxObjectName: Final[str] = "input_layer"
    detectPositiveRadioObjectName: Final[str] = "bright_detection_spot"
    detectNegativeRadioObjectName: Final[str] = "dark_detection_spot"
    use2DWaveletsFor3DImagesObjectName: Final[str] = "wavelet"
    displayBinaryImageCheckBoxObjectName: Final[str] = "display"
    computeWATwithROICheckBoxObjectName: Final[str] = "WAT"
    cropImageCheckBoxObjectName: Final[str] = "Crop"
    intersectionCheckBoxObjectName: Final[str] = "Intersection"
    cc_menuObjectName: Final[str] = "connect_component"
    regionOfInterestObjectName: Final[str] = "ROI"
    filterObjectName: Final[str] = "filter"
    prefilterObjectName: Final[str] = "prefilter"
    scrollPaneObjectName: Final[str] = "scale_parameter"
    outputWidgetObjectName: Final[str] = "output"

    def __init__(self, viewer: Viewer, tab: Union[QTabWidget, None], tag_widget: Union[QTabWidget, None] = None,
                 name: str = TAB_NAME, *args,
                 **kwargs):
        super(UDWTWaveletDetector, self).__init__(*args, **kwargs)
        self.tab = tab
        self.source = name
        self.metadata: Dict = dict()

        # ScrollArea
        self.qscroll_area_widget = QWidget()

        # Scroll Area Properties
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setWidgetResizable(True)
        self.setGeometry(10, 10, 80, 160)

        self.setWidget(self.qscroll_area_widget)

        self.UDWTScaleArrayList: List[UDWTScale] = list()
        self.waveletCore = UDWTWaveletCore()
        self.inputs = list()
        self.viewer = viewer

        layout = QVBoxLayout()
        self.qscroll_area_widget.setLayout(layout)

        self.setWindowTitle("Spot Detector")

        self.profile_widget = FileSelector(self._load_profile, group_name="Load Settings", label_name="File",
                                           filter_names="XML (*.xml)")
        # self.profile_widget.setLayout(QVBoxLayout())

        self.loadImagesFromXMLCheckBox = QCheckBox()
        self.loadImagesFromXMLCheckBox.setHidden(True)  # TODO: Not fully tested yet
        self.loadImagesFromXMLCheckBox.setText("Load Images from XML File")
        self.loadImagesFromXMLCheckBox.setChecked(False)
        self.profile_widget.layout().addWidget(self.loadImagesFromXMLCheckBox)
        layout.addWidget(self.profile_widget)

        if tag_widget is not None and isinstance(tag_widget, QTabWidget):
            self.tabWidget = tag_widget
            self.tabWidget_btn = QPushButton("Add Spot Detector Tab")
            self.tabWidget_btn.clicked.connect(self.addTab)
            layout.addWidget(self.tabWidget_btn)

        self.input_widget: QWidget = QWidget()
        self.input_widget.setLayout(QHBoxLayout())

        self.input_label: QLabel = QLabel("Input")
        self.input_box: QComboBox = QComboBox()
        self.input_box.setObjectName(UDWTWaveletDetector.input_boxObjectName)

        self.viewer.layers.events.inserted.connect(self.update_layer)
        self.viewer.layers.events.removed.connect(self.update_layer)

        self.input_box.addItems([layer.name for layer in self.viewer.layers if isinstance(layer, Image)])
        self.input_box.setCurrentText('')
        layers = [layer.name for layer in self.viewer.layers if isinstance(layer, Image)]
        try:
            if 'Spot 1' in self.source:
                # print("Spot 1")
                for item in layers:
                    # print(item)
                    if 'TXR' in item:
                        self.input_box.setCurrentText(item)
                        # print('set Spot 1',self,'to ',item)
                        # currentText = item
            elif 'Spot 2' in self.source:
                # print("Spot 2")
                for item in layers:
                    # print(item)
                    if 'FITC' in item:
                        self.input_box.setCurrentText(item)
                        # print('set Spot 2',self,'to ',item)
                        # currentText = item
        except Exception as e:
            print('Exception in __init__', e)

        # for layer in self.viewer.layers:
        #     if 'Spot1' in self.source and 'TXR' in layer.name:
        #         self.input_box.setCurrentText(item)
        #         break
        #     elif 'Spot2' in self.source and 'FITC' in layer.name:
        #         self.input_box.setCurrentText(item)
        #         break

        self.input_widget.layout().addWidget(self.input_label)
        self.input_widget.layout().addWidget(self.input_box)

        self.input_box.currentTextChanged.connect(self._update_file_name)
        self.input_box.currentTextChanged.connect(self._updateProcessTab)

        layout.addWidget(self.input_widget)

        # Pre filter
        self.prefilter_widget: QDropDownGroupBox = QDropDownGroupBox(class_name=PreFiltering, viewer=self.viewer)
        self.prefilter: PreFiltering = self.prefilter_widget.process
        self.prefilter.setObjectName(UDWTWaveletDetector.prefilterObjectName)
        self.prefilterDropDownMenu: QComboBox = self.prefilter_widget.combo

        layout.addWidget(self.prefilter_widget)

        # List of ROI Labels
        self.roiPanel = QGroupBox(title="List of Region of Interest")
        self.roiPanel.setLayout(QVBoxLayout())

        # Adding _roi_widget to roi group
        ROILayerAccessor.DEFAULT_ROI_ORIGIN = ROIOrigin.TOP_LEFT

        self.roiWidget: ROIWidget = ROIWidget(self.viewer)
        self.roiWidget._add_widget.setDisabled(True)
        self.roiWidget._add_widget.setHidden(True)
        self.roiWidget._save_widget.setDisabled(True)
        self.roiWidget._save_widget.setHidden(True)

        # Hide X/Y origin
        label_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.LabelRole)
        field_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.FieldRole)

        # Hide the items
        if label_item is not None:
            label_item.widget().setHidden(True)
        if field_item is not None:
            field_item.widget().setHidden(True)

        self.roiPanel.layout().addWidget(self.roiWidget)

        layout.addWidget(self.roiPanel)

        self.examplePanel = QGroupBox(title="Examples of input image that would use this setting:")
        self.examplePanel.setLayout(QHBoxLayout())

        layout.addWidget(self.examplePanel)

        data_path = os.path.join(os.path.dirname(__file__), 'plugins')

        self.examplePanel.layout().addWidget(QLabel('<h1><img src="{}/neg1.png"></h1>'.format(data_path)))
        self.examplePanel.layout().addWidget(QLabel('<h1><img src="{}/neg2.png"></h1>'.format(data_path)))
        self.examplePanel.layout().addWidget(QLabel('<h1><img src="{}/neg3.png"></h1>'.format(data_path)))
        self.examplePanel.layout().addWidget(QLabel('<h1><img src="{}/pos1.png"></h1>'.format(data_path)))

        self.examplePanel.layout().itemAt(0).widget().setVisible(False)
        self.examplePanel.layout().itemAt(1).widget().setVisible(False)
        self.examplePanel.layout().itemAt(2).widget().setVisible(False)

        self.buttonGroup: QGroupBox = QGroupBox()
        self.buttonGroup.setLayout(QVBoxLayout())

        # Positive Radio Button
        self.detectPositiveRadio: QRadioButton = QRadioButton("Detect bright spot over dark background")
        self.detectPositiveRadio.setObjectName(UDWTWaveletDetector.detectPositiveRadioObjectName)

        # self.detectPositiveRadio = QRadioButton("Detect dark spot over bright blackground")
        self.buttonGroup.layout().addWidget(self.detectPositiveRadio)
        self.detectPositiveRadio.setChecked(True)
        layout.addWidget(self.buttonGroup)

        # Negative Radio Button
        self.detectNegativeRadio: QRadioButton = QRadioButton("Detect dark spot over bright background")
        self.detectNegativeRadio.setObjectName(UDWTWaveletDetector.detectNegativeRadioObjectName)

        # self.detectNegativeRadio = QRadioButton("Detect bright spot over dark blackground")
        self.buttonGroup.layout().addWidget(self.detectNegativeRadio)
        self.detectNegativeRadio.setChecked(False)

        self.detectPositiveRadio.toggled.connect(self.refreshExamplePanel)
        self.detectNegativeRadio.toggled.connect(self.refreshExamplePanel)

        self.use2DWaveletsFor3DImages: QCheckBox = QCheckBox()
        self.use2DWaveletsFor3DImages.setObjectName(UDWTWaveletDetector.use2DWaveletsFor3DImagesObjectName)
        self.use2DWaveletsFor3DImages.setToolTip("If enabled, each "
                                                 "images of a 3D stack will be computed separately by a 2D wavelet. "
                                                 "This helps to bypass the scale size limitation due to a too small "
                                                 "number of z in the image.")
        self.use2DWaveletsFor3DImages.setText("Force use of 2D Wavelets for 3D")
        self.use2DWaveletsFor3DImages.setChecked(False)

        layout.addWidget(self.use2DWaveletsFor3DImages)

        self.displayBinaryImageCheckBox: QCheckBox = QCheckBox()
        self.displayBinaryImageCheckBox.setObjectName(UDWTWaveletDetector.displayBinaryImageCheckBoxObjectName)
        self.displayBinaryImageCheckBox.setText("Display binary image (before filtering)")
        self.displayBinaryImageCheckBox.setChecked(False)

        layout.addWidget(self.displayBinaryImageCheckBox)

        self.computeWATwithROICheckBox: QCheckBox = QCheckBox()
        self.computeWATwithROICheckBox.setObjectName(UDWTWaveletDetector.computeWATwithROICheckBoxObjectName)
        self.computeWATwithROICheckBox.setText("compute WAT considering ROIs")
        self.computeWATwithROICheckBox.setChecked(False)

        layout.addWidget(self.computeWATwithROICheckBox)

        self.cropImageCheckBox: QCheckBox = QCheckBox()
        self.cropImageCheckBox.setObjectName(UDWTWaveletDetector.cropImageCheckBoxObjectName)
        self.cropImageCheckBox.setText("Use Crop Image From Shape ROI instead of entire Image")
        self.cropImageCheckBox.setChecked(False)

        layout.addWidget(self.cropImageCheckBox)

        self.intersectionCheckBox: QCheckBox = QCheckBox()
        self.intersectionCheckBox.setObjectName(UDWTWaveletDetector.intersectionCheckBoxObjectName)
        self.intersectionCheckBox.setText("Union/Combine Enable Scale Thresholds")
        self.intersectionCheckBox.setChecked(False)

        layout.addWidget(self.intersectionCheckBox)

        self.scalePanel = QGroupBox("Size of spots to detect: (scale and sensitivity)")
        self.scalePanel.setLayout(QVBoxLayout())

        headerPanel = QGroupBox()
        headerPanel.setLayout(QHBoxLayout())
        headerPanel.layout().addWidget(QLabel("Scale Enabled"))
        headerPanel.layout().addWidget(QLabel("Sensitivity"))
        headerPanel.layout().addWidget(QLabel("Object size"))
        self.scalePanel.layout().addWidget(headerPanel)

        self.scrollPane = QScrollArea(self)
        self.scrollPane.setObjectName(UDWTWaveletDetector.scrollPaneObjectName)
        self.scrollWidget = QWidget()
        self.scalePanel.layout().addWidget(self.scrollPane)

        # Scroll Area Properties
        self.scrollPane.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.scrollPane.setWidgetResizable(True)
        self.scrollPane.setGeometry(15, 15, 50, 100)

        self.scrollPane.setWidget(self.scrollWidget)
        self.scrollWidget.setLayout(QVBoxLayout())

        self.addScalePanel(False)
        self.addScalePanel(True)
        self.addScalePanel(False)

        self.scrollPane.setFixedHeight(round(self.scrollWidget.layout().itemAt(0).widget().height() / 2))

        self.configurationPanel = QWidget()
        self.configurationPanel.setLayout(QHBoxLayout())

        self.addScaleButton = QPushButton()
        self.addScaleButton.setText("Add scale")
        self.addScaleButton.clicked.connect(self.addScalePanel)
        self.configurationPanel.layout().addWidget(self.addScaleButton)
        self.removeScaleButton = QPushButton()
        self.removeScaleButton.setText("Remove scale")
        self.removeScaleButton.clicked.connect(self.removeScalePanel)
        self.configurationPanel.layout().addWidget(self.removeScaleButton)

        self.scalePanel.layout().addWidget(self.configurationPanel)
        layout.addWidget(self.scalePanel)

        # ROI
        self.region_of_interest_widget: QDropDownGroupBox = QDropDownGroupBox(class_name=ROIDetection,
                                                                              viewer=self.viewer,
                                                                              nofilter=False)
        self.region_of_interest: ROIDetection = self.region_of_interest_widget.process
        self.region_of_interest.setObjectName(UDWTWaveletDetector.regionOfInterestObjectName)
        self.ROIDropDownMenu: QComboBox = self.region_of_interest_widget.combo
        layout.addWidget(self.region_of_interest_widget)

        # Post Filter
        self.filter_widget: QDropDownGroupBox = QDropDownGroupBox(class_name=PostFiltering, viewer=self.viewer)
        self.filter: PostFiltering = self.filter_widget.process
        self.filter.setObjectName(UDWTWaveletDetector.filterObjectName)
        self.filterDropDownMenu: QComboBox = self.filter_widget.combo
        layout.addWidget(self.filter_widget)

        # Connected Component
        self.cc = QWidget()
        self.cc.setLayout(QVBoxLayout())
        self.cc_menu: QComboBox = QComboBox()
        self.cc_menu.setObjectName(UDWTWaveletDetector.cc_menuObjectName)

        self.cc.layout().addWidget(QLabel('Connected Components Functions'))
        self.cc.layout().addWidget(self.cc_menu)
        self.cc_menu.addItems(['SimpleITK', 'Connected-Component-3D'])
        # self.cc_menu.addItems(['SimpleITK', 'Connected-Component-3D', 'OpenCV'])

        layout.addWidget(self.cc)

        # Output
        self.output_widget = OutputWidget(self.input_box)
        self.output_widget.setObjectName(UDWTWaveletDetector.outputWidgetObjectName)
        layout.addWidget(self.output_widget)

        # Set Connection
        self.output_widget.dialog_append.clicked.connect(self._enable_append_checkbox)
        self.output_widget.xml_file_naming.clicked.connect(self._update_file_name_xml)

        # Start Detection
        self.startButton: QHidePushButton = QHidePushButton(viewer=viewer,
                                                            combobox=self.input_box, text="Start Detection")
        self.startButton.clicked.connect(self._actionPerformed)
        text = self.input_box.currentText()
        if text != '':
            self.startButton.setEnabled(True)
        else:
            self.startButton.setEnabled(False)
        layout.addWidget(self.startButton)

        # Add Event Filter for Wheel Event
        self.scrollPane.viewport().installEventFilter(self)
        self.viewport().installEventFilter(self)
        self.input_box.installEventFilter(self)
        # self.filterDropDownMenu.installEventFilter(self)
        self.cc_menu.installEventFilter(self)

    def initialize_filter_layout(self,
                                 class_filter: Type[Process] = Process):  # , name: str = '', **kwargs) -> Filtering:
        filter_widget = QWidget()
        filter_widget.setLayout(QVBoxLayout())

        filterDropDownMenu = QComboBox()

        filter_classes = [cls.__name__ for cls in class_filter.__subclasses__()]
        filter_classes.insert(0, NoFiltering.__name__)
        filterDropDownMenu.addItems(filter_classes)

        filter_widget.layout().addWidget(filterDropDownMenu)
        filter_process: Process = (getattr(sys.modules[__name__], filter_classes[0]))()

        filter_widget.layout().addWidget(filter_process)

        filterDropDownMenu.currentTextChanged.connect(self.update_filter_widget)

        return filter_widget, filterDropDownMenu

    def eventFilter(self, source, event):
        if (event.type() == QtCore.QEvent.Wheel and (
                isinstance(source, QComboBox) or isinstance(source, QScrollArea))):
            return True
        return super().eventFilter(source, event)

    def addTab(self):
        c = sum([1 for i in range(self.tabWidget.count()) if isinstance(self.tabWidget.widget(i), UDWTWaveletDetector)])
        self.tabWidget.insertTab(self.tabWidget.indexOf(self) + 1, UDWTWaveletDetector(self.viewer, self.tabWidget),
                                 UDWTWaveletDetector.TAB_NAME + str(c))

    def _enable_append_checkbox(self):
        if self.output_widget.dialog_append.isChecked():
            self.output_widget.automatic_xls_file_naming_file_dialog.setCheckable(False)
            self.output_widget.dialog_append.dir_name_edit. \
                setText(self.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit.text())
        else:
            self.output_widget.automatic_xls_file_naming_file_dialog.setCheckable(True)

    def _update_file_name_xml(self):
        filename = os.path.splitext(self.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit.text())[0]
        self.output_widget.xml_file_naming.dir_name_edit.setText(filename + ".xml")

    def _update_file_name(self, text):
        pattern = '[-:._\\s]+'
        file_name = re.sub(pattern, "_", text)
        home_directory = os.path.expanduser('~')
        self.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit. \
            setText(os.path.join(home_directory, file_name + '.xlsx'))

    def update_roi_widget(self, text: str, **kwargs):
        if self.region_of_interest_widget.layout().count() != 2:  # Removes all Widget
            for i in range(self.region_of_interest_widget.layout().count()):
                self.region_of_interest_widget.layout().takeAt(i).widget().deleteLater()

            self.region_of_interest: ROIDetection = ROIFromSequence()
            self.region_of_interest_widget.layout().addWidget(self.ROIDropDownMenu)
            self.region_of_interest_widget.layout().addWidget(self.region_of_interest)
        else:
            self.region_of_interest_widget.layout().takeAt(1).widget().deleteLater()  # Should there only be 2 Widgets
            self.region_of_interest: ROIDetection = (eval(text))(**kwargs)
            self.region_of_interest_widget.layout().addWidget(self.region_of_interest)

        self.region_of_interest.setObjectName(UDWTWaveletDetector.regionOfInterestObjectName)

        self.region_of_interest_widget.update()

    def update_filter_widget(self, text: str, **kwargs):
        sender = self.sender()
        filter_widget: QWidget = sender.parentWidget() if sender else QWidget()  # Getting the QWidget from parent

        filter_process = filter_widget.layout().takeAt(1).widget()
        objectName: str = filter_widget.objectName()
        filter_widget.layout().removeWidget(filter_process)
        filter_process.setParent(None)
        filter_process.deleteLater()  # Should there only be 2 Widgets
        filter_process: Process = (eval(text))(**kwargs)
        filter_process.setObjectName(objectName)
        filter_widget.layout().addWidget(filter_process)

        filter_widget.update()

    def _updateProcessTab(self):
        if self.tab is None:
            return
        if 'Spot 1' in self.source:
            self.tab.processWidget.TelImage.setCurrentText(self.input_box.currentText())
        elif 'Spot 2' in self.source:
            self.tab.processWidget.FociImage.setCurrentText(self.input_box.currentText())

    def update_layer(self):
        currentText = self.input_box.currentText()

        layers = list()
        for layer in self.viewer.layers:
            if isinstance(layer, Image):
                # Absolute Value for scale
                layer.scale = np.abs(layer.scale)
                layers.append(layer.name)
                if isinstance(layer.data, xarray.DataArray):
                    if layer.data.chunks is None:
                        layer.data = layer.data.chunk(_calculate_chunk_sizes(layer.data.shape, layer.data.dtype))

        self.input_box.clear()
        self.input_box.addItems(layers)

        try:
            if 'Spot 1' in self.source:
                # print("Spot 1")
                for item in layers:
                    # print(item)
                    if 'TXR' in item:
                        self.input_box.setCurrentText(item)
                        # print('set Spot 1',self,'to ',item)
                        currentText = item
            elif 'Spot 2' in self.source:
                # print("Spot 2")
                for item in layers:
                    # print(item)
                    if 'FITC' in item:
                        self.input_box.setCurrentText(item)
                        # print('set Spot 2',self,'to ',item)
                        currentText = item
        except Exception as e:
            print('Exception in _update_layer', e)

        if currentText in layers:
            self.input_box.setCurrentText(currentText)

        if self.input_box.currentText() is not None:
            self.startButton.setEnabled(True)
        else:
            self.startButton.setEnabled(False)

    def refreshExamplePanel(self):
        if self.detectNegativeRadio.isChecked():
            self.examplePanel.layout().itemAt(0).widget().setVisible(True)
            self.examplePanel.layout().itemAt(1).widget().setVisible(True)
            self.examplePanel.layout().itemAt(2).widget().setVisible(True)
            self.examplePanel.layout().itemAt(3).widget().setVisible(False)
        else:
            self.examplePanel.layout().itemAt(0).widget().setVisible(False)
            self.examplePanel.layout().itemAt(1).widget().setVisible(False)
            self.examplePanel.layout().itemAt(2).widget().setVisible(False)
            self.examplePanel.layout().itemAt(3).widget().setVisible(True)

    @thread_worker
    def run_prefilter(self):
        print("Start Pre Filtering...")
        st = datetime.datetime.now()
        try:
            file_path = self.prefilter_widget.process.process(gdt=self.gdt)

            # if file_path is not None:
            #     self.prefilter_widget.process.open_file_viewer(file_path=file_path, gdt=self.gdt)

        except Exception as e:
            print(f'caught {e=}, {type(e)}: e')
            print("Reverting Filter")
            traceback.print_exc()
            raise e
        et = datetime.datetime.now()
        print('Finished Pre Filtering: ', et - st)

        return file_path

    def _actionPerformed(self):
        self.gdt = GlobalDetectionToken()
        self.gdt.inputSequence = Sequence(imageName=self.input_box.currentText(), viewer=self.viewer)
        self.gdt.inputComputationSequence = copy(self.gdt.inputSequence)
        try:
            prefilter_worker = self.run_prefilter()
            prefilter_worker.returned.connect(lambda file_path: continue_run(file_path))
            prefilter_worker.errored.connect(lambda error: (ErrorNotification(error), traceback.print_exc()))

            def continue_run(file_path):
                if file_path is not None:
                    self.prefilter_widget.process.open_file_viewer(file_path=file_path, gdt=self.gdt)

                worker = self.computeDetection()
                worker.returned.connect(lambda points: self._addIntermediateLayers(points=points))
                worker.finished.connect(lambda: self._reset())
                worker.started.connect(lambda: self._setup_metadata())
                worker.errored.connect(lambda error: (ErrorNotification(error), traceback.print_exc()))
                worker.start()

            self.st = datetime.datetime.now()
            prefilter_worker.start()
        except Exception as e:
            print(e)
            traceback.print_exc()
            return

    def _reset(self):
        print("Reset")
        self.qscroll_area_widget.setEnabled(True)
        self.startButton.setEnabled(True)

        et = datetime.datetime.now()
        print("Completed Spot Detector:", et - self.st)

    def _setup_metadata(self):
        self.startButton.setEnabled(False)
        self.qscroll_area_widget.setDisabled(True)
        self.xlsx = self.output_widget.automatic_xls_file_naming_file_dialog.dir_name_edit.text()
        self.xlsx_append = self.output_widget.dialog_append.dir_name_edit.text()
        self.xml = self.output_widget.xml_file_naming.dir_name_edit.text()

        self.metadata.update({"source": self.source})
        self.metadata.update(self.gdt.inputSequence.metadata())

        def append_metadata(widget: QWidget):
            # widget_types: List[QtWidgets] = [QRadioButton, QCheckBox, QComboBox, QCheckBox, UDWTScale, Process]
            # for w in widget_types:
            #     print(w)
            children_inner: List[QObject] = widget.children()  # findChildren(w, "")
            for c in children_inner:
                if c.objectName() == '':
                    continue
                print(c, c.objectName())
                if isinstance(c, UDWTScale):
                    UDWTScaleArrayList = [cc.row() for cc in children]
                    self.metadata.update({self.scrollPaneObjectName: UDWTScaleArrayList})
                elif isinstance(c, QComboBox):
                    self.metadata.update({c.objectName(): c.currentText()})
                elif isinstance(c, Process):
                    self.metadata.update({c.objectName(): c.parameters()})
                elif isinstance(c, QRadioButton) or isinstance(c, QCheckBox):
                    self.metadata.update({c.objectName(): c.isChecked()})
                else:
                    print("Not Available")

            # print("QRadioButton")
            # children: List[QRadioButton] = widget.findChildren(QRadioButton, "")
            # for child in children:
            #     print(child, child.objectName())
            #     self.metadata.update({child.objectName(): child.isChecked()})
            # print("QCheckBox")
            # children: List[QCheckBox] = widget.findChildren(QCheckBox, "")
            # for child in children:
            #     print(child, child.objectName())
            #     self.metadata.update({child.objectName(): child.isChecked()})
            # print("QComboBox")
            # children: List[QComboBox] = widget.findChildren(QComboBox, "")
            # for child in children:
            #     print(child, child.objectName())
            #     self.metadata.update({child.objectName(): child.currentText()})
            # print("UDWTScale")
            # children: List[UDWTScale] = widget.findChildren(UDWTScale, "")
            # UDWTScaleArrayList = [child.row() for child in children]
            # self.metadata.update({self.scrollPaneObjectName: UDWTScaleArrayList})
            # print("Process")
            # children: List[Process] = widget.findChildren(Process, "")
            # for child in children:
            #     print(child, child.objectName())
            #     self.metadata.update({child.objectName(): child.parameters()})

        append_metadata(self.widget())
        children: List[QGroupBox] = self.widget().findChildren(QGroupBox, "")
        for child in children:
            append_metadata(child)

        # self.metadata = {"source": self.source,
        #                  "filePath": self.gdt.inputSequence.path,
        #                  "channel": self.gdt.inputSequence.channel,
        #                  "channel_index": self.gdt.inputSequence.index,
        #                  "scene": self.gdt.inputSequence.current_scene,
        #                  "scene_index": self.gdt.inputSequence.current_scene_index,
        #                  "input_layer": self.input_box.currentText(),
        #                  "scale": self.gdt.inputSequence.scale,
        #                  "series": self.gdt.inputSequence.current_scene,
        #                  "bright_detection_spot": self.detectPositiveRadio.isChecked(),
        #                  "dark_detection_spot": self.detectNegativeRadio.isChecked(),
        #                  "display": self.displayBinaryImageCheckBox.isChecked(),
        #                  "wavelet": self.use2DWaveletsFor3DImages.isChecked(),
        #                  "WAT": self.computeWATwithROICheckBox.isChecked(),
        #                  "Crop": self.cropImageCheckBox.isChecked(),
        #                  "Intersection": self.intersectionCheckBox.isChecked(),
        #                  "connect_component": self.cc_menu.currentText(),
        #                  "scale_parameter": [i.row() for i in self.UDWTScaleArrayList],
        #                  "ROI": self.region_of_interest.parameters(),
        #                  "filter": self.filter.parameters(),
        #                  "prefilter": self.prefilter.parameters()
        #                  }

        # Print Parameters
        print("Parameters:", self.metadata)

    def _load_profile(self, filter_names):
        file_name, _ = QFileDialog.getOpenFileName(self, "Select a File", directory="",
                                                   filter=filter_names)  # "XML (*.xml)"
        if file_name and file_name != '':
            try:
                path = os.path.realpath(file_name)
                with open(path) as fd:
                    root = xmltodict.parse(fd.read())

                    doc = root['root']

                    self.detectPositiveRadio.setChecked(True if doc.get("bright_detection_spot", {"#text": 'false'})
                                                        .get('#text', "false").lower() == 'true' else False)
                    self.detectNegativeRadio.setChecked(True if doc.get("dark_detection_spot", {"#text": 'false'})
                                                        .get('#text', "false").lower() == 'true' else False)
                    if self.detectPositiveRadio.isChecked() is False and self.detectNegativeRadio.isChecked() is False:
                        self.detectPositiveRadio.setChecked(True)
                    self.displayBinaryImageCheckBox.setChecked(
                        True if doc.get("display", {"#text": 'false'}).get('#text',
                                                                           "false").lower() == 'true' else False)
                    self.use2DWaveletsFor3DImages.setChecked(
                        True if doc.get("wavelet", {"#text": 'false'}).get('#text',
                                                                           "false").lower() == 'true' else False)
                    self.computeWATwithROICheckBox.setChecked(
                        True if doc.get("WAT", {"#text": 'false'}).get('#text', "false").lower() == 'true' else False)
                    self.cropImageCheckBox.setChecked(True if doc.get("Crop", {"#text": 'false'}).get('#text', "false")
                                                      .lower() == 'true' else False)
                    self.intersectionCheckBox.setChecked(True if doc.get("Intersection", {"#text": 'false'})
                                                         .get('#text', "false").lower() == 'true' else False)
                    self.cc_menu.setCurrentText(
                        str(doc.get("connect_component", {"#text": 'SimpleITK'}).get('#text', "SimpleITK")))

                    filter_parameters = doc.get('filter', {"Name": None})
                    name = filter_parameters['Name']

                    pre_filter_parameters = doc.get('prefilter', {"Name": None})
                    pre_name = pre_filter_parameters['Name']

                    # TODO: Loading ROI and Filter as delays on deletes

                    # Load Filter
                    if name is not None and "#text" in name:
                        name = name.get('#text', "NoFiltering")
                        # self.filterDropDownMenu.setCurrentText(name)
                        self.filter.parentWidget().parentWidget().findChild(QComboBox).setCurrentText(name)
                        self.update_filter_widget(name, **filter_parameters)
                    else:
                        # self.filterDropDownMenu.setCurrentText('NoFiltering')
                        self.filter.parentWidget().parentWidget().findChild(QComboBox).setCurrentText('NoFiltering')
                        self.update_filter_widget('NoFiltering')

                    # Load PreFilter
                    if pre_name is not None and "#text" in name:
                        pre_name = pre_name.get('#text', "NoFiltering")
                        # self.filterDropDownMenu.setCurrentText(name)
                        self.prefilter.parentWidget().parentWidget().findChild(QComboBox).setCurrentText(name)
                        self.update_filter_widget(pre_name, **filter_parameters)
                    else:
                        # self.filterDropDownMenu.setCurrentText('NoFiltering')
                        self.prefilter.parentWidget().parentWidget().findChild(QComboBox).setCurrentText(name)
                        self.update_filter_widget('NoFiltering')

                    # Load ROI
                    roi_parameters = doc.get('ROI', {"Name": None})
                    name = roi_parameters['Name']
                    if name is not None and "#text" in name:
                        name = name.get('#text', "ROIFromSequence")
                        self.update_roi_widget(name, **roi_parameters)
                    else:
                        self.update_roi_widget('ROIFromSequence')

                    self.UDWTScaleArrayList = [UDWTScale(int(i.get('Scale_Number', {"#text": "1"}).get('#text', "1")),
                                                         True if i.get('Scale_Enabled',
                                                                       {"#text": "Disable"}).get('#text',
                                                                                                 "Disable") == 'Enabled'
                                                         else False,
                                                         float(i.get('Sensitivity', {"#text", "100.00"}).get('#text',
                                                                                                             "100.00")))
                                               for i in doc.get('scale_parameter', {"item": []}).get('item', "100.00")]

                    self.rebuildScalePanel()

                if self.loadImagesFromXMLCheckBox.isChecked():
                    print('Load Image')
                    img = AICSImage(doc['filePath'])
                    img.set_scene(doc['scene'])
                    img_channel: xarray.DataArray = img.xarray_data.squeeze()
                    if DimensionNames.Channel in img_channel.dims:
                        img_channel = img_channel.sel(C=doc['channel'])
                    meta: Dict[str, Any] = dict()
                    meta['name'] = doc['input_layer']
                    meta['scale'] = doc['scale']

                    # Apply all other metadata
                    img_meta = {"aicsimage": img, "raw_image_metadata": img.metadata}
                    try:
                        img_meta["ome_types"] = img.ome_metadata
                    except Exception as e:
                        print(e)
                        pass

                    meta["metadata"] = img_meta

                    self.viewer.add_image(img_channel, **meta)
            except (KeyError) as e:
                print(e)
                traceback.print_exc()
            except (FileNotFoundError, FileExistsError) as e:
                print(e)
                traceback.print_exc()
            except ValueError as e:
                print(e)
                traceback.print_exc()
            except TypeError as e:
                print(e)
                traceback.print_exc()
            except RuntimeError as e:
                print(e)
                traceback.print_exc()
        else:
            print("File Path Doesn't exist")

    # Post Processing Showing Reconstruction and Binary Sequence from Computation
    def _addIntermediateLayers(self, points):
        print("Finished")

        import dask.array as da
        import xarray as xr

        try:
            if self.output_widget.automatic_xls_file_naming.isChecked() and (
                    self.output_widget.automatic_xls_file_naming_file_dialog.isChecked() or
                    self.output_widget.dialog_append.isChecked()):
                self.saveXLS(self.gdt,
                             self.output_widget.dialog_append.isChecked())

            if self.output_widget.xml_file_naming.isChecked():
                self.saveXML()

            # TODO: Assume 3 or 2 dims
            if type(self.gdt.inputSequence.image.data) is xarray.DataArray:
                ndim = len(self.gdt.inputSequence.image.data.dims)
            else:
                ndim = len(self.gdt.inputSequence.image.data.shape)

            component = self.gdt.detectionResult.components
            if ndim == 2:
                points = points[:, 1:]
                component = component[0]

            print(points.shape)
            print(component.shape)

            self.viewer.add_points(
                xr.DataArray(da.from_array(points, chunks=_calculate_chunk_sizes(points.shape,
                                                                                 points.dtype))).compute(),
                edge_color='red', face_color='transparent',
                edge_width=0.2, size=2,
                opacity=0.5, out_of_slice_display=True,
                name='points - ' + self.input_box.currentText(),
                metadata=self.metadata,
                scale=self.gdt.inputSequence.scale, ndim=self.viewer.layers.ndim)

            if type(self.gdt.inputSequence.image.data) is xarray.DataArray:
                n = self.gdt.inputSequence.image.data.dims
            else:
                n = ['Z', 'Y', 'X']
                if ndim == 2:
                    n = ['Y', 'X']
            self.viewer.add_labels(xr.DataArray(da.from_array(component,
                                                              chunks=_calculate_chunk_sizes(component.shape,
                                                                                            component.dtype)),
                                                dims=n,
                                                # coords=self.gdt.inputSequence.image.data.coords,
                                                # attrs=self.gdt.inputSequence.image.data.attrs
                                                ).compute(),
                                   name='Label Components - ' + self.input_box.currentText(),
                                   scale=self.gdt.inputSequence.scale, metadata=self.metadata, blending='additive',
                                   # ndim=max(len(component.shape), self.viewer.ndisplay)
                                   )

            if self.tab is not None:
                if self.input_box.currentText() == self.tab.processWidget.TelImage.currentText():
                    self.tab.processWidget.TelLabels.setCurrentText(
                        'Label Components - ' + self.input_box.currentText())
                elif self.input_box.currentText() == self.tab.processWidget.FociImage.currentText():
                    self.tab.processWidget.FociLabels.setCurrentText(
                        'Label Components - ' + self.input_box.currentText())

            if self.displayBinaryImageCheckBox.isChecked() and self.waveletCore.binarySequence is not None:
                binarySequence = self.waveletCore.binarySequence.astype(Sequence.data_type_label_np)
                reconstruction = self.gdt.detectionResult.reconstruction
                roiDetetion = self.gdt.detectionResult.ROI2Detection[['z', 'y', 'x']].to_numpy(dtype=np.float32)
                if ndim == 2:
                    binarySequence = binarySequence[0]
                    reconstruction = reconstruction[0]
                    roiDetetion = roiDetetion[:, 1:]
                # self.gdt.inputSequence.image.data.dims
                self.viewer.add_labels(xr.DataArray(da.from_array(binarySequence,
                                                                  chunks=_calculate_chunk_sizes(
                                                                      binarySequence.shape, binarySequence.dtype)),
                                                    dims=n,
                                                    # coords=self.gdt.inputSequence.image.data.coords,
                                                    # attrs=self.gdt.inputSequence.image.data.attrs
                                                    ).compute(),
                                       name='Binary Sequence - ' + self.input_box.currentText(),
                                       scale=self.gdt.inputSequence.scale, metadata=self.metadata, blending='additive',
                                       # ndim=max(len(binarySequence.shape), self.viewer.ndisplay)
                                       )
                self.viewer.add_image(xr.DataArray(da.from_array(reconstruction,
                                                                 chunks=_calculate_chunk_sizes(
                                                                     reconstruction.shape, reconstruction.dtype)),
                                                   dims=n,
                                                   # coords=self.gdt.inputSequence.image.data.coords,
                                                   # attrs=self.gdt.inputSequence.image.data.attrs
                                                   ).compute(),
                                      name='Reconstruction - ' + self.input_box.currentText(),
                                      scale=self.gdt.inputSequence.scale, blending='additive',
                                      metadata=self.metadata,
                                      # ndim=max(len(reconstruction.shape), self.viewer.ndisplay)
                                      )
                self.viewer.add_points(
                    xr.DataArray(da.from_array(roiDetetion,
                                               chunks=_calculate_chunk_sizes(roiDetetion.shape,
                                                                             roiDetetion.dtype))).compute(),
                    edge_color='orange',
                    size=2, face_color='transparent', edge_width=0.2,
                    opacity=0.5, out_of_slice_display=False,
                    scale=self.gdt.inputSequence.scale,
                    name='points_intensity - ' + self.input_box.currentText(),
                    # ndim=self.gdt.detectionResult.stats['centroids'].shape[1],
                    metadata=self.metadata, ndim=max(roiDetetion.shape[1], self.viewer.layers.ndim))

            self.startButton.setEnabled(True)
            self.qscroll_area_widget.setEnabled(True)
        except ValueError or PermissionError:
            self._reset()
            traceback.print_exc()
        except (FileExistsError, FileNotFoundError) as e:
            print(e)
            self._reset()
            traceback.print_exc()
        except Exception as e:
            print(e)
            self._reset()
            traceback.print_exc()

    def addScalePanel(self, enabled=True):
        scale: UDWTScale = UDWTScale(len(self.UDWTScaleArrayList) + 1, enabled, 100)
        self.UDWTScaleArrayList.append(scale)
        self.scrollWidget.layout().addWidget(scale)

    # Assume that UDWTScaleArrayList is populated
    def rebuildScalePanel(self):
        for i in reversed(range(self.scrollWidget.layout().count())):
            self.scrollWidget.layout().itemAt(i).widget().deleteLater()

        b3Scale: UDWTScale
        for b3Scale in self.UDWTScaleArrayList:
            self.scrollWidget.layout().addWidget(b3Scale)

    def removeScalePanel(self):
        if len(self.UDWTScaleArrayList) != 0:
            self.scrollWidget.layout().itemAt((len(self.UDWTScaleArrayList) - 1)).widget().deleteLater()
            del self.UDWTScaleArrayList[(len(self.UDWTScaleArrayList) - 1)]

    def detectRadio(self):
        if self.detectNegativeRadio.isChecked():
            return True
        if self.detectPositiveRadio.isChecked():
            return False

        return True

    @thread_worker
    def computeDetection(self):
        detectNegative = self.detectRadio()
        computeWATwithROI = self.computeWATwithROICheckBox.isChecked()
        force2DWaveletsFor2D = self.use2DWaveletsFor3DImages.isChecked()
        cropping = self.cropImageCheckBox.isChecked()
        intersection = self.intersectionCheckBox.isChecked()

        print('Starting Computation...')
        # Notification('Starting Computation...')
        st_main = datetime.datetime.now()
        try:
            self.gdt.detectionResult: DetectionSpot = self.waveletCore.computeDetection(
                self.displayBinaryImageCheckBox.isChecked(),
                self.UDWTScaleArrayList,
                self.gdt.inputComputationSequence,
                detectNegative, computeWATwithROI,
                force2DWaveletsFor2D, self.gdt, self.cc_menu.currentText(), cropping, intersection)

        except Exception as e:
            print(f'caught {e=}, {type(e)}: e')
            self.gdt.inputComputationSequence.crop = False
            self.gdt.detectionResult = DetectionSpot(self.gdt.inputComputationSequence)
            traceback.print_exc()

            # self.startButton.setEnabled(True)
            # ErrorNotification(e)

            # raise e
            # return self.gdt.detectionResult.stats['centroids']

        print('Start Region of Interest')
        st = datetime.datetime.now()
        try:
            self.region_of_interest.process(gdt=self.gdt)
            et = datetime.datetime.now()
            print('Finished ROI: ', et - st)
        except Exception as e:
            print(f'caught {e=}, {type(e)}: e')
            print("Reverting Filter")
            traceback.print_exc()

            # raise e

            # self.gdt.detectionResult.stats['centroids'] = centroid_copy

        print('Start Filtering')
        st = datetime.datetime.now()

        try:
            self.filter.process(gdt=self.gdt)
            et = datetime.datetime.now()
            print('Finished Filtering: ', et - st)
        except Exception as e:
            print(f'caught {e=}, {type(e)}: e')
            print("Reverting Filter")
            traceback.print_exc()

            # self.gdt.detectionResult.stats['centroids'] = centroid_copy

            # self.startButton.setEnabled(True)
            # ErrorNotification(e)

            raise e

        centroid: np.ndarray = np.empty((0, self.gdt.detectionResult.stats['centroids'].shape[1]))
        print('Start Compress Spots')
        st = datetime.datetime.now()
        if self.gdt.inputSequence.getSizeZ() != 1:
            for i in range(self.gdt.inputSequence.getSizeZ()):
                centroidYX: np.ndarray = self.gdt.detectionResult.ROI2Detection[['z', 'y', 'x']].to_numpy(
                    dtype=np.float32)
                centroidYX[:, 0] = i
                centroid = np.append(centroid, centroidYX, axis=0)
        else:
            centroid = self.gdt.detectionResult.ROI2Detection[['z', 'y', 'x']].to_numpy(dtype=np.float32)

        et = datetime.datetime.now()
        print('Finished Compress: ', et - st)

        et = datetime.datetime.now()
        print('Finished Compute Detection: ', et - st_main)

        return centroid

    def saveXML(self):
        # Variable name of Dictionary is data
        xml = dicttoxml(self.metadata)

        # Obtain decode string by decode()
        # function
        xml_decode = xml.decode('utf-8')

        print("Save XML Output:", self.xml)

        xmlfile = open(self.xml, "w")
        xmlfile.write(xml_decode)
        xmlfile.close()

    def saveXLS(self, gdt: GlobalDetectionToken, appendToExistingFile: bool):
        xlsFile: str = ""
        try:
            xlsFile = self.xlsx_append if appendToExistingFile else self.xlsx
            workbook = openpyxl.load_workbook(xlsFile) if appendToExistingFile else openpyxl.Workbook(write_only=True)
        except FileNotFoundError:
            if appendToExistingFile:
                workbook = openpyxl.Workbook(write_only=True)
            else:
                return
        except IOError as e:
            print("Cannot write the XLS file ! Is it already open in another application ?")
            traceback.print_exc()
            ErrorNotification(e)

            return

        print("Save XLSX Output:", xlsFile)
        indexPage: int = len(workbook.sheetnames)

        basename = os.path.basename(gdt.inputSequence.path)
        pattern = '[-:._\\s]+'
        basename = re.sub(pattern, "_", basename)
        n = 20 if len(basename) >= 20 else len(basename)
        page: openpyxl.worksheet.worksheet.Worksheet = workbook.create_sheet(
            str(indexPage) + "-" + basename[0:n])

        # date
        page.append(["Date of XLSX page:"])
        page.append([datetime.datetime.now()])

        # input
        page.append(["Input"])
        page.append(("Input Module:", "Current Sequence Input"))
        page.append(("File Path", gdt.inputSequence.path))
        page.append(["Detection Spot"])

        def xlsx_widget(widget: QWidget, widget_type):
            children_inner: List[widget_type] = widget.children()
            for c in children_inner:
                if c.objectName() == '':
                    continue
                if isinstance(c, widget_type):
                    print(c, c.objectName())
                    page.append((c.objectName(), c.isChecked()))

        xlsx_widget(self.widget(), QRadioButton)
        children: List[QObject] = self.widget().children()
        for child in children:
            xlsx_widget(child, QRadioButton)

        page.append(["Parameter Checkboxes:"])
        xlsx_widget(self.widget(), QCheckBox)
        children: List[QObject] = self.widget().children()
        for child in children:
            xlsx_widget(child, QCheckBox)

        page.append(["------------"])
        gdt.inputSequence.saveXLS(page)
        page.append(["------------"])

        # Detector
        page.append(["Detector"])
        page.append(("Detector", self.__class__.__name__))
        page.append(["Parameters:"])

        # columns=['Scale enable', 'Sensitivity', 'Object size']
        for r in dataframe_to_rows(pd.DataFrame([i.row() for i in self.UDWTScaleArrayList]), index=False, header=True):
            page.append(r)

        if isNumberOfScaleOkForImage3D(gdt.inputSequence.getSizeX(), gdt.inputSequence.getSizeY(),
                                       gdt.inputSequence.getSizeZ(), self.waveletCore.getNumberOfMaxEnabledScale()):
            page.append(["ERROR : Scale configuration error"])
            minSize: str = str(getMinSize(self.waveletCore.getNumberOfMaxEnabledScale()))
            page.append(
                ["Scales selected needs an image of size " + minSize + "x" + minSize + "x" + minSize + " image"])
            page.append(
                ["Input Image size is: ", str(gdt.inputSequence.getSizeX()) + "x" + str(gdt.inputSequence.getSizeY()) +
                 "x" + str(gdt.inputSequence.getSizeZ()) + " image"])

        page.append(["------------"])
        page.append(['Processes'])

        def append_xls_process(widget: QWidget, p: openpyxl.worksheet.worksheet.Worksheet):
            children_inner: List = widget.children()
            for c in children_inner:
                if c.objectName() == '':
                    continue
                if isinstance(c, Process):
                    print(c, c.objectName())
                    p.append(("Process:", c.objectName()))
                    p.append([c.description])
                    c.saveXLS(page=p)
                    p.append(["------------"])

        append_xls_process(self.widget(), page)
        children: List[QObject] = self.widget().children()
        for child in children:
            append_xls_process(child, page)

        self.gdt.detectionResult.saveXLS(page)

        # Save & Close Workbook
        workbook.save(xlsFile)
        workbook.close()
