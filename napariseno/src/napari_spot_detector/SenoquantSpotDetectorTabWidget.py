import os

import napari
from qtpy.QtWidgets import QTabWidget

from .segmentations.Segmentation import Segmentation

# os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Environment for Tensorflow

from .UDWTWaveletDetector import UDWTWaveletDetector
from .extensions.napari_senoquant.src.napari_senoquant._widget import ProcessWidget
from .extensions.napari_senoquant.src.napari_senoquant._widget import Marker1Widget
from .extensions.napari_senoquant.src.napari_senoquant.NucleiWidget import NucleiWidget
from .input.BatchInputDetection import BatchInputDetection
from .input.EditInput import EditInput
from .output.Profile import Profile


class SenoquantSpotDetectorTabWidget(QTabWidget):
    def __init__(self, viewer: napari.Viewer, *args, **kwargs):
        super(SenoquantSpotDetectorTabWidget, self).__init__(*args, **kwargs)

        self.viewer = viewer

        # Nuclei - Nuclei channel selector, model selector, find nuclei button
        self.profile = Profile(viewer=self.viewer, tabWidget=self)
        self.nucleiWidget = NucleiWidget(napari_viewer=self.viewer, tab=self)
        self.detector = UDWTWaveletDetector(viewer=self.viewer, tab=self, tag_widget=None,
                                            name=UDWTWaveletDetector.TAB_NAME + " - Spot 1")
        self.detector2 = UDWTWaveletDetector(viewer=self.viewer, tab=self, tag_widget=None,
                                             name=UDWTWaveletDetector.TAB_NAME + " - Spot 2")
        self.markerWidget = Marker1Widget(napari_viewer=self.viewer)
        self.processWidget = ProcessWidget(napari_viewer=self.viewer, nucImage=self.nucleiWidget.NucImage)
        # markerList=self.markerWidget.markerList)

        # self.batchInput = BatchInputDetection(self.viewer)
        self.editInput = EditInput(self.viewer, self.nucleiWidget)

        self.addTab(self.editInput, EditInput.TAB_NAME)
        self.addTab(self.nucleiWidget, NucleiWidget.TAB_NAME)
        self.addTab(self.detector, self.detector.source)
        self.addTab(self.detector2, self.detector2.source)
        self.addTab(self.markerWidget, Marker1Widget.TAB_NAME)
        self.addTab(self.processWidget, ProcessWidget.TAB_NAME)
        self.addTab(self.profile, Profile.TAB_NAME)

        self.detector.update_layer()  # Load existing images from layer list
        self.detector2.update_layer()

        # self.addTab(self.batchInput, BatchInputDetection.TAB_NAME)


        # self.segmentation: Segmentation = Segmentation(viewer=self.viewer, nucleiWidget=self.nucleiWidget)
        # self.addTab(self.segmentation, Segmentation.TAB_NAME)

        # from importlib.metadata import version
        # version = version("napari-spot-detector")
        # print(version)
        # self.display_name = self.display_name + " " + version

        # self.setTabEnabled(1, False)
        # self.setTabEnabled(2, False)

        # self.viewer.layers.events.inserted.connect(self._update_layer)

    # @napari_hook_implementation
    # def napari_experimental_provide_dock_widget(self):
    #     return SenoquantSpotDetectorTabWidget
    def _update_layer(self):
        source_list = ['findTelomeres', 'findFoci', '3DModel']
        labels_list = ['nucleiLabels', 'telomereLabels', 'fociLabelsRaw', 'fociLabels', 'tafLabels']
        marker_list = ['Disk', 'Torus']

        names = [layer.name for layer in self.viewer.layers]

        result = [n for l in labels_list for n in names if l in n]

        if len(result) > 2:
            self.setTabEnabled(1, True)

        # names = [layer.name for layer in self.viewer.layers]

        for layer in self.viewer.layers:
            if 'source' in layer.metadata.keys() and layer.metadata['source'] == self.detector.source:
                self.setTabEnabled(2, True)
                break

        # result_marker = [n for l in marker_list for n in names if l in n]

        # currentText = self.input_box.currentText()

        # layers = [layer.name for layer in self.viewer.layers if layer.name in labels_list]
        # layers = [layer.name for layer in self.viewer.layers if [] in list(layer.metadata.keys)]
        # self.input_box.clear()
        # self.input_box.addItems(layers)
        #
        # if currentText in layers:
        #     self.input_box.setCurrentText(currentText)
        #
        # if self.input_box.currentText() is not None:
        #     self.startButton.setEnabled(True)
        # else:
        #     self.startButton.setEnabled(False)
# @napari_hook_implementation
# def napari_experimental_provide_dock_widget():
#     return SenoquantSpotDetectorTabWidget
