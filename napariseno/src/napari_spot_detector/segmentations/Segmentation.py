from functools import partial
from multiprocessing import Pool
from typing import Final, List, Union

import napari
import numpy as np
from napari.qt.threading import thread_worker
from napari.layers import Labels
from qtpy.QtWidgets import QWidget, QVBoxLayout, QGroupBox, QHBoxLayout, QLabel, QComboBox, QSpinBox

from skimage.segmentation import morphological_geodesic_active_contour
from skimage.segmentation import morphological_chan_vese
from skimage.util import img_as_float

from ..extensions.napari_senoquant.src.napari_senoquant.NucleiWidget import NucleiWidget
from ..input.QHidePushButton import QHidePushButton


def segment_label_morphological_chan_vese(label_value, init_ls, gimage, evolution,
                                          num_iter,
                                          # layer: Union[napari.layers.Layer, None] = None
                                          ):
    # label_value, init_ls, gimage, evolution, num_iter = args
    callback = store_evolution_in(evolution)
    binary_mask = (init_ls == label_value).astype(bool)
    ls = morphological_chan_vese(gimage, num_iter=num_iter,
                                 init_level_set=binary_mask,
                                 smoothing=3, lambda1=1, lambda2=1,
                                 # iter_callback=lambda x: callback(x, label_value)
                                 # iter_callback=lambda x: callback(x, label_value, layer)
                                 )
    # refined_labels[ls > 0] = label_value
    return label_value, ls


# Function for parallel segmentation of a single label
def segment_label_morphological_geodesic_active_contour(label_value, init_ls, gimage, evolution,
                                                        num_iter,
                                                        # layer: Union[napari.layers.Layer, None] = None
                                                        ):
    # label_value, init_ls, gimage, evolution, num_iter = args
    callback = store_evolution_in(evolution)

    binary_mask = (init_ls == label_value).astype(bool)
    ls = morphological_geodesic_active_contour(gimage, num_iter, binary_mask,
                                               smoothing=3,  # Adjust smoothing as needed
                                               balloon=1,  # Adjust other parameters as needed
                                               threshold=0.7,
                                               # iter_callback=lambda x: callback(x, label_value)
                                               # iter_callback=lambda x: callback(x, label_value, layer)
                                               )  # Replace with your callback function
    # refined_labels[ls > 0] = label_value
    return label_value, ls


# def process_worker(segment_function, args):
def process_worker(label_value, segment_function, init_ls, gimage, evolution,
                   num_iter
                   # , layer: Union[napari.layers.Layer, None] = None
                   ):
    # layer = napari.current_viewer()
    # .add_label(np.zeros_like(gimage.shape, dtype=np.uint8), name='Refined Iterative Labels')
    return segment_function(label_value, init_ls, gimage, evolution, num_iter,
                            # layer
                            )


def update_layer_yield(image, label_value, layer: Union[napari.layers.Layer] = None):
    try:
        # napari.current_viewer().layers['Refined Iterative Labels'].data[image > 0] = label_value
        layer.data[image > 0] = label_value
    except RuntimeError as e:
        refined_labed = np.zeros_like(image.shape, np.uint8)
        refined_labed[image > 0] = label_value
        napari.current_viewer().add_labels(refined_labed, name='Refined Iterative Labels', blending='additive')

        # napari.current_viewer().add_labels(refined_labed, name='Refined Iterative Labels', blending='additive')


#     # lambda x: napari.current_viewer().add_labels(x, name='Refined Iterative Labels')


# def store_evolution_in(lst: List):
#     def _store(image: np.ndarray, label_value, layer: Union[napari.layers.Layer, None]):
#         lst.append(np.copy(image))
#         print("Count:", len(lst))  # "Shape:", x.shape,
#         # Add Yield Properties
#         # try:
#         if layer is None:
#             return
#         # napari.current_viewer().layers['Refined Iterative Labels']
#         if len(lst) % 3 == 0:
#             print("Update Layer")
#             layer.data[image > 0] = label_value
#         # except RuntimeError as e:
#         #     refined_labed = np.zeros_like(image.shape, np.uint8)
#         #     refined_labed[image > 0] = label_value
#         #     napari.current_viewer().add_labels(refined_labed, name='Refined Iterative Labels')
#         # napari.current_viewer().add_labels(x, name='Refined Labels')
#         # yield x, v
#         # refined_labed = np.zeros_like(x.shape, np.uint8)
#         # refined_labed[x > 0] = v
#         # yield refined_labed
#
#     return _store

def store_evolution_in(lst: List):
    def _store(image: np.ndarray, label_value,
               # layer: Union[napari.layers.Layer, None]
               ):
        lst.append(np.copy(image))
        print("Count:", len(lst))  # "Shape:", x.shape,
        # Add Yield Properties
        # if layer is None:
        #     return
        if len(lst) % 3 == 0:
            print("Update Layer")
            # layer.data[image > 0] = label_value

    return _store


# def store_evolution_out(label):


functions = ['segment_label_morphological_chan_vese', 'segment_label_morphological_geodesic_active_contour']


class Segmentation(QWidget):
    TAB_NAME: Final[str] = "Segmentation"

    def __init__(self, viewer: napari.Viewer, nucleiWidget: NucleiWidget, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.viewer: napari.Viewer = viewer
        self.nucleiWidget: NucleiWidget = nucleiWidget

        self.setLayout(QVBoxLayout())

        self.editPanel: QGroupBox = QGroupBox(title="Segmentation")
        self.editPanel.setLayout(QVBoxLayout())
        self.layout().addWidget(self.editPanel)

        self.input_widget: QWidget = QWidget()
        self.input_widget.setLayout(QHBoxLayout())

        self.input_label: QLabel = QLabel("Image")
        self.input_box: QComboBox = QComboBox()

        self.input_box.addItems(
            [self.nucleiWidget.NucImage.itemText(i)
             for i in range(self.nucleiWidget.NucImage.count())]
        )
        self.input_box.setCurrentIndex(self.nucleiWidget.NucImage.currentIndex())

        self.label_widget: QWidget = QWidget()
        self.label_widget.setLayout(QHBoxLayout())

        self.input_label_name: QLabel = QLabel("Label")
        self.input_label_box: QComboBox = QComboBox()

        items_label = [layer.name for layer in self.viewer.layers if isinstance(layer, Labels)]
        self.input_label_box.addItems(items_label)

        self.input_widget.layout().addWidget(self.input_label)
        self.input_widget.layout().addWidget(self.input_box)

        self.editPanel.layout().addWidget(self.input_widget)

        self.label_widget.layout().addWidget(self.input_label_name)
        self.label_widget.layout().addWidget(self.input_label_box)

        self.editPanel.layout().addWidget(self.label_widget)

        self.function_box: QComboBox = QComboBox()
        self.function_box.addItems(functions)
        self.editPanel.layout().addWidget(self.function_box)

        self.spinbox_iter: QSpinBox = QSpinBox()
        self.spinbox_iter.setValue(5)
        self.spinbox_iter.setRange(1, 500)

        self.editPanel.layout().addWidget(self.spinbox_iter)

        self.viewer.layers.events.inserted.connect(self.update_layer)
        self.viewer.layers.events.removed.connect(self.update_layer)

        self.button: QHidePushButton = QHidePushButton(self.viewer, self.input_box, text="Segment")
        self.button.clicked.connect(lambda: self.run())
        self.layout().addWidget(self.button)

    def update_layer(self):
        current_text: str = self.input_box.currentText()
        self.input_box.clear()

        items = [self.nucleiWidget.NucImage.itemText(i)
                 for i in range(self.nucleiWidget.NucImage.count())]
        self.input_box.addItems(items)
        if current_text != '' and current_text in items:
            self.input_box.setCurrentText(current_text)
        else:
            self.input_box.setCurrentIndex(self.nucleiWidget.NucImage.currentIndex())

        current_text_label: str = self.input_label_box.currentText()

        items_label = [layer.name for layer in self.viewer.layers if isinstance(layer, Labels)]
        self.input_label_box.addItems(items_label)

        if current_text_label != '' and current_text_label in items:
            self.input_label_box.setCurrentText(current_text_label)
        else:
            if self.input_label_box.count() == 0:
                self.input_label_box.setCurrentText("")
            else:
                self.input_label_box.setCurrentIndex(0)

    # def process(self):
    #     worker = self.run()
    #     worker.returned.connect(lambda x: napari.current_viewer().add_labels(x, name='Refined Labels'))
    #     worker.yielded.connect(update_layer_yield)
    #
    #     worker.start()
    # @thread_worker
    @thread_worker(connect={
        "returned": lambda x: napari.current_viewer().add_labels(x, name='Refined Labels'),
        # 'yielded': update_layer_yield,
    })
    def run(self):
        # Assume the viewer is already open and accessible through a global instance
        # global viewer

        # def store_evolution_in(lst: List):
        #     def _store(image: np.ndarray, label_value, layer: Union[napari.layers.Layer, None]):
        #         lst.append(np.copy(image))
        #         print("Count:", len(lst))  # "Shape:", x.shape,
        #         # Add Yield Properties
        #         if layer is None:
        #             return
        #         if len(lst) % 3 == 0:
        #             print("Update Layer")
        #             yield image, label_value, layer
        #             # layer.data[image > 0] = label_value
        #
        #     return _store

        evolution = []
        # callback = store_evolution_in(evolution)

        image_data = self.viewer.layers[self.input_box.currentText()].data
        converted_data = img_as_float(image_data)
        gimage = converted_data

        if self.input_label_box.currentText() == '':
            return np.zeros_like(image_data.shape, dtype=np.uint8)
        init_ls = self.viewer.layers[self.input_label_box.currentText()].data

        # Process labels without parallel processing
        refined_labels: np.ndarray = np.zeros_like(init_ls.shape, dtype=np.uint8)

        num_iter = self.spinbox_iter.value()
        text = self.function_box.currentText()

        unique = [label_value for label_value in np.unique(init_ls)]
        # for label_value in np.unique(init_ls):
        # eval(text)
        # segment_label_morphological_chan_vese

        label: Union[np.ndarray] = np.zeros(image_data.shape, dtype=np.uint8)
        print(label.shape)
        print(num_iter)

        layer = self.viewer.add_labels(label, name='Refined Iterative Labels', blending='additive')

        for label_value in unique:
            partial_func = partial(process_worker, segment_function=eval(text),
                                   init_ls=init_ls, gimage=gimage,
                                   evolution=evolution, num_iter=num_iter,
                                   # layer=layer
                                   )
            ls = partial_func(label_value)
            refined_labels[ls > 0] = label_value

            yield ls, refined_labels, layer

        # with Pool() as pool:
        #     partial_func = partial(process_worker, segment_function=eval(text),
        #                            init_ls=init_ls, gimage=gimage,
        #                            evolution=evolution, num_iter=num_iter,
        #                            # layer=layer
        #                            )
        #     results = pool.map(partial_func, unique)
        #
        #     # results = [pool.apply_async(process_worker, (eval(text),
        #     #                             (label_value, init_ls, gimage, evolution, num_iter)))
        #     #            for label_value in np.unique(init_ls)]
        #
        #     for result in results:
        #         label_value, ls = result.get()
        #         refined_labels[ls > 0] = label_value

        # binary_mask = (init_ls == label_value).astype(bool)
        # ls = morphological_chan_vese(gimage, num_iter=200,
        #                              init_level_set=binary_mask,
        #                              smoothing=3, lambda1=1, lambda2=1,
        #                              # iter_callback=callback
        #                              )
        # refined_labels[ls > 0] = label_value
        # viewer.layers[1].data = refined_labels

        return refined_labels

        # Update the existing labels layer in napari
        # viewer.layers[1].data = refined_labels

        # # Add a new labels layer in napari
        # refined_labels = viewer.add_labels(name='Refined Labels')
        #
        # # Iterate through the level sets and add refined labels
        # for i, ls in enumerate(results):
        #     refined_labels.data[ls > 0] = np.unique(init_ls.data)[i]
