import math
import os.path
import traceback
from typing import <PERSON><PERSON>, Dict, List, Union

import numpy as np
import openpyxl.worksheet.worksheet
import pandas as pd
from aicsimageio.types import PhysicalPixelSizes
from aicsimageio.writers import OmeTiffWriter
from napari.layers import <PERSON><PERSON><PERSON>, Layer
from napari.utils import Colormap
from napari.utils.notifications import ErrorNotification
from napari_roi import R<PERSON><PERSON>rigin, ROIWidget
from napari_roi.qt import R<PERSON><PERSON>ayerAccessor
from ome_types._autogenerated.ome_2016_06 import PixelType
from overrides import override
from qtpy import QtCore
from qtpy.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QGroupBox, QLineEdit, QCheckBox,
                            QSpinBox, QFormLayout, QDoubleSpinBox, QComboBox)

from ...images.Sequence import Sequence
from ...tokens.GlobalDetectionToken import GlobalDetectionToken
from .FilterHelper import parallel_apply_along_axis, f
from .UFishFilter import UFishFilter
from ..Process import Process
from ...UDWT.UDWTWaveletCore import UDWT<PERSON>avelet<PERSON>ore
from ...images.ROI import ROI


# TODO: Parallelize Filtering Steps

class PreFiltering(Process):
    def __init__(self, description='PreFiltering', *args, **kwargs):
        super().__init__(description=description, *args, **kwargs)

    def open_file_viewer(self, file_path, gdt: GlobalDetectionToken):
        sequence: Sequence = gdt.inputSequence

        if file_path is not None:
            # Load a file using the napari-aicsimageio plugin
            layers: List[Layer] = self.viewer.open(file_path, plugin='napari-aicsimageio', blending='additive',
                                                   colormap=sequence.image.colormap)

            # Changing the computation sequence to recently added one
            self.setInputSequence(gdt=gdt, layer=layers[0])

    @staticmethod
    def setInputSequence(gdt: GlobalDetectionToken, layer: Layer):
        viewer = gdt.inputSequence.viewer
        # Set Data to inputComputationSequence
        gdt.inputComputationSequence = Sequence(layer.name, viewer)
        # gdt.inputComputationSequence = Sequence(viewer.layers[-1].name, viewer)

    @staticmethod
    def get_mask(sequence: Sequence, t: int = 1, crop: bool = False) -> np.ndarray:
        bounds = ROI(Shapes(data=[np.array([[0, 0], [sequence.getSizeY(),
                                                     sequence.getSizeX()]])], shape_type='rectangle',
                            visible=False, name='ROI Image Bound'))
        bounds.roi_origin = ROIOrigin.TOP_LEFT

        rois: List[ROI] = list()  # Threshold and Rectangles Boundaries
        rois.extend(sequence.rois)
        sequence.crop = crop
        if len(rois) == 0:
            rois.append(bounds)

        masks: np.ndarray = UDWTWaveletCore.buildBinaryMask(bounds=bounds, rois=rois, t=t, sequence=sequence)

        return masks

    @staticmethod
    def save_image_layer(sequence: Sequence, enh_img: np.ndarray) -> str:
        scale: Union[List, Tuple] = sequence.scale.tolist() if isinstance(sequence.scale,
                                                                          np.ndarray) else sequence.scale  # .tolist()
        if len(scale) == 2:
            scale.insert(0, None)
        physical_pixel_sizes = PhysicalPixelSizes(*scale)

        try:
            file_path = sequence.aicsimage.reader._path
        except Exception as e:
            print(e)
            file_path = sequence.image.source.path

        # Specify the file path and name
        filename, ext = os.path.splitext(file_path)

        # Use Channel Name of File path
        name = sequence.channel
        # name = sequence.image.name  # Actual Image Name from Layer List
        directory = os.path.dirname(f'{file_path}')
        file_path = os.path.join(directory, name + "_filter" + ext)  # To Match Channel Name

        # Old Method of file name
        # file_path: str = filename + "_filter" + ext

        filename, ext = os.path.splitext(file_path)
        counter = 1
        while os.path.exists(file_path):
            file_path = f"{filename}({counter}){ext}"
            counter += 1

        # Use the OmeTiffWriter to save the image with scale information
        colormap: Colormap = sequence.image.colormap
        channel_colors = list(colormap.colors[-1, 0:3] * 255)
        channel_colors = [[channel_colors]]
        image_index = 0
        try:
            ome_xml = sequence.aicsimage.ome_metadata
            print(enh_img.dtype)
            ome_xml.images[image_index].pixels.type = (
                PixelType.FLOAT) if enh_img.dtype == np.float32 else ome_xml.images[image_index].pixels.type

            ome_xml.images[image_index].pixels.size_c = 1
            ll = [counter for counter, i in enumerate(ome_xml.images[image_index].pixels.channels) if
                  i.name in sequence.imageName]
            image_index_location = 0 if len(ll) == 0 else ll[0]
            c = ome_xml.images[image_index].pixels.channels[image_index_location]
            c.id = 'Channel:0'
            c.name = sequence.channel + " filter"
            ome_xml.images[image_index].pixels.channels = [c]
            ome_xml.images[image_index].pixels.size_x = enh_img.shape[-1]
            ome_xml.images[image_index].pixels.size_y = enh_img.shape[-2]

            OmeTiffWriter.save(
                data=enh_img,
                uri=file_path,
                ome_xml=ome_xml,
                image_name=sequence.channel + " filter",
                physical_pixel_sizes=physical_pixel_sizes,
            )
        except Exception as e:
            print(e)

            OmeTiffWriter.save(
                data=enh_img,
                uri=file_path,
                image_name=sequence.channel + " filter",
                physical_pixel_sizes=physical_pixel_sizes,
                # physical_pixel_sizes=tuple(scale),
                dim_order='ZYX' if len(enh_img.shape) == 3 else 'YX',
                channel_names=[sequence.channel + " filter"],
                channel_colors=channel_colors
            )

        # OmeTiffWriter.save(
        #     data=enh_img,
        #     uri=file_path,
        #     # pixels_physical_size=physical_pixel_sizes,
        #     # dim_order='TCZYX' if len(enh_img.shape) == 3 else 'TCYX',
        #     ome_xml=sequence.aicsimage.ome_metadata,
        #     # channel_names=[sequence.channel + " filter"],
        #     # channel_colors=channel_colors
        # )
        # OmeTiffWriter.save(
        #     data=enh_img,
        #     uri=file_path,
        #     pixels_physical_size=physical_pixel_sizes,
        #     dim_order='TCZYX' if len(enh_img.shape) == 3 else 'TCYX',
        #     ome_xml=sequence.aicsimage.ome_metadata,
        #     channel_names=[sequence.channel + " filter"],
        #     channel_colors=channel_colors
        # )

        return file_path

        # return sequence.viewer.layers[-1].name
        # return Sequence(sequence.viewer.layers[-1].name, sequence.viewer)


class PostFiltering(Process):
    def __init__(self, description='PostFiltering', *args, **kwargs):
        super().__init__(description=description, *args, **kwargs)


class NoFiltering(Process):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        self.layout = QVBoxLayout()
        self.layout.addWidget(QLabel('No Filtering'))
        self.setLayout(self.layout)


class SizeFiltering(PostFiltering):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        self.layout = QVBoxLayout()

        self.buttonGroupMin = QGroupBox()
        self.buttonGroupMin.setLayout(QHBoxLayout())
        self.buttonGroupMin.layout().addWidget(QLabel('Min size: '))
        self.minValueTextField = QLineEdit("0" if 'Min' not in kwargs.keys()
                                           else kwargs.get('Min', {"#text": "0"}).get('#text', "0"))
        self.buttonGroupMin.layout().addWidget(self.minValueTextField)

        self.buttonGroupMax = QGroupBox()
        self.buttonGroupMax.setLayout(QHBoxLayout())
        self.buttonGroupMax.layout().addWidget(QLabel("Max size: "))
        self.maxValueTextField = QLineEdit("3000" if 'Max' not in kwargs.keys()
                                           else kwargs.get('Max', {"#text": "3000"}).get('#text', "3000"))
        self.buttonGroupMax.layout().addWidget(self.maxValueTextField)

        self.layout.addWidget(QLabel("Range of accepted objects (in pixels)"))
        self.layout.addWidget(self.buttonGroupMin)
        self.layout.addWidget(self.buttonGroupMax)

        self.setLayout(self.layout)

    def parameters(self) -> Dict:
        return {'Name': super().parameters()['Name'],
                "Min": self.minValueTextField.text(), "Max": self.maxValueTextField.text()}

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        page.append(("Min:", self.minValueTextField.text()))
        page.append(("Max:", self.maxValueTextField.text()))

    def process(self, gdt: GlobalDetectionToken):
        highValue: int = int(self.maxValueTextField.text())
        lowValue: int = int(self.minValueTextField.text())

        detectionSpots = gdt.detectionResult.ROI2Detection['Surface'].to_numpy(dtype=np.float32)

        high = detectionSpots <= highValue
        low = detectionSpots >= lowValue
        condition = np.logical_not(np.logical_and(high, low))
        removeIdx = np.where(condition)

        index_list = removeIdx[0].tolist()

        gdt.detectionResult.ROI2Detection = gdt.detectionResult.ROI2Detection.loc[
            ~gdt.detectionResult.ROI2Detection.index.isin(index_list)]

        removeIdx_plus = removeIdx[0] + 1

        mask = np.isin(gdt.detectionResult.components, removeIdx_plus)  # remove labels

        gdt.detectionResult.components[mask] = 0
        gdt.detectionResult.reconstruction[mask] = 0


class DeClusterFiltering(PostFiltering):
    def __init__(self, *args, **kwargs):
        Process.__init__(self, *args, **kwargs)

        # self.gdt: Union[GlobalDetectionToken, None]
        self.nbSplitDetection: int = 0

        self.layout = QVBoxLayout()

        self.spotRaySizeBox = QGroupBox()
        self.spotRaySizeBox.setLayout(QHBoxLayout())
        self.spotRaySizeBox.layout().addWidget(QLabel("spot ray:"))
        self.spotRaySizeTextBox = QLineEdit("4") if "spot_ray" not in kwargs.keys() else QLineEdit(
            str(kwargs.get('spot_ray', {"#text", "4"}).get('#text', "4")))
        self.spotRaySizeBox.layout().addWidget(self.spotRaySizeTextBox)

        self.layout.addWidget(self.spotRaySizeBox)

        self.setLayout(self.layout)

    def parameters(self) -> Dict:
        return {'Name': super().parameters()['Name'], "spot_ray": self.spotRaySizeTextBox.text()}

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        page.append(("spot ray:", self.spotRaySizeTextBox.text()))

    @staticmethod
    def _getSurfaceOfOneSpot(spotRay: float) -> float:
        bound: int = int(math.ceil((spotRay + 1)))
        surface: int = 0

        x: int
        y: int
        for x in range(-bound, bound + 1):
            for y in range(-bound, bound + 1):
                p: Tuple = (x, y)
                if math.dist((0, 0), p) < spotRay:
                    surface += 1

        return surface

    # two spots right next to each other offset by 1 pixel.
    @staticmethod
    def _getSurfaceOfTwoContiguousSpot(spotRay: float) -> float:
        bound: int = int(math.ceil(spotRay + 1) + 1)
        surface: int = 0

        x: int
        y: int
        for x in range(-bound, bound + 1):
            for y in range(-bound, bound + 1):
                p: Tuple = (x, y)
                if math.dist((0, 0), p) < spotRay or math.dist((1, 0), p):
                    surface += 1

        return surface

    def filterTest(self, seqid: int, gdt: GlobalDetectionToken):
        maxValue: float
        p: Tuple

        image = gdt.inputSequence.getAllImage()
        if len(image.shape) == 2:
            image = image[np.newaxis, :, :]
        component = gdt.detectionResult.components

        seqids = gdt.detectionResult.ROI2Detection['Detection #'].to_numpy()[:, np.newaxis]

        maxValues = parallel_apply_along_axis(f, 1, seqids, image=image, component=component)

        condition_max = maxValues > 80
        condition_max_count = np.count_nonzero(condition_max)

        self.nbSplitDetection += condition_max_count

        decluster_df = gdt.detectionResult.ROI2Detection[condition_max]

        decluster_df['y'] = decluster_df['y'] + 1
        decluster_df['x'] = decluster_df['x'] + 1

        decluster_df.loc[:, ['Minimum Intensity', 'Maximum Intensity', 'Average Intensity', 'Sum Intensity']] = \
            [None, None, None, None]
        decluster_df['Surface'] = 1
        decluster_df['Volume'] = gdt.inputSequence.calc_volume_from_pixel()
        decluster_df['Detection #'] = np.arange(gdt.detectionResult.N + 1,
                                                gdt.detectionResult.N + len(decluster_df) + 1)

        gdt.detectionResult.ROI2Detection = pd.concat([gdt.detectionResult.ROI2Detection, decluster_df],
                                                      ignore_index=True)

    def process(self, gdt: GlobalDetectionToken):
        print('Cluster filtering...')
        # self.gdt = gdt

        spotRay: float = float(self.spotRaySizeTextBox.text())

        # compute surface of 1 spot:
        surfaceOfOneSpot: int = int(self._getSurfaceOfOneSpot(spotRay))
        surfaceOfTwoContiguousSpot: int = int(self._getSurfaceOfTwoContiguousSpot(spotRay))

        print("surface of one spot:", surfaceOfOneSpot)
        print("surface of 2 glued spots:", surfaceOfTwoContiguousSpot)
        print("difference between the 2:", (surfaceOfTwoContiguousSpot - surfaceOfOneSpot))
        self.nbSplitDetection = 0

        self.filterTest(gdt.detectionResult.N, gdt)

        print("Nombre de split phase 1:", self.nbSplitDetection)


class UFISHPreFiltering(PreFiltering):
    chunk_checkbox_label = "ufish_chunk"
    spinbox_batch_size_object_name = "ufish_batch_size"
    spinbox_threshold_label = "ufish_threshold"
    checkbox_3d_blend_object_name = "ufish_blend"
    # order_list = ['X', 'Y', 'Z', 'C', 'T']
    order_list = ['Z', 'Y', 'X']
    # order_list = ['X', 'Y', 'Z']

    num_dims = 3
    defaults = [1, 512, 512]
    ratios = [2, 3, 3]
    maximum = 10000

    def __init__(self, input_box: Union[None, QComboBox] = None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.ufish: UFishFilter = UFishFilter()

        self.input_box: QComboBox = input_box

        self.layout = QVBoxLayout()

        self.inference_ufish = QGroupBox("Inference U-Fish to Produce an Enhance Image")
        self.inference_ufish.setLayout(QVBoxLayout())
        self.layout.addWidget(self.inference_ufish)

        self.checkbox_3d_blend = QCheckBox("Blend 3D")
        self.checkbox_3d_blend.setObjectName(self.checkbox_3d_blend_object_name)
        self.inference_ufish.layout().addWidget(self.checkbox_3d_blend)

        self.batch_size_widget = QWidget()
        self.batch_size_widget.setLayout(QHBoxLayout())
        self.batch_size_widget.layout().addWidget(QLabel("Batch Size"))
        self.spinbox_batch_size: QSpinBox = QSpinBox()
        self.spinbox_batch_size.setValue(4)
        self.spinbox_batch_size.setRange(1, 50)
        self.spinbox_batch_size.setSingleStep(1)
        self.spinbox_batch_size.setObjectName(self.spinbox_batch_size_object_name)
        self.batch_size_widget.layout().addWidget(self.spinbox_batch_size)
        self.inference_ufish.layout().addWidget(self.batch_size_widget)

        # self.threshold_widget = QWidget()
        # self.threshold_widget.setLayout(QHBoxLayout())
        # self.threshold_widget.layout().addWidget(QLabel("P Threshold"))
        # self.spinbox_threshold = QDoubleSpinBox()
        # self.spinbox_threshold.setValue(0.5)
        # # self.spinbox_threshold.setMaximum(1.0)
        # self.spinbox_threshold.setObjectName(self.spinbox_threshold_label)
        # self.threshold_widget.layout().addWidget(self.spinbox_threshold)
        # self.inference_ufish.layout().addWidget(self.threshold_widget)

        self.chunk_checkbox: QCheckBox = QCheckBox("Chunking")
        self.chunk_checkbox.setObjectName(self.chunk_checkbox_label)

        self.chunk_widget: QWidget = QWidget()
        # grid_layout = QGridLayout()
        self.chunk_widget.setLayout(QVBoxLayout())
        self.layout.addWidget(self.chunk_widget)

        for n in range(self.num_dims):
            spinbox: QSpinBox = QSpinBox()
            spinbox.setObjectName(self.order_list[n])
            spinbox.setMinimum(1)
            spinbox.setMaximum(self.maximum)

            spinbox.setValue(self.defaults[n])
            self.chunk_widget.layout().addWidget(spinbox)

        label = QLabel("Orientation: " + "".join(self.order_list))
        label.setAlignment(QtCore.Qt.AlignRight)
        self.chunk_widget.layout().addWidget(label)
        self.layout.addWidget(self.chunk_widget)

        if self.viewer is not None and len(self.viewer.layers) != 0:
            for n in range(len(self.viewer.layers[0].data.shape), 0, -1):
                s: QSpinBox = self.chunk_widget.findChild(QSpinBox, self.order_list[-n])
                s.setValue(math.ceil(self.viewer.layers[0].data.shape[-n] / self.ratios[-n]))

        self.chunk_checkbox.setChecked(True)
        self.inference_ufish.layout().addWidget(self.chunk_checkbox)

        # Add Function for Change for DropDown (Not being used at the moment)
        if self.input_box is not None:
            self.input_box.currentTextChanged.connect(self.on_process_text_changed)

        self.setLayout(self.layout)

    @override
    def on_process_text_changed(self, text: Union[str, slice]):
        try:
            if text != '':
                for n in range(len(self.viewer.layers[text].data.shape), 0, -1):
                    s: QSpinBox = self.chunk_widget.findChild(QSpinBox, self.order_list[-n])
                    s.setValue(math.ceil(self.viewer.layers[text].data.shape[-n] / self.ratios[-n]))
        except Union[IndexError, AttributeError, RuntimeError, ValueError, TypeError] as e:
            print(e)
            traceback.print_stack()
            ErrorNotification(e)

    def parameters(self) -> Dict:
        d: Dict = {}
        for c in self.children():
            if c.objectName() != '':
                if isinstance(c, QSpinBox):
                    d[c.objectName()] = c.value()
                elif isinstance(c, QCheckBox):
                    d[c.objectName()] = c.isChecked()

        return d
        # return {self.chunk_checkbox_label: self.findChild()}
        # return {'Name': super().parameters()['Name'], "spot_ray": self.spotRaySizeTextBox.text()}

    def process(self, gdt: GlobalDetectionToken):
        layer_name = gdt.inputSequence.imageName
        data = gdt.inputSequence.getAllImage()
        print("Run inference on", layer_name)
        # input_axes = None
        blend_3d = self.checkbox_3d_blend.isChecked()
        batch_size = self.spinbox_batch_size.value()
        # p_thresh = self.spinbox_threshold.value()
        p_thresh = 0.5
        chunking = self.chunk_checkbox.isChecked()

        # chunk_size = (round(data.shape[-2] / batch_size), round(data.shape[-1] / batch_size))
        # chunk_size = (int(data.shape[-2]/batch_size), int(data.shape[-1]/batch_size))
        # chunk_size = (256, 256)
        # chunk_size = (512, 512)
        # chunk_size = (1024, 1024)
        # chunk_size = (2048, 2048)
        # chunk_size = (4096, 4096)
        # chunk_size = None
        # chunk_size = data.shape

        print("Min:", data.min(), "Max:", data.max(), "dtype:", data.dtype, "Shape:", data.shape)

        from onnxruntime.capi.onnxruntime_pybind11_state import RuntimeException
        try:
            if chunking:
                chunk_size: Tuple = tuple([self.findChild(QSpinBox, self.order_list[-o]).value() for o in
                                           range(len(data.shape), 0, -1)])
                # if len(data.shape) == 2:
                #     chunk_size: Tuple[int] = (self.findChild(QSpinBox, o).value() for o in self.order_list[1:])
                #     # chunk_size = (int(data.shape[-2] / 2), int(data.shape[-1] / 2))
                # else:
                #     chunk_size: Tuple[int] = (self.findChild(QSpinBox, o).value() for o in self.order_list)
                #     # chunk_size = (int(data.shape[-3] / 2), int(data.shape[-2] / 2), int(data.shape[-1] / 2))

                print("Size of Image ", data.shape)
                print("Size of Chunk ", chunk_size)

                _, enh_img = self.ufish.predict_chunks(data, blend_3d=blend_3d, batch_size=batch_size,
                                                       p_thresh=p_thresh, chunk_size=chunk_size)
            else:
                _, enh_img = self.ufish.predict(data, blend_3d=blend_3d, batch_size=batch_size, p_thresh=p_thresh)

            print("Enhance Image - Min:", enh_img.min(), "Max:", enh_img.max(), "dtype:", enh_img.dtype, "Shape:",
                  enh_img.shape)

            # Normalized Images
            img_normalized = (enh_img - np.min(enh_img)) / (np.max(enh_img) - np.min(enh_img))
            img_scaled = img_normalized * (data.max() - data.min()) + data.min()
            img_uint8 = img_scaled.astype(gdt.inputSequence.image.data.dtype)

            return PreFiltering.save_image_layer(sequence=gdt.inputSequence, enh_img=img_uint8)
        except Union[RuntimeException, RuntimeError, TypeError, MemoryError, IOError] as e:
            print(e)
            traceback.print_stack()
            ErrorNotification(e)
            return None


class EditFiltering(PreFiltering):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.setLayout(QVBoxLayout())

        # Adding _roi_widget to roi group
        ROILayerAccessor.DEFAULT_ROI_ORIGIN = ROIOrigin.TOP_LEFT

        self.roiWidget: ROIWidget = ROIWidget(self.viewer)
        self.roiWidget._add_widget.setDisabled(True)
        self.roiWidget._add_widget.setHidden(True)
        self.roiWidget._save_widget.setDisabled(True)
        self.roiWidget._save_widget.setHidden(True)

        # Hide X/Y origin
        label_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.LabelRole)
        field_item = self.roiWidget._roi_table_widget.layout().itemAt(1, QFormLayout.FieldRole)

        # Hide the items
        if label_item is not None:
            label_item.widget().setHidden(True)
        if field_item is not None:
            field_item.widget().setHidden(True)

        self.layout().addWidget(self.roiWidget)

    @override
    def process(self, gdt: GlobalDetectionToken):
        sequence: Sequence = gdt.inputComputationSequence

        masks = PreFiltering.get_mask(sequence=sequence)
        masks = masks.reshape(
            sequence.getAllImage().shape)
        image: np.ndarray = sequence.getAllImage()
        enh_img: np.ndarray = image * masks.reshape(sequence.getAllImage().shape)  # Filtering Image using Mask

        return PreFiltering.save_image_layer(sequence=sequence, enh_img=enh_img)
