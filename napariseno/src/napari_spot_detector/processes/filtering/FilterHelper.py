# import concurrent
import multiprocessing
# import multiprocessing as mp

# from ...tokens.GlobalDetectionToken import GlobalDetectionToken

import numpy as np


def f(seqid, image: np.ndarray, component: np.ndarray):
    extracted_image: np.ndarray = image[component == seqid]
    maxValue = np.max(extracted_image)

    return maxValue
def parallel_apply_along_axis(func1d, axis, arr, *args, **kwargs):
    """
    Like numpy.apply_along_axis(), but takes advantage of multiple
    cores.
    """
    # Effective axis where apply_along_axis() will be applied by each
    # worker (any non-zero axis number would work, so as to allow the use
    # of `np.array_split()`, which is only done on axis 0):
    effective_axis = 1 if axis == 0 else axis
    if effective_axis != axis:
        arr = arr.swapaxes(axis, effective_axis)

    # Chunks for the mapping (only a few chunks):
    chunks = [(func1d, effective_axis, sub_arr, args, kwargs)
              for sub_arr in np.array_split(arr, multiprocessing.cpu_count())]
    individual_results = []
    # for c in chunks:
    #     (func1d, axis, arr, args, kwargs) = c
    #     results = np.apply_along_axis(func1d, axis, arr, *args, **kwargs)
    #     individual_results.append(results)
    pool = multiprocessing.Pool()
    for c in chunks:
        (func1d, axis, arr, args, kwargs) = c
        # results = np.apply_along_axis(func1d, axis, arr, *args, **kwargs)

        # result = pool.apply_async(np.apply_along_axis, args=(func1d, axis, arr, *args), **kwargs)
        result = pool.apply_async(np.apply_along_axis, (func1d, axis, arr), kwds=kwargs)
        # results.append(result)
        individual_results.append(result)

    # Get the results.
    results = [result.get() for result in individual_results]

    individual_results = results




    # if __name__ == '__main__':
    #     data = np.random.rand(1000, 100)  # input data
    #     pool = Pool(processes=4)  # create a pool of 4 workers
    #
    #     results = []
    #     for i in range(data.shape[1]):
    #         result = pool.apply_async(np.apply_along_axis, args=(func1d, 0, data[:, i]))
    #         results.append(result)
    #
    #     # Get the results.
    #     results = [result.get() for result in results]

    # pool = multiprocessing.Pool()
    # individual_results = pool.map(unpacking_apply_along_axis, chunks)
    # Freeing the workers:
    # pool.close()
    # pool.join()

    return np.concatenate(individual_results)

def unpacking_apply_along_axis(all_args):
    (func1d, axis, arr, args, kwargs) = all_args
    """
    Like numpy.apply_along_axis(), but with arguments in a tuple
    instead.

    This function is useful with multiprocessing.Pool().map(): (1)
    map() only handles functions that take a single argument, and (2)
    this function can generally be imported from a module, as required
    by map().
    """
    return np.apply_along_axis(func1d, axis, arr, *args, **kwargs)


# def filterTestingParallelized(numLabel: int):
#     with concurrent.futures.ProcessPoolExecutor(max_workers=int(os.cpu_count()) - 1) as executor:
#         for wh, r in zip([wh for wh in range(arrayIn.shape[1])],
#                          executor.map(filterZdirection_opt,
#                                       [(arrayIn[:, wh], stepS) for wh in range(arrayIn.shape[1])],
#                                       chunksize=int(arrayIn.shape[1] / os.cpu_count()))):
#             # with multiprocessing.Pool(multiprocessing.cpu_count() - 1) as pool:
#             #     for z, r in zip([z for z in range(depth)], pool.map(worker, [(z, currentArray3D, width,
#             #                                               height, stepS) for z in range(depth)])):
#             arrayOut[:, wh] = r
#
# def sizeFilter():
#     pass
#
# def filterTest(seqid: int, gdt: GlobalDetectionToken):
#     maxValue: float
#     p: Tuple
#
#     extracted_image: np.ndarray = (gdt.inputSequence.getAllImage())[gdt.detectionResult.components == seqid]
#     maxValue = np.max(extracted_image)
#
#
#     if maxValue > 80:
#         global nbSplitDetection
#         nbSplitDetection += 1
#         # self.nbSplitDetection += 1
#         row = np.array([[gdt.detectionResult.stats['centroids'][seqid][0],
#                          gdt.detectionResult.stats['centroids'][seqid][1] + 1,
#                          gdt.detectionResult.stats['centroids'][seqid][2] + 1]])
#         gdt.detectionResult.stats['centroids'] = np.append(gdt.detectionResult.stats['centroids'],
#                                                            row, axis=0)
#         gdt.detectionResult.stats['voxel_counts'] = np.append(gdt.detectionResult.stats['voxel_counts'],
#                                                               [1], axis=0)
#         gdt.detectionResult.N += 1
