from typing import List

from ..Process import Process
from ...images import ROI
from ...tokens import GlobalDetectionToken

import numpy as np
import pandas as pd


class ROIDetection(Process):
    def __init__(self, description: str = 'Region of interest generated by: ROIDetection', *args, **kwargs):
        super().__init__(description=description, *args, **kwargs)
        self.name: str = kwargs['Name']['#text'] if 'Name' in kwargs.keys() else 'ROIDetection'


class ROIFromSequence(ROIDetection):

    def __init__(self, description: str = "ROI From Sequence Module", *args, **kwargs):
        super().__init__('Region of interest generated by: ' + description, *args, **kwargs)

    def process(self, gdt: GlobalDetectionToken):
        roiArrayList: List[ROI] = gdt.inputSequence.getROIs()

        num_points = gdt.detectionResult.stats['centroids'].shape[0]
        detection_num = np.arange(1, gdt.detectionResult.N + 1)[:, np.newaxis]
        if len(roiArrayList) == 0:
            text_list = np.array([None] * num_points)[:, np.newaxis]
            name_list = np.array([None] * num_points)[:, np.newaxis]
            shape_list = np.array(['rectangle'] * num_points)[:, np.newaxis]

            data = np.concatenate((detection_num,
                                   gdt.detectionResult.stats['voxel_counts'][:, np.newaxis],
                                   gdt.detectionResult.stats['centroids'],
                                   np.zeros(shape=(num_points, 1)) * gdt.detectionResult.t,
                                   gdt.detectionResult.intensity,
                                   gdt.detectionResult.volumes, text_list, name_list, shape_list), axis=1)

            df = pd.DataFrame(data=data, columns=gdt.detectionResult.ROI2Detection.columns)
            df = df.astype(gdt.detectionResult.dtypes)

            gdt.detectionResult.ROI2Detection = pd.concat([gdt.detectionResult.ROI2Detection, df], ignore_index=True)

        else:
            for roi in roiArrayList:
                shape: np.ndarray
                text: str
                shape_type: str
                for (text, shape_type, shape) in zip(roi.layer.text.values, roi.layer.shape_type, roi.layer.data):
                    vertices = shape

                    # create a bounding box around the shape
                    if vertices.shape[1] == 2:
                        y_min, x_min = np.min(vertices, axis=0)
                        y_max, x_max = np.max(vertices, axis=0)  # TODO: check ordering of shape vertices
                    else:
                        _, y_min, x_min = np.min(vertices, axis=0)
                        _, y_max, x_max = np.max(vertices, axis=0)  # TODO: check ordering of shape vertices

                    centroids = gdt.detectionResult.stats['centroids']

                    # filter the centroids to have coordinates within this ROI Shape
                    mask = (x_min <= centroids[:, 2]) & (centroids[:, 2] <= x_max) & \
                           (y_min <= centroids[:, 1]) & (centroids[:, 1] <= y_max)  # & \
                    # (z_min <= centroids[:, 0]) & (centroids[:, 0] <= z_max)
                    num_points = centroids[mask].shape[0]

                    text_list = np.array([text] * num_points)[:, np.newaxis].astype('str')
                    name_list = np.array([roi.layer.name] * num_points)[:, np.newaxis].astype('str')
                    shape_list = np.array([shape_type] * num_points)[:, np.newaxis].astype('str')

                    data = np.concatenate((detection_num[mask],
                                           gdt.detectionResult.stats['voxel_counts'][mask][:, np.newaxis],
                                           centroids[mask],
                                           np.zeros(shape=(num_points, 1)) * gdt.detectionResult.t,
                                           gdt.detectionResult.intensity[mask],
                                           gdt.detectionResult.volumes[mask], text_list, name_list, shape_list),
                                          axis=1).tolist()
                    df = pd.DataFrame(data=data, columns=gdt.detectionResult.ROI2Detection.columns)
                    df = df.astype(gdt.detectionResult.dtypes)

                    gdt.detectionResult.ROI2Detection = pd.concat([gdt.detectionResult.ROI2Detection, df],
                                                                  ignore_index=True)

        # gdt.detectionResult.ROI2Detection = gdt.detectionResult.ROI2Detection.astype(gdt.detectionResult.dtypes)
