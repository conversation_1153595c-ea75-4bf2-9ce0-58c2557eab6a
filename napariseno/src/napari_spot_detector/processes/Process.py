from typing import Dict, Union

import napari
import openpyxl
from napari._qt.qthreading import thread_worker
from qtpy.QtWidgets import QWidget

from ..tokens.GlobalDetectionToken import GlobalDetectionToken


class Process(QWidget):
    def __init__(self, description='', viewer: napari.Viewer = napari.current_viewer(), *args, **kwargs):
        QWidget.__init__(self)
        self.description: str = description
        self.name: str = kwargs['Name']['#text'] if 'Name' in kwargs.keys() else 'Filtering'
        self.viewer: napari.Viewer = kwargs['Viewer'] if 'Viewer' in kwargs.keys() else viewer

    def process(self, gdt: GlobalDetectionToken):
        pass

    def saveXLS(self, page: openpyxl.worksheet.worksheet.Worksheet):
        pass

    def parameters(self) -> Dict:
        return {"Name": self.__class__.__name__}

    @thread_worker
    def run(self, gdt: GlobalDetectionToken):
        return self.process(gdt=gdt)

    def on_process_text_changed(self, text: Union[str, slice]):
        pass
        # print(text)
