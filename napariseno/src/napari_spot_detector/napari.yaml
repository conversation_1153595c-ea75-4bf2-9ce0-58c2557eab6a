name: napari-senoquant-spot-detector
display_name: Senoquant Spot Detector
contributions:
  commands:
    - id: napari-senoquant-spot-detector.get_reader
      python_name: napari_spot_detector._reader:napari_get_reader
      title: Open data with Spot Detector
    - id: napari-senoquant-spot-detector.write_multiple
      python_name: napari_spot_detector._writer:write_multiple
      title: Save multi-layer data with Spot Detector
    - id: napari-senoquant-spot-detector.write_single_image
      python_name: napari_spot_detector._writer:write_single_image
      title: Save image data with Spot Detector
    - id: napari-senoquant-spot-detector.make_sample_data
      python_name: napari_spot_detector._sample_data:make_sample_data
      title: Load sample data from Spot Detector
    - id: napari-senoquant-spot-detector.make_qwidget
      python_name: napari_spot_detector.SenoquantSpotDetectorTabWidget:SenoquantSpotDetectorTabWidget
      title: Napari Senoquant Spot Detector
  readers:
    - command: napari-senoquant-spot-detector.get_reader
      accepts_directories: false
      filename_patterns: ['*.npy']
  writers:
    - command: napari-senoquant-spot-detector.write_multiple
      layer_types: ['image*','labels*']
      filename_extensions: []
    - command: napari-senoquant-spot-detector.write_single_image
      layer_types: ['image']
      filename_extensions: ['.npy']
  sample_data:
    - command: napari-senoquant-spot-detector.make_sample_data
      display_name: Spot Detector
      key: unique_id.1
  widgets:
    - command: napari-senoquant-spot-detector.make_qwidget
      display_name: Napari Senoquant Spot Detector