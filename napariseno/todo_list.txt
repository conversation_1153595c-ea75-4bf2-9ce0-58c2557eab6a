combine plugin
    * Database AnalysisID
    * Fix UI to extend Process/Database section
    * Help add xlsx save data to senoquant
    * UI Tab split

threading code cc3d and opencv
cropping data instead of zeros
batching
recording profiles and reinserted it look at analyze look at xml

Chichi in the spot detector: is this a typo ?  Detect bright spots over blackground, should it be background instead?

Database AnalysisID
remove c dependency
connected components analysis java convert to c or python


Today
* Database AnalysisID
* cropping
* loading old parameters
* fix_naming


fix UI with tabs from word document
looking this https://pypi.org/project/napari-segment-blobs-and-things-with-membranes/

help jon with label text on dots

prcessing notification wheel

full integrate spot detector and
reload parameters for everything
UI fixing

UI diagram - fast and favrious

senoquant the main name

rewrite connected componets in 1D array numba

fix cropping issues
performance issue on spot ray
fix other bugs