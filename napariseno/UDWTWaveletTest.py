import napari
from aicsimageio import AICSImage

from src.napari_spot_detector.UDWTWaveletDetector import UDWTWaveletDetector
import numpy as np

if __name__ == "__main__":


    # img = AICSImage('X:\BiomarkerAnalysis\muscle\\76.15 TAF.lif"')
    # img = AICSImage("X:\\BiomarkerAnalysis\\muscle\\132.15 TAF.lif")
    # img.set_scene(0)
    # layer = viewer.add_image(img.get_image_data("CZYX"), channel_axis=0,
    #                            name=["nuclei", "foci", "telomeres", "other"])
    # telomeres = img.get_image_data("CZYX")[2]
    # telomeres = np.amax(telomeres, axis=0, keepdims=True)
    # print(telomeres.shape)
    # layer = viewer.add_image(telomeres)

    viewer = napari.Viewer()
    dWidget = UDWTWaveletDetector(viewer)
    viewer.window.add_dock_widget(dWidget)
    napari.run()
