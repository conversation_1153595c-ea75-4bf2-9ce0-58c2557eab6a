[metadata]
name = napari-senoquant-spot-detector
version = attr: setuptools_scm.get_version
description = A plugin to use senoquant (stardist models) spot detector within napari
long_description = file: README.md
long_description_content_type = text/markdown
license = GPL-3.0-only
license_files = LICENSE
classifiers =
    Development Status :: 2 - Pre-Alpha
    Framework :: napari
    Intended Audience :: Developers
    License :: OSI Approved :: GNU General Public License v3 (GPLv3)
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Scientific/Engineering :: Image Processing

author = <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
author_email = <EMAIL>, <EMAIL>, <EMAIL>

[options]
packages = find_namespace:
use_scm_version = True

install_requires =
    numpy
    magicgui
    qtpy
    napari-roi
    stardist
    aicsimageio
    napari-aicsimageio
    dicttoxml
    openpyxl
    SimpleITK
    connected-components-3d
    numba
    joblib
    xmltodict
    xarray
    pandas
    sqlalchemy
    napari-segment-blobs-and-things-with-membranes
    csbdeep
    superqt
    tensorflow
    readlif
    pyclesperanto-prototype==0.23.1
    pymysql

python_requires = >=3.9,<3.12
include_package_data = True
package_dir =
    =src
setup_requires =
    setuptools_scm

[options.packages.find]
where = src

[options.entry_points]
napari.manifest =
    napari-senoqant-spot-detector = napari_spot_detector:napari.yaml

[options.extras_require]
testing =
    tox
    pytest  # https://docs.pytest.org/en/latest/contents.html
    pytest-cov  # https://pytest-cov.readthedocs.io/en/latest/
    pytest-qt  # https://pytest-qt.readthedocs.io/en/latest/
    napari
    pyqt5

[options.package_data]
* = *.yaml
napari_spot_detector.plugins = *.png
UDWT.Library = *.dll
models = *.json, *.h5
