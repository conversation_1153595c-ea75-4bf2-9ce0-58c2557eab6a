# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so
!src/napari_spot_detector/UDWT/Library/libB3SplineUDWT.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

!src/napari_spot_detector/UDWT/Library/build/libB3SplineUDWT.o

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/
.napari_cache

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask instance folder
instance/

# Sphinx documentation
docs/_build/

# MkDocs documentation
/site/

# PyBuilder
target/

# Pycharm and VSCode
.idea/
venv/
.vscode/

# IPython Notebook
.ipynb_checkpoints

# pyenv
.python-version

# OS
.DS_Store

# written by setuptools_scm
**/_version.py

# github
ex.github-old

*/*.xls
*.xls
src/napari_spot_detector/version.py
src/napari_spot_detector/version.py.bak
src/napari_spot_detector/*version.py*

*.ipynb