#conda env create -f stargpu-v2_1.yaml
#conda activate starGPUv2_1
git clone --branch revise_ui_combine_plugins http://birrepos.mayo.edu/M266467/napari-spot-detector.git
cd napari-spot-detector/src/napari_spot_detector/extensions
mkdir napari_senoquant
git clone --branch DEV_Integrated_UI http://birrepos.mayo.edu/BioMarkerAnalysis/napari-senoquant.git napari_senoquant
cd ../../../
pip install -e .
napari
