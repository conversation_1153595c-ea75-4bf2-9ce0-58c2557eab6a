"""
Optimized functions for the notebook to speed up non-rigid registration.
"""

import ants
import cupy as cp
import numpy as np
from skimage.transform import resize

def ants_from_cp(img: cp.ndarray) -> ants.ANTsImage:
    """Convert CuPy array to ANTs image."""
    return ants.from_numpy(cp.asnumpy(img))

def ants_to_cp(img: ants.ANTsImage) -> cp.ndarray:
    """Convert ANTs image to CuPy array."""
    return cp.asarray(img.numpy())

def normalize_image(img: cp.ndarray) -> cp.ndarray:
    """Normalize image to [0,1] range."""
    img_min = img.min()
    img_max = img.max()
    return (img - img_min) / (img_max - img_min + 1e-12)

def optimized_non_rigid_registration(fixed, moving):
    """
    Perform non-rigid registration using SyN with optimizations for speed.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        
    Returns:
        Tuple of (warped image, transform paths)
    """
    # Free memory before starting
    cp.get_default_memory_pool().free_all_blocks()
    
    # Convert to numpy arrays if they're cupy arrays
    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed
    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving
    
    # Downsample for faster processing (25% of original size)
    downsample_factor = 0.25
    new_shape = (int(fixed_np.shape[0] * downsample_factor), 
                 int(fixed_np.shape[1] * downsample_factor))
    
    # Resize images
    fixed_small = resize(fixed_np, new_shape, preserve_range=True, anti_aliasing=True)
    moving_small = resize(moving_np, new_shape, preserve_range=True, anti_aliasing=True)
    
    # Convert to ANTs format
    fixed_ants = ants.from_numpy(fixed_small)
    moving_ants = ants.from_numpy(moving_small)
    
    print(f"Downsampled images from {fixed_np.shape} to {new_shape} for faster registration")
    
    # Optimized SyN parameters for faster convergence
    reg = ants.registration(
        fixed=fixed_ants, 
        moving=moving_ants, 
        type_of_transform="SyN",
        grad_step=0.2,  # Increased for faster convergence
        flow_sigma=3,
        total_sigma=0,
        syn_metric='CC',
        syn_sampling=64,  # Increased for faster processing
        reg_iterations=(50,30,10)  # Reduced iterations
    )
    
    non_rigid_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Non-rigid registration completed. Transform paths: {non_rigid_transform_paths}")
    
    # Free memory after processing
    cp.get_default_memory_pool().free_all_blocks()
    
    return cp.asarray(reg['warpedmovout'].numpy()), non_rigid_transform_paths

def optimized_micro_registration(fixed, moving, patch_size=64, overlap=32):
    """
    Perform micro-registration using small patches with optimizations for speed.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        patch_size: Size of patches for registration
        overlap: Overlap between patches
        
    Returns:
        Tuple of (warped image, transform paths)
    """
    # Free memory before starting
    cp.get_default_memory_pool().free_all_blocks()
    
    # Convert to numpy arrays if they're cupy arrays
    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed
    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving
    
    # Downsample for faster processing (50% of original size)
    downsample_factor = 0.5
    new_shape = (int(fixed_np.shape[0] * downsample_factor), 
                 int(fixed_np.shape[1] * downsample_factor))
    
    # Resize images
    fixed_small = resize(fixed_np, new_shape, preserve_range=True, anti_aliasing=True)
    moving_small = resize(moving_np, new_shape, preserve_range=True, anti_aliasing=True)
    
    # Convert to ANTs format
    fixed_ants = ants.from_numpy(fixed_small)
    moving_ants = ants.from_numpy(moving_small)
    
    print(f"Downsampled images from {fixed_np.shape} to {new_shape} for faster micro-registration")
    
    # Optimized parameters for faster micro-registration
    reg = ants.registration(
        fixed_ants, 
        moving_ants, 
        type_of_transform="SyN", 
        reg_iterations=(10, 5, 0),  # Reduced iterations
        syn_metric='CC', 
        syn_sampling=4,  # Increased for faster processing
        syn_radius=4, 
        syn_patch_size=patch_size, 
        syn_overlap=overlap
    )
    
    micro_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Micro-registration completed. Transform paths: {micro_transform_paths}")
    
    # Free memory after processing
    cp.get_default_memory_pool().free_all_blocks()
    
    return cp.asarray(reg['warpedmovout'].numpy()), micro_transform_paths
