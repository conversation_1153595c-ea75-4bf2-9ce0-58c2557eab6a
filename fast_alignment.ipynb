{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fast Image Alignment\n", "\n", "This notebook uses optimized registration functions to speed up the alignment process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')\n", "\n", "# Import required libraries\n", "import os\n", "import re\n", "from pathlib import Path\n", "import time\n", "\n", "import cupy as cp\n", "import numpy as np\n", "import tifffile\n", "import ants\n", "import matplotlib.pyplot as plt\n", "from skimage.registration import phase_cross_correlation\n", "from tqdm import tqdm\n", "\n", "# Import our optimized functions\n", "from optimized_notebook_functions import (\n", "    normalize_image,\n", "    optimized_non_rigid_registration,\n", "    optimized_micro_registration,\n", "    ants_from_cp,\n", "    ants_to_cp\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths\n", "PADDED_DIR = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/\"\n", "REFERENCE_IMG = (\n", "    \"/mnt/d/Users/<USER>/antho 4i alignment/\"\n", "    \"lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\"\n", ")\n", "OUTPUT_DIR = os.path.join(PADDED_DIR, \"aligned_images_fast\")\n", "\n", "# Create output directory if it doesn't exist\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pad_to_largest(images):\n", "    max_h = max(img.shape[0] for img in images)\n", "    max_w = max(img.shape[1] for img in images)\n", "    padded = []\n", "    for img in images:\n", "        pad_h = max_h - img.shape[0]\n", "        pad_w = max_w - img.shape[1]\n", "        pad_top = pad_h // 2\n", "        pad_bottom = pad_h - pad_top\n", "        pad_left = pad_w // 2\n", "        pad_right = pad_w - pad_left\n", "        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),\n", "                            mode='constant', constant_values=0)\n", "        padded.append(img_padded)\n", "    return padded\n", "\n", "def get_basename_key(filename):\n", "    match = re.match(r\"(.+)_ch\\d{2}\\.tif$\", filename)\n", "    if match:\n", "        return match.group(1)\n", "    raise ValueError(f\"Filename {filename} does not match expected pattern *_chXX.tif\")\n", "\n", "def group_images_by_basename(folder):\n", "    files = [f for f in os.listdir(folder) if f.endswith('.tif')]\n", "    groups = {}\n", "    for f in files:\n", "        try:\n", "            base = get_basename_key(f)\n", "            ch_match = re.search(r\"_ch(\\d{2})\\.tif$\", f)\n", "            if not ch_match:\n", "                continue\n", "            ch = f\"ch{ch_match.group(1)}\"\n", "            groups.setdefault(base, {})[ch] = os.path.join(folder, f)\n", "        except ValueError:\n", "            continue\n", "    return groups\n", "\n", "def load_channels(paths):\n", "    channels = {}\n", "    for ch, p in paths.items():\n", "        img = tifffile.imread(p)\n", "        if img.ndim > 2:\n", "            img = img[0]\n", "        channels[ch] = cp.asarray(img)\n", "    return channels\n", "\n", "def save_image(img, output_dir, filename):\n", "    out_path = os.path.join(output_dir, filename)\n", "    np_img = cp.asnumpy(img)\n", "    tifffile.imwrite(out_path, np_img.astype(np.float32))\n", "    print(f\"Saved image: {out_path}\")\n", "\n", "def cupy_fourier_shift(img, shift):\n", "    # Free memory explicitly\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    \n", "    # Use spatial domain shift instead of Fourier for large images\n", "    shift_y, shift_x = shift\n", "    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)\n", "\n", "def apply_transforms_to_channel(channel, fixed, transformlist, interpolator='bSpline'):\n", "    # Free memory before starting\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    \n", "    fixed_ants = ants_from_cp(fixed)\n", "    moving_ants = ants_from_cp(channel)\n", "    \n", "    warped = ants.apply_transforms(\n", "        fixed=fixed_ants,\n", "        moving=moving_ants,\n", "        transformlist=transformlist,\n", "        interpolator=interpolator\n", "    )\n", "    \n", "    # Free memory after processing\n", "    cp.get_default_memory_pool().free_all_blocks()\n", "    \n", "    return ants_to_cp(warped)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fast_batch_align_to_reference(\n", "    reference_img_path,\n", "    input_folder,\n", "    output_folder,\n", "    affine_transform_type=\"Affine\",\n", "    use_non_rigid=True,\n", "    use_micro_registration=False,  # Disable micro-registration by default for speed\n", "    save_intermediate=False\n", "):\n", "    start_time = time.time()\n", "    print(f\"[INFO] Loading reference ch00 image from {reference_img_path}...\")\n", "    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))\n", "\n", "    groups = group_images_by_basename(input_folder)\n", "    print(f\"[INFO] Found {len(groups)} groups to align.\")\n", "\n", "    Path(output_folder).mkdir(parents=True, exist_ok=True)\n", "\n", "    for base, channel_paths in tqdm(groups.items(), desc=\"Aligning groups\"):\n", "        if 'ch00' not in channel_paths:\n", "            print(f\"[WARNING] Skipping '{base}': no ch00 channel found.\")\n", "            continue\n", "\n", "        print(f\"\\n[INFO] Processing group: {base}\")\n", "        group_start_time = time.time()\n", "        \n", "        channels = load_channels(channel_paths)\n", "        moving_ch00 = cp.asarray(channels.get('ch00'))\n", "        if moving_ch00 is None:\n", "            print(f\"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.\")\n", "            continue\n", "\n", "        # Pad all images to same size first\n", "        print(\" - Padding images...\")\n", "        all_imgs = [fixed_ch00] + list(channels.values())\n", "        all_padded = pad_to_largest(all_imgs)\n", "        fixed_ch00_padded = all_padded[0]\n", "        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))\n", "\n", "        # Phase correlation shift estimation\n", "        print(\" - Estimating initial shift using phase correlation...\")\n", "        shift, _, _ = phase_cross_correlation(\n", "            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=10\n", "        )\n", "\n", "        # Apply shift to all channels\n", "        for ch_name in channels_padded:\n", "            current_img = channels_padded[ch_name]\n", "            # Use direct spatial shift instead of Fourier transform\n", "            shifted = cupy_fourier_shift(current_img, shift)\n", "            channels_padded[ch_name] = shifted\n", "            # Free memory after each iteration\n", "            cp.get_default_memory_pool().free_all_blocks()\n", "\n", "        best_img = None\n", "        best_transforms = []\n", "\n", "        # Affine registration\n", "        print(\" - Running affine registration...\")\n", "        fixed_ants = ants_from_cp(normalize_image(fixed_ch00_padded))\n", "        moving_ants = ants_from_cp(normalize_image(channels_padded['ch00']))\n", "        \n", "        reg = ants.registration(\n", "            fixed=fixed_ants,\n", "            moving=moving_ants,\n", "            type_of_transform=affine_transform_type\n", "        )\n", "        \n", "        affine_transforms = reg.get('fwdtransforms', [])\n", "        if isinstance(affine_transforms, str):\n", "            affine_transforms = [affine_transforms]\n", "            \n", "        affine_img = ants_to_cp(reg['warpedmovout'])\n", "        print(f\"   >> Affine registration completed\")\n", "        \n", "        best_img = affine_img\n", "        best_transforms = affine_transforms\n", "\n", "        # Non-rigid registration (optimized)\n", "        if use_non_rigid:\n", "            print(\" - Running optimized non-rigid registration...\")\n", "            nonrigid_img, nonrigid_transforms = optimized_non_rigid_registration(\n", "                fixed_ch00_padded,\n", "                best_img\n", "            )\n", "            best_img = nonrigid_img\n", "            best_transforms += nonrigid_transforms\n", "\n", "        # Micro-registration (optional, optimized)\n", "        if use_micro_registration:\n", "            print(\" - Running optimized micro-registration...\")\n", "            micro_img, micro_transforms = optimized_micro_registration(\n", "                fixed_ch00_padded,\n", "                best_img\n", "            )\n", "            best_img = micro_img\n", "            best_transforms += micro_transforms\n", "\n", "        # Apply final transforms to all padded channels\n", "        for ch_name, img in channels_padded.items():\n", "            if ch_name == 'ch00':\n", "                warped = best_img\n", "            else:\n", "                warped = apply_transforms_to_channel(\n", "                    img, fixed_ch00_padded, best_transforms, interpolator='bSpline'\n", "                )\n", "            save_image(warped, output_folder, f\"{base}_{ch_name}.tif\")\n", "\n", "        if save_intermediate:\n", "            np.save(os.path.join(output_folder, f\"{base}_transform_list.npy\"), best_transforms)\n", "            \n", "        group_end_time = time.time()\n", "        print(f\"[INFO] Group {base} processed in {group_end_time - group_start_time:.2f} seconds\")\n", "\n", "    end_time = time.time()\n", "    print(f\"[INFO] Total processing time: {end_time - start_time:.2f} seconds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the optimized batch alignment\n", "fast_batch_align_to_reference(\n", "    REFERENCE_IMG,\n", "    PADDED_DIR,\n", "    OUTPUT_DIR,\n", "    affine_transform_type=\"Affine\",\n", "    use_non_rigid=True,\n", "    use_micro_registration=False,  # Disable micro-registration for speed\n", "    save_intermediate=False\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}