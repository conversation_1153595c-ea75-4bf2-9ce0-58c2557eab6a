import os
import re
import gc
import logging
import numpy as np
from tqdm import tqdm
from tifffile import imread, imwrite
from valis import registration
import matplotlib.pyplot as plt
from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration

# ----------------------------------
# Configuration
# ----------------------------------
MAIN_DIR = "/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/"
OUTPUT_BASE = "/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/aligned/"
REFERENCE_IMG = (
    "/mnt/d/Users/<USER>/antho 4i alignment/"
    "LB03/region R2/LB03 BP lung 4i aSMA green CD31 red 15apr25.lif - R 2_Merged_ch00.tif"
)
micro_reg_fraction = 0.3

WARPED_DIR = os.path.join(OUTPUT_BASE, "warped")
MULTICHANNEL_DIR = os.path.join(OUTPUT_BASE, "multichannel")
INDIVIDUAL_DIR = os.path.join(MULTICHANNEL_DIR, "individual")
PADDED_DIR = os.path.join(OUTPUT_BASE, "padded")

for d in (WARPED_DIR, MULTICHANNEL_DIR, INDIVIDUAL_DIR, PADDED_DIR):
    os.makedirs(d, exist_ok=True)

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

FILENAME_PATTERN = re.compile(r"(?P<base>.+?)_Merged_ch(?P<ch>\d{2})\.tif{1,2}$", re.IGNORECASE)

# ----------------------------------
# Optimized Registration Parameters
# ----------------------------------
# These parameters are optimized for better alignment quality
FEATURE_DETECTION_PARAMS = {
    'max_features': 5000,  # Increase number of features detected
    'feature_params': {
        'maxCorners': 5000,
        'qualityLevel': 0.01,  # Lower quality threshold to detect more features
        'minDistance': 7,      # Minimum distance between features
        'blockSize': 7
    }
}

RIGID_REGISTRATION_PARAMS = {
    'feature_retention_ratio': 0.9,  # Keep more features
    'max_features': 5000,
    'match_filter_method': 'ratio_test',
    'match_ratio_threshold': 0.75,   # More permissive matching
    'ransac_reproj_threshold': 5.0,  # More permissive RANSAC
    'rigid_registration_method': 'affine'  # Use affine instead of rigid
}

NON_RIGID_REGISTRATION_PARAMS = {
    'mesh_size': 30,           # Smaller mesh for finer control
    'regularization_weight': 0.1,  # Lower regularization for more flexibility
    'pyramid_levels': 5,       # More pyramid levels for multi-scale alignment
    'epsilon': 1e-7,
    'max_iterations': 1000     # More iterations for convergence
}

MICRO_REGISTRATION_PARAMS = {
    'mesh_size': 20,           # Even smaller mesh for micro-registration
    'regularization_weight': 0.05,
    'pyramid_levels': 6,
    'epsilon': 1e-8,
    'max_iterations': 1500
}

MICRO_REGISTRATION_ROI_PARAMS = {
    'roi_size': [600, 600],    # Larger ROI size
    'num_rois': 8,             # More ROIs for better coverage
    'roi_overlap_ratio': 0.2   # Allow some overlap between ROIs
}

ERROR_METRIC_PARAMS = {
    'metric': 'mutual_information',  # Better metric for multi-modal images
    'metric_weights': None
}

# ----------------------------------
# Utility Functions
# ----------------------------------
def display_images(orig, warped, title, show=False):
    if not show:
        return
    plt.figure(figsize=(12, 6))
    for i, (img, label) in enumerate(zip([orig, warped], ["Original", "Warped"])):
        plt.subplot(1, 2, i + 1)
        plt.imshow(img, cmap='gray')
        plt.title(f"{label} {title}")
        plt.axis('off')
    plt.tight_layout()
    plt.show()

def pad_images_to_largest(files, out_dir):
    logging.info("Padding images to the largest size...")
    sizes = [imread(f).shape[:2] for f in files]
    max_height, max_width = map(max, zip(*sizes))

    for fpath in files:
        img = imread(fpath)
        h, w = img.shape[:2]
        pad_h = (max_height - h) // 2, (max_height - h + 1) // 2
        pad_w = (max_width - w) // 2, (max_width - w + 1) // 2
        padded = np.pad(img, (pad_h, pad_w), mode='constant', constant_values=0)
        imwrite(os.path.join(out_dir, os.path.basename(fpath)), padded)
    return out_dir

def visualize_alignment(img1, img2, title="Alignment Check", save_path=None):
    """Visualize alignment between two images"""
    # Create a composite image (red-green)
    composite = np.zeros((img1.shape[0], img1.shape[1], 3), dtype=np.float32)
    composite[:,:,0] = img1 / np.max(img1)  # Red channel
    composite[:,:,1] = img2 / np.max(img2)  # Green channel
    
    plt.figure(figsize=(12, 10))
    plt.imshow(composite)
    plt.title(title)
    plt.axis('off')
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path)
    plt.show()

# ----------------------------------
# Processing Functions
# ----------------------------------
def process_folder(path, display=False):
    if not os.path.exists(REFERENCE_IMG):
        logging.error(f"Reference slide not found: {REFERENCE_IMG}")
        return

    logging.info(f"Processing folder: {path}")
    all_files = sorted(
        os.path.join(path, f)
        for f in os.listdir(path)
        if f.lower().endswith((".tif", ".tiff"))
    )

    dapi_files = [f for f in all_files if f.endswith("_Merged_ch00.tif")]
    if not dapi_files:
        logging.error("No DAPI (ch00) files found.")
        return

    padded_dir = pad_images_to_largest(all_files, PADDED_DIR)
    padded_files = sorted(
        os.path.join(padded_dir, f)
        for f in os.listdir(padded_dir)
        if f.lower().endswith((".tif", ".tiff"))
    )

    # After padding, use the padded DAPI image as the reference
    padded_dapi_files = [f for f in padded_files if f.endswith("_Merged_ch00.tif")]
    if not padded_dapi_files:
        logging.error("No padded DAPI (ch00) files found.")
        return

    # Use the padded DAPI image as the reference for the registration
    padded_reference_img = padded_dapi_files[0]

    # Initialize Valis registration with the padded images and optimized parameters
    registrar = registration.Valis(
        padded_dir,
        OUTPUT_BASE,
        img_list=padded_dapi_files,
        reference_img_f=padded_reference_img,  # use the padded DAPI reference image
        micro_rigid_registrar_cls=MicroRigidRegistrar,
        align_to_reference=True,
        # Add optimized parameters
        feature_detection_params=FEATURE_DETECTION_PARAMS,
        rigid_registration_params=RIGID_REGISTRATION_PARAMS,
        non_rigid_registration_params=NON_RIGID_REGISTRATION_PARAMS,
        micro_registration_params=MICRO_REGISTRATION_PARAMS,
        micro_registration_roi_params=MICRO_REGISTRATION_ROI_PARAMS,
        error_metric_params=ERROR_METRIC_PARAMS
    )
    print("Performing registration...")
    rigid_registrar, non_rigid_registrar, error_df = registrar.register()

    reference_slide = registrar.get_ref_slide()
    micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)
    print(f"Performing micro-registration with size: {micro_reg_size}...")
    micro_reg, micro_error = registrar.register_micro(
        max_non_rigid_registration_dim_px=micro_reg_size,
        align_to_reference=True
    )
    # Process and warp images
    print(f"Total padded files: {len(padded_files)}")
    for f in padded_files:
        print(os.path.basename(f))
    
    for fpath in tqdm(padded_files, desc="Warping images"):
        fname = os.path.basename(fpath)
        match = FILENAME_PATTERN.match(fname)
        if not match:
            print(f"SKIPPED (pattern): {fname}")
            continue

        base, ch = match.group("base"), int(match.group("ch"))
        slide_id = f"{base}_Merged_ch00"
        slide = registrar.get_slide(slide_id)
        if slide is None:
            print(f"SKIPPED (no slide): {fname}")
            continue

        print(f"Processing: {fname}")
    
        img = imread(fpath)
        warped = slide.warp_img(img)
        warped = warped.squeeze() if warped.ndim == 3 and warped.shape[2] == 1 else warped

        display_images(img, warped, f"{base}_ch{ch:02d}", display)

        imwrite(os.path.join(WARPED_DIR, fname), warped)
        
        # Save alignment visualization for each image
        if ch == 0:  # If this is a DAPI channel
            reference_img = imread(padded_reference_img)
            alignment_path = os.path.join(OUTPUT_BASE, f"alignment_check_{base}.png")
            visualize_alignment(reference_img, warped, f"Alignment Check: {base}", alignment_path)

    gc.collect()

def combine_all_channels():
    logging.info("Combining all warped images into a single multi-channel TIFF...")

    warped_files = sorted(
        os.path.join(WARPED_DIR, f)
        for f in os.listdir(WARPED_DIR)
        if f.lower().endswith((".tif", ".tiff"))
    )

    images = []
    channel_names = []
    for f in warped_files:
        img = imread(f)
        if img.ndim != 2:
            logging.error(f"Non-2D image: {f}")
            continue
        images.append(img)
        channel_names.append(os.path.basename(f))

    if not images:
        logging.warning("No images found to combine.")
        return

    combined = np.stack(images, axis=-1)
    out_path = os.path.join(MULTICHANNEL_DIR, "all_combined.ome.tif")
    imwrite(out_path, combined, planarconfig="contig")
    logging.info(f"Saved all channels: {out_path} | shape: {combined.shape} | channels: {channel_names}")

def check_alignment_quality():
    """Check alignment quality between pairs of images"""
    warped_files = sorted(
        os.path.join(WARPED_DIR, f)
        for f in os.listdir(WARPED_DIR)
        if f.lower().endswith(("_Merged_ch00.tif"))
    )
    
    if len(warped_files) < 2:
        logging.warning("Not enough images to check alignment quality")
        return
    
    reference_img = imread(warped_files[0])
    
    for i in range(1, len(warped_files)):
        img = imread(warped_files[i])
        base_name = os.path.basename(warped_files[i]).split('_Merged_ch00')[0]
        save_path = os.path.join(OUTPUT_BASE, f"alignment_check_0_{i}.png")
        visualize_alignment(reference_img, img, f"Alignment between reference and {base_name}", save_path)

def register_with_manual_points(control_points=None):
    """Register images with manual control points for problematic regions"""
    if not control_points or len(control_points) == 0:
        logging.warning("No manual control points provided. Add points to use this function.")
        return
    
    padded_dir = PADDED_DIR
    padded_files = sorted(
        os.path.join(padded_dir, f)
        for f in os.listdir(padded_dir)
        if f.lower().endswith((".tif", ".tiff"))
    )
    
    padded_dapi_files = [f for f in padded_files if f.endswith("_Merged_ch00.tif")]
    if not padded_dapi_files:
        logging.error("No padded DAPI (ch00) files found.")
        return
    
    padded_reference_img = padded_dapi_files[0]
    
    # Initialize Valis registration with manual control points
    registrar = registration.Valis(
        padded_dir,
        os.path.join(OUTPUT_BASE, "manual_points"),
        img_list=padded_dapi_files,
        reference_img_f=padded_reference_img,
        micro_rigid_registrar_cls=MicroRigidRegistrar,
        align_to_reference=True,
        feature_detection_params=FEATURE_DETECTION_PARAMS,
        rigid_registration_params=RIGID_REGISTRATION_PARAMS,
        non_rigid_registration_params=NON_RIGID_REGISTRATION_PARAMS,
        micro_registration_params=MICRO_REGISTRATION_PARAMS,
        micro_registration_roi_params=MICRO_REGISTRATION_ROI_PARAMS,
        error_metric_params=ERROR_METRIC_PARAMS,
        manual_control_points=control_points
    )
    
    print("Performing registration with manual control points...")
    registrar.register()
    
    reference_slide = registrar.get_ref_slide()
    micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)
    registrar.register_micro(
        max_non_rigid_registration_dim_px=micro_reg_size,
        align_to_reference=True
    )
    
    # Process and warp images
    for fpath in tqdm(padded_files, desc="Warping images with manual points"):
        fname = os.path.basename(fpath)
        match = FILENAME_PATTERN.match(fname)
        if not match:
            continue

        base, ch = match.group("base"), int(match.group("ch"))
        slide_id = f"{base}_Merged_ch00"
        slide = registrar.get_slide(slide_id)
        if slide is None:
            continue

        img = imread(fpath)
        warped = slide.warp_img(img)
        warped = warped.squeeze() if warped.ndim == 3 and warped.shape[2] == 1 else warped

        manual_warped_dir = os.path.join(OUTPUT_BASE, "manual_points", "warped")
        os.makedirs(manual_warped_dir, exist_ok=True)
        imwrite(os.path.join(manual_warped_dir, fname), warped)

# ----------------------------------
# Main
# ----------------------------------
if __name__ == "__main__":
    process_folder(MAIN_DIR, display=True)
    combine_all_channels()
    check_alignment_quality()
    
    # Uncomment and add control points if needed
    # Example:
    # control_points = [
    #     [0, 500, 500, 1, 510, 505],  # Format: [image_idx1, x1, y1, image_idx2, x2, y2]
    #     [0, 1000, 1000, 1, 1020, 1010]
    # ]
    # register_with_manual_points(control_points)