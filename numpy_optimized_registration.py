"""
Optimized registration functions for faster image alignment using NumPy instead of CuPy.
This module provides optimized versions of registration functions
that can significantly speed up the alignment process.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union
import time

import numpy as np
import tifffile
import ants
import matplotlib.pyplot as plt
from skimage.transform import resize
from skimage.registration import phase_cross_correlation
from tqdm import tqdm

def normalize_image(img: np.ndarray) -> np.ndarray:
    """Normalize image to [0,1] range."""
    img_min = img.min()
    img_max = img.max()
    return (img - img_min) / (img_max - img_min + 1e-12)

def optimized_ants_registration(
    fixed: np.ndarray, 
    moving: np.ndarray,
    transform_type: str,
    initializer: Optional[ants.ANTsTransform] = None,
    interpolator: str = 'linear',
    downsample_factor: float = 0.5,  # Default to 50% downsampling for SyN
    memory_efficient: bool = True
) -> <PERSON><PERSON>[np.ndarray, List[str], float]:
    """
    Optimized ANTs registration function with downsampling for faster processing.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        transform_type: Type of transform ('Rigid', 'Affine', 'SyN', etc.)
        initializer: Optional initial transform
        interpolator: Interpolation method
        downsample_factor: Factor to downsample images (1.0 = no downsampling, 0.5 = half size)
        memory_efficient: Whether to free memory aggressively during processing
    
    Returns:
        Tuple of (warped image, transform paths, similarity metric)
    """
    # Normalize images
    fixed_norm = normalize_image(fixed)
    moving_norm = normalize_image(moving)
    
    # Downsample for faster processing if requested
    if downsample_factor < 1.0 and transform_type == "SyN":
        # Calculate new dimensions
        new_shape = (int(fixed_norm.shape[0] * downsample_factor), 
                     int(fixed_norm.shape[1] * downsample_factor))
        
        # Resize images
        fixed_small = resize(fixed_norm, new_shape, preserve_range=True, anti_aliasing=True)
        moving_small = resize(moving_norm, new_shape, preserve_range=True, anti_aliasing=True)
        
        # Convert to ANTs format
        fixed_ants = ants.from_numpy(fixed_small)
        moving_ants = ants.from_numpy(moving_small)
        
        print(f"Downsampled images from {fixed_norm.shape} to {new_shape} for faster registration")
    else:
        # Use original resolution
        fixed_ants = ants.from_numpy(fixed_norm)
        moving_ants = ants.from_numpy(moving_norm)
    
    # Optimize parameters based on transform type
    if transform_type == "SyN":
        # Optimized SyN parameters for faster convergence
        reg = ants.registration(
            fixed=fixed_ants, 
            moving=moving_ants, 
            type_of_transform=transform_type,
            grad_step=0.2,  # Increased for faster convergence
            flow_sigma=3,
            total_sigma=0,
            syn_metric='CC',
            syn_sampling=64,  # Increased for faster processing
            reg_iterations=(50,30,10)  # Reduced iterations
        )
    else:
        reg = ants.registration(
            fixed=fixed_ants,
            moving=moving_ants,
            type_of_transform=transform_type,
            initial_transform=initializer
        )
    
    # Get transforms
    fwd_transforms = reg.get('fwdtransforms', [])
    if isinstance(fwd_transforms, str):
        fwd_transforms = [fwd_transforms]

    # If we downsampled for registration but need to apply at full resolution
    if downsample_factor < 1.0 and transform_type == "SyN":
        # Apply transforms at original resolution
        fixed_ants_full = ants.from_numpy(fixed_norm)
        moving_ants_full = ants.from_numpy(moving_norm)
        
        warped = ants.apply_transforms(
            fixed=fixed_ants_full,
            moving=moving_ants_full,
            transformlist=fwd_transforms,
            interpolator=interpolator
        )
    else:
        # Apply at same resolution as registration
        warped = ants.apply_transforms(
            fixed=fixed_ants,
            moving=moving_ants,
            transformlist=fwd_transforms,
            interpolator=interpolator
        )
    
    # Calculate similarity
    similarity = ants.image_similarity(fixed_ants, warped, 'MattesMutualInformation')
    
    return warped.numpy(), fwd_transforms, similarity

def optimized_non_rigid_registration(fixed: np.ndarray, moving: np.ndarray, downsample_factor: float = 0.5):
    """
    Perform optimized non-rigid registration using SyN with downsampling.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        downsample_factor: Factor to downsample images for faster processing
        
    Returns:
        Tuple of (warped image, transform paths)
    """
    warped, transform_paths, _ = optimized_ants_registration(
        fixed, 
        moving, 
        transform_type="SyN",
        downsample_factor=downsample_factor
    )
    print(f"Non-rigid registration completed. Transform paths: {transform_paths}")
    return warped, transform_paths

def optimized_apply_transforms(
    channel: np.ndarray, 
    fixed: np.ndarray,
    transformlist: List[str], 
    interpolator: str = 'bSpline'
) -> np.ndarray:
    """
    Apply transforms with memory optimization.
    
    Args:
        channel: Image to transform
        fixed: Reference image
        transformlist: List of transform paths
        interpolator: Interpolation method
        
    Returns:
        Transformed image
    """
    fixed_ants = ants.from_numpy(fixed)
    moving_ants = ants.from_numpy(channel)
    
    warped = ants.apply_transforms(
        fixed=fixed_ants,
        moving=moving_ants,
        transformlist=transformlist,
        interpolator=interpolator
    )
    
    return warped.numpy()

def numpy_roll(img: np.ndarray, shift: tuple) -> np.ndarray:
    """
    Optimized spatial domain shift for large images.
    
    Args:
        img: Image to shift
        shift: (y, x) shift values
        
    Returns:
        Shifted image
    """
    # Use spatial domain shift instead of Fourier for large images
    shift_y, shift_x = shift
    return np.roll(np.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)
