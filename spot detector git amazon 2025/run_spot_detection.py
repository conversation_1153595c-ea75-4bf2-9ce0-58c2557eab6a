#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Comprehensive Spot Detection with Mixture of Experts

This script provides a complete solution for detecting spots of varying sizes and densities
in images using a Mixture of Experts approach. It combines all components into a single workflow.
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
from tqdm import tqdm
import cv2
from skimage import io, measure
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import random
import time
import json
import argparse

# Import our modules
from OptimizedSpotLoss import OptimizedSpotLoss
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from data_utils import SpotDataset, create_data_loaders
from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders
from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator
from metrics import SpotDetectionMetrics
from trainer import SpotDetectionTrainer
from predict import SpotDetectionPredictor

# Set random seed for reproducibility
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

# Default configurations
def get_default_configs():
    # Data configuration
    data_config = {
        'data_dir': "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/",
        'image_size': 256,
        'batch_size': 8,
        'use_synthetic': True,  # Set to False if you only want to use real data
        'synthetic_size': 500,  # Number of synthetic samples to generate
        'augmentation_level': 'strong',  # 'none', 'light', 'medium', or 'strong'
        'train_val_split': 0.8,  # 80% training, 20% validation
        'num_workers': 4,  # Number of workers for data loading
        'is_3d': False,  # Set to True for 3D data
        'confidence_threshold': 0.9,  # Threshold for high-confidence predictions
        'ignore_threshold': 0.3  # Threshold below which to ignore predictions
    }

    # Model configuration
    model_config = {
        'in_channels': 1,
        'num_experts': 3,
        'base_filters': 64,
        'dropout_rate': 0.2,
        'is_3d': data_config['is_3d']
    }

    # Loss function configuration
    loss_config = {
        'bce_weight': 1.0,
        'dice_weight': 1.0,
        'focal_weight': 0.5,
        'focal_gamma': 2.0,
        'size_adaptive': True,
        'density_aware': True,
        'confidence_weighted': True
    }

    # Training configuration
    train_config = {
        'learning_rate': 0.001,
        'num_epochs': 30,
        'early_stopping_patience': 10,
        'save_best_model': True,
        'model_save_path': 'best_spot_detection_model.pth',
        'gradient_clipping': 1.0,  # Set to None to disable gradient clipping
        'use_mixed_precision': True  # Use mixed precision training if available
    }

    # Prediction configuration
    pred_config = {
        'threshold': 0.5,
        'min_spot_size': 3,
        'use_test_time_augmentation': False,  # Use test-time augmentation for better results
        'tta_flips': True,  # Use flips for test-time augmentation
        'tta_rotations': True  # Use rotations for test-time augmentation
    }

    # Semi-supervised learning configuration
    ssl_config = {
        'enabled': True,
        'num_iterations': 3,
        'confidence_threshold': 0.9,
        'epochs_per_iteration': 10
    }

    # Synthetic data configuration
    synthetic_config = {
        'min_spots': 5,
        'max_spots': 50,
        'min_radius': 2,
        'max_radius': 10,
        'allow_overlapping': True,
        'shape_variation': 0.3,
        'add_gradients': True,
        'realistic_noise': True
    }
    
    return {
        'data': data_config,
        'model': model_config,
        'loss': loss_config,
        'train': train_config,
        'pred': pred_config,
        'ssl': ssl_config,
        'synthetic': synthetic_config
    }

def load_data(config):
    """Load data based on configuration"""
    print("Loading data...")
    
    # Check if data directory exists
    data_dir = config['data']['data_dir']
    if config['data']['use_synthetic'] or (data_dir and os.path.exists(data_dir)):
        # Create data loaders
        if config['ssl']['enabled']:
            train_loader, val_loader, train_dataset, val_dataset = create_sparse_data_loaders(
                data_dir=data_dir if data_dir and os.path.exists(data_dir) else None,
                batch_size=config['data']['batch_size'],
                image_size=(config['data']['image_size'], config['data']['image_size']),
                train_val_split=config['data']['train_val_split'],
                synthetic=config['data']['use_synthetic'],
                synthetic_size=config['data']['synthetic_size'],
                augmentation_level=config['data']['augmentation_level'],
                num_workers=config['data']['num_workers'],
                confidence_threshold=config['data']['confidence_threshold'],
                ignore_threshold=config['data']['ignore_threshold']
            )
        else:
            train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(
                data_dir=data_dir if data_dir and os.path.exists(data_dir) else None,
                batch_size=config['data']['batch_size'],
                image_size=(config['data']['image_size'], config['data']['image_size']),
                train_val_split=config['data']['train_val_split'],
                synthetic=config['data']['use_synthetic'],
                synthetic_size=config['data']['synthetic_size'],
                augmentation_level=config['data']['augmentation_level'],
                num_workers=config['data']['num_workers']
            )
        
        print(f"Training samples: {len(train_dataset)}")
        print(f"Validation samples: {len(val_dataset)}")
        
        return train_loader, val_loader, train_dataset, val_dataset
    else:
        print(f"Data directory {data_dir} not found and synthetic data is disabled.")
        print("Please provide a valid data directory or enable synthetic data generation.")
        sys.exit(1)

def create_model(config, device):
    """Create model based on configuration"""
    print("Creating model...")
    
    # Create model
    model = OptimizedSpotDetectionModel(
        in_channels=config['model']['in_channels'],
        num_experts=config['model']['num_experts'],
        base_filters=config['model']['base_filters'],
        dropout_rate=config['model']['dropout_rate']
    )
    
    # Move model to device
    model = model.to(device)
    
    # Create loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=config['loss']['bce_weight'],
        dice_weight=config['loss']['dice_weight'],
        focal_weight=config['loss']['focal_weight'],
        focal_gamma=config['loss']['focal_gamma'],
        size_adaptive=config['loss']['size_adaptive'],
        density_aware=config['loss']['density_aware'],
        confidence_weighted=config['loss']['confidence_weighted']
    )
    
    return model, loss_fn

def train_model(model, loss_fn, train_loader, val_loader, config, device):
    """Train model based on configuration"""
    print("Training model...")
    
    # Create optimizer
    optimizer = optim.Adam(model.parameters(), lr=config['train']['learning_rate'])
    
    # Create learning rate scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    # Create metrics calculator
    metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
    
    # Create trainer
    trainer = SpotDetectionTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        metrics_calculator=metrics_calculator,
        scheduler=scheduler
    )
    
    # Train model
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['train']['num_epochs'],
        early_stopping_patience=config['train']['early_stopping_patience'],
        save_best_model=config['train']['save_best_model'],
        model_save_path=config['train']['model_save_path']
    )
    
    # Plot training history
    trainer.plot_history()
    
    return trainer, history

def semi_supervised_learning(model, train_dataset, val_dataset, config, device):
    """Perform semi-supervised learning"""
    print("Performing semi-supervised learning...")
    
    num_iterations = config['ssl']['num_iterations']
    
    for iteration in range(num_iterations):
        print(f"Semi-supervised learning iteration {iteration+1}/{num_iterations}")
        
        # Create predictor
        predictor = SpotDetectionPredictor(
            model=model,
            device=device,
            threshold=config['pred']['threshold'],
            min_spot_size=config['pred']['min_spot_size']
        )
        
        # Make predictions on training set
        train_loader = DataLoader(
            train_dataset,
            batch_size=config['data']['batch_size'],
            shuffle=False,
            num_workers=config['data']['num_workers']
        )
        
        # Collect predictions
        all_images = []
        all_preds = []
        
        model.eval()
        with torch.no_grad():
            for batch in tqdm(train_loader, desc="Making predictions"):
                images = batch['image'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Get main output
                if isinstance(outputs, dict):
                    pred = outputs.get('output', outputs.get('combined_output', None))
                else:
                    pred = outputs
                
                # Add to lists
                all_images.extend([img for img in batch['image']])
                all_preds.extend([p for p in pred])
        
        # Update dataset with high-confidence predictions
        train_dataset.update_with_predictions(
            all_images, 
            all_preds, 
            threshold=config['ssl']['confidence_threshold']
        )
        
        # Create new data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=config['data']['batch_size'],
            shuffle=True,
            num_workers=config['data']['num_workers'],
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=config['data']['batch_size'],
            shuffle=False,
            num_workers=config['data']['num_workers'],
            pin_memory=True
        )
        
        # Create optimizer
        optimizer = optim.Adam(model.parameters(), lr=config['train']['learning_rate'])
        
        # Create learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5, verbose=True
        )
        
        # Create metrics calculator
        metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
        
        # Create trainer
        trainer = SpotDetectionTrainer(
            model=model,
            loss_fn=OptimizedSpotLoss(
                bce_weight=config['loss']['bce_weight'],
                dice_weight=config['loss']['dice_weight'],
                focal_weight=config['loss']['focal_weight'],
                focal_gamma=config['loss']['focal_gamma'],
                size_adaptive=config['loss']['size_adaptive'],
                density_aware=config['loss']['density_aware'],
                confidence_weighted=config['loss']['confidence_weighted']
            ),
            optimizer=optimizer,
            device=device,
            metrics_calculator=metrics_calculator,
            scheduler=scheduler
        )
        
        # Train model for a few epochs
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=config['ssl']['epochs_per_iteration'],
            early_stopping_patience=5,
            save_best_model=True,
            model_save_path=f'semi_supervised_model_iter{iteration+1}.pth'
        )
        
        # Evaluate
        val_metrics = trainer.validate(val_loader)
        print(f"Iteration {iteration+1} Validation Metrics:")
        for k, v in val_metrics.items():
            print(f"{k}: {v:.4f}")
        print()
    
    return model

def evaluate_model(model, val_loader, device):
    """Evaluate model on validation set"""
    print("Evaluating model...")
    
    # Create metrics calculator
    metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
    
    # Create trainer for evaluation
    trainer = SpotDetectionTrainer(
        model=model,
        loss_fn=None,  # Not needed for evaluation
        optimizer=None,  # Not needed for evaluation
        device=device,
        metrics_calculator=metrics_calculator
    )
    
    # Evaluate on validation set
    val_metrics = trainer.validate(val_loader)
    
    # Print metrics
    print("Validation Metrics:")
    for k, v in val_metrics.items():
        print(f"{k}: {v:.4f}")
    
    return val_metrics

def visualize_predictions(model, val_dataset, config, device, num_samples=5):
    """Visualize predictions on validation samples"""
    print("Visualizing predictions...")
    
    # Create predictor
    predictor = SpotDetectionPredictor(
        model=model,
        device=device,
        threshold=config['pred']['threshold'],
        min_spot_size=config['pred']['min_spot_size']
    )
    
    # Get some validation samples
    val_samples = [val_dataset[i] for i in range(min(num_samples, len(val_dataset)))]
    
    # Make predictions and visualize
    for i, sample in enumerate(val_samples):
        image = sample['image']
        mask = sample['mask']
        
        # Make prediction
        result = predictor.predict(image, return_heatmap=True)
        
        # Visualize
        print(f"Sample {i+1} - Detected {result['num_spots']} spots")
        predictor.visualize(
            image=image,
            result=result,
            show_spots=True,
            show_heatmap=True,
            show_experts=True
        )
        
        # Print spot properties
        print("Spot Properties:")
        for j, prop in enumerate(result['spot_props'][:5]):  # Show first 5 spots
            print(f"Spot {prop['id']}: Area={prop['area']}, Mean Intensity={prop['mean_intensity']:.4f}")
        print()

def analyze_expert_specialization(model, val_loader, device, num_samples=5):
    """Analyze what each expert specializes in"""
    print("Analyzing expert specialization...")
    
    model.eval()
    samples = []
    
    with torch.no_grad():
        for batch in val_loader:
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            outputs = model(images)
            
            # Get outputs
            if isinstance(outputs, dict) and 'expert_outputs' in outputs:
                expert_outputs = outputs['expert_outputs']
                gating_weights = outputs['gating_weights']
                
                # Add to samples
                for i in range(len(images)):
                    samples.append({
                        'image': images[i].cpu(),
                        'mask': masks[i].cpu(),
                        'expert_outputs': [out[i].cpu() for out in expert_outputs],
                        'gating_weights': gating_weights[i].cpu()
                    })
                    
                    if len(samples) >= num_samples:
                        break
            
            if len(samples) >= num_samples:
                break
    
    # Visualize samples
    for i, sample in enumerate(samples):
        image = sample['image'].squeeze().numpy()
        mask = sample['mask'].squeeze().numpy()
        expert_outputs = [torch.sigmoid(out).squeeze().numpy() for out in sample['expert_outputs']]
        gating_weights = sample['gating_weights'].numpy()
        
        # Create figure
        fig, axes = plt.subplots(1, 3 + len(expert_outputs), figsize=(15, 5))
        
        # Plot image
        axes[0].imshow(image, cmap='gray')
        axes[0].set_title('Image')
        axes[0].axis('off')
        
        # Plot mask
        axes[1].imshow(mask, cmap='hot')
        axes[1].set_title('Ground Truth')
        axes[1].axis('off')
        
        # Plot combined output
        combined = np.zeros_like(expert_outputs[0])
        for j, output in enumerate(expert_outputs):
            combined += output * gating_weights[j]
        
        axes[2].imshow(combined, cmap='hot')
        axes[2].set_title('Combined Output')
        axes[2].axis('off')
        
        # Plot expert outputs
        for j, output in enumerate(expert_outputs):
            axes[3+j].imshow(output, cmap='hot')
            axes[3+j].set_title(f'Expert {j+1} (w={gating_weights[j]:.2f})')
            axes[3+j].axis('off')
        
        plt.tight_layout()
        plt.show()

def main():
    """Main function"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Spot Detection with Mixture of Experts')
    parser.add_argument('--data_dir', type=str, default=None, help='Path to data directory')
    parser.add_argument('--config', type=str, default=None, help='Path to configuration file')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'eval', 'predict', 'ssl', 'analyze'],
                        help='Mode to run in')
    parser.add_argument('--model_path', type=str, default=None, help='Path to pretrained model')
    parser.add_argument('--use_synthetic', action='store_true', help='Use synthetic data')
    parser.add_argument('--no_synthetic', dest='use_synthetic', action='store_false', help='Do not use synthetic data')
    parser.add_argument('--batch_size', type=int, default=None, help='Batch size')
    parser.add_argument('--num_epochs', type=int, default=None, help='Number of epochs')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    args = parser.parse_args()
    
    # Set random seed
    set_seed(args.seed)
    
    # Get default configurations
    config = get_default_configs()
    
    # Load configuration from file if provided
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            file_config = json.load(f)
            # Update config with file config
            for key, value in file_config.items():
                if key in config:
                    config[key].update(value)
    
    # Override config with command line arguments
    if args.data_dir:
        config['data']['data_dir'] = args.data_dir
    if args.use_synthetic is not None:
        config['data']['use_synthetic'] = args.use_synthetic
    if args.batch_size:
        config['data']['batch_size'] = args.batch_size
    if args.num_epochs:
        config['train']['num_epochs'] = args.num_epochs
    
    # Check if GPU is available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    train_loader, val_loader, train_dataset, val_dataset = load_data(config)
    
    # Create or load model
    if args.model_path and os.path.exists(args.model_path):
        print(f"Loading model from {args.model_path}")
        model, loss_fn = create_model(config, device)
        model.load_state_dict(torch.load(args.model_path, map_location=device))
    else:
        model, loss_fn = create_model(config, device)
    
    # Run in specified mode
    if args.mode == 'train':
        # Train model
        trainer, history = train_model(model, loss_fn, train_loader, val_loader, config, device)
        
        # Evaluate model
        evaluate_model(model, val_loader, device)
        
        # Visualize predictions
        visualize_predictions(model, val_dataset, config, device)
        
    elif args.mode == 'eval':
        # Evaluate model
        evaluate_model(model, val_loader, device)
        
    elif args.mode == 'predict':
        # Visualize predictions
        visualize_predictions(model, val_dataset, config, device)
        
    elif args.mode == 'ssl':
        # Perform semi-supervised learning
        if config['ssl']['enabled']:
            model = semi_supervised_learning(model, train_dataset, val_dataset, config, device)
            
            # Evaluate model
            evaluate_model(model, val_loader, device)
            
            # Visualize predictions
            visualize_predictions(model, val_dataset, config, device)
        else:
            print("Semi-supervised learning is disabled in the configuration.")
            
    elif args.mode == 'analyze':
        # Analyze expert specialization
        analyze_expert_specialization(model, val_loader, device)
    
    print("Done!")

if __name__ == "__main__":
    main()