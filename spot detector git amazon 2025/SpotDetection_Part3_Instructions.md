# Part 3: Training Instructions

## Overview

This section sets up the training pipeline with gradient clipping and semi-supervised learning. It includes optimizations for both 2D and 3D data and handles sparse annotations effectively.

## Training Configuration

### Basic Configuration

Adjust these parameters based on your dataset and available resources:

```python
# Basic training configuration
LEARNING_RATE = 1e-3
WEIGHT_DECAY = 1e-4
NUM_EPOCHS = 100
PATIENCE = 10
SAVE_FREQ = 10
SAVE_DIR = './checkpoints'
EXPERIMENT_NAME = 'spot_detection'
```

### 2D vs 3D Configuration

For 3D data, adjust these parameters:

```python
# For 3D data
LEARNING_RATE = 5e-4  # Lower learning rate for stability
WEIGHT_DECAY = 1e-4
NUM_EPOCHS = 50  # Fewer epochs due to longer iteration time
BATCH_SIZE = 2  # Smaller batch size for memory efficiency
```

### Sparse Annotation Configuration

For datasets with sparse annotations:

```python
# For sparse annotations
SEMI_SUPERVISED = True
CONFIDENCE_THRESHOLD = 0.9  # Threshold for high-confidence predictions
UPDATE_INTERVAL = 5  # Update dataset every 5 epochs
```

## Gradient Clipping

Gradient clipping prevents exploding gradients and stabilizes training:

```python
# Adjust gradient clipping strength
CLIP_GRAD_NORM = 1.0  # Default value
CLIP_GRAD_NORM = 0.5  # More aggressive clipping for unstable training
CLIP_GRAD_NORM = 2.0  # Less aggressive clipping for stable training
```

## Semi-supervised Learning

The semi-supervised learning approach:

1. **Initial Training**: Train on available annotations
2. **Prediction**: Generate predictions on training data
3. **Confidence Filtering**: Keep high-confidence predictions
4. **Dataset Update**: Add high-confidence predictions to training set
5. **Iterative Training**: Repeat the process

To adjust semi-supervised learning:

```python
# More conservative approach (higher confidence threshold)
trainer = SparseSpotTrainer(
    # ... other parameters ...
    semi_supervised=True,
    confidence_threshold=0.95,  # Very high confidence threshold
    update_interval=10  # Less frequent updates
)

# More aggressive approach (lower confidence threshold)
trainer = SparseSpotTrainer(
    # ... other parameters ...
    semi_supervised=True,
    confidence_threshold=0.8,  # Lower confidence threshold
    update_interval=3  # More frequent updates
)
```

## Training Strategies

### Standard Training

For well-annotated datasets:

```python
# Create trainer without semi-supervised learning
trainer = SparseSpotTrainer(
    model=model,
    loss_fn=loss_fn,
    train_loader=train_loader,
    val_loader=val_loader,
    optimizer=optimizer,
    scheduler=scheduler,
    device=device,
    save_dir=SAVE_DIR,
    experiment_name=EXPERIMENT_NAME,
    metrics_threshold=0.5,
    iou_threshold=0.5,
    clip_grad_norm=1.0,
    semi_supervised=False
)

# Train model
history = trainer.train(
    num_epochs=NUM_EPOCHS,
    patience=PATIENCE,
    save_freq=SAVE_FREQ,
    primary_metric='val_pixel_f1'
)
```

### Semi-supervised Training

For sparsely annotated datasets:

```python
# Create trainer with semi-supervised learning
trainer = SparseSpotTrainer(
    # ... other parameters ...
    semi_supervised=True,
    confidence_threshold=0.9,
    update_interval=5
)

# Train model
history = trainer.train(
    num_epochs=NUM_EPOCHS,
    patience=PATIENCE,
    save_freq=SAVE_FREQ,
    primary_metric='val_pixel_f1'
)
```

### 3D Training

For 3D datasets:

```python
# Create optimizer with gradient accumulation for 3D data
optimizer = optim.AdamW(
    list(model.parameters()) + list(loss_fn.parameters()),
    lr=LEARNING_RATE,
    weight_decay=WEIGHT_DECAY
)

# Create trainer with 3D-specific settings
trainer = SparseSpotTrainer(
    # ... other parameters ...
    clip_grad_norm=0.5,  # More aggressive clipping for 3D
    semi_supervised=False  # Start without semi-supervised learning for 3D
)

# Train model with fewer epochs
history = trainer.train(
    num_epochs=50,  # Fewer epochs for 3D
    patience=5,     # Less patience for 3D
    save_freq=5,    # More frequent saving for 3D
    primary_metric='val_pixel_f1'
)
```

## Memory Optimization

For large datasets or 3D volumes:

### Gradient Checkpointing

```python
# Enable gradient checkpointing in the model
model = EnhancedSpotDetectionModel(
    # ... other parameters ...
    use_gradient_checkpointing=True  # Add this parameter to the model
)
```

### Mixed Precision Training

```python
# Import libraries
from torch.cuda.amp import autocast, GradScaler

# Create scaler
scaler = GradScaler()

# In training loop
with autocast():
    # Forward pass
    outputs = model(inputs)
    
    # Compute loss
    loss, losses_dict = loss_fn(outputs, targets)

# Scale loss and backward
scaler.scale(loss).backward()

# Unscale gradients and clip
scaler.unscale_(optimizer)
torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad_norm)

# Update weights
scaler.step(optimizer)
scaler.update()
```

### Batch Size Adjustment

For 3D data or limited memory:

```python
# Reduce batch size
BATCH_SIZE = 2  # For 3D data
BATCH_SIZE = 1  # For very large 3D volumes

# Use gradient accumulation for effective larger batch size
ACCUMULATION_STEPS = 4  # Accumulate gradients over 4 iterations
```

## Monitoring Training

The trainer provides comprehensive monitoring:

1. **Loss Components**: Heatmap loss, dice loss, boundary loss, etc.
2. **Validation Metrics**: Pixel IoU, F1 score, instance metrics, etc.
3. **Learning Rate**: Current learning rate from scheduler
4. **Expert Usage**: Utilization of each expert

To visualize training progress:

```python
# Plot training curves
plot_training_curves(history)

# Save training curves
plt.savefig(os.path.join(SAVE_DIR, EXPERIMENT_NAME, 'training_curves.png'))
```

## Next Steps

After training the model, proceed to Part 4 for evaluation and prediction.