import matplotlib.pyplot as plt
import numpy as np
import torch

def visualize_samples(dataset, num_samples=5):
    """
    Visualize samples from the dataset before and after augmentation
    to check for orientation issues, especially with confidence maps.
    
    Args:
        dataset: Dataset to visualize samples from
        num_samples: Number of samples to visualize
    """
    # Get random indices
    indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    
    # Create figure
    fig, axes = plt.subplots(num_samples, 6, figsize=(18, 3*num_samples))
    
    # If only one sample, reshape axes
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    for i, idx in enumerate(indices):
        # Get sample
        sample = dataset[idx]
        
        # Get original data (before augmentation)
        if isinstance(dataset.images[idx], str):
            # Load from file path
            import cv2
            from skimage import io
            
            # Original image
            orig_img = io.imread(dataset.images[idx])
            if len(orig_img.shape) == 3 and orig_img.shape[2] > 1:
                orig_img = cv2.cvtColor(orig_img, cv2.COLOR_RGB2GRAY)
            orig_img = orig_img.astype(np.float32) / 255.0
            
            # Original mask
            orig_mask = io.imread(dataset.masks[idx])
            if len(orig_mask.shape) == 3 and orig_mask.shape[2] > 1:
                orig_mask = cv2.cvtColor(orig_mask, cv2.COLOR_RGB2GRAY)
            orig_mask = (orig_mask > 0).astype(np.float32)
            
            # Original confidence mask
            if isinstance(dataset.confidence_masks[idx], str):
                orig_conf = io.imread(dataset.confidence_masks[idx])
                if len(orig_conf.shape) == 3 and orig_conf.shape[2] > 1:
                    orig_conf = cv2.cvtColor(orig_conf, cv2.COLOR_RGB2GRAY)
                orig_conf = orig_conf.astype(np.float32) / 255.0
            else:
                orig_conf = dataset.confidence_masks[idx].copy()
        else:
            # Already in memory
            orig_img = dataset.images[idx].copy()
            orig_mask = dataset.masks[idx].copy()
            orig_conf = dataset.confidence_masks[idx].copy()
        
        # Get augmented data
        aug_img = sample['image'].squeeze().cpu().numpy()
        aug_mask = sample['mask'].squeeze().cpu().numpy()
        aug_conf = sample['confidence'].squeeze().cpu().numpy()
        
        # Plot original data
        axes[i, 0].imshow(orig_img, cmap='gray')
        axes[i, 0].set_title(f"Original Image {idx}")
        axes[i, 0].axis('off')
        
        axes[i, 1].imshow(orig_mask, cmap='gray')
        axes[i, 1].set_title(f"Original Mask {idx}")
        axes[i, 1].axis('off')
        
        axes[i, 2].imshow(orig_conf, cmap='viridis')
        axes[i, 2].set_title(f"Original Confidence {idx}")
        axes[i, 2].axis('off')
        
        # Plot augmented data
        axes[i, 3].imshow(aug_img, cmap='gray')
        axes[i, 3].set_title(f"Augmented Image {idx}")
        axes[i, 3].axis('off')
        
        axes[i, 4].imshow(aug_mask, cmap='gray')
        axes[i, 4].set_title(f"Augmented Mask {idx}")
        axes[i, 4].axis('off')
        
        axes[i, 5].imshow(aug_conf, cmap='viridis')
        axes[i, 5].set_title(f"Augmented Confidence {idx}")
        axes[i, 5].axis('off')
        
        # Print shape information
        print(f"Sample {idx}:")
        print(f"  Original shapes: Image {orig_img.shape}, Mask {orig_mask.shape}, Confidence {orig_conf.shape}")
        print(f"  Augmented shapes: Image {aug_img.shape}, Mask {aug_mask.shape}, Confidence {aug_conf.shape}")
        
        # Check for flipping
        if np.sum(np.abs(orig_conf - np.flipud(aug_conf))) < np.sum(np.abs(orig_conf - aug_conf)):
            print(f"  DETECTED: Confidence map appears to be flipped vertically in sample {idx}")
        
        if np.sum(np.abs(orig_conf - np.fliplr(aug_conf))) < np.sum(np.abs(orig_conf - aug_conf)):
            print(f"  DETECTED: Confidence map appears to be flipped horizontally in sample {idx}")
    
    plt.tight_layout()
    plt.show()

def visualize_augmentation_consistency(dataset, idx=0):
    """
    Visualize the consistency of augmentations across image, mask, and confidence map
    
    Args:
        dataset: Dataset to visualize
        idx: Index of sample to visualize
    """
    # Get sample
    sample = dataset[idx]
    
    # Get data
    image = sample['image'].squeeze().cpu().numpy()
    mask = sample['mask'].squeeze().cpu().numpy()
    confidence = sample['confidence'].squeeze().cpu().numpy()
    
    # Create figure
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Plot data
    axes[0].imshow(image, cmap='gray')
    axes[0].set_title("Image")
    axes[0].axis('off')
    
    axes[1].imshow(mask, cmap='gray')
    axes[1].set_title("Mask")
    axes[1].axis('off')
    
    axes[2].imshow(confidence, cmap='viridis')
    axes[2].set_title("Confidence")
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Check for orientation consistency
    print(f"Image shape: {image.shape}")
    print(f"Mask shape: {mask.shape}")
    print(f"Confidence shape: {confidence.shape}")
    
    # Check if confidence map is flipped compared to mask
    if np.sum(np.abs(mask - np.flipud(confidence))) < np.sum(np.abs(mask - confidence)):
        print("ISSUE DETECTED: Confidence map appears to be flipped vertically compared to mask")
    
    if np.sum(np.abs(mask - np.fliplr(confidence))) < np.sum(np.abs(mask - confidence)):
        print("ISSUE DETECTED: Confidence map appears to be flipped horizontally compared to mask")