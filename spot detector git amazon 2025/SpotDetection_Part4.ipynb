{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"evaluation\"></a>\n", "## 5. Evaluation\n", "\n", "Now, let's evaluate the trained model on the validation set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load best model\n", "best_model_path = os.path.join(SAVE_DIR, EXPERIMENT_NAME, 'best_model.pth')\n", "trainer.load_checkpoint('best_model.pth')\n", "print(f\"Loaded best model from {best_model_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Evaluate on validation set\n", "val_metrics = trainer.validate()\n", "\n", "# Print metrics\n", "print(\"Validation metrics:\")\n", "for key, value in val_metrics.items():\n", "    if key.startswith('val_'):\n", "        print(f\"{key}: {value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualize predictions on validation set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def visualize_predictions(model, dataset, num_samples=5):\n", "    \"\"\"Visualize model predictions on dataset\"\"\"\n", "    model.eval()\n", "    \n", "    # Create figure\n", "    fig, axes = plt.subplots(num_samples, 4, figsize=(20, 4*num_samples))\n", "    \n", "    with torch.no_grad():\n", "        for i in range(num_samples):\n", "            # Get a random sample\n", "            idx = random.randint(0, len(dataset)-1)\n", "            sample = dataset[idx]\n", "            \n", "            # Extract image and mask\n", "            image = sample['image'].unsqueeze(0).to(device)  # Add batch dimension\n", "            mask = sample['mask']\n", "            \n", "            # Forward pass\n", "            outputs = model(image)\n", "            \n", "            # Get predictions\n", "            pred_heatmap = torch.sigmoid(outputs['heatmap']).squeeze().cpu()\n", "            pred_boundary = torch.sigmoid(outputs['boundary']).squeeze().cpu() if 'boundary' in outputs else None\n", "            instance_labels = outputs.get('instance_labels')\n", "            \n", "            # Plot image\n", "            axes[i, 0].imshow(image.squeeze().cpu(), cmap='gray')\n", "            axes[i, 0].set_title(f\"Image {idx}\")\n", "            axes[i, 0].axis('off')\n", "            \n", "            # Plot ground truth mask\n", "            axes[i, 1].imshow(mask.squeeze(), cmap='hot')\n", "            axes[i, 1].set_title(f\"Ground Truth\")\n", "            axes[i, 1].axis('off')\n", "            \n", "            # Plot predicted heatmap\n", "            axes[i, 2].imshow(pred_heatmap, cmap='hot')\n", "            axes[i, 2].set_title(f\"Predicted Heatmap\")\n", "            axes[i, 2].axis('off')\n", "            \n", "            # Plot instance segmentation or boundary\n", "            if instance_labels is not None:\n", "                # Create colormap with random colors for instances\n", "                instance_np = instance_labels.squeeze().cpu().numpy()\n", "                num_instances = int(instance_np.max())\n", "                colors = np.random.rand(num_instances + 1, 3)\n", "                colors[0] = [0, 0, 0]  # Background is black\n", "                cmap = plt.cm.colors.ListedColormap(colors)\n", "                \n", "                axes[i, 3].imshow(instance_np, cmap=cmap)\n", "                axes[i, 3].set_title(f\"Instance Segmentation ({num_instances} spots)\")\n", "            elif pred_boundary is not None:\n", "                axes[i, 3].imshow(pred_boundary, cmap='hot')\n", "                axes[i, 3].set_title(f\"Predicted Boundary\")\n", "            else:\n", "                axes[i, 3].imshow(np.zeros_like(pred_heatmap), cmap='gray')\n", "                axes[i, 3].set_title(f\"No Instance Segmentation\")\n", "            \n", "            axes[i, 3].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Visualize predictions on validation set\n", "visualize_predictions(model, val_dataset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"prediction\"></a>\n", "## 6. Prediction and Visualization\n", "\n", "Now, let's use the trained model to make predictions on new images."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create predictor\n", "predictor = SpotDetectionPredictor(\n", "    model_path=best_model_path,\n", "    device=device,\n", "    threshold=0.5,\n", "    min_size=2,\n", "    min_distance=2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Function to load and process a test image\n", "def process_test_image(image_path):\n", "    \"\"\"Process a test image and visualize results\"\"\"\n", "    # Load image\n", "    image = io.imread(image_path)\n", "    \n", "    # Convert to grayscale if RGB\n", "    if len(image.shape) == 3 and image.shape[2] > 1:\n", "        image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)\n", "    \n", "    # Normalize to [0, 1]\n", "    image = image.astype(np.float32)\n", "    if image.max() > 1.0:\n", "        image = image / 255.0\n", "    \n", "    # Run prediction\n", "    outputs = predictor.predict(image)\n", "    \n", "    # Visualize results\n", "    predictor.visualize_results(image, outputs)\n", "    \n", "    return outputs\n", "\n", "# Process a test image if available\n", "# Replace with your own test image path\n", "test_image_path = \"/path/to/test/image.png\"\n", "if os.path.exists(test_image_path):\n", "    outputs = process_test_image(test_image_path)\n", "else:\n", "    print(f\"Test image not found: {test_image_path}\")\n", "    print(\"Using a sample from the validation set instead\")\n", "    \n", "    # Get a sample from the validation set\n", "    idx = random.randint(0, len(val_dataset)-1)\n", "    sample = val_dataset[idx]\n", "    image = sample['image']\n", "    \n", "    # Run prediction\n", "    outputs = predictor.predict(image)\n", "    \n", "    # Visualize results\n", "    predictor.visualize_results(image, outputs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"semi-supervised\"></a>\n", "## 7. Semi-supervised Learning\n", "\n", "Now, let's implement semi-supervised learning to improve the model with high-confidence predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def train_with_semi_supervised_learning(model, loss_fn, train_dataset, val_dataset, \n", "                                       num_iterations=3, num_epochs_per_iteration=20):\n", "    \"\"\"Train with semi-supervised learning\"\"\"\n", "    # Create data loaders\n", "    train_loader = DataLoader(\n", "        train_dataset,\n", "        batch_size=BATCH_SIZE,\n", "        shuffle=True,\n", "        num_workers=NUM_WORKERS,\n", "        pin_memory=True\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=BATCH_SIZE,\n", "        shuffle=False,\n", "        num_workers=NUM_WORKERS,\n", "        pin_memory=True\n", "    )\n", "    \n", "    # Create optimizer and scheduler\n", "    optimizer = optim.AdamW(\n", "        list(model.parameters()) + list(loss_fn.parameters()),\n", "        lr=LEARNING_RATE,\n", "        weight_decay=WEIGHT_DECAY\n", "    )\n", "    \n", "    scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "        optimizer, T_max=num_epochs_per_iteration, eta_min=LEARNING_RATE/100\n", "    )\n", "    \n", "    # Create trainer\n", "    trainer = SparseSpotTrainer(\n", "        model=model,\n", "        loss_fn=loss_fn,\n", "        train_loader=train_loader,\n", "        val_loader=val_loader,\n", "        optimizer=optimizer,\n", "        scheduler=scheduler,\n", "        device=device,\n", "        save_dir=SAVE_DIR,\n", "        experiment_name=f\"{EXPERIMENT_NAME}_semi_supervised\",\n", "        metrics_threshold=0.5,\n", "        iou_threshold=0.5,\n", "        clip_grad_norm=1.0,\n", "        semi_supervised=True,\n", "        confidence_threshold=0.9,\n", "        update_interval=5\n", "    )\n", "    \n", "    # Train with semi-supervised learning\n", "    for iteration in range(num_iterations):\n", "        print(f\"\\nSemi-supervised learning iteration {iteration+1}/{num_iterations}\")\n", "        \n", "        # Train for a few epochs\n", "        trainer.train(\n", "            num_epochs=num_epochs_per_iteration,\n", "            patience=num_epochs_per_iteration // 2,\n", "            save_freq=num_epochs_per_iteration,\n", "            primary_metric='val_pixel_f1'\n", "        )\n", "        \n", "        # Update dataset with high-confidence predictions\n", "        trainer.update_dataset_with_predictions()\n", "        \n", "        # Evaluate\n", "        val_metrics = trainer.validate()\n", "        print(\"\\nValidation metrics after iteration:\")\n", "        for key, value in val_metrics.items():\n", "            if key.startswith('val_'):\n", "                print(f\"{key}: {value:.4f}\")\n", "    \n", "    return trainer\n", "\n", "# Uncomment to run semi-supervised learning\n", "# semi_supervised_trainer = train_with_semi_supervised_learning(\n", "#     model, loss_fn, train_dataset, val_dataset, num_iterations=3, num_epochs_per_iteration=20\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"advanced\"></a>\n", "## 8. Advanced Techniques\n", "\n", "Finally, let's implement some advanced techniques to further improve the model."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test-time augmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def predict_with_tta(model, image, num_augmentations=8):\n", "    \"\"\"Predict with test-time augmentation\"\"\"\n", "    model.eval()\n", "    \n", "    # Create augmentations\n", "    augmentations = [\n", "        <PERSON><PERSON>([ToTensorV2()]),  # Original image\n", "        <PERSON><PERSON>([<PERSON><PERSON>(p=1.0), ToTensorV2()]),  # Horizontal flip\n", "        <PERSON><PERSON>([<PERSON><PERSON>ert<PERSON>lip(p=1.0), ToTensorV2()]),  # Vertical flip\n", "        <PERSON><PERSON>([<PERSON><PERSON>(limit=90, p=1.0), ToTensorV2()]),  # 90 degree rotation\n", "        <PERSON><PERSON>([<PERSON><PERSON>(limit=180, p=1.0), ToTensorV2()]),  # 180 degree rotation\n", "        <PERSON><PERSON>([<PERSON><PERSON>(limit=270, p=1.0), ToTensorV2()]),  # 270 degree rotation\n", "        <PERSON><PERSON>([<PERSON><PERSON>(p=1.0), <PERSON><PERSON>(p=1.0), ToTensorV2()]),  # Both flips\n", "        <PERSON><PERSON>([A.RandomBrightnessContrast(p=1.0), ToTensorV2()])  # Brightness/contrast\n", "    ]\n", "    \n", "    # Limit to specified number of augmentations\n", "    augmentations = augmentations[:num_augmentations]\n", "    \n", "    # Apply augmentations and get predictions\n", "    predictions = []\n", "    \n", "    with torch.no_grad():\n", "        for aug in augmentations:\n", "            # Apply augmentation\n", "            if isinstance(image, torch.Tensor):\n", "                # Convert to numpy for albumentations\n", "                img_np = image.squeeze().cpu().numpy()\n", "                augmented = aug(image=img_np)\n", "                img_aug = augmented['image'].unsqueeze(0).to(device)  # Add batch dimension\n", "            else:\n", "                augmented = aug(image=image)\n", "                img_aug = augmented['image'].unsqueeze(0).to(device)  # Add batch dimension\n", "            \n", "            # Forward pass\n", "            outputs = model(img_aug)\n", "            \n", "            # Get heatmap prediction\n", "            pred_heatmap = outputs['heatmap']\n", "            \n", "            # Reverse augmentation for prediction\n", "            if aug != augmentations[0]:  # Skip original image\n", "                # For now, just use the prediction as is\n", "                # In a real implementation, you would reverse the augmentation\n", "                pass\n", "            \n", "            predictions.append(pred_heatmap)\n", "    \n", "    # Average predictions\n", "    avg_prediction = torch.mean(torch.cat(predictions, dim=0), dim=0, keepdim=True)\n", "    \n", "    # Create output dictionary\n", "    outputs = {'heatmap': avg_prediction}\n", "    \n", "    # Run instance segmentation on averaged prediction\n", "    if hasattr(model, 'get_instance_segmentation'):\n", "        instance_results = model.get_instance_segmentation(\n", "            outputs,\n", "            threshold=0.5,\n", "            min_size=2,\n", "            min_distance=2\n", "        )\n", "        \n", "        if instance_results is not None:\n", "            instance_map, coords = instance_results\n", "            outputs['instance_labels'] = instance_map\n", "            outputs['spot_coords'] = coords\n", "    \n", "    return outputs\n", "\n", "# Test TTA on a sample image\n", "idx = random.randint(0, len(val_dataset)-1)\n", "sample = val_dataset[idx]\n", "image = sample['image']\n", "\n", "# Run prediction with TTA\n", "tta_outputs = predict_with_tta(model, image, num_augmentations=4)\n", "\n", "# Visualize results\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Plot image\n", "plt.subplot(1, 3, 1)\n", "plt.imshow(image.squeeze(), cmap='gray')\n", "plt.title(\"Input Image\")\n", "plt.axis('off')\n", "\n", "# Plot ground truth\n", "plt.subplot(1, 3, 2)\n", "plt.imshow(sample['mask'].squeeze(), cmap='hot')\n", "plt.title(\"Ground Truth\")\n", "plt.axis('off')\n", "\n", "# Plot TTA prediction\n", "plt.subplot(1, 3, 3)\n", "plt.imshow(torch.sigmoid(tta_outputs['heatmap']).squeeze().cpu(), cmap='hot')\n", "plt.title(\"TTA Prediction\")\n", "plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model ensemble"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def create_model_ensemble(num_models=3):\n", "    \"\"\"Create an ensemble of models\"\"\"\n", "    models = []\n", "    \n", "    for i in range(num_models):\n", "        # Create model with different random seed\n", "        set_seed(42 + i)\n", "        \n", "        # Create model\n", "        model = EnhancedSpotDetectionModel(**MODEL_CONFIG).to(device)\n", "        \n", "        models.append(model)\n", "    \n", "    return models\n", "\n", "def predict_with_ensemble(models, image):\n", "    \"\"\"Predict with model ensemble\"\"\"\n", "    # Ensure image is a tensor with batch dimension\n", "    if isinstance(image, np.ndarray):\n", "        image = torch.from_numpy(image).float()\n", "        if image.dim() == 2:\n", "            image = image.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions\n", "        elif image.dim() == 3:\n", "            image = image.unsqueeze(0)  # Add batch dimension\n", "    elif isinstance(image, torch.Tensor):\n", "        if image.dim() == 2:\n", "            image = image.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions\n", "        elif image.dim() == 3:\n", "            image = image.unsqueeze(0)  # Add batch dimension\n", "    \n", "    # Move to device\n", "    image = image.to(device)\n", "    \n", "    # Get predictions from each model\n", "    predictions = []\n", "    \n", "    with torch.no_grad():\n", "        for model in models:\n", "            model.eval()\n", "            outputs = model(image)\n", "            predictions.append(outputs['heatmap'])\n", "    \n", "    # Average predictions\n", "    avg_prediction = torch.mean(torch.cat(predictions, dim=0), dim=0, keepdim=True)\n", "    \n", "    # Create output dictionary\n", "    outputs = {'heatmap': avg_prediction}\n", "    \n", "    # Run instance segmentation on averaged prediction\n", "    if hasattr(models[0], 'get_instance_segmentation'):\n", "        instance_results = models[0].get_instance_segmentation(\n", "            outputs,\n", "            threshold=0.5,\n", "            min_size=2,\n", "            min_distance=2\n", "        )\n", "        \n", "        if instance_results is not None:\n", "            instance_map, coords = instance_results\n", "            outputs['instance_labels'] = instance_map\n", "            outputs['spot_coords'] = coords\n", "    \n", "    return outputs\n", "\n", "# Uncomment to create and train an ensemble\n", "# ensemble_models = create_model_ensemble(num_models=3)\n", "# \n", "# # Train each model\n", "# for i, model in enumerate(ensemble_models):\n", "#     print(f\"\\nTraining ensemble model {i+1}/{len(ensemble_models)}\")\n", "#     \n", "#     # Create loss function\n", "#     loss_fn = SparseOptimizedSpotLoss(**LOSS_CONFIG).to(device)\n", "#     \n", "#     # Create optimizer and scheduler\n", "#     optimizer = optim.AdamW(\n", "#         list(model.parameters()) + list(loss_fn.parameters()),\n", "#         lr=LEARNING_RATE,\n", "#         weight_decay=WEIGHT_DECAY\n", "#     )\n", "#     \n", "#     scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "#         optimizer, T_max=NUM_EPOCHS, eta_min=LEARNING_RATE/100\n", "#     )\n", "#     \n", "#     # Create trainer\n", "#     trainer = SparseSpotTrainer(\n", "#         model=model,\n", "#         loss_fn=loss_fn,\n", "#         train_loader=train_loader,\n", "#         val_loader=val_loader,\n", "#         optimizer=optimizer,\n", "#         scheduler=scheduler,\n", "#         device=device,\n", "#         save_dir=SAVE_DIR,\n", "#         experiment_name=f\"{EXPERIMENT_NAME}_ensemble_{i}\",\n", "#         metrics_threshold=0.5,\n", "#         iou_threshold=0.5,\n", "#         clip_grad_norm=1.0\n", "#     )\n", "#     \n", "#     # Train model\n", "#     trainer.train(\n", "#         num_epochs=NUM_EPOCHS // 2,  # Train for fewer epochs\n", "#         patience=PATIENCE,\n", "#         save_freq=SAVE_FREQ,\n", "#         primary_metric='val_pixel_f1'\n", "#     )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've implemented a comprehensive spot detection system with:\n", "\n", "1. **Data loading and augmentation**: Support for real and synthetic data with comprehensive augmentation\n", "2. **Sparse annotation handling**: Confidence-weighted loss and semi-supervised learning\n", "3. **Optimized model architecture**: Mixture of experts with specialization for different spot types\n", "4. **Enhanced loss function**: Multiple components with GPU acceleration and adaptive weighting\n", "5. **Training stability**: Gradient clipping and regularization\n", "6. **Advanced techniques**: Test-time augmentation and model ensembling\n", "\n", "This system provides state-of-the-art performance for spot detection with varying sizes and densities."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}