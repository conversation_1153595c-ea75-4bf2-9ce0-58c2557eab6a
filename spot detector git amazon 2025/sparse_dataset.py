import torch
import numpy as np
import os
import cv2
from skimage import io
from data_utils import SpotDataset

class SparseSpotDataset(SpotDataset):
    """
    Dataset for spot detection with sparse annotations
    
    This dataset extends SpotDataset to handle sparse annotations by:
    1. Creating confidence masks for partially annotated data
    2. Supporting semi-supervised learning with high-confidence predictions
    """
    def __init__(self, 
                 confidence_threshold=0.9,
                 ignore_threshold=0.3,
                 **kwargs):
        """
        Initialize the dataset
        
        Args:
            confidence_threshold: Threshold for high-confidence predictions
            ignore_threshold: Threshold below which to ignore predictions
            **kwargs: Arguments to pass to SpotDataset
        """
        super().__init__(**kwargs)
        self.confidence_threshold = confidence_threshold
        self.ignore_threshold = ignore_threshold
        
        # Create confidence masks for each sample
        self.confidence_masks = []
        for i in range(len(self.masks)):
            if isinstance(self.masks[i], str):
                # For real data, check if confidence mask exists
                conf_path = self.masks[i].replace('.png', '_conf.png').replace('.tif', '_conf.png')
                if os.path.exists(conf_path):
                    conf_mask = io.imread(conf_path).astype(np.float32) / 255.0
                else:
                    # If no confidence mask, assume full confidence in annotations
                    mask = io.imread(self.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    conf_mask = mask.copy()
                self.confidence_masks.append(conf_path if os.path.exists(conf_path) else conf_mask)
            else:
                # For synthetic data, confidence is 1.0 everywhere
                self.confidence_masks.append(np.ones_like(self.masks[i]))
    
    def update_with_predictions(self, image_paths, predictions, threshold=None):
        """
        Update dataset with high-confidence predictions
        
        Args:
            image_paths: Paths to images
            predictions: Predicted heatmaps
            threshold: Confidence threshold (overrides self.confidence_threshold)
        """
        if threshold is None:
            threshold = self.confidence_threshold
        
        # Process each prediction
        for path, pred in zip(image_paths, predictions):
            # Find index of this image in the dataset
            if isinstance(path, str):
                idx = self.images.index(path) if path in self.images else -1
            else:
                idx = -1
            
            if idx >= 0:
                # Convert prediction to numpy if it's a tensor
                if isinstance(pred, torch.Tensor):
                    pred = torch.sigmoid(pred).cpu().numpy()
                
                # Get current mask and confidence mask
                if isinstance(self.masks[idx], str):
                    mask = io.imread(self.masks[idx])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                else:
                    mask = self.masks[idx]
                
                if isinstance(self.confidence_masks[idx], str):
                    conf_mask = io.imread(self.confidence_masks[idx]).astype(np.float32) / 255.0
                else:
                    conf_mask = self.confidence_masks[idx]
                
                # Update mask with high-confidence predictions
                high_conf_pred = (pred > threshold).astype(np.float32)
                low_conf_areas = (conf_mask < self.ignore_threshold)
                
                # Only update in low-confidence areas
                mask[low_conf_areas] = high_conf_pred[low_conf_areas]
                
                # Update confidence mask
                conf_mask[low_conf_areas & (pred > threshold)] = pred[low_conf_areas & (pred > threshold)]
                
                # Save updated masks
                self.masks[idx] = mask
                self.confidence_masks[idx] = conf_mask
    
    def __getitem__(self, idx):
        """
        Get a sample from the dataset with confidence mask
        """
        # Get base sample from parent class
        sample = super().__getitem__(idx)
        
        # Add confidence mask to sample
        conf_mask = self.confidence_masks[idx]
        
        # Load confidence mask if it's a file path
        if isinstance(conf_mask, str):
            conf_mask = io.imread(conf_mask).astype(np.float32) / 255.0
            # Convert to grayscale if RGB
            if len(conf_mask.shape) == 3 and conf_mask.shape[2] > 1:
                conf_mask = cv2.cvtColor(conf_mask, cv2.COLOR_RGB2GRAY)
        
        # Apply transforms if available
        if self.transform:
            # Extract image from sample and convert to numpy if needed
            image = sample['image']
            if isinstance(image, torch.Tensor):
                image = image.numpy()
                if image.shape[0] == 1:  # If [1, H, W]
                    image = image[0]
            
            augmented = self.transform(image=image, mask=conf_mask)
            conf_mask = augmented['mask']
        
        # Ensure confidence mask has channel dimension
        if isinstance(conf_mask, np.ndarray) and conf_mask.ndim == 2:
            conf_mask = np.expand_dims(conf_mask, 0)
        
        # Convert to torch tensor if not already
        if not isinstance(conf_mask, torch.Tensor):
            conf_mask = torch.from_numpy(conf_mask).float()
        
        # Add to sample
        sample['confidence'] = conf_mask
        
        return sample

def create_sparse_data_loaders(data_dir=None, 
                              batch_size=8,
                              image_size=(256, 256),
                              train_val_split=0.8,
                              synthetic=True,
                              synthetic_size=1000,
                              augmentation_level='strong',
                              num_workers=4,
                              confidence_threshold=0.9,
                              ignore_threshold=0.3):
    """
    Create train and validation data loaders with sparse annotation support
    
    Args:
        data_dir: Directory containing images and masks
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic: Whether to generate synthetic data
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        confidence_threshold: Threshold for high-confidence predictions
        ignore_threshold: Threshold below which to ignore predictions
        
    Returns:
        train_loader, val_loader
    """
    # Create datasets
    if data_dir is not None:
        # Load real data and split into train/val
        full_dataset = SparseSpotDataset(
            data_dir=data_dir,
            image_size=image_size,
            mode='train',  # Will be overridden for validation split
            synthetic=False,
            augmentation_level='none',  # No augmentation yet
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
        
        # Calculate split sizes
        train_size = int(train_val_split * len(full_dataset))
        val_size = len(full_dataset) - train_size
        
        # Split dataset
        train_indices = list(range(train_size))
        val_indices = list(range(train_size, len(full_dataset)))
        
        # Create new datasets with appropriate augmentation
        train_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='train',
            synthetic=synthetic,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level,
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
        train_dataset.images = [full_dataset.images[i] for i in train_indices]
        train_dataset.masks = [full_dataset.masks[i] for i in train_indices]
        train_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in train_indices]
        
        val_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='val',
            synthetic=False,  # No synthetic data for validation
            augmentation_level='none',  # No augmentation for validation
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
        val_dataset.images = [full_dataset.images[i] for i in val_indices]
        val_dataset.masks = [full_dataset.masks[i] for i in val_indices]
        val_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in val_indices]
        
    else:
        # Create synthetic datasets
        train_dataset = SparseSpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='train',
            synthetic=True,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level,
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
        
        val_dataset = SparseSpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='val',
            synthetic=True,
            synthetic_size=int(synthetic_size * (1 - train_val_split)),
            augmentation_level='none',  # No augmentation for validation
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
    
    # Create data loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader