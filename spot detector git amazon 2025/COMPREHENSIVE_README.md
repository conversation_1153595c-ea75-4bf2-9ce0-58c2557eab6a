# Comprehensive Spot Detection with Mixture of Experts

This repository provides a complete solution for detecting spots of varying sizes and densities in images using a Mixture of Experts approach.

## Key Features

- **Mixture of Experts Architecture**: Specialized experts for different spot types
- **Optimized Loss Function**: Combines multiple components for better spot detection
- **Adaptive Weighting**: Density-aware and size-adaptive weighting
- **Support for Sparse Annotations**: <PERSON>les incomplete ground truth
- **Semi-supervised Learning**: Leverages high-confidence predictions to expand training set
- **Data Augmentation**: Comprehensive augmentation pipeline
- **Synthetic Data Generation**: Creates realistic synthetic data with variable spot sizes and densities

## Repository Structure

The repository is organized into several Python modules and Jupyter notebooks:

### Python Modules

- `data_utils.py`: Base dataset class for loading images and masks
- `sparse_data_utils.py`: Extended dataset class for handling sparse annotations
- `synthetic_data_generator.py`: Classes for generating synthetic data
- `OptimizedSpotLoss.py`: Loss function implementation
- `OptimizedSpotDetection_model.py`: Model architecture implementation
- `metrics.py`: Evaluation metrics
- `trainer.py`: Training pipeline
- `predict.py`: Prediction and visualization utilities

### Jupyter Notebooks

- `SpotDetection_Part1.ipynb`: Setup, dependencies, and data loading
- `SpotDetection_Part2.ipynb`: Model and loss function implementation
- `SpotDetection_Part3.ipynb`: Training pipeline
- `SpotDetection_Part4.ipynb`: Evaluation, prediction, and advanced techniques

## Getting Started

### Installation

1. Clone this repository
2. Install the required packages:
```bash
pip install torch torchvision albumentations scikit-image tqdm matplotlib opencv-python
```

### Data Preparation

For real data, organize your images and masks in one of these directory structures:

1. **Separate directories**:
   - `data_dir/images/` - Contains image files
   - `data_dir/masks/` - Contains mask files with the same names

2. **Paired files in the same directory**:
   - `data_dir/image_name.png` - Image file
   - `data_dir/image_name_mask.png` - Corresponding mask file

For sparse annotations, you can optionally provide confidence masks:
   - `data_dir/image_name_conf.png` - Confidence mask file

### Configuration

Edit the configuration parameters in the notebooks or create a `config.py` file:

```python
# Data configuration
DATA_CONFIG = {
    'data_dir': "/path/to/your/data",  # Change this to your data directory
    'image_size': 256,
    'batch_size': 8,
    'use_synthetic': True,  # Set to False if you only want to use real data
    'synthetic_size': 500,  # Number of synthetic samples to generate
    'augmentation_level': 'strong',  # 'none', 'light', 'medium', or 'strong'
    'train_val_split': 0.8,  # 80% training, 20% validation
    'num_workers': 4,  # Number of workers for data loading
    'is_3d': False,  # Set to True for 3D data
    'confidence_threshold': 0.9,  # Threshold for high-confidence predictions
    'ignore_threshold': 0.3  # Threshold below which to ignore predictions
}

# Model configuration
MODEL_CONFIG = {
    'in_channels': 1,
    'num_experts': 3,
    'base_filters': 64,
    'dropout_rate': 0.2,
    'is_3d': DATA_CONFIG['is_3d']
}

# Loss function configuration
LOSS_CONFIG = {
    'bce_weight': 1.0,
    'dice_weight': 1.0,
    'focal_weight': 0.5,
    'focal_gamma': 2.0,
    'size_adaptive': True,
    'density_aware': True,
    'confidence_weighted': True
}

# Training configuration
TRAIN_CONFIG = {
    'learning_rate': 0.001,
    'num_epochs': 30,
    'early_stopping_patience': 10,
    'save_best_model': True,
    'model_save_path': 'best_spot_detection_model.pth',
    'gradient_clipping': 1.0,  # Set to None to disable gradient clipping
    'use_mixed_precision': True  # Use mixed precision training if available
}
```

## Usage Guide

### 1. Data Loading

```python
from data_utils import SpotDataset, create_data_loaders
from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders

# For regular data
train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(
    data_dir=DATA_CONFIG['data_dir'],
    batch_size=DATA_CONFIG['batch_size'],
    image_size=(DATA_CONFIG['image_size'], DATA_CONFIG['image_size']),
    train_val_split=DATA_CONFIG['train_val_split'],
    synthetic=DATA_CONFIG['use_synthetic'],
    synthetic_size=DATA_CONFIG['synthetic_size'],
    augmentation_level=DATA_CONFIG['augmentation_level'],
    num_workers=DATA_CONFIG['num_workers']
)

# For sparse annotations
train_loader, val_loader, train_dataset, val_dataset = create_sparse_data_loaders(
    data_dir=DATA_CONFIG['data_dir'],
    batch_size=DATA_CONFIG['batch_size'],
    image_size=(DATA_CONFIG['image_size'], DATA_CONFIG['image_size']),
    train_val_split=DATA_CONFIG['train_val_split'],
    synthetic=DATA_CONFIG['use_synthetic'],
    synthetic_size=DATA_CONFIG['synthetic_size'],
    augmentation_level=DATA_CONFIG['augmentation_level'],
    num_workers=DATA_CONFIG['num_workers'],
    confidence_threshold=DATA_CONFIG['confidence_threshold'],
    ignore_threshold=DATA_CONFIG['ignore_threshold']
)
```

### 2. Creating the Model and Loss Function

```python
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from OptimizedSpotLoss import OptimizedSpotLoss

# Create model
model = OptimizedSpotDetectionModel(
    in_channels=MODEL_CONFIG['in_channels'],
    num_experts=MODEL_CONFIG['num_experts'],
    base_filters=MODEL_CONFIG['base_filters'],
    dropout_rate=MODEL_CONFIG['dropout_rate']
)

# Move model to device
model = model.to(device)

# Create loss function
loss_fn = OptimizedSpotLoss(
    bce_weight=LOSS_CONFIG['bce_weight'],
    dice_weight=LOSS_CONFIG['dice_weight'],
    focal_weight=LOSS_CONFIG['focal_weight'],
    focal_gamma=LOSS_CONFIG['focal_gamma'],
    size_adaptive=LOSS_CONFIG['size_adaptive'],
    density_aware=LOSS_CONFIG['density_aware'],
    confidence_weighted=LOSS_CONFIG['confidence_weighted']
)
```

### 3. Training

```python
from metrics import SpotDetectionMetrics
from trainer import SpotDetectionTrainer

# Create optimizer
optimizer = optim.Adam(model.parameters(), lr=TRAIN_CONFIG['learning_rate'])

# Create learning rate scheduler
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', factor=0.5, patience=5, verbose=True
)

# Create metrics calculator
metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)

# Create trainer
trainer = SpotDetectionTrainer(
    model=model,
    loss_fn=loss_fn,
    optimizer=optimizer,
    device=device,
    metrics_calculator=metrics_calculator,
    scheduler=scheduler
)

# Train model
history = trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=TRAIN_CONFIG['num_epochs'],
    early_stopping_patience=TRAIN_CONFIG['early_stopping_patience'],
    save_best_model=TRAIN_CONFIG['save_best_model'],
    model_save_path=TRAIN_CONFIG['model_save_path']
)

# Plot training history
trainer.plot_history()
```

### 4. Prediction and Visualization

```python
from predict import SpotDetectionPredictor

# Create predictor
predictor = SpotDetectionPredictor(
    model=model,
    device=device,
    threshold=PRED_CONFIG['threshold'],
    min_spot_size=PRED_CONFIG['min_spot_size']
)

# Make prediction on an image
image = val_dataset[0]['image']
result = predictor.predict(image, return_heatmap=True)

# Visualize result
predictor.visualize(
    image=image,
    result=result,
    show_spots=True,
    show_heatmap=True,
    show_experts=True
)
```

### 5. Semi-supervised Learning

```python
def semi_supervised_learning(model, train_dataset, val_dataset, num_iterations=3):
    """Perform semi-supervised learning"""
    for iteration in range(num_iterations):
        print(f"Semi-supervised learning iteration {iteration+1}/{num_iterations}")
        
        # Create predictor
        predictor = SpotDetectionPredictor(
            model=model,
            device=device,
            threshold=0.5,
            min_spot_size=3
        )
        
        # Make predictions on training set
        train_loader = DataLoader(
            train_dataset,
            batch_size=DATA_CONFIG['batch_size'],
            shuffle=False,
            num_workers=DATA_CONFIG['num_workers']
        )
        
        # Collect predictions
        all_images = []
        all_preds = []
        
        model.eval()
        with torch.no_grad():
            for batch in tqdm(train_loader, desc="Making predictions"):
                images = batch['image'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Get main output
                if isinstance(outputs, dict):
                    pred = outputs.get('output', outputs.get('combined_output', None))
                else:
                    pred = outputs
                
                # Add to lists
                all_images.extend([img for img in batch['image']])
                all_preds.extend([p for p in pred])
        
        # Update dataset with high-confidence predictions
        train_dataset.update_with_predictions(all_images, all_preds, threshold=0.9)
        
        # Create new data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=DATA_CONFIG['batch_size'],
            shuffle=True,
            num_workers=DATA_CONFIG['num_workers'],
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=DATA_CONFIG['batch_size'],
            shuffle=False,
            num_workers=DATA_CONFIG['num_workers'],
            pin_memory=True
        )
        
        # Train model for a few epochs
        trainer = SpotDetectionTrainer(
            model=model,
            loss_fn=loss_fn,
            optimizer=optimizer,
            device=device,
            metrics_calculator=metrics_calculator,
            scheduler=scheduler
        )
        
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=10,
            early_stopping_patience=5,
            save_best_model=True,
            model_save_path=f'semi_supervised_model_iter{iteration+1}.pth'
        )
        
        # Evaluate
        val_metrics = trainer.validate(val_loader)
        print(f"Iteration {iteration+1} Validation Metrics:")
        for k, v in val_metrics.items():
            print(f"{k}: {v:.4f}")
        print()
    
    return model

# Perform semi-supervised learning
if SSL_CONFIG['enabled']:
    model = semi_supervised_learning(
        model, 
        train_dataset, 
        val_dataset, 
        num_iterations=SSL_CONFIG['num_iterations']
    )
```

## Working with 3D Data

To work with 3D data:

1. Set `is_3d=True` in `DATA_CONFIG`
2. Ensure your data loader provides 3D volumes
3. Input shape should be `[B, C, D, H, W]`
4. Consider using smaller batch sizes due to increased memory requirements
5. Enable gradient checkpointing for memory efficiency

## Troubleshooting

- **Out of Memory Errors**: Reduce batch size, enable gradient checkpointing
- **Slow Training**: Ensure GPU is being used, reduce image size if necessary
- **Poor Performance on Dense Regions**: Increase weight for Expert 4, enable density-aware weighting
- **Small Spots Not Detected**: Increase weight for Expert 1, enable size-adaptive weighting
- **Touching Spots Not Separated**: Increase boundary loss weight, enable contrastive loss

## Citation

If you use this code in your research, please cite:

```
@misc{SpotDetection2023,
  author = {Your Name},
  title = {Optimized Spot Detection with Mixture of Experts},
  year = {2023},
  publisher = {GitHub},
  journal = {GitHub repository},
  howpublished = {\url{https://github.com/yourusername/spot-detection}}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.