import torch
import torch.nn as nn
import torch.optim as optim
import argparse
import os
import sys
import numpy as np
import random
from typing import Dict, List, Tuple, Optional, Union

# Import modules
from data_utils import create_data_loaders
from trainer import SpotDetectionTrainer
from metrics import SpotDetectionMetrics
from predict import SpotDetectionPredictor

# Import model and loss function
# In a real project, you would use:
# from model import OptimizedSpotDetectionModel
# from loss import OptimizedSpotLoss

# For this example, we'll assume they're imported from the files we created
sys.path.append('/tmp')
from OptimizedSpotLoss import OptimizedSpotLoss
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel


def set_seed(seed: int = 42):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False


def train(args):
    """Train the model"""
    # Set random seed
    set_seed(args.seed)
    
    # Create data loaders
    train_loader, val_loader = create_data_loaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        image_size=(args.image_size, args.image_size),
        train_val_split=args.train_val_split,
        synthetic=args.synthetic,
        synthetic_size=args.synthetic_size,
        augmentation_level=args.augmentation,
        num_workers=args.num_workers
    )
    
    # Create model
    model = OptimizedSpotDetectionModel(
        in_channels=args.in_channels,
        base_filters=args.base_filters,
        is_3d=args.is_3d,
        num_experts=args.num_experts,
        dropout=args.dropout,
        stochastic_depth=args.stochastic_depth,
        deep_supervision=args.deep_supervision,
        use_attention=args.use_attention
    )
    
    # Create loss function
    loss_fn = OptimizedSpotLoss(
        heatmap_weight=args.heatmap_weight,
        boundary_weight=args.boundary_weight,
        distance_weight=args.distance_weight,
        moe_weight=args.moe_weight,
        pos_weight=args.pos_weight,
        dice_weight=args.dice_weight,
        focal_gamma=args.focal_gamma,
        learn_weights=args.learn_weights,
        density_aware_weighting=args.density_aware_weighting,
        size_adaptive_weighting=args.size_adaptive_weighting,
        num_experts=args.num_experts
    )
    
    # Create optimizer
    optimizer = optim.AdamW(
        list(model.parameters()) + list(loss_fn.parameters()),
        lr=args.learning_rate,
        weight_decay=args.weight_decay
    )
    
    # Create learning rate scheduler
    if args.scheduler == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=args.epochs, eta_min=args.learning_rate/100
        )
    elif args.scheduler == 'step':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer, step_size=args.step_size, gamma=args.gamma
        )
    elif args.scheduler == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=args.gamma, patience=args.patience // 2
        )
    else:
        scheduler = None
    
    # Create trainer
    trainer = SpotDetectionTrainer(
        model=model,
        loss_fn=loss_fn,
        train_loader=train_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        scheduler=scheduler,
        device=args.device,
        save_dir=args.save_dir,
        experiment_name=args.experiment_name,
        metrics_threshold=args.threshold,
        iou_threshold=args.iou_threshold
    )
    
    # Train model
    trainer.train(
        num_epochs=args.epochs,
        patience=args.patience,
        save_freq=args.save_freq,
        primary_metric=args.primary_metric
    )


def evaluate(args):
    """Evaluate the model"""
    # Create data loaders
    _, val_loader = create_data_loaders(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        image_size=(args.image_size, args.image_size),
        train_val_split=0.0,  # Use all data for validation
        synthetic=args.synthetic,
        synthetic_size=args.synthetic_size,
        augmentation_level='none',
        num_workers=args.num_workers
    )
    
    # Create model
    model = OptimizedSpotDetectionModel(
        in_channels=args.in_channels,
        base_filters=args.base_filters,
        is_3d=args.is_3d,
        num_experts=args.num_experts
    )
    
    # Create loss function
    loss_fn = OptimizedSpotLoss(
        heatmap_weight=args.heatmap_weight,
        boundary_weight=args.boundary_weight,
        distance_weight=args.distance_weight,
        moe_weight=args.moe_weight
    )
    
    # Load checkpoint
    checkpoint = torch.load(args.checkpoint, map_location=args.device)
    model.load_state_dict(checkpoint['model_state_dict'])
    loss_fn.load_state_dict(checkpoint['loss_state_dict'])
    
    # Create metrics calculator
    metrics = SpotDetectionMetrics(
        threshold=args.threshold,
        iou_threshold=args.iou_threshold
    )
    
    # Move model and loss function to device
    device = torch.device(args.device if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    model.to(device)
    loss_fn.to(device)
    
    # Set model to evaluation mode
    model.eval()
    
    # Evaluate model
    total_loss = 0.0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        for batch in val_loader:
            # Get inputs and targets
            inputs = batch['image'].to(device)
            targets = {
                'masks': batch['mask'].to(device)
            }
            
            # Forward pass
            outputs = model(inputs)
            
            # Compute loss
            loss, _ = loss_fn(outputs, targets)
            
            # Update metrics
            total_loss += loss.item()
            
            # Update evaluation metrics
            metrics.update(
                pred_heatmaps=outputs['heatmap'],
                gt_masks=targets['masks'],
                pred_instances=outputs.get('instance_labels'),
                gt_instances=None  # We don't have ground truth instances
            )
    
    # Compute average loss
    avg_loss = total_loss / num_batches
    
    # Compute evaluation metrics
    eval_metrics = metrics.compute()
    
    # Print metrics
    print(f"Evaluation results:")
    print(f"Loss: {avg_loss:.4f}")
    for key, value in eval_metrics.items():
        print(f"{key}: {value:.4f}")


def predict(args):
    """Run inference with the model"""
    # Create predictor
    predictor = SpotDetectionPredictor(
        model_path=args.checkpoint,
        device=args.device,
        threshold=args.threshold,
        min_size=args.min_size,
        min_distance=args.min_distance
    )
    
    # Process input
    if os.path.isdir(args.input):
        predictor.process_directory(args.input, args.output)
    else:
        predictor.process_image_file(args.input, args.output)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Spot Detection with Mixture of Experts')
    subparsers = parser.add_subparsers(dest='mode', help='Mode')
    
    # Common arguments
    common_parser = argparse.ArgumentParser(add_help=False)
    common_parser.add_argument('--device', type=str, default='cuda', help='Device to use (cuda or cpu)')
    common_parser.add_argument('--in-channels', type=int, default=1, help='Number of input channels')
    common_parser.add_argument('--image-size', type=int, default=256, help='Image size')
    common_parser.add_argument('--threshold', type=float, default=0.5, help='Threshold for binary segmentation')
    common_parser.add_argument('--iou-threshold', type=float, default=0.5, help='IoU threshold for instance matching')
    
    # Training arguments
    train_parser = subparsers.add_parser('train', parents=[common_parser], help='Train the model')
    train_parser.add_argument('--data-dir', type=str, default=None, help='Path to data directory')
    train_parser.add_argument('--save-dir', type=str, default='./checkpoints', help='Directory to save checkpoints')
    train_parser.add_argument('--experiment-name', type=str, default='spot_detection', help='Experiment name')
    train_parser.add_argument('--batch-size', type=int, default=8, help='Batch size')
    train_parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    train_parser.add_argument('--learning-rate', type=float, default=1e-3, help='Learning rate')
    train_parser.add_argument('--weight-decay', type=float, default=1e-4, help='Weight decay')
    train_parser.add_argument('--scheduler', type=str, default='cosine', choices=['cosine', 'step', 'plateau'], help='Learning rate scheduler')
    train_parser.add_argument('--step-size', type=int, default=30, help='Step size for step scheduler')
    train_parser.add_argument('--gamma', type=float, default=0.1, help='Gamma for step scheduler')
    train_parser.add_argument('--patience', type=int, default=10, help='Patience for early stopping')
    train_parser.add_argument('--save-freq', type=int, default=10, help='Frequency of saving checkpoints (in epochs)')
    train_parser.add_argument('--primary-metric', type=str, default='val_pixel_f1', help='Primary metric for model selection')
    train_parser.add_argument('--train-val-split', type=float, default=0.8, help='Train/validation split ratio')
    train_parser.add_argument('--synthetic', action='store_true', help='Use synthetic data')
    train_parser.add_argument('--synthetic-size', type=int, default=1000, help='Number of synthetic samples')
    train_parser.add_argument('--augmentation', type=str, default='strong', choices=['none', 'light', 'medium', 'strong'], help='Augmentation level')
    train_parser.add_argument('--num-workers', type=int, default=4, help='Number of workers for data loading')
    train_parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    # Model arguments
    train_parser.add_argument('--base-filters', type=int, default=32, help='Number of base filters')
    train_parser.add_argument('--is-3d', action='store_true', help='Use 3D convolutions')
    train_parser.add_argument('--num-experts', type=int, default=4, help='Number of experts')
    train_parser.add_argument('--dropout', type=float, default=0.1, help='Dropout rate')
    train_parser.add_argument('--stochastic-depth', type=float, default=0.1, help='Stochastic depth rate')
    train_parser.add_argument('--deep-supervision', action='store_true', help='Use deep supervision')
    train_parser.add_argument('--use-attention', action='store_true', help='Use attention gates')
    
    # Loss function arguments
    train_parser.add_argument('--heatmap-weight', type=float, default=0.5, help='Weight for heatmap loss')
    train_parser.add_argument('--boundary-weight', type=float, default=0.2, help='Weight for boundary loss')
    train_parser.add_argument('--distance-weight', type=float, default=0.2, help='Weight for distance loss')
    train_parser.add_argument('--moe-weight', type=float, default=0.1, help='Weight for MoE loss')
    train_parser.add_argument('--pos-weight', type=float, default=2.0, help='Positive class weight')
    train_parser.add_argument('--dice-weight', type=float, default=0.5, help='Weight for dice loss')
    train_parser.add_argument('--focal-gamma', type=float, default=2.0, help='Gamma for focal loss')
    train_parser.add_argument('--learn-weights', action='store_true', help='Learn loss weights')
    train_parser.add_argument('--density-aware-weighting', action='store_true', help='Use density-aware weighting')
    train_parser.add_argument('--size-adaptive-weighting', action='store_true', help='Use size-adaptive weighting')
    
    # Evaluation arguments
    eval_parser = subparsers.add_parser('evaluate', parents=[common_parser], help='Evaluate the model')
    eval_parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    eval_parser.add_argument('--data-dir', type=str, default=None, help='Path to data directory')
    eval_parser.add_argument('--batch-size', type=int, default=8, help='Batch size')
    eval_parser.add_argument('--synthetic', action='store_true', help='Use synthetic data')
    eval_parser.add_argument('--synthetic-size', type=int, default=100, help='Number of synthetic samples')
    eval_parser.add_argument('--num-workers', type=int, default=4, help='Number of workers for data loading')
    eval_parser.add_argument('--base-filters', type=int, default=32, help='Number of base filters')
    eval_parser.add_argument('--is-3d', action='store_true', help='Use 3D convolutions')
    eval_parser.add_argument('--num-experts', type=int, default=4, help='Number of experts')
    eval_parser.add_argument('--heatmap-weight', type=float, default=0.5, help='Weight for heatmap loss')
    eval_parser.add_argument('--boundary-weight', type=float, default=0.2, help='Weight for boundary loss')
    eval_parser.add_argument('--distance-weight', type=float, default=0.2, help='Weight for distance loss')
    eval_parser.add_argument('--moe-weight', type=float, default=0.1, help='Weight for MoE loss')
    
    # Prediction arguments
    pred_parser = subparsers.add_parser('predict', parents=[common_parser], help='Run inference with the model')
    pred_parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    pred_parser.add_argument('--input', type=str, required=True, help='Path to input image or directory')
    pred_parser.add_argument('--output', type=str, default=None, help='Path to output directory')
    pred_parser.add_argument('--min-size', type=int, default=2, help='Minimum spot size in pixels')
    pred_parser.add_argument('--min-distance', type=int, default=2, help='Minimum distance between spots')
    
    args = parser.parse_args()
    
    # Run the appropriate mode
    if args.mode == 'train':
        train(args)
    elif args.mode == 'evaluate':
        evaluate(args)
    elif args.mode == 'predict':
        predict(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()