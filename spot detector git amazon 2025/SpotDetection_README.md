# Optimized Spot Detection with Mixture of Experts

This repository provides a comprehensive solution for spot detection in both 2D and 3D images using a Mixture of Experts (MoE) approach. It's specifically designed to handle spots of varying sizes and densities, with support for sparse annotations through semi-supervised learning.

## Features

- **Mixture of Experts Architecture**: Specialized experts for different spot types (small, medium, large, dense)
- **Optimized Loss Function**: Combines multiple components with GPU acceleration
- **Sparse Annotation Handling**: Confidence-weighted loss and semi-supervised learning
- **2D and 3D Support**: Works with both 2D and 3D images
- **Advanced Techniques**: Test-time augmentation and model ensembling
- **Memory Optimization**: Gradient checkpointing and efficient implementations

## Repository Structure

- **SpotDetection.ipynb**: Main notebook that imports all parts
- **SpotDetection_Part1.ipynb**: Setup, dependencies, and data loading
- **SpotDetection_Part2.ipynb**: Model and loss function implementation
- **SpotDetection_Part3.ipynb**: Training pipeline
- **SpotDetection_Part4.ipynb**: Evaluation and advanced techniques
- **SpotDetection_Documentation.md**: Detailed documentation about the model and loss function
- **SpotDetection_Instructions.md**: General instructions for using the notebook
- **Part*_Instructions.md**: Specific instructions for each notebook part

## Getting Started

### Prerequisites

- Python 3.6+
- PyTorch 1.7+
- CUDA-capable GPU (recommended)
- Required packages: torch, torchvision, albumentations, scikit-image, tqdm, matplotlib, opencv-python

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/spot-detection.git
cd spot-detection

# Install dependencies
pip install -r requirements.txt
```

### Quick Start

1. Open the main notebook:
```bash
jupyter notebook SpotDetection.ipynb
```

2. Follow the instructions in each section to:
   - Load and prepare your data
   - Configure and initialize the model
   - Train the model
   - Evaluate and make predictions

## Working with 2D Images

For 2D images (default configuration):

```python
# Configuration for 2D images
MODEL_CONFIG = {
    'in_channels': 1,
    'base_filters': 32,
    'is_3d': False,  # Set to False for 2D images
    'num_experts': 4,
    'dropout': 0.1,
    'stochastic_depth': 0.1,
    'deep_supervision': True,
    'use_attention': True,
    'specialization_weight': 0.1
}
```

## Working with 3D Images

For 3D images, make these adjustments:

```python
# Configuration for 3D images
MODEL_CONFIG = {
    'in_channels': 1,
    'base_filters': 16,  # Reduced for memory efficiency
    'is_3d': True,  # Set to True for 3D images
    'num_experts': 4,
    'dropout': 0.2,  # Increased for better regularization
    'stochastic_depth': 0.2,
    'deep_supervision': True,
    'use_attention': True,
    'specialization_weight': 0.1
}

# Training configuration for 3D
BATCH_SIZE = 2  # Smaller batch size for 3D
LEARNING_RATE = 5e-4  # Lower learning rate for stability
NUM_EPOCHS = 50  # Fewer epochs due to longer iteration time
```

## Handling Sparse Annotations

For datasets with sparse annotations:

1. Provide confidence masks if available (with `_conf` suffix)
2. Enable semi-supervised learning:

```python
# Create trainer with semi-supervised learning
trainer = SparseSpotTrainer(
    # ... other parameters ...
    semi_supervised=True,
    confidence_threshold=0.9,
    update_interval=5
)
```

## Model Architecture

The model uses a Mixture of Experts approach with:

1. **Encoder-Decoder Backbone**: U-Net-like architecture with skip connections
2. **Mixture of Experts**: Four specialized experts for different spot types
3. **Router Network**: Determines which expert(s) to use for each input
4. **Cross-Scale Attention**: Integrates features across multiple scales
5. **Output Heads**: Multiple heads for heatmap, boundary, distance, etc.

For more details, see [SpotDetection_Documentation.md](SpotDetection_Documentation.md).

## Loss Function

The loss function combines multiple components:

1. **Heatmap Loss**: Focal BCE loss for spot detection
2. **Dice Loss**: For better handling of class imbalance
3. **Boundary Loss**: For improved spot separation
4. **Distance Transform Loss**: For shape awareness
5. **Contrastive Loss**: To better separate touching spots
6. **MoE Loss**: For balanced expert utilization

## Advanced Techniques

### Test-time Augmentation (TTA)

```python
# Predict with TTA
tta_outputs = predict_with_tta(model, image, num_augmentations=4)
```

### Model Ensembling

```python
# Create and train ensemble
ensemble_models = create_model_ensemble(num_models=3)
# ...

# Predict with ensemble
ensemble_outputs = predict_with_ensemble(ensemble_models, image)
```

## Citation

If you use this code in your research, please cite:

```
@article{optimized_spot_detection,
  title={Optimized Spot Detection with Mixture of Experts},
  author={Your Name},
  journal={arXiv preprint arXiv:XXXX.XXXXX},
  year={2023}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- The model architecture is inspired by U-Net and Mixture of Experts approaches
- The loss function incorporates ideas from focal loss, dice loss, and contrastive learning
- The semi-supervised learning approach is inspired by self-training methods