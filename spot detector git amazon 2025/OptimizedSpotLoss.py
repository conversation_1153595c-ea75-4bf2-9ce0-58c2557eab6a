import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Union, Tuple

class OptimizedSpotLoss(nn.Module):
    """Loss function that handles masks with consistent orientation"""
    
    def __init__(self,
                 bce_weight: float = 1.0,
                 dice_weight: float = 1.0,
                 focal_weight: float = 0.5,
                 focal_gamma: float = 1.0,
                 size_adaptive: bool = True,
                 density_aware: bool = True,
                 confidence_weighted: bool = True):
        """
        Initialize the loss function
        
        Args:
            bce_weight: Weight for BCE loss
            dice_weight: Weight for Dice loss
            focal_weight: Weight for Focal loss
            focal_gamma: Gamma parameter for Focal loss
            size_adaptive: Whether to use size-adaptive weighting
            density_aware: Whether to use density-aware weighting
            confidence_weighted: Whether to use confidence-weighted loss
        """
        super().__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.focal_weight = focal_weight
        self.focal_gamma = focal_gamma
        self.size_adaptive = size_adaptive
        self.density_aware = density_aware
        self.confidence_weighted = confidence_weighted
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor, 
                confidence: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Calculate loss while maintaining consistent orientation"""
        # Ensure inputs have shape [B, C, H, W] or [B, C, D, H, W]
        if len(pred.shape) == 3:
            pred = pred.unsqueeze(1)
        if len(target.shape) == 3:
            target = target.unsqueeze(1)
        if confidence is not None and len(confidence.shape) == 3:
            confidence = confidence.unsqueeze(1)
            
        # Calculate standard losses
        bce_loss = self._binary_cross_entropy(pred, target, confidence)
        dice_loss = self._dice_loss(pred, target, confidence)
        focal_loss = self._focal_loss(pred, target, confidence)
        
        # Apply density-aware weighting if enabled
        if self.density_aware:
            density_weights = self._calculate_density_weights(target)
            bce_loss = bce_loss * density_weights.to(bce_loss.device)
            dice_loss = dice_loss * density_weights.to(dice_loss.device)
            focal_loss = focal_loss * density_weights.to(focal_loss.device)
        
        # Apply size-adaptive weighting if enabled
        if self.size_adaptive:
            size_weights = self._calculate_size_weights(target)
            bce_loss = bce_loss * size_weights.to(bce_loss.device)
            dice_loss = dice_loss * size_weights.to(dice_loss.device)
            focal_loss = focal_loss * size_weights.to(focal_loss.device)
        
        # Combine losses with weights
        total_loss = (
            self.bce_weight * bce_loss + 
            self.dice_weight * dice_loss + 
            self.focal_weight * focal_loss
        )
        
        # Return as dictionary to match expected format in trainer
        return {'loss': total_loss, 'bce': bce_loss, 'dice': dice_loss, 'focal': focal_loss}
    
    def _binary_cross_entropy(self, pred: torch.Tensor, target: torch.Tensor,
                            confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate BCE loss with consistent orientation handling"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Calculate BCE with reduction='none' to apply weighting
        bce = F.binary_cross_entropy(pred_sig, target, reduction='none')
        
        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            bce = bce * confidence
            
        return bce.mean()
    
    def _dice_loss(self, pred: torch.Tensor, target: torch.Tensor,
                   confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate Dice loss with consistent orientation handling"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Calculate intersection and union
        intersection = (pred_sig * target).sum(dim=(-2, -1))
        if len(pred.shape) == 5:  # 3D data
            intersection = intersection.sum(dim=-1)
            
        union = pred_sig.sum(dim=(-2, -1)) + target.sum(dim=(-2, -1))
        if len(pred.shape) == 5:  # 3D data
            union = union.sum(dim=-1)
            
        # Calculate Dice coefficient
        dice = (2.0 * intersection + 1e-6) / (union + 1e-6)
        
        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            conf_sum = confidence.sum(dim=(-2, -1))
            if len(pred.shape) == 5:
                conf_sum = conf_sum.sum(dim=-1)
            # Use where instead of direct multiplication with boolean
            dice = torch.where(conf_sum > 0, dice, torch.zeros_like(dice))
            
        return 1.0 - dice.mean()
    
    def _focal_loss(self, pred: torch.Tensor, target: torch.Tensor,
                    confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate Focal loss with consistent orientation handling"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Calculate focal weights
        pt = target * pred_sig + (1 - target) * (1 - pred_sig)
        focal_weights = (1 - pt) ** self.focal_gamma
        
        # Calculate BCE
        bce = F.binary_cross_entropy(pred_sig, target, reduction='none')
        
        # Apply focal weights
        focal = focal_weights * bce
        
        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            focal = focal * confidence
            
        return focal.mean()
    
    def _calculate_density_weights(self, target: torch.Tensor) -> torch.Tensor:
        """Calculate density-aware weights with consistent orientation"""
        # Calculate spot density per image
        if len(target.shape) == 4:  # 2D data
            density = target.sum(dim=(-2, -1), keepdim=True)
        else:  # 3D data
            density = target.sum(dim=(-3, -2, -1), keepdim=True)
            
        # Normalize weights (add small epsilon to avoid division issues)
        weights = 1.0 / (torch.sqrt(density + 1e-8) + 1.0)
        return weights
    
    def _calculate_size_weights(self, target: torch.Tensor) -> torch.Tensor:
        """Calculate size-adaptive weights with consistent orientation"""
        # Calculate average spot size
        if len(target.shape) == 4:  # 2D data
            # Use float casting to ensure proper gradient computation
            num_spots = (target > 0.5).float().sum(dim=(-2, -1), keepdim=True)
            total_area = float(target.shape[-2] * target.shape[-1])
        else:  # 3D data
            num_spots = (target > 0.5).float().sum(dim=(-3, -2, -1), keepdim=True)
            total_area = float(target.shape[-3] * target.shape[-2] * target.shape[-1])
            
        # Calculate average spot size (add small epsilon to avoid division by zero)
        avg_size = total_area / (num_spots + 1e-8)
        
        # Normalize weights with epsilon for numerical stability
        weights = torch.sqrt((avg_size + 1e-8) / (total_area + 1e-8))
        return weights