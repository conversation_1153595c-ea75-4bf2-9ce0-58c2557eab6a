import torch
import torch.nn as nn
import torch.nn.functional as F

class ConvBlock(nn.Module):
    """Simplified convolutional block"""
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, padding=padding)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.relu(self.conv(x))

class OptimizedSpotDetectionModel_Lite(nn.Module):
    """Memory-optimized spot detection model"""
    def __init__(self, in_channels=1, base_filters=32):
        super().__init__()
        
        # Encoder (smaller)
        self.enc1 = ConvBlock(in_channels, base_filters)
        self.enc2 = ConvBlock(base_filters, base_filters * 2)
        self.enc3 = ConvBlock(base_filters * 2, base_filters * 4)
        
        # Pooling
        self.pool = nn.MaxPool2d(2)
        
        # Decoder (smaller)
        self.up1 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.dec1 = ConvBlock(base_filters * 4, base_filters * 2)
        self.up2 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.dec2 = ConvBlock(base_filters * 2, base_filters)
        
        # Output
        self.outc = nn.Conv2d(base_filters, 1, kernel_size=1)
    
    def forward(self, x):
        # Encoder
        x1 = self.enc1(x)
        x2 = self.enc2(self.pool(x1))
        x3 = self.enc3(self.pool(x2))
        
        # Decoder
        x = self.dec1(self.up1(x3))
        x = self.dec2(self.up2(x))
        
        # Output with sigmoid
        x = self.outc(x)
        x = torch.sigmoid(x)
        
        return x