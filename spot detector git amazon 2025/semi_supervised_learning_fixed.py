def semi_supervised_learning(model, train_dataset, val_dataset, config):
    """
    Perform semi-supervised learning with TensorBoard logging and model checkpointing
    
    Args:
        model: The model to train
        train_dataset: Training dataset
        val_dataset: Validation dataset
        config: Dictionary containing training configuration:
            - num_ssl_iterations: Number of semi-supervised learning cycles
            - epochs_per_iteration: Number of epochs to train in each cycle
            - batch_size: Number of samples per batch
            - confidence_threshold: Threshold for selecting high-confidence predictions
            - learning_rate: Learning rate for optimizer
            - early_stopping_patience: Number of epochs to wait for improvement
    """
    # Create TensorBoard writer for this run
    writer = SummaryWriter(os.path.join(TENSORBOARD_DIR, 'semi_supervised'))
    
    # Keep track of best metrics
    best_val_metrics = None
    best_model_path = None
    best_loss = float('inf')
    
    total_steps = 0  # Keep track of total training steps
    steps_per_epoch = len(train_dataset) // config['batch_size']
    
    for iteration in range(config['num_ssl_iterations']):
        print(f'\nSemi-supervised learning iteration {iteration+1}/{config["num_ssl_iterations"]}')
        print(f'Current training set size: {len(train_dataset)} samples')
        
        # Create predictor
        predictor = SpotDetectionPredictor(
            model=model,
            device=device,
            threshold=0.5,
            min_spot_size=1
        )
        
        # Make predictions on training set
        train_loader = DataLoader(
            train_dataset,
            batch_size=config['batch_size'],
            shuffle=False,
            num_workers=NUM_WORKERS
        )
        
        # Collect predictions
        all_images = []
        all_preds = []
        
        model.eval()
        with torch.no_grad():
            for batch in tqdm(train_loader, desc="Making predictions"):
                images = batch['image'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Model now returns the heatmap directly (not a dictionary)
                pred = outputs
                
                # Add to lists
                all_images.extend([img for img in batch['image']])
                all_preds.extend([p for p in pred])
        
        # Update dataset with high-confidence predictions
        train_dataset.update_with_predictions(all_images, all_preds, threshold=config['confidence_threshold'])
        
        # Create new data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=config['batch_size'],
            shuffle=True,
            num_workers=NUM_WORKERS,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=config['batch_size'],
            shuffle=False,
            num_workers=NUM_WORKERS,
            pin_memory=True
        )
        
        # Train for this iteration
        print(f'Training for {config["epochs_per_iteration"]} epochs ({steps_per_epoch} steps per epoch)')
        trainer = SpotDetectionTrainer(
            model=model,
            loss_fn=loss_fn,
            optimizer=optimizer,
            device=device,
            metrics_calculator=metrics_calculator,
            scheduler=scheduler
        )
        
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=config['epochs_per_iteration'],
            early_stopping_patience=config['early_stopping_patience'],
            save_best_model=True,
            model_save_path=os.path.join(RUN_DIR, f'model_iter{iteration+1}_best.pth')
        )
        
        total_steps += steps_per_epoch * config['epochs_per_iteration']
        
        # Log training progress
        writer.add_scalar('progress/total_steps', total_steps, iteration)
        writer.add_scalar('progress/dataset_size', len(train_dataset), iteration)
        
        # Evaluate
        val_metrics = trainer.validate(val_loader)
        print(f"Iteration {iteration+1} Validation Metrics:")
        for k, v in val_metrics.items():
            print(f"{k}: {v:.4f}")
        print()
        
        # Update best model if validation loss improves
        val_loss = val_metrics.get('loss', float('inf'))
        if val_loss < best_loss:
            best_loss = val_loss
            best_val_metrics = val_metrics
            best_model_path = os.path.join(RUN_DIR, f'model_iter{iteration+1}_best.pth')
    
    writer.close()
    return model, best_model_path