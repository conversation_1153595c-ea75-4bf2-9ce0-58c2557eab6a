# Part 2: Model and Loss Function Instructions

## Overview

This section implements the model architecture and loss function for spot detection. The implementation supports both 2D and 3D images and includes enhancements for handling sparse annotations and improving spot separation.

## Model Architecture

### Mixture of Experts (MoE) Approach

The model uses a Mixture of Experts approach with specialized experts for different spot types:

1. **Expert 1 (Small Spots)**: Uses small kernels (3×3) for fine detail detection
2. **Expert 2 (Medium Spots)**: Uses medium kernels (5×5) for balanced detection
3. **Expert 3 (Large Spots)**: Uses large kernels (7×7) for capturing larger structures
4. **Expert 4 (Dense Regions)**: Uses dilated convolutions to handle overlapping spots

### 2D vs 3D Configuration

The model automatically adapts to 2D or 3D inputs based on the `is_3d` parameter:

```python
# For 2D images (default)
MODEL_CONFIG = {
    'in_channels': 1,
    'base_filters': 32,
    'is_3d': False,  # Set to False for 2D images
    'num_experts': 4,
    'dropout': 0.1,
    'stochastic_depth': 0.1,
    'deep_supervision': True,
    'use_attention': True,
    'specialization_weight': 0.1
}

# For 3D images
MODEL_CONFIG_3D = {
    'in_channels': 1,
    'base_filters': 16,  # Reduced for memory efficiency
    'is_3d': True,  # Set to True for 3D images
    'num_experts': 4,
    'dropout': 0.2,  # Increased for better regularization
    'stochastic_depth': 0.2,
    'deep_supervision': True,
    'use_attention': True,
    'specialization_weight': 0.1
}
```

### Memory Optimization for 3D

For 3D images, consider these memory optimizations:

1. **Reduce Base Filters**: Start with fewer base filters (16 instead of 32)
2. **Increase Dropout**: Use higher dropout rates (0.2 instead of 0.1)
3. **Enable Gradient Checkpointing**: Reduces memory usage during training
4. **Smaller Batch Size**: Use batch size of 1 or 2 for large 3D volumes

## Loss Function

### Components

The loss function combines multiple components:

1. **Heatmap Loss**: Focal BCE loss for spot detection
2. **Dice Loss**: For better handling of class imbalance
3. **Boundary Loss**: For improved spot separation
4. **Distance Transform Loss**: For shape awareness
5. **Contrastive Loss**: To better separate touching spots
6. **MoE Loss**: For balanced expert utilization

### Configuration

Adjust loss function parameters based on your dataset:

```python
# Default configuration
LOSS_CONFIG = {
    'heatmap_weight': 0.5,
    'boundary_weight': 0.2,
    'distance_weight': 0.2,
    'moe_weight': 0.1,
    'contrastive_weight': 0.1,
    'pos_weight': 2.0,
    'dice_weight': 0.5,
    'focal_gamma': 2.0,
    'learn_weights': True,
    'density_aware_weighting': True,
    'size_adaptive_weighting': True,
    'num_experts': 4
}

# For datasets with many small spots
LOSS_CONFIG_SMALL_SPOTS = {
    'heatmap_weight': 0.5,
    'boundary_weight': 0.3,  # Increased for better separation
    'distance_weight': 0.2,
    'moe_weight': 0.1,
    'contrastive_weight': 0.2,  # Increased for better separation
    'pos_weight': 3.0,  # Increased for better small spot detection
    'dice_weight': 0.5,
    'focal_gamma': 2.0,
    'learn_weights': True,
    'density_aware_weighting': True,
    'size_adaptive_weighting': True,
    'num_experts': 4
}

# For datasets with dense regions
LOSS_CONFIG_DENSE_REGIONS = {
    'heatmap_weight': 0.4,
    'boundary_weight': 0.3,  # Increased for better separation
    'distance_weight': 0.3,  # Increased for better separation
    'moe_weight': 0.1,
    'contrastive_weight': 0.2,  # Increased for better separation
    'pos_weight': 2.0,
    'dice_weight': 0.6,  # Increased for better region handling
    'focal_gamma': 2.0,
    'learn_weights': True,
    'density_aware_weighting': True,
    'size_adaptive_weighting': True,
    'num_experts': 4
}
```

### Handling Sparse Annotations

The `SparseOptimizedSpotLoss` class handles sparse annotations by:

1. **Confidence Weighting**: Weights loss based on annotation confidence
2. **Ignore Regions**: Low-confidence predictions in unannotated regions are ignored

For very sparse annotations, consider:

1. **Increasing Positive Weight**: Set `pos_weight` to 3.0 or higher
2. **Reducing Boundary Weight**: Set `boundary_weight` to 0.1 initially
3. **Starting with Lower Learning Rate**: Use 5e-4 instead of 1e-3

## Expert Specialization

The model includes expert specialization to encourage each expert to focus on specific spot types:

1. **Small Spots Expert**: Trained on spots smaller than the 33rd percentile
2. **Medium Spots Expert**: Trained on spots between 33rd and 67th percentiles
3. **Large Spots Expert**: Trained on spots larger than the 67th percentile
4. **Dense Regions Expert**: Trained on spots in dense regions (close to other spots)

To adjust expert specialization:

```python
# Modify these parameters in the compute_expert_specialization_loss method
small_thresh = np.percentile(areas, 33)  # Threshold for small spots
large_thresh = np.percentile(areas, 67)  # Threshold for large spots
close_distance = 20  # Threshold for "close" spots in dense regions
```

## GPU Acceleration

The implementation uses GPU acceleration for all operations:

1. **Boundary Detection**: Uses PyTorch operations instead of scipy
2. **Distance Transform**: Uses iterative max pooling instead of scipy
3. **Connected Components**: Still uses CPU but minimizes data transfers

For very large 3D volumes, consider:

1. **Slice-by-Slice Processing**: Process 3D volumes slice by slice
2. **Mixed Precision Training**: Enable AMP for faster training
3. **Model Parallelism**: Split model across multiple GPUs

## Next Steps

After implementing the model and loss function, proceed to Part 3 for training setup.