import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint

class ConvBlock(nn.Module):
    """Enhanced convolutional block with modern activations and regularization"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1,
                is_3d=False, use_batchnorm=True, dropout=0.0, activation='silu'):
        super().__init__()
        
        Conv = nn.Conv3d if is_3d else nn.Conv2d
        BatchNorm = nn.BatchNorm3d if is_3d else nn.BatchNorm2d
        Dropout = nn.Dropout3d if is_3d else nn.Dropout2d
        
        # Select activation function
        if activation == 'silu':
            act_fn = nn.SiLU(inplace=True)  # More modern than ReLU
        elif activation == 'mish':
            act_fn = nn.Mish(inplace=True)  # Another good alternative
        else:
            act_fn = nn.ReLU(inplace=True)  # Default fallback
            
        layers = [
            Conv(in_channels, out_channels, kernel_size, stride, padding, bias=not use_batchnorm)
        ]
        
        if use_batchnorm:
            layers.append(BatchNorm(out_channels))
        
        layers.append(act_fn)
        
        # Add spatial dropout for regularization
        if dropout > 0:
            layers.append(Dropout(dropout))
        
        self.conv_block = nn.Sequential(*layers)
        
    def forward(self, x):
        return self.conv_block(x)


class SEBlock(nn.Module):
    """Squeeze-and-Excitation block for channel attention"""
    def __init__(self, channels, reduction=16, is_3d=False):
        super().__init__()
        
        AdaptiveAvgPool = nn.AdaptiveAvgPool3d if is_3d else nn.AdaptiveAvgPool2d
        
        self.avg_pool = AdaptiveAvgPool(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.SiLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
        self.is_3d = is_3d
        
    def forward(self, x):
        b, c = x.size()[:2]
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y)
        
        # Safe implementation for both compiled and non-compiled modes
        if self.is_3d:
            y_view = y.view(b, c, 1, 1, 1)
        else:
            y_view = y.view(b, c, 1, 1)
        
        return x * y_view


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample - checkpoint-compatible version"""
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob
        self.keep_prob = 1 - drop_prob
        # Store last random tensor and its shape for deterministic recomputation
        self.register_buffer('random_tensor', None, persistent=False)
        self.register_buffer('last_input_shape', None, persistent=False)

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        
        # Get shape
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        
        # Use saved tensor when input shape is the same (for checkpointing)
        if (hasattr(self, 'random_tensor') and 
            self.random_tensor is not None and
            self.last_input_shape is not None and
            torch.all(torch.tensor(shape) == self.last_input_shape)):
            random_tensor = self.random_tensor
        else:
            # Generate new tensor with fixed seed based on portion of input
            # This makes it deterministic for checkpointing
            if x.is_cuda:
                # Use part of input as seed influence
                seed_influence = (x[0, 0].mean() * 1000).item() if x.numel() > 0 else 0
                seed = int(torch.randint(0, 2**31 - 1, (1,)).item() + seed_influence) % (2**31 - 1)
                torch.cuda.manual_seed(seed)
            
            # Generate the random tensor
            random_tensor = self.keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
            random_tensor.floor_()  # binarize
            
            # Save for reuse
            self.random_tensor = random_tensor
            self.last_input_shape = torch.tensor(shape)
        
        # Apply drop path
        output = x.div(self.keep_prob) * random_tensor
        return output


class ResidualBlock(nn.Module):
    """Enhanced residual block with SE attention and stochastic depth - checkpoint compatible"""
    def __init__(self, channels, is_3d=False, dropout=0.1, use_se=True, reduction=16):
        super().__init__()
        
        self.conv1 = ConvBlock(channels, channels, is_3d=is_3d, dropout=dropout)
        self.conv2 = ConvBlock(channels, channels, is_3d=is_3d)
        
        # Add SE attention
        self.se = SEBlock(channels, reduction=reduction, is_3d=is_3d) if use_se else nn.Identity()
        
        # Stochastic depth (random dropping of layers during training)
        self.drop_path = DropPath(dropout) if dropout > 0 else nn.Identity()
        self.has_dropout = dropout > 0
        
    def forward(self, x):
        residual = x
        out = self.conv1(x)
        out = self.conv2(out)
        out = self.se(out)
        
        # Apply drop path if enabled
        if self.has_dropout and self.training:
            out = self.drop_path(out)
        
        # Add residual connection
        return residual + out


class ExpertBlock(nn.Module):
    """Expert block for different spot types/scales"""
    def __init__(self, channels, is_3d=False, kernel_size=3, dilation=1):
        super().__init__()
        
        Conv = nn.Conv3d if is_3d else nn.Conv2d
        BatchNorm = nn.BatchNorm3d if is_3d else nn.BatchNorm2d
        
        padding = kernel_size//2 if dilation == 1 else dilation
        
        # Enhanced expert block with optional dilation for different receptive fields
        self.conv1 = nn.Sequential(
            Conv(channels, channels, kernel_size=kernel_size, padding=padding, dilation=dilation),
            BatchNorm(channels),
            nn.SiLU(inplace=True)
        )
        self.conv2 = nn.Sequential(
            Conv(channels, channels, kernel_size=kernel_size, padding=padding, dilation=dilation),
            BatchNorm(channels),
            nn.SiLU(inplace=True)
        )
        
        # Add SE attention for better channel adaptation
        self.se = SEBlock(channels, reduction=8, is_3d=is_3d)
        
    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.se(x)  # Apply SE attention
        return x


class AttentionGate(nn.Module):
    """Enhanced Attention Gate with improved spatial context"""
    def __init__(self, x_channels, g_channels, inter_channels=None, use_bn=True, use_checkpointing=False):
        super().__init__()
        
        # Auto-determine intermediate channels if not specified
        if inter_channels is None:
            inter_channels = max(1, x_channels // 2)
            
        # Feature transformation with optional batch norm
        self.W_x = nn.Conv2d(x_channels, inter_channels, kernel_size=1, bias=not use_bn)
        self.W_g = nn.Conv2d(g_channels, inter_channels, kernel_size=1)
        
        # Optional batch normalization
        self.bn_x = nn.BatchNorm2d(inter_channels) if use_bn else nn.Identity()
        self.bn_g = nn.BatchNorm2d(inter_channels) if use_bn else nn.Identity()
        
        # Attention coefficient with larger kernel for better spatial context
        self.psi = nn.Sequential(
            nn.Conv2d(inter_channels, inter_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(inter_channels // 2) if use_bn else nn.Identity(),
            nn.SiLU(inplace=True),  # Switched to SiLU for better gradient properties
            nn.Conv2d(inter_channels // 2, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Learnable scale parameter with improved initialization
        self.scale = nn.Parameter(torch.ones(1) * 0.5)  # Initialize at 0.5 for stable training
        
        # Option to use checkpointing for memory efficiency
        self.use_checkpointing = use_checkpointing
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize weights using Kaiming initialization"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def _attention_block(self, x1, g):
        """Attention computation block that can be checkpointed"""
        # Always interpolate g to match x's spatial dimensions
        g_resized = F.interpolate(g, size=x1.shape[2:], mode='bilinear', align_corners=False)
        g1 = self.bn_g(self.W_g(g_resized))
        
        # Combine and generate attention map
        out = F.silu(x1 + g1)  # Use SiLU for better gradient properties
        attention = self.psi(out)
        return attention
        
    def forward(self, x, g):
        # Process input feature map
        x1 = self.bn_x(self.W_x(x))
        
        # Use checkpointing for memory efficiency if enabled
        if self.use_checkpointing and self.training:
            attention = torch.utils.checkpoint.checkpoint(
                self._attention_block, x1, g, use_reentrant=False
            )
        else:
            attention = self._attention_block(x1, g)
        
        # Apply attention with learnable scale and residual connection for better gradient flow
        return x * (attention * self.scale + 1.0)


class AttentionGate3D(nn.Module):
    """Enhanced 3D Attention Gate with improved spatial context"""
    def __init__(self, x_channels, g_channels, inter_channels=None, use_bn=True, use_checkpointing=True):
        super().__init__()
        
        # Auto-determine intermediate channels if not specified
        if inter_channels is None:
            inter_channels = max(1, x_channels // 2)
            
        # Feature transformation with optional batch norm
        self.W_x = nn.Conv3d(x_channels, inter_channels, kernel_size=1, bias=not use_bn)
        self.W_g = nn.Conv3d(g_channels, inter_channels, kernel_size=1)
        
        # Optional batch normalization
        self.bn_x = nn.BatchNorm3d(inter_channels) if use_bn else nn.Identity()
        self.bn_g = nn.BatchNorm3d(inter_channels) if use_bn else nn.Identity()
        
        # Attention coefficient with larger kernel for better spatial context
        self.psi = nn.Sequential(
            nn.Conv3d(inter_channels, inter_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm3d(inter_channels // 2) if use_bn else nn.Identity(),
            nn.SiLU(inplace=True),  # Switched to SiLU for better gradient properties
            nn.Conv3d(inter_channels // 2, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Learnable scale parameter with improved initialization
        self.scale = nn.Parameter(torch.ones(1) * 0.5)  # Initialize at 0.5 for stable training
        
        # Enable checkpointing by default for 3D to save memory
        self.use_checkpointing = use_checkpointing
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize weights using Kaiming initialization"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def _attention_block(self, x1, g):
        """Attention computation block that can be checkpointed"""
        # Use x1.shape[2:] to get all spatial dimensions at once
        g_resized = F.interpolate(g, size=x1.shape[2:], mode='trilinear', align_corners=False)
        g1 = self.bn_g(self.W_g(g_resized))
        
        # Combine and generate attention map
        out = F.silu(x1 + g1)  # Use SiLU for better gradient properties
        attention = self.psi(out)
        return attention
        
    def forward(self, x, g):
        # Process input feature map
        x1 = self.bn_x(self.W_x(x))
        
        # Use checkpointing for memory efficiency in 3D (enabled by default)
        if self.use_checkpointing and self.training:
            attention = torch.utils.checkpoint.checkpoint(
                self._attention_block, x1, g, use_reentrant=False
            )
        else:
            attention = self._attention_block(x1, g)
        
        # Apply attention with learnable scale and residual connection for better gradient flow
        return x * (attention * self.scale + 1.0)


class CrossScaleAttention(nn.Module):
    """Enhanced cross-scale attention for better multi-scale feature integration"""
    def __init__(self, channels, is_3d=False):
        super().__init__()
        
        Conv = nn.Conv3d if is_3d else nn.Conv2d
        BatchNorm = nn.BatchNorm3d if is_3d else nn.BatchNorm2d
        
        # Improved attention gates with consistent channel handling
        self.attention_gates = nn.ModuleList([
            Conv(channels, 1, kernel_size=1),           # For x (x2_up)
            Conv(channels * 2, 1, kernel_size=1),       # For x_half (x_moe) 
            Conv(channels * 2, 1, kernel_size=1)        # For x_quarter (x3)
        ])
        
        # Enhanced scale-specific transformations for better feature extraction
        self.scale_transforms = nn.ModuleList([
            nn.Sequential(
                Conv(channels, channels // 2, kernel_size=1),
                BatchNorm(channels // 2),
                nn.SiLU(inplace=True)
            ),
            nn.Sequential(
                Conv(channels * 2, channels // 2, kernel_size=1),
                BatchNorm(channels // 2),
                nn.SiLU(inplace=True)
            ),
            nn.Sequential(
                Conv(channels * 2, channels // 2, kernel_size=1),
                BatchNorm(channels // 2),
                nn.SiLU(inplace=True)
            )
        ])
        
        # Enhanced cross-scale attention with residual connection
        self.cross_attention = nn.Sequential(
            Conv(channels * 3 // 2, channels, kernel_size=1),
            BatchNorm(channels),
            nn.SiLU(inplace=True),
            Conv(channels, channels, kernel_size=3, padding=1),
            BatchNorm(channels),
            nn.SiLU(inplace=True)
        )
        
        # Add SE attention for channel recalibration
        self.se = SEBlock(channels, reduction=8, is_3d=is_3d)
        
    def forward(self, x, x_half, x_quarter):
        # Upsample smaller scales with appropriate interpolation mode
        interp_mode = 'trilinear' if x.dim() == 5 else 'bilinear'
        x_half_up = F.interpolate(x_half, size=x.shape[2:], mode=interp_mode, align_corners=False)
        x_quarter_up = F.interpolate(x_quarter, size=x.shape[2:], mode=interp_mode, align_corners=False)
        
        # Apply attention gates with improved sigmoid scaling
        att1 = torch.sigmoid(self.attention_gates[0](x))
        att2 = torch.sigmoid(self.attention_gates[1](x_half_up))
        att3 = torch.sigmoid(self.attention_gates[2](x_quarter_up))
        
        # Apply attention and transform with improved channel transformation
        x_att = self.scale_transforms[0](x * att1)
        x_half_att = self.scale_transforms[1](x_half_up * att2)
        x_quarter_att = self.scale_transforms[2](x_quarter_up * att3)
        
        # Concatenate and apply cross-scale attention
        fused = torch.cat([x_att, x_half_att, x_quarter_att], dim=1)
        refined = self.cross_attention(fused)
        
        # Apply SE attention for better channel-wise calibration
        refined = self.se(refined)
        
        # Add residual connection for better gradient flow
        return refined + x  # Add residual connection to original input


class DownBlock(nn.Module):
    """Enhanced downsampling block with dropout and residual connections"""
    def __init__(self, in_channels, out_channels, is_3d=False, dropout=0.0):
        super().__init__()
        
        Pool = nn.MaxPool3d if is_3d else nn.MaxPool2d
        
        self.pool = Pool(kernel_size=2, stride=2)
        self.conv = ConvBlock(in_channels, out_channels, is_3d=is_3d, dropout=dropout/2)
        self.residual = ResidualBlock(out_channels, is_3d=is_3d, dropout=dropout)
        
    def forward(self, x):
        x = self.pool(x)
        x = self.conv(x)
        x = self.residual(x)
        return x


class UpBlock(nn.Module):
    """Enhanced upsampling block with dropout and improved padding handling"""
    def __init__(self, in_channels, out_channels, is_3d=False, dropout=0.0):
        super().__init__()
        
        self.is_3d = is_3d
        ConvTranspose = nn.ConvTranspose3d if is_3d else nn.ConvTranspose2d
        
        self.up = ConvTranspose(in_channels, out_channels, kernel_size=2, stride=2)
        self.conv = ConvBlock(out_channels * 2, out_channels, is_3d=is_3d, dropout=dropout/2)
        self.residual = ResidualBlock(out_channels, is_3d=is_3d, dropout=dropout)
        
    def forward(self, x, skip):
        x = self.up(x)
        
        # Improved and simplified padding logic for shape mismatch
        if x.shape[2:] != skip.shape[2:]:
            # Handle both 2D and 3D cases with dynamic padding
            if self.is_3d:
                d_diff = skip.size(2) - x.size(2)
                h_diff = skip.size(3) - x.size(3)
                w_diff = skip.size(4) - x.size(4)
                
                if d_diff > 0 or h_diff > 0 or w_diff > 0:
                    # Calculate padding for each dimension
                    pad_d = (d_diff // 2, d_diff - d_diff // 2) if d_diff > 0 else (0, 0)
                    pad_h = (h_diff // 2, h_diff - h_diff // 2) if h_diff > 0 else (0, 0)
                    pad_w = (w_diff // 2, w_diff - w_diff // 2) if w_diff > 0 else (0, 0)
                    
                    # Apply padding in reverse order (pytorch expects [left,right,top,bottom,front,back])
                    x = F.pad(x, pad_w + pad_h + pad_d)
            else:
                h_diff = skip.size(2) - x.size(2)
                w_diff = skip.size(3) - x.size(3)
                
                if h_diff > 0 or w_diff > 0:
                    # Calculate padding for each dimension
                    pad_h = (h_diff // 2, h_diff - h_diff // 2) if h_diff > 0 else (0, 0)
                    pad_w = (w_diff // 2, w_diff - w_diff // 2) if w_diff > 0 else (0, 0)
                    
                    # Apply padding in reverse order (pytorch expects [left,right,top,bottom])
                    x = F.pad(x, pad_w + pad_h)
        
        # Concatenate features after padding is applied
        x = torch.cat([x, skip], dim=1)
        x = self.conv(x)
        x = self.residual(x)
        return x


def checkpoint_wrapper(module, *args, **kwargs):
    """Enhanced wrapper for checkpointing modules with proper reentrant settings"""
    # Always use non-reentrant checkpoint for better compatibility
    return torch.utils.checkpoint.checkpoint(module, *args, use_reentrant=False, **kwargs)