import torch
import gc

def free_memory():
    """
    Free up GPU memory by clearing cache and running garbage collection
    """
    # Clear CUDA cache
    torch.cuda.empty_cache()
    
    # Run garbage collection
    gc.collect()
    
    # Print memory stats
    if torch.cuda.is_available():
        print(f"GPU memory allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB")
        print(f"GPU memory reserved: {torch.cuda.memory_reserved() / 1e9:.2f} GB")
        print(f"GPU memory free: {torch.cuda.get_device_properties(0).total_memory / 1e9 - torch.cuda.memory_allocated() / 1e9:.2f} GB")

def optimize_model_memory(model):
    """
    Optimize model memory usage
    """
    # Use mixed precision
    if torch.cuda.is_available():
        model = model.half()  # Convert model to half precision
    
    return model