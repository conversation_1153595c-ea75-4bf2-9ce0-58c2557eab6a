import numpy as np
import random
import cv2
from typing import Tu<PERSON>, List, Dict, Optional

class SyntheticSpotGenerator:
    """
    Generator for synthetic spot data with variable sizes and densities
    
    This class provides methods to generate synthetic images with spots of
    varying sizes and densities, where each spot has a unique ID.
    """
    
    def __init__(self, 
                 image_size: Tuple[int, int] = (256, 256),
                 min_spots: int = 5,
                 max_spots: int = 50,
                 min_radius: int = 2,
                 max_radius: int = 10,
                 min_intensity: float = 0.5,
                 max_intensity: float = 1.0,
                 noise_level: Tuple[float, float] = (0.05, 0.2),
                 background_level: Tuple[float, float] = (0.1, 0.3)):
        """
        Initialize the synthetic spot generator
        
        Args:
            image_size: Size of the generated images (height, width)
            min_spots: Minimum number of spots per image
            max_spots: Maximum number of spots per image
            min_radius: Minimum spot radius
            max_radius: Maximum spot radius
            min_intensity: Minimum spot intensity
            max_intensity: Maximum spot intensity
            noise_level: Range of noise levels (min, max)
            background_level: Range of background levels (min, max)
        """
        self.image_size = image_size
        self.min_spots = min_spots
        self.max_spots = max_spots
        self.min_radius = min_radius
        self.max_radius = max_radius
        self.min_intensity = min_intensity
        self.max_intensity = max_intensity
        self.noise_level = noise_level
        self.background_level = background_level
    
    def generate_sample(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a single synthetic sample
        
        Returns:
            image: Synthetic image as numpy array
            mask: Mask with unique IDs for each spot
        """
        # Create empty image and mask
        height, width = self.image_size
        image = np.zeros((height, width), dtype=np.float32)
        mask = np.zeros((height, width), dtype=np.float32)
        
        # Determine number of spots (variable density)
        num_spots = random.randint(self.min_spots, self.max_spots)
        
        # Generate background noise
        noise_level = random.uniform(0.05, 0.2)
        image = np.random.normal(0.2, noise_level, (height, width))
        
        # Ensure image values are in [0, 1]
        image = np.clip(image, 0, 1)
        
        # Keep track of spot centers and radii
        spot_centers = []
        
        # Generate spots with variable sizes
        for spot_id in range(1, num_spots + 1):
            # Try to place a spot without overlapping (max 10 attempts)
            for attempt in range(10):
                # Random position
                x = random.randint(10, width - 10)
                y = random.randint(10, height - 10)
                
                # Random size (radius)
                radius = random.randint(self.min_radius, self.max_radius)
                
                # Check if this spot would overlap with existing spots
                valid_position = True
                for cx, cy, cr in spot_centers:
                    # Calculate distance between centers
                    dist = np.sqrt((x - cx)**2 + (y - cy)**2)
                    # If distance is less than sum of radii, spots would overlap
                    # We allow touching (dist == radius + cr) but not overlapping
                    if dist < (radius + cr):
                        valid_position = False
                        break
                
                if valid_position:
                    # Add to spot centers list
                    spot_centers.append((x, y, radius))
                    
                    # Random intensity
                    intensity = random.uniform(0.5, 1.0)
                    
                    # Create spot in image
                    y_grid, x_grid = np.ogrid[-y:height-y, -x:width-x]
                    dist = np.sqrt(x_grid*x_grid + y_grid*y_grid)
                    spot = np.exp(-(dist**2) / (2 * (radius/2)**2)) * intensity
                    
                    # Add spot to image
                    image += spot
                    
                    # Create spot in mask with unique ID
                    # Use a sharper threshold for the mask to match the visible spot
                    mask_spot = (dist <= radius).astype(np.float32)
                    mask[mask_spot > 0] = spot_id  # Assign unique ID to each spot
                    
                    # Successfully placed this spot, break the attempt loop
                    break
        
        # Ensure image values are in [0, 1]
        image = np.clip(image, 0, 1)
        
        return image, mask
        
    def generate_batch(self, batch_size: int) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        Generate a batch of synthetic samples
        
        Args:
            batch_size: Number of samples to generate
            
        Returns:
            images: List of synthetic images
            masks: List of masks with unique IDs for each spot
        """
        images = []
        masks = []
        
        for _ in range(batch_size):
            image, mask = self.generate_sample()
            images.append(image)
            masks.append(mask)
        
        return images, masks
    
    def generate_dataset(self, 
                         num_samples: int, 
                         variable_params: bool = True) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        Generate a dataset of synthetic samples
        
        Args:
            num_samples: Number of samples to generate
            variable_params: Whether to vary parameters between batches
            
        Returns:
            images: List of synthetic images
            masks: List of masks with unique IDs for each spot
        """
        images = []
        masks = []
        
        for i in range(num_samples):
            # Optionally vary parameters between batches
            if variable_params and i % 10 == 0:
                self.min_spots = random.randint(3, 10)
                self.max_spots = random.randint(20, 100)
                self.min_radius = random.randint(1, 3)
                self.max_radius = random.randint(5, 15)
            
            image, mask = self.generate_sample()
            images.append(image)
            masks.append(mask)
        
        return images, masks


class AdvancedSyntheticSpotGenerator(SyntheticSpotGenerator):
    """
    Advanced generator for synthetic spot data with more realistic features
    
    This class extends SyntheticSpotGenerator to create more realistic synthetic data by:
    1. Adding variable spot shapes (not just circular)
    2. Adding overlapping spots
    3. Adding intensity gradients across the image
    4. Adding more realistic noise patterns
    """
    
    def __init__(self, 
                image_size=(256, 256),
                min_spots=5,
                max_spots=50,
                min_radius=2,
                max_radius=10,
                density_factor=1.0,  # New parameter to control density
                mask_threshold=0.15,  # New parameter to control mask size
                allow_touching=True,  # Allow spots to touch but not overlap
                shape_variation=0.3,
                add_gradients=True,
                realistic_noise=True):
        """
        Initialize the advanced synthetic spot generator
        
        Args:
            image_size: Size of the generated images (height, width)
            min_spots: Minimum number of spots per image
            max_spots: Maximum number of spots per image
            min_radius: Minimum spot radius
            max_radius: Maximum spot radius
            density_factor: Factor to control spot density (higher = more dense)
            allow_touching: Whether to allow spots to touch but not overlap
            shape_variation: Amount of variation in spot shapes
            add_gradients: Whether to add intensity gradients
            realistic_noise: Whether to use realistic noise patterns
        """
        super().__init__(
            image_size=image_size,
            min_spots=min_spots,
            max_spots=max_spots,
            min_radius=min_radius,
            max_radius=max_radius
        )
        self.density_factor = density_factor
        self.mask_threshold = mask_threshold
        self.allow_touching = allow_touching
        self.shape_variation = shape_variation
        self.add_gradients = add_gradients
        self.realistic_noise = realistic_noise
    
    def generate_sample(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate a single advanced synthetic sample
        
        Returns:
            image: Synthetic image as numpy array
            mask: Mask with unique IDs for each spot
        """
        # Create empty image and mask
        height, width = self.image_size
        image = np.zeros((height, width), dtype=np.float32)
        mask = np.zeros((height, width), dtype=np.float32)
        
        # Determine number of spots (variable density)
        num_spots = int(random.randint(self.min_spots, self.max_spots) * self.density_factor)
        
        # Generate background
        background_value = random.uniform(self.background_level[0], self.background_level[1])
        
        # Add gradient if enabled
        if self.add_gradients:
            # Create a random gradient direction
            angle = random.uniform(0, 2 * np.pi)
            gradient_strength = random.uniform(0.05, 0.2)
            
            # Create gradient
            y_grid, x_grid = np.mgrid[0:height, 0:width]
            gradient = (x_grid * np.cos(angle) + y_grid * np.sin(angle)) / np.sqrt(height**2 + width**2)
            gradient = gradient * gradient_strength + background_value
        else:
            gradient = np.ones((height, width)) * background_value
        
        # Add noise
        if self.realistic_noise:
            # Create correlated noise
            noise_scale = random.uniform(self.noise_level[0], self.noise_level[1])
            small_noise = np.random.normal(0, noise_scale, (height//8, width//8))
            noise = cv2.resize(small_noise, (width, height))
            
            # Add some fine grain noise
            fine_noise = np.random.normal(0, noise_scale/2, (height, width))
            noise = noise * 0.7 + fine_noise * 0.3
        else:
            noise_level = random.uniform(self.noise_level[0], self.noise_level[1])
            noise = np.random.normal(0, noise_level, (height, width))
        
        # Combine background and noise
        image = gradient + noise
        
        # Keep track of spot centers and radii
        spot_centers = []
        
        # Generate spots with variable sizes and shapes
        for spot_id in range(1, num_spots + 1):
            # Try to place a spot without overlapping (max 10 attempts)
            for attempt in range(10):
                # Random position
                x = random.randint(10, width - 10)
                y = random.randint(10, height - 10)
                
                # Random size (radius)
                radius = random.randint(self.min_radius, self.max_radius)
                
                # Check if this spot would overlap with existing spots
                valid_position = True
                for cx, cy, cr in spot_centers:
                    # Calculate distance between centers
                    dist = np.sqrt((x - cx)**2 + (y - cy)**2)
                    
                    # Allow spots to be closer together (touching or slightly overlapping)
                    # Instead of requiring dist >= radius + cr, we'll use a smaller threshold
                    # This will make spots touch more often
                    min_distance = (radius + cr) * 0.7  # Reduce minimum distance to 70% of sum of radii
                    
                    if dist < min_distance:
                        valid_position = False
                        break
                
                if valid_position:
                    # Add to spot centers list
                    spot_centers.append((x, y, radius))
                    
                    # Random intensity
                    intensity = random.uniform(self.min_intensity, self.max_intensity)
                    
                    # Create base distance grid
                    y_grid, x_grid = np.ogrid[-y:height-y, -x:width-x]
                    
                    # Add shape variation if enabled
                    if self.shape_variation > 0:
                        # Create random deformation
                        angle = random.uniform(0, 2 * np.pi)
                        stretch = 1.0 + random.uniform(0, self.shape_variation)
                        
                        # Apply deformation
                        x_deform = x_grid * np.cos(angle) + y_grid * np.sin(angle)
                        y_deform = -x_grid * np.sin(angle) + y_grid * np.cos(angle)
                        
                        # Stretch in one direction
                        x_deform = x_deform * stretch
                        
                        # Calculate distance with deformation
                        dist = np.sqrt(x_deform**2 + y_deform**2)
                    else:
                        # Regular circular distance
                        dist = np.sqrt(x_grid**2 + y_grid**2)
                    
                    # Create spot in image
                    spot = np.exp(-(dist**2) / (2 * (radius/2)**2)) * intensity
                    
                    # Add spot to image
                    image += spot
                    
                    # Create spot in mask with unique ID
                    # Use a much higher threshold for the mask to make it smaller
                    # The mask_threshold parameter should be much higher (e.g., 0.5 or higher)
                    intensity_threshold = self.mask_threshold * intensity
                    mask_spot = (spot > intensity_threshold).astype(np.float32)
                    mask[mask_spot > 0] = spot_id  # Assign unique ID to each spot
                    
                    # Successfully placed this spot, break the attempt loop
                    break
        
        # Ensure image values are in [0, 1]
        image = np.clip(image, 0, 1)
        
        return image, mask

