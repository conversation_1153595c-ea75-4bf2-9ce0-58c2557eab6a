{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"training\"></a>\n", "## 4. Training\n", "\n", "Now, let's set up the training pipeline with gradient clipping and semi-supervised learning."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create optimizer and learning rate scheduler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration for training\n", "LEARNING_RATE = 1e-3\n", "WEIGHT_DECAY = 1e-4\n", "NUM_EPOCHS = 100\n", "PATIENCE = 10\n", "SAVE_FREQ = 10\n", "SAVE_DIR = './checkpoints'\n", "EXPERIMENT_NAME = 'spot_detection'\n", "\n", "# Create optimizer\n", "optimizer = optim.AdamW(\n", "    list(model.parameters()) + list(loss_fn.parameters()),\n", "    lr=LEARNING_RATE,\n", "    weight_decay=WEIGHT_DECAY\n", ")\n", "\n", "# Create learning rate scheduler\n", "scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "    optimizer, T_max=NUM_EPOCHS, eta_min=LEARNING_RATE/100\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create a custom trainer with gradient clipping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class SparseSpotTrainer(SpotDetectionTrainer):\n", "    \"\"\"\n", "    Trainer for spot detection with sparse annotations and semi-supervised learning\n", "    \n", "    This trainer extends SpotDetectionTrainer with:\n", "    1. Gradient clipping for training stability\n", "    2. Support for confidence-weighted loss\n", "    3. Semi-supervised learning with high-confidence predictions\n", "    \"\"\"\n", "    def __init__(self, \n", "                 clip_grad_norm=1.0,\n", "                 semi_supervised=True,\n", "                 confidence_threshold=0.9,\n", "                 update_interval=5,\n", "                 **kwargs):\n", "        super().__init__(**kwargs)\n", "        self.clip_grad_norm = clip_grad_norm\n", "        self.semi_supervised = semi_supervised\n", "        self.confidence_threshold = confidence_threshold\n", "        self.update_interval = update_interval\n", "    \n", "    def train_epoch(self):\n", "        \"\"\"Train for one epoch with gradient clipping\"\"\"\n", "        self.model.train()\n", "        total_loss = 0.0\n", "        component_losses = {\n", "            'heatmap_loss': 0.0,\n", "            'dice_loss': 0.0,\n", "            'boundary_loss': 0.0,\n", "            'distance_loss': 0.0,\n", "            'moe_loss': 0.0,\n", "            'contrastive_loss': 0.0,\n", "            'specialization_loss': 0.0\n", "        }\n", "        \n", "        start_time = time.time()\n", "        num_batches = len(self.train_loader)\n", "        \n", "        # Use tqdm for progress bar\n", "        pbar = tqdm(self.train_loader, desc=\"Training\")\n", "        \n", "        for batch_idx, batch in enumerate(pbar):\n", "            # Get inputs and targets\n", "            inputs = batch['image'].to(self.device)\n", "            targets = {\n", "                'masks': batch['mask'].to(self.device)\n", "            }\n", "            \n", "            # Add confidence mask if available\n", "            if 'confidence' in batch:\n", "                targets['confidence'] = batch['confidence'].to(self.device)\n", "            \n", "            # Zero gradients\n", "            self.optimizer.zero_grad()\n", "            \n", "            # Forward pass\n", "            outputs = self.model(inputs, targets)\n", "            \n", "            # Compute loss\n", "            loss, losses_dict = self.loss_fn(outputs, targets)\n", "            \n", "            # Add specialization loss if available\n", "            if 'specialization_loss' in outputs:\n", "                specialization_loss = outputs['specialization_loss']\n", "                losses_dict['specialization_loss'] = specialization_loss\n", "                loss += self.model.specialization_weight * specialization_loss\n", "            \n", "            # Backward pass\n", "            loss.backward()\n", "            \n", "            # Apply gradient clipping\n", "            if self.clip_grad_norm > 0:\n", "                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.clip_grad_norm)\n", "                torch.nn.utils.clip_grad_norm_(self.loss_fn.parameters(), self.clip_grad_norm)\n", "            \n", "            # Update weights\n", "            self.optimizer.step()\n", "            \n", "            # Update metrics\n", "            total_loss += loss.item()\n", "            for key, value in losses_dict.items():\n", "                if key in component_losses:\n", "                    component_losses[key] += value.item()\n", "            \n", "            # Update progress bar\n", "            pbar.set_postfix({\n", "                'loss': f\"{loss.item():.4f}\",\n", "                'lr': f\"{self.optimizer.param_groups[0]['lr']:.6f}\"\n", "            })\n", "        \n", "        # Compute average losses\n", "        metrics = {\n", "            'loss': total_loss / num_batches\n", "        }\n", "        for key, value in component_losses.items():\n", "            metrics[key] = value / num_batches\n", "        \n", "        # Get current loss weights\n", "        if hasattr(self.loss_fn, 'get_current_weights'):\n", "            weights = self.loss_fn.get_current_weights()\n", "            for key, value in weights.items():\n", "                if isinstance(value, torch.Tensor):\n", "                    metrics[f'weight_{key}'] = value.item()\n", "                else:\n", "                    metrics[f'weight_{key}'] = value\n", "        \n", "        # Get expert usage statistics\n", "        if hasattr(self.model, 'expert_usage'):\n", "            expert_usage = self.model.expert_usage.cpu().numpy()\n", "            for i, usage in enumerate(expert_usage):\n", "                metrics[f'expert_{i}_usage'] = usage\n", "        \n", "        # Log training time\n", "        elapsed = time.time() - start_time\n", "        metrics['time'] = elapsed\n", "        \n", "        return metrics\n", "    \n", "    def train(self, num_epochs=100, patience=10, save_freq=10, primary_metric='val_pixel_f1'):\n", "        \"\"\"Train with semi-supervised learning\"\"\"\n", "        # Check if train_dataset supports semi-supervised learning\n", "        if self.semi_supervised and hasattr(self.train_loader.dataset, 'update_with_predictions'):\n", "            self.logger.info(\"Using semi-supervised learning\")\n", "        else:\n", "            self.semi_supervised = False\n", "            self.logger.info(\"Semi-supervised learning disabled\")\n", "        \n", "        # Call parent train method\n", "        super().train(num_epochs, patience, save_freq, primary_metric)\n", "    \n", "    def update_dataset_with_predictions(self):\n", "        \"\"\"Update dataset with high-confidence predictions\"\"\"\n", "        if not self.semi_supervised:\n", "            return\n", "        \n", "        self.logger.info(\"Updating dataset with high-confidence predictions...\")\n", "        \n", "        # Set model to evaluation mode\n", "        self.model.eval()\n", "        \n", "        # Get all images from the dataset\n", "        image_paths = self.train_loader.dataset.images\n", "        batch_size = self.train_loader.batch_size\n", "        \n", "        # Process images in batches\n", "        predictions = []\n", "        paths = []\n", "        \n", "        with torch.no_grad():\n", "            for i in range(0, len(image_paths), batch_size):\n", "                # Get batch of images\n", "                batch_paths = image_paths[i:i+batch_size]\n", "                batch_images = []\n", "                \n", "                # Load images\n", "                for path in batch_paths:\n", "                    if isinstance(path, str):\n", "                        # Load image from disk\n", "                        image = io.imread(path)\n", "                        if len(image.shape) == 3 and image.shape[2] > 1:\n", "                            image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)\n", "                        image = image.astype(np.float32) / 255.0\n", "                        image = torch.from_numpy(image).unsqueeze(0)  # Add channel dimension\n", "                    else:\n", "                        # Use image directly\n", "                        image = torch.from_numpy(path).unsqueeze(0) if isinstance(path, np.ndarray) else path\n", "                    \n", "                    batch_images.append(image)\n", "                \n", "                # Stack images\n", "                batch_tensor = torch.stack(batch_images).to(self.device)\n", "                \n", "                # Forward pass\n", "                outputs = self.model(batch_tensor)\n", "                \n", "                # Add predictions and paths\n", "                predictions.append(outputs['heatmap'].detach())\n", "                paths.extend(batch_paths)\n", "        \n", "        # Concatenate predictions\n", "        all_predictions = torch.cat(predictions, dim=0)\n", "        \n", "        # Update dataset\n", "        self.train_loader.dataset.update_with_predictions(\n", "            paths, all_predictions, threshold=self.confidence_threshold\n", "        )\n", "        \n", "        self.logger.info(\"Dataset updated with high-confidence predictions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create trainer and start training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Create trainer\n", "trainer = SparseSpotTrainer(\n", "    model=model,\n", "    loss_fn=loss_fn,\n", "    train_loader=train_loader,\n", "    val_loader=val_loader,\n", "    optimizer=optimizer,\n", "    scheduler=scheduler,\n", "    device=device,\n", "    save_dir=SAVE_DIR,\n", "    experiment_name=EXPERIMENT_NAME,\n", "    metrics_threshold=0.5,\n", "    iou_threshold=0.5,\n", "    clip_grad_norm=1.0,\n", "    semi_supervised=True,\n", "    confidence_threshold=0.9,\n", "    update_interval=5\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Start training\n", "history = trainer.train(\n", "    num_epochs=NUM_EPOCHS,\n", "    patience=PATIENCE,\n", "    save_freq=SAVE_FREQ,\n", "    primary_metric='val_pixel_f1'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot training curves"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["def plot_training_curves(history):\n", "    \"\"\"Plot training curves\"\"\"\n", "    # Create figure with subplots\n", "    fig, axs = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Plot loss curves\n", "    axs[0, 0].plot(history['train_loss'], label='Train Loss')\n", "    axs[0, 0].plot(history['val_loss'], label='Val Loss')\n", "    axs[0, 0].set_title('Loss Curves')\n", "    axs[0, 0].set_xlabel('Epoch')\n", "    axs[0, 0].set_ylabel('Loss')\n", "    axs[0, 0].legend()\n", "    axs[0, 0].grid(True)\n", "    \n", "    # Plot learning rate\n", "    axs[0, 1].plot(history['learning_rate'])\n", "    axs[0, 1].set_title('Learning Rate')\n", "    axs[0, 1].set_xlabel('Epoch')\n", "    axs[0, 1].set_ylabel('LR')\n", "    axs[0, 1].set_yscale('log')\n", "    axs[0, 1].grid(True)\n", "    \n", "    # Plot pixel metrics\n", "    if len(history['metrics']) > 0:\n", "        epochs = range(len(history['metrics']))\n", "        pixel_iou = [m.get('val_pixel_iou', 0) for m in history['metrics']]\n", "        pixel_dice = [m.get('val_pixel_dice', 0) for m in history['metrics']]\n", "        pixel_f1 = [m.get('val_pixel_f1', 0) for m in history['metrics']]\n", "        \n", "        axs[1, 0].plot(epochs, pixel_iou, label='IoU')\n", "        axs[1, 0].plot(epochs, pixel_dice, label='Dice')\n", "        axs[1, 0].plot(epochs, pixel_f1, label='F1')\n", "        axs[1, 0].set_title('Pixel Metrics')\n", "        axs[1, 0].set_xlabel('Epoch')\n", "        axs[1, 0].set_ylabel('Score')\n", "        axs[1, 0].legend()\n", "        axs[1, 0].grid(True)\n", "        \n", "        # Plot instance metrics\n", "        instance_f1 = [m.get('val_instance_f1', 0) for m in history['metrics']]\n", "        instance_precision = [m.get('val_instance_precision', 0) for m in history['metrics']]\n", "        instance_recall = [m.get('val_instance_recall', 0) for m in history['metrics']]\n", "        \n", "        axs[1, 1].plot(epochs, instance_f1, label='F1')\n", "        axs[1, 1].plot(epochs, instance_precision, label='Precision')\n", "        axs[1, 1].plot(epochs, instance_recall, label='Recall')\n", "        axs[1, 1].set_title('Instance Metrics')\n", "        axs[1, 1].set_xlabel('Epoch')\n", "        axs[1, 1].set_ylabel('Score')\n", "        axs[1, 1].legend()\n", "        axs[1, 1].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Plot training curves\n", "plot_training_curves(history)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}