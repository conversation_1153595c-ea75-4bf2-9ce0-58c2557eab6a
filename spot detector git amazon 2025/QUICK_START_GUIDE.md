# Spot Detection Quick Start Guide

This guide provides a quick overview of how to use the spot detection pipeline with the existing files.

## Step 1: Setup and Dependencies

Install the required packages:

```bash
pip install torch torchvision albumentations scikit-image tqdm matplotlib opencv-python
```

## Step 2: Data Organization

Organize your data in one of these formats:

### Option 1: Separate directories for images and masks
```
data_dir/
├── images/
│   ├── image1.png
│   ├── image2.png
│   └── ...
└── masks/
    ├── image1.png
    ├── image2.png
    └── ...
```

### Option 2: Paired files in the same directory
```
data_dir/
├── image1.png
├── image1_mask.png
├── image2.png
├── image2_mask.png
└── ...
```

## Step 3: Run the Complete Pipeline

Use the `run_spot_detection.py` script:

```bash
python run_spot_detection.py --data_dir /path/to/your/data --mode train
```

## Step 4: Using the Jupyter Notebooks

If you prefer using Jupyter notebooks, follow these steps:

1. Open `SpotDetection_Part1.ipynb` for setup and data loading
2. Open `SpotDetection_Part2.ipynb` for model and loss function implementation
3. Open `SpotDetection_Part3.ipynb` for training
4. Open `SpotDetection_Part4.ipynb` for evaluation and prediction

## Step 5: Using Individual Components

### Data Loading

```python
from data_utils import SpotDataset, create_data_loaders
from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders

# For regular data
train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(
    data_dir="/path/to/your/data",
    batch_size=8,
    image_size=(256, 256),
    train_val_split=0.8,
    synthetic=True,
    synthetic_size=500,
    augmentation_level='strong',
    num_workers=4
)

# For sparse annotations
train_loader, val_loader, train_dataset, val_dataset = create_sparse_data_loaders(
    data_dir="/path/to/your/data",
    batch_size=8,
    image_size=(256, 256),
    train_val_split=0.8,
    synthetic=True,
    synthetic_size=500,
    augmentation_level='strong',
    num_workers=4,
    confidence_threshold=0.9,
    ignore_threshold=0.3
)
```

### Model and Loss Function

```python
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from OptimizedSpotLoss import OptimizedSpotLoss

# Create model
model = OptimizedSpotDetectionModel(
    in_channels=1,
    num_experts=3,
    base_filters=64,
    dropout_rate=0.2
)

# Move model to device
model = model.to(device)

# Create loss function
loss_fn = OptimizedSpotLoss(
    bce_weight=1.0,
    dice_weight=1.0,
    focal_weight=0.5,
    focal_gamma=2.0,
    size_adaptive=True,
    density_aware=True,
    confidence_weighted=True
)
```

### Training

```python
from metrics import SpotDetectionMetrics
from trainer import SpotDetectionTrainer

# Create optimizer
optimizer = optim.Adam(model.parameters(), lr=0.001)

# Create learning rate scheduler
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', factor=0.5, patience=5, verbose=True
)

# Create metrics calculator
metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)

# Create trainer
trainer = SpotDetectionTrainer(
    model=model,
    loss_fn=loss_fn,
    optimizer=optimizer,
    device=device,
    metrics_calculator=metrics_calculator,
    scheduler=scheduler
)

# Train model
history = trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=30,
    early_stopping_patience=10,
    save_best_model=True,
    model_save_path='best_spot_detection_model.pth'
)

# Plot training history
trainer.plot_history()
```

### Prediction and Visualization

```python
from predict import SpotDetectionPredictor

# Create predictor
predictor = SpotDetectionPredictor(
    model=model,
    device=device,
    threshold=0.5,
    min_spot_size=3
)

# Make prediction on an image
image = val_dataset[0]['image']
result = predictor.predict(image, return_heatmap=True)

# Visualize result
predictor.visualize(
    image=image,
    result=result,
    show_spots=True,
    show_heatmap=True,
    show_experts=True
)
```

### Semi-supervised Learning

```python
# Update dataset with high-confidence predictions
train_dataset.update_with_predictions(all_images, all_preds, threshold=0.9)

# Create new data loaders
train_loader = DataLoader(
    train_dataset,
    batch_size=8,
    shuffle=True,
    num_workers=4,
    pin_memory=True
)

val_loader = DataLoader(
    val_dataset,
    batch_size=8,
    shuffle=False,
    num_workers=4,
    pin_memory=True
)

# Train model for a few more epochs
trainer = SpotDetectionTrainer(
    model=model,
    loss_fn=loss_fn,
    optimizer=optimizer,
    device=device,
    metrics_calculator=metrics_calculator,
    scheduler=scheduler
)

history = trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=10,
    early_stopping_patience=5,
    save_best_model=True,
    model_save_path='semi_supervised_model.pth'
)
```

## Step 6: Synthetic Data Generation

```python
from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator

# Create synthetic data generator
generator = AdvancedSyntheticSpotGenerator(
    image_size=(256, 256),
    min_spots=5,
    max_spots=50,
    min_radius=2,
    max_radius=10,
    allow_overlapping=True,
    shape_variation=0.3,
    add_gradients=True,
    realistic_noise=True
)

# Generate samples
num_samples = 5
images, masks = generator.generate_batch(num_samples)

# Visualize samples
for i in range(num_samples):
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.imshow(images[i], cmap='gray')
    plt.title(f"Synthetic Image {i+1}")
    plt.axis('off')
    
    plt.subplot(1, 2, 2)
    plt.imshow(masks[i], cmap='hot')
    plt.title(f"Mask with {len(np.unique(masks[i]))-1} spots")
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()
```

## Troubleshooting

- **Out of Memory Errors**: Reduce batch size, enable gradient checkpointing
- **Slow Training**: Ensure GPU is being used, reduce image size if necessary
- **Poor Performance on Dense Regions**: Increase weight for Expert 3, enable density-aware weighting
- **Small Spots Not Detected**: Increase weight for Expert 1, enable size-adaptive weighting
- **Touching Spots Not Separated**: Increase boundary loss weight, enable contrastive loss