# Setup workspace paths
import os

# Get the current notebook directory
NOTEBOOK_DIR = os.path.dirname(os.path.abspath('__file__'))
print(NOTEBOOK_DIR)

# Create workspace directories
WORKSPACE = {
    'models': os.path.join(NOTEBOOK_DIR, 'models'),
    'logs': os.path.join(NOTEBOOK_DIR, 'logs'),
    'results': os.path.join(NOTEBOOK_DIR, 'results'),
    'tensorboard': os.path.join(NOTEBOOK_DIR, 'tensorboard_logs')
}

# Create directories if they don't exist
for dir_path in WORKSPACE.values():
    os.makedirs(dir_path, exist_ok=True)
    print(f'Created directory: {dir_path}')

# Import TensorBoard for visualization
from torch.utils.tensorboard import SummaryWriter
import datetime

# Create a timestamp for this run
TIMESTAMP = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')

# Setup paths for this run
RUN_DIR = os.path.join(WORKSPACE['models'], f'run_{TIMESTAMP}')
os.makedirs(RUN_DIR, exist_ok=True)
TENSORBOARD_DIR = os.path.join(WORKSPACE['tensorboard'], f'run_{TIMESTAMP}')

print(f'\nWorkspace setup complete. Run directory: {RUN_DIR}')

# Import standard libraries
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
from tqdm.notebook import tqdm
import cv2
from skimage import io, measure
from scipy.ndimage import binary_erosion, gaussian_filter
from scipy import ndimage
from typing import Dict, List, Tuple, Optional, Union
import random
import time
import json
from skimage.feature import peak_local_max
from matplotlib.patches import Polygon
from matplotlib.collections import PatchCollection
from PIL import Image

# Set random seed for reproducibility
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

set_seed()

# Check if GPU is available
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Import our modules
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from data_utils import SpotDataset, create_data_loaders
from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders
from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator
from fixed_spot_loss import FixedSpotLoss
from fixed_metrics import FixedSpotMetrics
from fixed_trainer import FixedSpotTrainer
from enhanced_spot_loss import EnhancedSpotLoss
from enhanced_metrics import EnhancedSpotMetrics
from improved_trainer import ImprovedSpotTrainer
from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly
from force_spot_separation import force_spot_separation, visualize_spot_detection_steps
from debug_binary_mask import debug_binary_mask, fix_spot_detection
from fix_spot_detection import detect_individual_spots, visualize_spot_detection

# Configurable parameters for synthetic data generation
synthetic_params = {
    'image_size': (256, 256),
    'min_spots': 50,
    'max_spots': 220,
    'min_radius': 1,
    'max_radius': 5,
    'density_factor': 2.0,  # Higher values create more dense spot patterns
    'mask_threshold': 0.35,  # Controls mask size
    'allow_touching': True,  # Allow spots to touch but not overlap
    'shape_variation': 0.05,  # Amount of variation in spot shapes
    'add_gradients': True,   # Add intensity gradients
    'realistic_noise': True  # Use realistic noise patterns
}

class SyntheticDataSaver:
    """
    Enhanced synthetic data generator with disk saving capabilities
    
    This class extends the synthetic data generation functionality to save
    synthetic images and corresponding instance detection masks to disk,
    where each spot has its own unique ID.
    """
    
    def __init__(self, synthetic_params):
        """Initialize the synthetic data saver with parameters"""
        from enhanced_synthetic_data_generator import EnhancedSyntheticSpotGenerator
        
        self.generator = EnhancedSyntheticSpotGenerator(**synthetic_params)
        self.params = synthetic_params
    
    def save_dataset_to_disk(self, 
                           num_samples: int,
                           output_dir: str = "synthetic_dataset",
                           save_visualizations: bool = True,
                           image_format: str = "tif",
                           mask_format: str = "tif") -> None:
        """
        Generate and save synthetic dataset to disk
        
        Args:
            num_samples: Number of samples to generate and save
            output_dir: Directory to save the dataset
            save_visualizations: Whether to save visualization images
            image_format: Format for saving images ('tiff', 'png', 'jpg') - default 8-bit TIFF
            mask_format: Format for saving masks ('tiff', 'png') - default 8-bit TIFF
        """
        # Create output directories
        os.makedirs(output_dir, exist_ok=True)
        images_dir = os.path.join(output_dir, "images")
        masks_dir = os.path.join(output_dir, "masks")
        metadata_dir = os.path.join(output_dir, "metadata")
        
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(masks_dir, exist_ok=True)
        os.makedirs(metadata_dir, exist_ok=True)
        
        if save_visualizations:
            vis_dir = os.path.join(output_dir, "visualizations")
            os.makedirs(vis_dir, exist_ok=True)
        
        print(f"🔄 Generating and saving {num_samples} synthetic samples to {output_dir}...")
        
        # Generate and save samples
        for i in tqdm(range(num_samples), desc="Generating samples"):
            # Generate sample with detailed spot data
            image, mask, spot_data = self.generator.generate_sample()
            
            # Create filenames
            sample_id = f"{i+1:06d}"
            image_filename = f"image_{sample_id}.{image_format}"
            mask_filename = f"mask_{sample_id}.{mask_format}"
            metadata_filename = f"metadata_{sample_id}.json"
            
            # Save image (scale to 0-255 for saving)
            self._save_image(image, os.path.join(images_dir, image_filename), image_format)
            
            # Save instance mask (preserve unique IDs)
            self._save_mask(mask, os.path.join(masks_dir, mask_filename), mask_format)
            
            # Create and save metadata
            metadata = self._create_metadata(sample_id, image, mask, spot_data)
            with open(os.path.join(metadata_dir, metadata_filename), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Save visualization if requested
            if save_visualizations:
                vis_filename = f"vis_{sample_id}.png"
                self._create_visualization(image, mask, spot_data, 
                                         os.path.join(vis_dir, vis_filename))
        
        # Save dataset summary
        self._save_dataset_summary(output_dir, num_samples)
        
        print(f"✅ Successfully saved {num_samples} samples to {output_dir}")
        print(f"📁 Dataset structure:")
        print(f"   {output_dir}/")
        print(f"   ├── images/         - Original synthetic images (8-bit {image_format.upper()})")
        print(f"   ├── masks/          - Instance masks with unique spot IDs (8-bit {mask_format.upper()})")
        print(f"   ├── metadata/       - JSON files with detailed spot information")
        if save_visualizations:
            print(f"   ├── visualizations/ - Visual overlays showing spots and IDs")
        print(f"   └── dataset_info.json - Dataset summary and statistics")
    
    def _save_image(self, image: np.ndarray, filepath: str, format: str):
        """Save image with proper scaling and format as 8-bit"""
        from PIL import Image as PILImage
        
        # Scale to 0-255 and convert to uint8 (8-bit for all formats)
        image_scaled = (np.clip(image, 0, 1) * 255).astype(np.uint8)
        
        # Save as 8-bit for all formats
        PILImage.fromarray(image_scaled, mode='L').save(filepath)
    
    def _save_mask(self, mask: np.ndarray, filepath: str, format: str):
        """Save instance mask preserving unique spot IDs as 8-bit"""
        from PIL import Image as PILImage
        
        # Save as 8-bit (limited to 255 unique spots) for all formats
        mask_uint8 = np.clip(mask, 0, 255).astype(np.uint8)
        PILImage.fromarray(mask_uint8, mode='L').save(filepath)
    
    def _create_metadata(self, sample_id, image, mask, spot_data):
        """Create metadata for a sample with JSON-serializable types"""
        import numpy as np
        
        def convert_to_serializable(obj):
            """Convert numpy types to native Python types for JSON serialization"""
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_to_serializable(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_to_serializable(item) for item in obj)
            else:
                return obj
        
        # Create base metadata
        metadata = {
            'sample_id': str(sample_id),
            'image_shape': list(image.shape) if hasattr(image, 'shape') else None,
            'mask_shape': list(mask.shape) if hasattr(mask, 'shape') else None,
            'num_spots': int(len(spot_data)) if spot_data else 0,
            'generation_params': convert_to_serializable(self.params),
            'spots': []
        }
        
        # Add spot information
        if spot_data:
            for i, spot in enumerate(spot_data):
                spot_info = {
                    'spot_id': int(i + 1),
                    'center_x': float(spot.get('center_x', spot.get('x', 0))),
                    'center_y': float(spot.get('center_y', spot.get('y', 0))),
                    'radius': float(spot.get('radius', 0)),
                    'intensity': float(spot.get('intensity', 0)),
                    'area': int(spot.get('area', 0)) if 'area' in spot else None
                }
                metadata['spots'].append(spot_info)
        
        return metadata
    
    def _create_visualization(self, image: np.ndarray, mask: np.ndarray, 
                            spot_data: List[Dict], save_path: str):
        """Create and save visualization of image with mask overlay and spot IDs"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Original image
        axes[0].imshow(image, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Instance mask with colormap
        mask_colored = np.ma.masked_where(mask == 0, mask)
        axes[1].imshow(image, cmap='gray', alpha=0.7)
        axes[1].imshow(mask_colored, cmap='tab20', alpha=0.8)
        axes[1].set_title('Instance Mask')
        axes[1].axis('off')
        
        # Overlay with spot ID annotations
        axes[2].imshow(image, cmap='gray', alpha=0.7)
        axes[2].imshow(mask_colored, cmap='tab20', alpha=0.3)
        axes[2].set_title('Overlay with Spot IDs')
        axes[2].axis('off')
        
        # Add spot ID annotations using spot_data
        for spot in spot_data:
            x, y = spot['x'], spot['y']
            spot_id = spot['id']
            axes[2].text(x, y, str(spot_id), 
                       color='red', fontsize=8, ha='center', va='center',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
            
            # Add circle to show spot boundary
            circle = plt.Circle((x, y), spot['radius'], fill=False, 
                              color='red', linewidth=1, alpha=0.6)
            axes[2].add_patch(circle)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def _save_dataset_summary(self, output_dir: str, num_samples: int):
        """Save dataset summary and statistics"""
        summary = {
            'dataset_info': {
                'num_samples': num_samples,
                'generation_timestamp': str(np.datetime64('now')),
                'generator_params': self.params,
                'file_format': {
                    'images': '8-bit TIFF',
                    'masks': '8-bit TIFF',
                    'max_spots_per_image': 255,
                    'background_value': 0
                }
            },
            'directory_structure': {
                'images': 'Original synthetic images (8-bit TIFF)',
                'masks': 'Instance masks where each spot has unique ID (8-bit TIFF)',
                'metadata': 'JSON files with detailed spot information',
                'visualizations': 'Visual overlays showing spots and IDs'
            },
            'usage_notes': {
                'mask_format': 'Instance masks use unique integer IDs for each spot (0=background, 1-255=spots)',
                'coordinate_system': 'Image coordinates with origin at top-left',
                'spot_data': 'Each spot includes centroid, radius, intensity, and shape parameters',
                'bit_depth': '8-bit format supports up to 255 unique spots per image'
            }
        }
        
        summary_path = os.path.join(output_dir, 'dataset_info.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

def generate_test_dataset(num_samples=50, 
                         output_dir="test_dataset",
                         image_size=(512, 512),
                         min_spots=50,
                         max_spots=200,
                         save_as_8bit_tiff=True):
    """
    Generate a test dataset with synthetic spot data
    
    Args:
        num_samples: Number of test samples to generate
        output_dir: Directory to save the test dataset
        image_size: Size of generated images (height, width)
        min_spots: Minimum number of spots per image
        max_spots: Maximum number of spots per image
        save_as_8bit_tiff: Whether to save as 8-bit TIFF files
    
    Returns:
        SyntheticDataSaver: The data saver instance used for generation
    """
    print(f"🔬 Generating test dataset with {num_samples} samples...")
    

    
    # Create test-specific parameters
    test_params = {
        'image_size': image_size,
        'min_spots': min_spots,
        'max_spots': max_spots,
        'min_radius': 1,
        'max_radius': 5,
        'density_factor': 1.5,  # Slightly lower density for test data
        'mask_threshold': 0.21,
        'allow_touching': True,
        'shape_variation': 0.2,
        'add_gradients': True,
        'realistic_noise': True
    }
    
    # Create synthetic data saver
    data_saver = SyntheticDataSaver(test_params)
    
    # Determine file format based on preference
    image_format = "tif" if save_as_8bit_tiff else "png"
    mask_format = "tif"
    
    # Generate and save the dataset
    data_saver.save_dataset_to_disk(
        num_samples=num_samples,
        output_dir=output_dir,
        save_visualizations=True,
        image_format=image_format,
        mask_format=mask_format
    )
    
    print(f"✅ Test dataset generated successfully!")
    print(f"📁 Location: {output_dir}")
    
    return data_saver

test_data_saver = generate_test_dataset()

def generate_training_dataset(num_samples, 
                            output_dir="training_dataset",
                            image_size=(512, 512),
                            min_spots=50,
                            max_spots=220,
                            save_as_8bit_tiff=True):
    """
    Generate a training dataset with synthetic spot data
    
    Args:
        num_samples: Number of training samples to generate
        output_dir: Directory to save the training dataset
        image_size: Size of generated images (height, width)
        min_spots: Minimum number of spots per image
        max_spots: Maximum number of spots per image
        save_as_8bit_tiff: Whether to save as 8-bit TIFF files
    
    Returns:
        SyntheticDataSaver: The data saver instance used for generation
    """
    print(f"🏋️ Generating training dataset with {num_samples} samples...")
    
    # Create training-specific parameters
    training_params = {
        'image_size': image_size,
        'min_spots': min_spots,
        'max_spots': max_spots,
        'min_radius': 1,
        'max_radius': 6,
        'density_factor': 1.8,  # Higher density for training variety
        'mask_threshold': 0.21,
        'allow_touching': True,
        'shape_variation': 0.3,
        'add_gradients': True,
        'realistic_noise': True
    }
    
    # Create synthetic data saver
    data_saver = SyntheticDataSaver(training_params)
    
    # Determine file format based on preference
    image_format = "tif" if save_as_8bit_tiff else "png"
    mask_format = "tif"
    
    # Generate and save the dataset
    data_saver.save_dataset_to_disk(
        num_samples=num_samples,
        output_dir=output_dir,
        save_visualizations=True,
        image_format=image_format,
        mask_format=mask_format
    )
    
    print(f"✅ Training dataset generated successfully!")
    print(f"📁 Location: {output_dir}")
    
    return data_saver

training_data_saver = generate_training_dataset(1000)

def check_dataset_integrity(dataset_dir, expected_samples=None, verbose=True):
    """
    Check the integrity of a saved dataset
    
    Args:
        dataset_dir: Directory containing the dataset
        expected_samples: Expected number of samples (optional)
        verbose: Whether to print detailed information
    
    Returns:
        dict: Dictionary containing check results
    """
    import os
    import json
    from PIL import Image
    import numpy as np
    
    results = {
        'dataset_exists': False,
        'images_dir_exists': False,
        'masks_dir_exists': False,
        'metadata_dir_exists': False,
        'visualizations_dir_exists': False,
        'dataset_info_exists': False,
        'num_images': 0,
        'num_masks': 0,
        'num_metadata': 0,
        'num_visualizations': 0,
        'files_match': False,
        'sample_checks': [],
        'errors': []
    }
    
    if verbose:
        print(f"🔍 Checking dataset integrity: {dataset_dir}")
    
    # Check if dataset directory exists
    if not os.path.exists(dataset_dir):
        results['errors'].append(f"Dataset directory does not exist: {dataset_dir}")
        if verbose:
            print(f"❌ Dataset directory not found: {dataset_dir}")
        return results
    
    results['dataset_exists'] = True
    
    # Check subdirectories
    subdirs = ['images', 'masks', 'metadata', 'visualizations']
    for subdir in subdirs:
        subdir_path = os.path.join(dataset_dir, subdir)
        exists = os.path.exists(subdir_path)
        results[f'{subdir}_dir_exists'] = exists
        
        if exists:
            # Count files in subdirectory
            files = [f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))]
            results[f'num_{subdir}'] = len(files)
            if verbose:
                print(f"✅ {subdir.capitalize()} directory: {len(files)} files")
        else:
            if verbose:
                print(f"❌ {subdir.capitalize()} directory not found")
    
    # Check dataset info file
    dataset_info_path = os.path.join(dataset_dir, 'dataset_info.json')
    if os.path.exists(dataset_info_path):
        results['dataset_info_exists'] = True
        if verbose:
            print(f"✅ Dataset info file exists")
    else:
        results['errors'].append("Dataset info file missing")
        if verbose:
            print(f"❌ Dataset info file missing")
    
    # Check if file counts match
    if results['images_dir_exists'] and results['masks_dir_exists']:
        files_match = (results['num_images'] == results['num_masks'] == results['num_metadata'])
        results['files_match'] = files_match
        
        if files_match:
            if verbose:
                print(f"✅ File counts match: {results['num_images']} samples")
        else:
            results['errors'].append(f"File count mismatch: images={results['num_images']}, masks={results['num_masks']}, metadata={results['num_metadata']}")
            if verbose:
                print(f"❌ File count mismatch")
    
    # Check expected sample count
    if expected_samples is not None:
        if results['num_images'] == expected_samples:
            if verbose:
                print(f"✅ Expected sample count matches: {expected_samples}")
        else:
            results['errors'].append(f"Expected {expected_samples} samples, found {results['num_images']}")
            if verbose:
                print(f"❌ Expected {expected_samples} samples, found {results['num_images']}")
    
    return results



def check_sample_quality(dataset_dir, num_samples_to_check=5, verbose=True):
    """
    Check the quality of individual samples in the dataset
    
    Args:
        dataset_dir: Directory containing the dataset
        num_samples_to_check: Number of random samples to check
        verbose: Whether to print detailed information
    
    Returns:
        dict: Dictionary containing quality check results
    """
    import os
    import json
    import random
    from PIL import Image
    import numpy as np
    
    results = {
        'samples_checked': 0,
        'valid_samples': 0,
        'invalid_samples': 0,
        'sample_details': [],
        'errors': []
    }
    
    if verbose:
        print(f"🔬 Checking sample quality in: {dataset_dir}")
    
    # Get list of image files
    images_dir = os.path.join(dataset_dir, 'images')
    if not os.path.exists(images_dir):
        results['errors'].append("Images directory not found")
        return results
    
    image_files = [f for f in os.listdir(images_dir) if f.endswith(('.tif', '.tiff', '.png', '.jpg'))]
    if len(image_files) == 0:
        results['errors'].append("No image files found")
        return results
    
    # Randomly select samples to check
    samples_to_check = min(num_samples_to_check, len(image_files))
    selected_files = random.sample(image_files, samples_to_check)
    
    for image_file in selected_files:
        sample_id = image_file.split('.')[0].replace('image_', '')
        sample_result = {
            'sample_id': sample_id,
            'image_file': image_file,
            'image_valid': False,
            'mask_valid': False,
            'metadata_valid': False,
            'spots_detected': 0,
            'image_shape': None,
            'mask_shape': None,
            'unique_spot_ids': 0,
            'errors': []
        }
        
        try:
            # Check image
            image_path = os.path.join(images_dir, image_file)
            image = Image.open(image_path)
            image_array = np.array(image)
            sample_result['image_shape'] = image_array.shape
            sample_result['image_valid'] = True
            
            # Check mask
            mask_file = image_file.replace('image_', 'mask_')
            mask_path = os.path.join(dataset_dir, 'masks', mask_file)
            if os.path.exists(mask_path):
                mask = Image.open(mask_path)
                mask_array = np.array(mask)
                sample_result['mask_shape'] = mask_array.shape
                sample_result['mask_valid'] = True
                
                # Count unique spot IDs
                unique_ids = np.unique(mask_array[mask_array > 0])
                sample_result['unique_spot_ids'] = len(unique_ids)
                sample_result['spots_detected'] = len(unique_ids)
            else:
                sample_result['errors'].append(f"Mask file not found: {mask_file}")
            
            # Check metadata
            metadata_file = f"metadata_{sample_id}.json"
            metadata_path = os.path.join(dataset_dir, 'metadata', metadata_file)
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                sample_result['metadata_valid'] = True
                
                # Verify metadata consistency
                if 'num_spots' in metadata:
                    metadata_spots = metadata['num_spots']
                    if metadata_spots != sample_result['spots_detected']:
                        sample_result['errors'].append(f"Spot count mismatch: metadata={metadata_spots}, detected={sample_result['spots_detected']}")
            else:
                sample_result['errors'].append(f"Metadata file not found: {metadata_file}")
            
            # Determine if sample is valid
            is_valid = (sample_result['image_valid'] and 
                       sample_result['mask_valid'] and 
                       sample_result['metadata_valid'] and 
                       len(sample_result['errors']) == 0)
            
            if is_valid:
                results['valid_samples'] += 1
            else:
                results['invalid_samples'] += 1
            
            results['sample_details'].append(sample_result)
            results['samples_checked'] += 1
            
            if verbose:
                status = "✅" if is_valid else "❌"
                print(f"{status} Sample {sample_id}: {sample_result['spots_detected']} spots")
                if sample_result['errors']:
                    for error in sample_result['errors']:
                        print(f"   ⚠️  {error}")
        
        except Exception as e:
            sample_result['errors'].append(f"Exception during check: {str(e)}")
            results['invalid_samples'] += 1
            results['sample_details'].append(sample_result)
            results['samples_checked'] += 1
            
            if verbose:
                print(f"❌ Sample {sample_id}: Error - {str(e)}")
    
    if verbose:
        print(f"\n📊 Quality Check Summary:")
        print(f"   Samples checked: {results['samples_checked']}")
        print(f"   Valid samples: {results['valid_samples']}")
        print(f"   Invalid samples: {results['invalid_samples']}")
    
    return results

# Generate test dataset as requested
# print("Generating test dataset...")
# test_data_saver = generate_test_dataset()

# # Generate training dataset as requested (with 100 samples)
# print("\nGenerating training dataset...")
# training_data_saver = generate_training_dataset(100)

# # Check the integrity of both datasets
# print("\nChecking test dataset integrity...")
# test_integrity = check_dataset_integrity("test_dataset", expected_samples=50)

# print("\nChecking training dataset integrity...")
# training_integrity = check_dataset_integrity("training_dataset", expected_samples=100)

# Check sample quality
print("\nChecking test dataset sample quality...")
test_quality = check_sample_quality("test_dataset", num_samples_to_check=3)

print("\nChecking training dataset sample quality...")
training_quality = check_sample_quality("training_dataset", num_samples_to_check=5)

# Summary
print("\n" + "="*50)
print("📋 DATASET GENERATION SUMMARY")
print("="*50)
print(f"Test Dataset: {test_integrity['num_images']} samples generated")
print(f"Training Dataset: {training_integrity['num_images']} samples generated")
print(f"Test Dataset Quality: {test_quality['valid_samples']}/{test_quality['samples_checked']} samples valid")
print(f"Training Dataset Quality: {training_quality['valid_samples']}/{training_quality['samples_checked']} samples valid")

if test_integrity['errors'] or training_integrity['errors']:
    print("\n⚠️  ERRORS DETECTED:")
    for error in test_integrity['errors']:
        print(f"   Test Dataset: {error}")
    for error in training_integrity['errors']:
        print(f"   Training Dataset: {error}")
else:
    print("\n✅ All datasets generated successfully with no errors!")

from skimage.measure import find_contours
import matplotlib.pyplot as plt
import numpy as np

def test_synthetic_params(params, num_samples=4):
    """Generate and visualize synthetic data with given parameters"""
    # Create generator with parameters
    generator = AdvancedSyntheticSpotGenerator(**params)
    
    # Generate samples
    images, masks = generator.generate_batch(num_samples)
    
    # Visualize samples
    fig, axes = plt.subplots(num_samples, 2, figsize=(10, 5*num_samples))
    
    for i in range(num_samples):
        img = images[i]
        mask = masks[i]
        
        # Display image with red mask contours
        axes[i, 0].imshow(img, cmap='gray')
        
        # Find and overlay red contours
        contours = find_contours(mask, level=0.5)
        for contour in contours:
            axes[i, 0].plot(contour[:, 1], contour[:, 0], color='red', linewidth=1)
        
        axes[i, 0].set_title(f'Synthetic Image {i+1} with Mask Contours')
        axes[i, 0].axis('off')
        
        # Display raw mask with label colormap
        axes[i, 1].imshow(mask, cmap='nipy_spectral')
        axes[i, 1].set_title(f'Mask {i+1} (Unique IDs)')
        axes[i, 1].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Count spots in each mask
    for i, mask in enumerate(masks):
        unique_ids = np.unique(mask)
        num_spots = len(unique_ids) - (1 if 0 in unique_ids else 0)  # Subtract background
        print(f"Sample {i+1}: {num_spots} spots detected")
    
    return images, masks


# Test with our parameters
test_images, test_masks = test_synthetic_params(synthetic_params)

# Create a model
model = OptimizedSpotDetectionModel(
    in_channels=1,
    num_experts=3,
    base_filters=64,
    dropout_rate=0.2
)
model = model.to(device)

# Use the first synthetic image for testing
test_image = test_images[0]

# Function to get prediction from model
def get_prediction(model, image):
    """Get prediction heatmap from model"""
    # Convert to tensor
    if isinstance(image, np.ndarray):
        if len(image.shape) == 2:
            image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()
        else:
            image_tensor = torch.from_numpy(image).unsqueeze(0).float()
    else:
        image_tensor = image
    
    # Ensure values are in [0, 1]
    if image_tensor.max() > 1.0:
        image_tensor = image_tensor / 255.0
    
    # Move to device
    image_tensor = image_tensor.to(device)
    
    # Get prediction
    with torch.no_grad():
        outputs = model(image_tensor)
        
        # Get main output
        if isinstance(outputs, dict):
            pred = outputs.get('output', outputs.get('combined_output', None))
        else:
            pred = outputs
        
        # Apply sigmoid
        pred_sigmoid = torch.sigmoid(pred)
        
        # Convert to numpy
        heatmap = pred_sigmoid.squeeze().cpu().numpy()
    
    return heatmap

class ImprovedPeakDetector:
    """
    Enhanced peak detection that uses actual spot boundaries from instance masks
    instead of fixed circles for more accurate spot detection.
    """
    
    def __init__(self, min_distance=5, min_intensity=0.1, min_area=3, max_area=1000):
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.min_area = min_area
        self.max_area = max_area
    
    def detect_spots_with_labels(self, image, heatmap, ground_truth_mask=None):
        """
        Detect spots using peak detection and extract real spot boundaries
        
        Args:
            image: Original image
            heatmap: Model prediction heatmap
            ground_truth_mask: Optional ground truth instance mask for comparison
        
        Returns:
            Dictionary with detected spots and their real boundaries
        """
        from skimage.feature import peak_local_max
        from skimage import measure
        from scipy import ndimage
        
        # Find peaks in heatmap
        peaks = peak_local_max(
            heatmap,
            min_distance=self.min_distance,
            threshold_abs=self.min_intensity,
            exclude_border=False,
            indices=True
        )
        
        # Create binary mask from heatmap
        binary_mask = heatmap > self.min_intensity
        
        # Label connected components
        labeled_mask = measure.label(binary_mask)
        
        # Get region properties (ensure integer type)
        labeled_mask_int = labeled_mask.astype(np.int32)
        regions = measure.regionprops(labeled_mask_int, intensity_image=heatmap)
        
        # Filter regions by area and intensity
        valid_spots = []
        for region in regions:
            if (self.min_area <= region.area <= self.max_area and 
                region.max_intensity >= self.min_intensity):
                
                # Get spot information
                spot_info = {
                    'id': region.label,
                    'centroid': region.centroid,
                    'area': region.area,
                    'bbox': region.bbox,
                    'coords': region.coords,
                    'intensity_mean': region.mean_intensity,
                    'intensity_max': region.max_intensity,
                    'equivalent_diameter': region.equivalent_diameter,
                    'eccentricity': region.eccentricity,
                    'solidity': region.solidity
                }
                valid_spots.append(spot_info)
        
        # Compare with ground truth if provided
        comparison_results = None
        if ground_truth_mask is not None:
            comparison_results = self._compare_with_ground_truth(
                valid_spots, ground_truth_mask
            )
        
        return {
            'spots': valid_spots,
            'num_spots': len(valid_spots),
            'labeled_mask': labeled_mask,
            'binary_mask': binary_mask,
            'peaks': peaks,
            'comparison': comparison_results
        }
    
    def _compare_with_ground_truth(self, detected_spots, ground_truth_mask):
        """
        Compare detected spots with ground truth instance mask
        """
        from skimage import measure
        import numpy as np
        
        # Get ground truth regions (ensure integer type)
        gt_mask_int = ground_truth_mask.astype(np.int32)
        gt_regions = measure.regionprops(gt_mask_int)
        gt_centroids = [region.centroid for region in gt_regions]
        
        # Match detected spots to ground truth
        matches = []
        false_positives = []
        
        for spot in detected_spots:
            spot_centroid = spot['centroid']
            
            # Find closest ground truth centroid
            min_distance = float('inf')
            closest_gt = None
            
            for i, gt_centroid in enumerate(gt_centroids):
                distance = np.sqrt(
                    (spot_centroid[0] - gt_centroid[0])**2 + 
                    (spot_centroid[1] - gt_centroid[1])**2
                )
                if distance < min_distance:
                    min_distance = distance
                    closest_gt = i
            
            # Consider it a match if within reasonable distance
            if min_distance < self.min_distance:
                matches.append({
                    'detected_spot': spot,
                    'ground_truth_region': gt_regions[closest_gt],
                    'distance': min_distance
                })
            else:
                false_positives.append(spot)
        
        # Calculate metrics
        num_detected = len(detected_spots)
        num_ground_truth = len(gt_regions)
        num_matches = len(matches)
        num_false_positives = len(false_positives)
        num_false_negatives = num_ground_truth - num_matches
        
        precision = num_matches / num_detected if num_detected > 0 else 0
        recall = num_matches / num_ground_truth if num_ground_truth > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'matches': matches,
            'false_positives': false_positives,
            'num_detected': num_detected,
            'num_ground_truth': num_ground_truth,
            'num_matches': num_matches,
            'num_false_positives': num_false_positives,
            'num_false_negatives': num_false_negatives,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score
        }
    
    def visualize_detection(self, image, heatmap, detection_results, ground_truth_mask=None):
        """
        Create comprehensive visualization of detection results
        """
        import matplotlib.pyplot as plt
        import numpy as np
        from matplotlib.patches import Polygon
        from matplotlib.collections import PatchCollection
        
        # Determine number of subplots
        num_plots = 4 if ground_truth_mask is not None else 3
        fig, axes = plt.subplots(1, num_plots, figsize=(5*num_plots, 5))
        
        if num_plots == 3:
            axes = [axes[0], axes[1], axes[2]]
        else:
            axes = [axes[0], axes[1], axes[2], axes[3]]
        
        # Original image
        axes[0].imshow(image, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Heatmap
        im1 = axes[1].imshow(heatmap, cmap='hot')
        axes[1].set_title('Prediction Heatmap')
        axes[1].axis('off')
        plt.colorbar(im1, ax=axes[1], fraction=0.046, pad=0.04)
        
        # Detection results with real boundaries
        axes[2].imshow(image, cmap='gray', alpha=0.7)
        
        # Draw actual spot boundaries
        patches = []
        colors = []
        
        for i, spot in enumerate(detection_results['spots']):
            # Get spot boundary coordinates
            coords = spot['coords']
            
            # Create polygon from coordinates
            if len(coords) > 2:
                # Convert to (x, y) format for matplotlib
                polygon_coords = [(coord[1], coord[0]) for coord in coords]
                polygon = Polygon(polygon_coords, closed=True)
                patches.append(polygon)
                colors.append(i)
            
            # Add centroid marker
            centroid = spot['centroid']
            axes[2].plot(centroid[1], centroid[0], 'r+', markersize=8, markeredgewidth=2)
            
            # Add spot ID
            axes[2].text(centroid[1], centroid[0], str(spot['id']), 
                        color='red', fontsize=8, ha='center', va='center',
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        
        # Add patches to plot
        if patches:
            p = PatchCollection(patches, alpha=0.3, cmap='tab20')
            p.set_array(np.array(colors))
            axes[2].add_collection(p)
        
        axes[2].set_title(f'Detected Spots: {detection_results["num_spots"]} (Real Boundaries)')
        axes[2].axis('off')
        
        # Ground truth comparison if available
        if ground_truth_mask is not None and detection_results['comparison'] is not None:
            comp = detection_results['comparison']
            
            axes[3].imshow(image, cmap='gray', alpha=0.7)
            
            # Show ground truth in blue, matches in green, false positives in red
            gt_mask_colored = np.ma.masked_where(ground_truth_mask == 0, ground_truth_mask)
            axes[3].imshow(gt_mask_colored, cmap='Blues', alpha=0.3)
            
            # Mark matches in green
            for match in comp['matches']:
                centroid = match['detected_spot']['centroid']
                axes[3].plot(centroid[1], centroid[0], 'go', markersize=8)
            
            # Mark false positives in red
            for fp in comp['false_positives']:
                centroid = fp['centroid']
                axes[3].plot(centroid[1], centroid[0], 'rx', markersize=8, markeredgewidth=2)
            
            axes[3].set_title(f'Comparison (P:{comp["precision"]:.2f}, R:{comp["recall"]:.2f}, F1:{comp["f1_score"]:.2f})')
            axes[3].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Print detection statistics
        print(f"\n📊 Detection Results:")
        print(f"   • Number of spots detected: {detection_results['num_spots']}")
        print(f"   • Average spot area: {np.mean([s['area'] for s in detection_results['spots']]):.1f} pixels")
        print(f"   • Average spot intensity: {np.mean([s['intensity_mean'] for s in detection_results['spots']]):.3f}")
        
        if detection_results['comparison'] is not None:
            comp = detection_results['comparison']
            print(f"\n🎯 Accuracy Metrics:")
            print(f"   • Precision: {comp['precision']:.3f}")
            print(f"   • Recall: {comp['recall']:.3f}")
            print(f"   • F1-Score: {comp['f1_score']:.3f}")
            print(f"   • True Positives: {comp['num_matches']}")
            print(f"   • False Positives: {comp['num_false_positives']}")
            print(f"   • False Negatives: {comp['num_false_negatives']}")

print("✅ ImprovedPeakDetector class defined successfully!")

# Fix any import issues and ensure all required functions are available
try:
    from skimage.feature import peak_local_max
    from skimage import measure
    from scipy import ndimage
    from scipy.ndimage import gaussian_filter
    from matplotlib.patches import Polygon
    from matplotlib.collections import PatchCollection
    print("✅ All imports successful!")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install missing packages with: pip install scikit-image scipy matplotlib")

# Ensure get_prediction function exists
if 'get_prediction' not in globals():
    def get_prediction(model, image):
        """Get prediction heatmap from model"""
        # Convert to tensor
        if isinstance(image, np.ndarray):
            if len(image.shape) == 2:
                image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()
            else:
                image_tensor = torch.from_numpy(image).unsqueeze(0).float()
        else:
            image_tensor = image
        
        # Move to device
        image_tensor = image_tensor.to(device)
        
        # Get prediction
        model.eval()
        with torch.no_grad():
            prediction = model(image_tensor)
            if isinstance(prediction, tuple):
                prediction = prediction[0]  # Take first output if tuple
            prediction = torch.sigmoid(prediction).squeeze().cpu().numpy()
        
        return prediction
    
    print("✅ get_prediction function defined!")

print("🔧 Setup complete - ready for testing!")

# Enhanced ImprovedPeakDetector with better type handling
class ImprovedPeakDetectorFixed(ImprovedPeakDetector):
    """Enhanced version with better type handling"""
    
    def detect_spots_with_labels(self, image, heatmap, ground_truth_mask=None):
        """
        Detect spots using peak detection and extract real spot boundaries
        Enhanced with proper type handling
        """
        from skimage.feature import peak_local_max
        from skimage import measure
        from scipy import ndimage
        import numpy as np
        
        # Ensure inputs are numpy arrays with correct types
        image = np.asarray(image, dtype=np.float64)
        heatmap = np.asarray(heatmap, dtype=np.float64)
        
        if ground_truth_mask is not None:
            ground_truth_mask = np.asarray(ground_truth_mask, dtype=np.int32)
        
        # Find peaks in heatmap
        try:
            peaks = peak_local_max(
                heatmap,
                min_distance=self.min_distance,
                threshold_abs=self.min_intensity,
                exclude_border=False
            )
        except Exception as e:
            print(f"Warning: peak_local_max failed: {e}")
            # Fallback: find peaks manually
            peaks = self._find_peaks_manual(heatmap)
        
        # Create binary mask from heatmap
        binary_mask = heatmap > self.min_intensity
        
        # Label connected components
        labeled_mask = measure.label(binary_mask)
        labeled_mask = labeled_mask.astype(np.int32)  # Ensure integer type
        
        # Get region properties
        try:
            regions = measure.regionprops(labeled_mask, intensity_image=heatmap)
        except Exception as e:
            print(f"Warning: regionprops failed: {e}")
            return {
                'spots': [],
                'num_spots': 0,
                'labeled_mask': labeled_mask,
                'binary_mask': binary_mask,
                'peaks': peaks,
                'comparison': None
            }
        
        # Filter regions by area and intensity
        valid_spots = []
        for region in regions:
            if (self.min_area <= region.area <= self.max_area and 
                region.max_intensity >= self.min_intensity):
                
                # Get spot information
                spot_info = {
                    'id': int(region.label),
                    'centroid': tuple(float(x) for x in region.centroid),
                    'area': int(region.area),
                    'bbox': tuple(int(x) for x in region.bbox),
                    'coords': region.coords.tolist(),
                    'intensity_mean': float(region.mean_intensity),
                    'intensity_max': float(region.max_intensity),
                    'equivalent_diameter': float(region.equivalent_diameter),
                    'eccentricity': float(region.eccentricity),
                    'solidity': float(region.solidity)
                }
                valid_spots.append(spot_info)
        
        # Compare with ground truth if provided
        comparison_results = None
        if ground_truth_mask is not None:
            comparison_results = self._compare_with_ground_truth_fixed(
                valid_spots, ground_truth_mask
            )
        
        return {
            'spots': valid_spots,
            'num_spots': len(valid_spots),
            'labeled_mask': labeled_mask,
            'binary_mask': binary_mask,
            'peaks': peaks,
            'comparison': comparison_results
        }
    
    def _find_peaks_manual(self, heatmap):
        """Manual peak finding as fallback"""
        from scipy.ndimage import maximum_filter
        
        # Find local maxima
        local_maxima = maximum_filter(heatmap, size=self.min_distance) == heatmap
        local_maxima &= heatmap > self.min_intensity
        
        # Get coordinates
        peaks = np.column_stack(np.where(local_maxima))
        return peaks
    
    def _compare_with_ground_truth_fixed(self, detected_spots, ground_truth_mask):
        """Compare with ground truth with proper type handling"""
        from skimage import measure
        import numpy as np
        
        # Ensure ground truth mask is integer type
        gt_mask_int = np.asarray(ground_truth_mask, dtype=np.int32)
        
        try:
            # Get ground truth regions
            gt_regions = measure.regionprops(gt_mask_int)
            gt_centroids = [region.centroid for region in gt_regions]
        except Exception as e:
            print(f"Warning: Could not process ground truth mask: {e}")
            return None
        
        # Match detected spots to ground truth
        matches = []
        false_positives = []
        
        for spot in detected_spots:
            spot_centroid = spot['centroid']
            
            # Find closest ground truth centroid
            min_distance = float('inf')
            closest_gt = None
            
            for i, gt_centroid in enumerate(gt_centroids):
                distance = np.sqrt(
                    (spot_centroid[0] - gt_centroid[0])**2 + 
                    (spot_centroid[1] - gt_centroid[1])**2
                )
                if distance < min_distance:
                    min_distance = distance
                    closest_gt = i
            
            # Consider it a match if within reasonable distance
            if min_distance < self.min_distance:
                matches.append({
                    'detected_spot': spot,
                    'ground_truth_region': gt_regions[closest_gt],
                    'distance': float(min_distance)
                })
            else:
                false_positives.append(spot)
        
        # Calculate metrics
        num_detected = len(detected_spots)
        num_ground_truth = len(gt_regions)
        num_matches = len(matches)
        num_false_positives = len(false_positives)
        num_false_negatives = num_ground_truth - num_matches
        
        precision = num_matches / num_detected if num_detected > 0 else 0.0
        recall = num_matches / num_ground_truth if num_ground_truth > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            'matches': matches,
            'false_positives': false_positives,
            'num_detected': num_detected,
            'num_ground_truth': num_ground_truth,
            'num_matches': num_matches,
            'num_false_positives': num_false_positives,
            'num_false_negatives': num_false_negatives,
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1_score)
        }

print("✅ ImprovedPeakDetectorFixed class defined with enhanced type safety!")

def refine_parameter_search(base_params, test_image, mock_heatmap, test_mask, detector_cls, delta=1):
    """Refine the parameter search around the best base_params"""
    from itertools import product

    print(f"\n🔍 Refining parameters around: {base_params}")
    
    param_ranges = {
        'min_distance': range(max(1, base_params['min_distance'] - delta), base_params['min_distance'] + delta + 1),
        'min_intensity': np.linspace(
            max(0.01, base_params['min_intensity'] - 0.05),
            min(1.0, base_params['min_intensity'] + 0.05),
            3
        ),
        'min_area': range(max(1, base_params['min_area'] - delta), base_params['min_area'] + delta + 1),
        'max_area': range(max(base_params['max_area'] - delta * 100, 10),
                          base_params['max_area'] + delta * 100 + 1, 100)
    }

    best_f1 = 0
    best_params = {}
    best_result = None

    combinations = list(product(
        param_ranges['min_distance'],
        param_ranges['min_intensity'],
        param_ranges['min_area'],
        param_ranges['max_area']
    ))

    print(f"🧪 Testing {len(combinations)} refined combinations...")

    for min_dist, min_int, min_area, max_area in combinations:
        params = {
            'min_distance': int(min_dist),
            'min_intensity': float(min_int),
            'min_area': int(min_area),
            'max_area': int(max_area)
        }
        detector = detector_cls(**params)
        result = detector.detect_spots_with_labels(test_image, mock_heatmap, ground_truth_mask=test_mask)
        comp = result['comparison']
        if comp['f1_score'] > best_f1:
            best_f1 = comp['f1_score']
            best_params = params
            best_result = result

    print(f"\n✅ Refined best parameters: {best_params} (F1: {best_f1:.3f})")
    return best_result, best_params
# Test the improved peak detector with synthetic data
def test_improved_peak_detection():
    """Test the improved peak detector with synthetic data"""
    
    # Generate synthetic data for testing
    print("🔄 Generating synthetic test data...")
    
    # Create synthetic data saver if not already created
    if 'data_saver' not in globals():
        data_saver = SyntheticDataSaver(synthetic_params)
    
    # Generate a test sample
    test_image, test_mask, test_spot_data = data_saver.generator.generate_sample()
    
    print(f"   • Generated image with {len(test_spot_data)} spots")
    print(f"   • Image shape: {test_image.shape}")
    print(f"   • Mask has {len(np.unique(test_mask[test_mask > 0]))} unique spot IDs")
    
    # Create a mock heatmap (in real scenario, this would come from your trained model)
    # For demonstration, we'll create a heatmap based on the ground truth
    mock_heatmap = np.zeros_like(test_image)
    
    # Add Gaussian blobs at each spot location with some noise
    from scipy.ndimage import gaussian_filter
    
    for spot in test_spot_data:
        x, y = int(spot['x']), int(spot['y'])
        radius = spot['radius']
        intensity = spot['intensity'] * 0.8 + np.random.normal(0, 0.1)  # Add some noise
        
        # Create a small blob at the spot location
        y_coords, x_coords = np.ogrid[:test_image.shape[0], :test_image.shape[1]]
        mask_blob = (x_coords - x)**2 + (y_coords - y)**2 <= (radius * 1.2)**2
        mock_heatmap[mask_blob] = intensity
    
    # Apply Gaussian smoothing to make it more realistic
    mock_heatmap = gaussian_filter(mock_heatmap, sigma=1.0)
    
    # Add some background noise
    noise = np.random.normal(0, 0.05, mock_heatmap.shape)
    mock_heatmap = np.clip(mock_heatmap + noise, 0, 1)
    
    print(f"   • Created mock heatmap with max intensity: {mock_heatmap.max():.3f}")
    
    # Test different parameter sets
    parameter_sets = {
        'Conservative': {'min_distance': 8, 'min_intensity': 0.3, 'min_area': 5, 'max_area': 500},
        'Balanced': {'min_distance': 5, 'min_intensity': 0.2, 'min_area': 3, 'max_area': 800},
        'Sensitive': {'min_distance': 3, 'min_intensity': 0.1, 'min_area': 2, 'max_area': 1000}
    }
    
    print(f"\n🧪 Testing different parameter sets...")
    
    best_f1 = 0
    best_params = None
    best_results = None
    
    for name, params in parameter_sets.items():
        print(f"\n--- Testing {name} parameters ---")
        print(f"Parameters: {params}")
        
        # Create detector with current parameters
        detector = ImprovedPeakDetectorFixed(**params)
        
        # Detect spots
        results = detector.detect_spots_with_labels(
            test_image, 
            mock_heatmap, 
            ground_truth_mask=test_mask
        )
        
        # Print results
        comp = results['comparison']
        print(f"Detected: {results['num_spots']}, Ground Truth: {comp['num_ground_truth']}")
        print(f"Precision: {comp['precision']:.3f}, Recall: {comp['recall']:.3f}, F1: {comp['f1_score']:.3f}")
        
        # Track best results
        if comp['f1_score'] > best_f1:
            best_f1 = comp['f1_score']
            best_params = name
            best_results = results
    
    print(f"\n🏆 Best parameter set: {best_params} (F1-Score: {best_f1:.3f})")
    
    # Refine the best parameter set
    refined_results, refined_params = refine_parameter_search(
        base_params=parameter_sets[best_params],
        test_image=test_image,
        mock_heatmap=mock_heatmap,
        test_mask=test_mask,
        detector_cls=ImprovedPeakDetectorFixed
    )
    
    # Visualize the best results
    print(f"\n📊 Visualizing best results...")
    detector = ImprovedPeakDetectorFixed(**parameter_sets[best_params])
    detector.visualize_detection(
        test_image, 
        mock_heatmap, 
        best_results, 
        ground_truth_mask=test_mask
    )
    
    return {
        'test_image': test_image,
        'test_mask': test_mask,
        'mock_heatmap': mock_heatmap,
        'best_results': best_results,
        'best_params': best_params
    }

# Run the test
test_results = test_improved_peak_detection()

# Enhanced predictor class that uses real spot boundaries
class EnhancedSpotPredictor:
    """
    Enhanced spot predictor that combines model predictions with improved peak detection
    to provide accurate spot boundaries instead of fixed circles.
    """
    
    def __init__(self, model, device, detector_params=None):
        self.model = model
        self.device = device
        
        # Default detector parameters (can be optimized based on your data)
        default_params = {
            'min_distance': 5,
            'min_intensity': 0.2,
            'min_area': 3,
            'max_area': 800
        }
        
        if detector_params:
            default_params.update(detector_params)
        
        self.detector = ImprovedPeakDetectorFixed(**default_params)
    
    def predict_with_boundaries(self, image, ground_truth_mask=None):
        """
        Predict spots with real boundaries instead of fixed circles
        
        Args:
            image: Input image
            ground_truth_mask: Optional ground truth for evaluation
        
        Returns:
            Dictionary with prediction results and real spot boundaries
        """
        # Get model prediction
        heatmap = get_prediction(self.model, image)
        
        # Detect spots with real boundaries
        detection_results = self.detector.detect_spots_with_labels(
            image, heatmap, ground_truth_mask
        )
        
        return {
            'heatmap': heatmap,
            'detection_results': detection_results,
            'num_spots': detection_results['num_spots'],
            'spots': detection_results['spots']
        }
    
    def visualize_prediction(self, image, ground_truth_mask=None):
        """
        Visualize prediction results with real spot boundaries
        """
        results = self.predict_with_boundaries(image, ground_truth_mask)
        
        self.detector.visualize_detection(
            image,
            results['heatmap'],
            results['detection_results'],
            ground_truth_mask
        )
        
        return results
    
    def update_detector_params(self, **params):
        """
        Update detector parameters for fine-tuning
        """
        for param, value in params.items():
            if hasattr(self.detector, param):
                setattr(self.detector, param, value)
        
        print(f"Updated detector parameters: {params}")
    
    def evaluate_on_dataset(self, images, ground_truth_masks):
        """
        Evaluate the predictor on a dataset
        
        Args:
            images: List of images
            ground_truth_masks: List of corresponding ground truth masks
        
        Returns:
            Evaluation metrics
        """
        all_precisions = []
        all_recalls = []
        all_f1_scores = []
        
        for i, (image, gt_mask) in enumerate(zip(images, ground_truth_masks)):
            results = self.predict_with_boundaries(image, gt_mask)
            
            if results['detection_results']['comparison'] is not None:
                comp = results['detection_results']['comparison']
                all_precisions.append(comp['precision'])
                all_recalls.append(comp['recall'])
                all_f1_scores.append(comp['f1_score'])
        
        return {
            'mean_precision': np.mean(all_precisions),
            'mean_recall': np.mean(all_recalls),
            'mean_f1_score': np.mean(all_f1_scores),
            'std_precision': np.std(all_precisions),
            'std_recall': np.std(all_recalls),
            'std_f1_score': np.std(all_f1_scores)
        }

print("✅ EnhancedSpotPredictor class defined successfully!")
print("\n💡 Usage Examples:")
print("\n1. Create enhanced predictor:")
print("```python")
print("predictor = EnhancedSpotPredictor(model, device)")
print("```")
print("\n2. Predict with real boundaries:")
print("```python")
print("results = predictor.predict_with_boundaries(image, ground_truth_mask)")
print("spots = results['spots']  # Each spot has real boundary coordinates")
print("```")
print("\n3. Visualize with accurate boundaries:")
print("```python")
print("predictor.visualize_prediction(image, ground_truth_mask)")
print("```")
print("\n4. Fine-tune detection parameters:")
print("```python")
print("predictor.update_detector_params(min_distance=3, min_intensity=0.15)")
print("```")

# Get prediction
heatmap = get_prediction(model, test_image)

# Test different peak detection parameters
best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)

from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly

# Test different parameters to find the best ones
best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)

# Use the best parameters for future detections
result = detect_peaks_directly(
    test_image, 
    heatmap, 
    min_distance=best_distance, 
    min_intensity=best_intensity
)

# Display the result
plt.figure(figsize=(12, 4))
plt.subplot(131)
plt.imshow(test_image, cmap='gray')
plt.title('Original Image')
plt.axis('off')

plt.subplot(132)
plt.imshow(heatmap, cmap='hot')
plt.title('Heatmap')
plt.axis('off')

plt.subplot(133)
plt.imshow(result['visualization'])
plt.title(f'Detected Spots: {result["num_spots"]}')
plt.axis('off')

plt.tight_layout()
plt.show()


from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly

# Test different parameters to find the best ones
best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)

# Use the best parameters for future detections
result = detect_peaks_directly(
    test_image, 
    heatmap, 
    min_distance=best_distance, 
    min_intensity=best_intensity
)

# Display the result
plt.figure(figsize=(12, 4))
plt.subplot(131)
plt.imshow(test_image, cmap='gray')
plt.title('Original Image')
plt.axis('off')

plt.subplot(132)
plt.imshow(heatmap, cmap='hot')
plt.title('Heatmap')
plt.axis('off')

plt.subplot(133)
plt.imshow(result['visualization'])
plt.title(f'Detected Spots: {result["num_spots"]}')
plt.axis('off')

plt.tight_layout()
plt.show()


# Create data loaders with our synthetic parameters
train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(
    data_dir=None,  # No real data, using synthetic only
    batch_size=8,
    image_size=256,
    train_val_split=0.8,
    synthetic=True,
    synthetic_size=100,  # Small dataset for demonstration
    augmentation_level='strong',
    num_workers=4,
    synthetic_params=synthetic_params  # Use our custom parameters
)

# Define loss function with peak detection integration
class PeakDetectionLoss(nn.Module):
    def __init__(self, base_loss=None, peak_weight=0.5, min_distance=5, min_intensity=0.1):
        super().__init__()
        self.base_loss = base_loss or EnhancedSpotLoss()
        self.peak_weight = peak_weight
        self.min_distance = min_distance
        self.min_intensity = min_intensity
    
    def forward(self, pred, target):
        # Base loss
        base_loss_val = self.base_loss(pred, target)
        
        # Peak detection loss
        batch_size = pred.size(0)
        peak_loss = 0.0
        
        for i in range(batch_size):
            # Get prediction and target for this sample
            pred_i = torch.sigmoid(pred[i]).squeeze().detach().cpu().numpy()
            target_i = target[i].squeeze().cpu().numpy()
            
            # Find peaks in prediction
            pred_peaks = peak_local_max(
                pred_i,
                min_distance=self.min_distance,
                threshold_abs=self.min_intensity,
                exclude_border=False
            )
            
            # Find peaks in target
            target_peaks = peak_local_max(
                target_i,
                min_distance=self.min_distance,
                threshold_abs=0.5,  # Higher threshold for ground truth
                exclude_border=False
            )
            
            # Count difference in number of peaks
            peak_diff = abs(len(pred_peaks) - len(target_peaks))
            peak_loss += peak_diff / max(1, len(target_peaks))
        
        # Average over batch
        peak_loss = peak_loss / batch_size
        
        # Combine losses
        total_loss = base_loss_val + self.peak_weight * peak_loss
        
        return total_loss

# Create loss function with peak detection
loss_fn = PeakDetectionLoss(
    base_loss=EnhancedSpotLoss(),
    peak_weight=0.5,
    min_distance=best_distance,
    min_intensity=best_intensity
)

# Create optimizer
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# Create trainer
trainer = ImprovedSpotTrainer(
    model=model,
    loss_fn=loss_fn,
    optimizer=optimizer,
    device=device,
    metrics=EnhancedSpotMetrics(),
    tensorboard_dir=TENSORBOARD_DIR
)

# Train for a few epochs (for demonstration)
best_model_path = os.path.join(RUN_DIR, 'best_model.pth')
trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=5,  # Small number for demonstration
    save_path=best_model_path
)

# Create a peak detection predictor class
class PeakDetectionPredictor:
    def __init__(self, model, device, min_distance=5, min_intensity=0.1, min_spot_size=3):
        self.model = model
        self.device = device
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.min_spot_size = min_spot_size
        
    def update_parameters(self, min_distance=None, min_intensity=None, min_spot_size=None):
        """Update detection parameters"""
        if min_distance is not None:
            self.min_distance = min_distance
        if min_intensity is not None:
            self.min_intensity = min_intensity
        if min_spot_size is not None:
            self.min_spot_size = min_spot_size
        print(f"Updated parameters: min_distance={self.min_distance}, "
              f"min_intensity={self.min_intensity}, min_spot_size={self.min_spot_size}")
    
    def predict(self, image):
        # Get prediction heatmap
        heatmap = get_prediction(self.model, image)
        
        # Detect spots using direct peak detection
        result = detect_peaks_directly(
            image, 
            heatmap, 
            min_distance=self.min_distance, 
            min_intensity=self.min_intensity
        )
        
        return {
            'heatmap': heatmap,
            'num_spots': result['num_spots'],
            'coordinates': result['coordinates'],
            'visualization': result['visualization']
        }
    
    def visualize(self, image):
        # Get prediction
        result = self.predict(image)
        
        # Display results
        plt.figure(figsize=(15, 5))
        
        plt.subplot(131)
        plt.imshow(image, cmap='gray')
        plt.title('Original Image')
        plt.axis('off')
        
        plt.subplot(132)
        plt.imshow(result['heatmap'], cmap='hot')
        plt.title('Prediction Heatmap')
        plt.axis('off')
        
        plt.subplot(133)
        plt.imshow(result['visualization'])
        plt.title(f'Detected Spots: {result["num_spots"]}')
        plt.axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return result

# Create predictor with initial parameters
predictor = PeakDetectionPredictor(
    model=model,
    device=device,
    min_distance=best_distance,
    min_intensity=best_intensity,
    min_spot_size=3
)

# Test on a synthetic image
result = predictor.visualize(test_image)

# Function to optimize detection parameters on a validation set
def optimize_detection_parameters(model, val_loader, device):
    """Find optimal detection parameters after training"""
    print("Optimizing detection parameters...")
    
    # Get a batch of validation images
    val_batch = next(iter(val_loader))
    images = val_batch['image'].to(device)
    masks = val_batch['mask'].cpu().numpy()
    
    # Get predictions
    with torch.no_grad():
        outputs = model(images)
        if isinstance(outputs, dict):
            preds = outputs.get('output', outputs.get('combined_output', None))
        else:
            preds = outputs
        preds = torch.sigmoid(preds).cpu().numpy()
    
    # Parameters to test
    min_distances = [3, 5, 7, 10]
    min_intensities = [0.05, 0.1, 0.2, 0.3, 0.4]
    
    # Store results
    results = {}
    
    # Test each parameter combination
    for min_distance in min_distances:
        for min_intensity in min_intensities:
            total_f1 = 0
            total_samples = 0
            
            # Test on each image in the batch
            for i in range(len(images)):
                image = images[i].squeeze().cpu().numpy()
                pred = preds[i].squeeze()
                mask = masks[i].squeeze()
                
                # Get ground truth spots
                true_labels = measure.label(mask > 0)
                true_props = measure.regionprops(true_labels)
                true_centroids = [prop.centroid for prop in true_props]
                
                # Detect peaks with current parameters
                peaks = peak_local_max(
                    pred,
                    min_distance=min_distance,
                    threshold_abs=min_intensity,
                    exclude_border=False
                )
                
                # Calculate metrics (simplified F1 score based on centroid matching)
                if len(true_centroids) == 0:
                    if len(peaks) == 0:
                        f1 = 1.0  # Perfect match (no spots)
                    else:
                        f1 = 0.0  # False positives
                elif len(peaks) == 0:
                    f1 = 0.0  # False negatives
                else:
                    # Match peaks to ground truth centroids
                    matched = 0
                    for y, x in peaks:
                        for cy, cx in true_centroids:
                            dist = np.sqrt((y - cy)**2 + (x - cx)**2)
                            if dist < min_distance:
                                matched += 1
                                break
                    
                    precision = matched / len(peaks) if len(peaks) > 0 else 0
                    recall = matched / len(true_centroids) if len(true_centroids) > 0 else 0
                    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                total_f1 += f1
                total_samples += 1
            
            # Average F1 score for this parameter combination
            avg_f1 = total_f1 / total_samples if total_samples > 0 else 0
            results[(min_distance, min_intensity)] = avg_f1
    
    # Find best parameters
    best_params = max(results.items(), key=lambda x: x[1])
    best_distance, best_intensity = best_params[0]
    best_f1 = best_params[1]
    
    print(f"\nBest parameters: min_distance={best_distance}, min_intensity={best_intensity:.2f}")
    print(f"F1 Score: {best_f1:.4f}")
    
    # Visualize results as a heatmap
    result_matrix = np.zeros((len(min_distances), len(min_intensities)))
    for i, d in enumerate(min_distances):
        for j, t in enumerate(min_intensities):
            result_matrix[i, j] = results[(d, t)]
    
    plt.figure(figsize=(10, 8))
    plt.imshow(result_matrix, cmap='viridis', interpolation='nearest')
    plt.colorbar(label='F1 Score')
    plt.xticks(range(len(min_intensities)), [f"{t:.2f}" for t in min_intensities])
    plt.yticks(range(len(min_distances)), min_distances)
    plt.xlabel('Minimum Intensity')
    plt.ylabel('Minimum Distance')
    plt.title('Parameter Optimization Results')
    
    for i in range(len(min_distances)):
        for j in range(len(min_intensities)):
            plt.text(j, i, f"{result_matrix[i, j]:.2f}", 
                     ha="center", va="center", color="white" if result_matrix[i, j] < 0.7 else "black")
    
    plt.tight_layout()
    plt.show()
    
    # Save optimal parameters
    optimal_params = {
        'min_distance': int(best_distance),
        'min_intensity': float(best_intensity),
        'f1_score': float(best_f1)
    }
    
    params_path = os.path.join(RUN_DIR, 'optimal_detection_params.json')
    with open(params_path, 'w') as f:
        json.dump(optimal_params, f, indent=2)
    
    print(f"Saved optimal parameters to {params_path}")
    
    return best_distance, best_intensity

# Run optimization after training
optimized_distance, optimized_intensity = optimize_detection_parameters(model, val_loader, device)

# Update predictor with optimized parameters
predictor.min_distance = optimized_distance
predictor.min_intensity = optimized_intensity
print(f"\nUpdated predictor with optimized parameters: min_distance={optimized_distance}, min_intensity={optimized_intensity:.2f}")

# Define different parameter sets to compare
parameter_sets = {
    'sparse': {
        'image_size': (256, 256),
        'min_spots': 5,
        'max_spots': 15,
        'min_radius': 3,
        'max_radius': 8,
        'density_factor': 0.5,
        'mask_threshold': 0.3,
        'allow_touching': False,
        'shape_variation': 0.1,
        'add_gradients': False,
        'realistic_noise': True
    },
    'dense': {
        'image_size': (256, 256),
        'min_spots': 30,
        'max_spots': 100,
        'min_radius': 2,
        'max_radius': 6,
        'density_factor': 2.0,
        'mask_threshold': 0.3,
        'allow_touching': True,
        'shape_variation': 0.2,
        'add_gradients': True,
        'realistic_noise': True
    },
    'varied_sizes': {
        'image_size': (256, 256),
        'min_spots': 10,
        'max_spots': 40,
        'min_radius': 1,
        'max_radius': 12,
        'density_factor': 1.0,
        'mask_threshold': 0.3,
        'allow_touching': True,
        'shape_variation': 0.3,
        'add_gradients': True,
        'realistic_noise': True
    }
}

# Compare parameter sets
for name, params in parameter_sets.items():
    print(f"\n=== Testing {name} parameter set ===")
    images, masks = test_synthetic_params(params, num_samples=2)
    
    # Test peak detection on the first image
    result = predictor.visualize(images[0])