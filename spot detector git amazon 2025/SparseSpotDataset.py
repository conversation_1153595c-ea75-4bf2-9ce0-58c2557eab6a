import torch
import numpy as np
import os
import cv2
from skimage import io
from glob import glob
from torch.utils.data import Dataset

class SparseSpotDataset(Dataset):
    def __init__(self, data_dir, transform=None, is_3d=False):
        super().__init__()
        self.data_dir = data_dir
        self.transform = transform
        self.is_3d = is_3d
        self.images = []
        self.masks = []
        self.confidence_masks = []
        
        # Load all image paths
        self._load_data()
    
    def _load_data(self):
        """Load image and mask paths with consistent orientation"""
        for img_path in glob.glob(os.path.join(self.data_dir, '*.*')):
            # Skip confidence masks
            if '_conf.' in img_path:
                continue
                
            # Get corresponding mask path
            mask_path = img_path.replace('.png', '_mask.png').replace('.tif', '_mask.tif')
            conf_path = img_path.replace('.png', '_conf.png').replace('.tif', '_conf.png')
            
            if os.path.exists(mask_path):
                self.images.append(img_path)
                self.masks.append(mask_path)
                
                # Add confidence mask if it exists, otherwise create default
                if os.path.exists(conf_path):
                    self.confidence_masks.append(conf_path)
                else:
                    # Create default confidence mask (full confidence)
                    mask = io.imread(mask_path)
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    conf_mask = (mask > 0).astype(np.float32)
                    self.confidence_masks.append(conf_mask)

    def __getitem__(self, idx):
        # Load image
        if isinstance(self.images[idx], str):
            image = io.imread(self.images[idx])
            if len(image.shape) == 3 and image.shape[2] > 1:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            image = image.astype(np.float32) / 255.0
        else:
            image = self.images[idx].copy()

        # Load mask and ensure consistent orientation
        if isinstance(self.masks[idx], str):
            mask = io.imread(self.masks[idx])
            if len(mask.shape) == 3 and mask.shape[2] > 1:
                mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
            mask = (mask > 0).astype(np.float32)
            # Ensure consistent orientation with image
            if mask.shape != image.shape:
                mask = cv2.resize(mask, (image.shape[1], image.shape[0]))
        else:
            mask = self.masks[idx].copy()

        # Load confidence mask and ensure consistent orientation
        if isinstance(self.confidence_masks[idx], str):
            conf_mask = io.imread(self.confidence_masks[idx])
            if len(conf_mask.shape) == 3 and conf_mask.shape[2] > 1:
                conf_mask = cv2.cvtColor(conf_mask, cv2.COLOR_RGB2GRAY)
            conf_mask = conf_mask.astype(np.float32) / 255.0
            # Ensure consistent orientation with image
            if conf_mask.shape != image.shape:
                conf_mask = cv2.resize(conf_mask, (image.shape[1], image.shape[0]))
        else:
            conf_mask = self.confidence_masks[idx].copy()

        # Apply transforms to all components together to maintain alignment
        if self.transform:
            augmented = self.transform(
                image=image.copy(),  # Use copy to prevent modifying original
                mask=mask.copy(),
                mask1=conf_mask.copy()
            )
            image = augmented['image']
            mask = augmented['mask']
            conf_mask = augmented['mask1']

        # Ensure proper shape and orientation after transforms
        if len(image.shape) == 2:
            image = np.expand_dims(image, axis=0)
        if len(mask.shape) == 2:
            mask = np.expand_dims(mask, axis=0)
        if len(conf_mask.shape) == 2:
            conf_mask = np.expand_dims(conf_mask, axis=0)

        # Final orientation check - ensure all have same shape
        assert image.shape[1:] == mask.shape[1:] == conf_mask.shape[1:], \
            f"Shape mismatch: image {image.shape}, mask {mask.shape}, conf_mask {conf_mask.shape}"

        return {
            'image': torch.from_numpy(image).float(),
            'mask': torch.from_numpy(mask).float(),
            'confidence': torch.from_numpy(conf_mask).float()
        }

    def __len__(self):
        return len(self.images)


def create_sparse_data_loaders(data_dir=None, 
                              batch_size=8,
                              image_size=(256, 256),
                              train_val_split=0.8,
                              synthetic=True,
                              synthetic_size=1000,
                              augmentation_level='strong',
                              num_workers=4,
                              confidence_threshold=0.9,
                              ignore_threshold=0.3):
    """
    Create train and validation data loaders with sparse annotation support
    
    Args:
        data_dir: Directory containing images and masks
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic: Whether to generate synthetic data
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        confidence_threshold: Threshold for high-confidence predictions
        ignore_threshold: Threshold below which to ignore predictions
        
    Returns:
        train_loader, val_loader
    """
    # Create datasets
    if data_dir is not None:
        # Load real data and split into train/val
        full_dataset = SparseSpotDataset(
            data_dir=data_dir,
            transform=None,
            is_3d=False
        )
        
        # Calculate split sizes
        train_size = int(train_val_split * len(full_dataset))
        val_size = len(full_dataset) - train_size
        
        # Split dataset
        train_indices = list(range(train_size))
        val_indices = list(range(train_size, len(full_dataset)))
        
        # Create new datasets with appropriate augmentation
        train_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            transform=None,
            is_3d=False
        )
        train_dataset.images = [full_dataset.images[i] for i in train_indices]
        train_dataset.masks = [full_dataset.masks[i] for i in train_indices]
        train_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in train_indices]
        
        val_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            transform=None,
            is_3d=False
        )
        val_dataset.images = [full_dataset.images[i] for i in val_indices]
        val_dataset.masks = [full_dataset.masks[i] for i in val_indices]
        val_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in val_indices]
        
    else:
        # Create synthetic datasets
        train_dataset = SparseSpotDataset(
            data_dir=None,
            transform=None,
            is_3d=False
        )
        
        val_dataset = SparseSpotDataset(
            data_dir=None,
            transform=None,
            is_3d=False
        )
    
    # Create data loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader