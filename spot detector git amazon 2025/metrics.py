import torch
import numpy as np
from sklearn.metrics import precision_recall_curve, average_precision_score
from typing import Dict, List, Tuple, Optional, Union
from skimage import measure

class SpotDetectionMetrics:
    """Calculate metrics while maintaining consistent orientation"""
    
    def __init__(self, threshold: float = 0.5, iou_threshold: float = 0.5):
        self.threshold = threshold
        self.iou_threshold = iou_threshold

    def calculate_metrics(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, float]:
        """Calculate metrics with consistent orientation handling"""
        # Ensure tensors have shape [B, C, H, W] or [B, C, D, H, W]
        if len(pred.shape) == 3:
            pred = pred.unsqueeze(1)
        if len(target.shape) == 3:
            target = target.unsqueeze(1)
        
        # Convert to numpy with consistent orientation
        pred_np = pred.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy()
        
        # Calculate metrics for each sample
        batch_size = pred.shape[0]
        metrics_dict = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        for i in range(batch_size):
            # Handle 2D and 3D data
            if len(pred.shape) == 4:  # 2D
                sample_metrics = self._calculate_2d_metrics(
                    pred_np[i, 0], target_np[i, 0]
                )
            else:  # 3D
                sample_metrics = self._calculate_3d_metrics(
                    pred_np[i, 0], target_np[i, 0]
                )
            
            # Accumulate metrics
            for k, v in sample_metrics.items():
                metrics_dict[k] += v
        
        # Average across batch
        for k in metrics_dict:
            metrics_dict[k] /= batch_size
            
        return metrics_dict
    
    def _calculate_2d_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """Calculate 2D metrics with consistent orientation"""
        # Create binary masks
        pred_binary = (pred > self.threshold).astype(np.uint8)
        target_binary = (target > 0).astype(np.uint8)
        
        # Get instance labels with consistent orientation
        pred_labels = measure.label(pred_binary)
        target_labels = measure.label(target_binary)
        
        # Get region properties
        pred_props = measure.regionprops(pred_labels)
        target_props = measure.regionprops(target_labels)
        
        # Calculate instance-level metrics
        tp, fp, fn = 0, 0, 0
        matched_pairs = self._find_matching_pairs(
            pred_props, target_props, pred_labels, target_labels
        )
        
        tp = len(matched_pairs)
        fp = len(pred_props) - tp
        fn = len(target_props) - tp
        
        # Calculate metrics
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        # Calculate IoU and Dice
        intersection = np.logical_and(pred_binary, target_binary).sum()
        union = np.logical_or(pred_binary, target_binary).sum()
        iou = intersection / union if union > 0 else 0
        dice = 2 * intersection / (pred_binary.sum() + target_binary.sum()) if (pred_binary.sum() + target_binary.sum()) > 0 else 0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'iou': iou,
            'dice': dice
        }
    
    def _calculate_3d_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """Calculate 3D metrics with consistent orientation"""
        metrics_3d = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0
        }
        
        # Calculate metrics slice by slice and average
        depth = pred.shape[0]
        for z in range(depth):
            slice_metrics = self._calculate_2d_metrics(pred[z], target[z])
            for k, v in slice_metrics.items():
                metrics_3d[k] += v
        
        # Average metrics across slices
        for k in metrics_3d:
            metrics_3d[k] /= depth
            
        return metrics_3d
    
    def _find_matching_pairs(self, pred_props, target_props, pred_labels, target_labels):
        """Find matching spot pairs while maintaining orientation"""
        matched_pairs = []
        
        for pred_prop in pred_props:
            pred_mask = pred_labels == pred_prop.label
            best_iou = self.iou_threshold
            best_target = None
            
            for target_prop in target_props:
                # Skip already matched targets
                if target_prop in [pair[1] for pair in matched_pairs]:
                    continue
                    
                # Calculate IoU
                target_mask = target_labels == target_prop.label
                intersection = np.logical_and(pred_mask, target_mask).sum()
                union = np.logical_or(pred_mask, target_mask).sum()
                iou = intersection / union if union > 0 else 0
                
                # Update best match if IoU is higher
                if iou > best_iou:
                    best_iou = iou
                    best_target = target_prop
            
            # Add pair if match found
            if best_target is not None:
                matched_pairs.append((pred_prop, best_target))
        
        return matched_pairs