{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Complete Spot Detection with Mixture of Experts\n", "\n", "This notebook provides a comprehensive solution for detecting spots of varying sizes and densities in images using a Mixture of Experts approach. All steps are included in a single notebook for ease of use.\n", "\n", "## Table of Contents\n", "\n", "1. [Setup and Dependencies](#setup)\n", "2. [Data Loading and Preparation](#data)\n", "3. [Model and Loss Function](#model)\n", "4. [Training](#training)\n", "5. [Evaluation](#evaluation)\n", "6. [Prediction and Visualization](#prediction)\n", "7. [Semi-supervised Learning](#semi-supervised)\n", "8. [Advanced Techniques](#advanced)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"setup\"></a>\n", "## 1. Setup and Dependencies\n", "\n", "First, let's install the required dependencies and set up our environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision albumentations scikit-image tqdm matplotlib opencv-python"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}], "source": ["# Import standard libraries\n", "import os\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, Dataset\n", "import albumentations as A\n", "from albumentations.pytorch import ToTensorV2\n", "from tqdm.notebook import tqdm\n", "import cv2\n", "from skimage import io, measure, morphology, filters\n", "from typing import Dict, List, Tuple, Optional, Union, Any, Callable\n", "import random\n", "import time\n", "import json\n", "\n", "# Set random seed for reproducibility\n", "def set_seed(seed=42):\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.manual_seed(seed)\n", "    if torch.cuda.is_available():\n", "        torch.cuda.manual_seed(seed)\n", "        torch.cuda.manual_seed_all(seed)\n", "        torch.backends.cudnn.deterministic = True\n", "        torch.backends.cudnn.benchmark = False\n", "\n", "set_seed()\n", "\n", "# Check if GPU is available\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Configuration\n", "\n", "Let's set up the configuration parameters for our spot detection pipeline."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Data configuration\n", "DATA_CONFIG = {\n", "    'data_dir': \"/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/\",  # Change this to your data directory\n", "    'image_size': 256,\n", "    'batch_size': 8,\n", "    'use_synthetic': True,  # Set to False if you only want to use real data\n", "    'synthetic_size': 500,  # Number of synthetic samples to generate\n", "    'augmentation_level': 'medium',  # 'none', 'light', 'medium', or 'strong'\n", "    'train_val_split': 0.8,  # 80% training, 20% validation\n", "    'num_workers': 4,  # Number of workers for data loading\n", "    'is_3d': False,  # Set to True for 3D data\n", "    'confidence_threshold': 0.9,  # Threshold for high-confidence predictions\n", "    'ignore_threshold': 0.3  # Threshold below which to ignore predictions\n", "}\n", "\n", "# Model configuration\n", "MODEL_CONFIG = {\n", "    'in_channels': 1,\n", "    'num_experts': 3,\n", "    'base_filters': 64,\n", "    'dropout_rate': 0.2,\n", "    'is_3d': DATA_CONFIG['is_3d']\n", "}\n", "\n", "# Loss function configuration\n", "LOSS_CONFIG = {\n", "    'bce_weight': 1.0,\n", "    'dice_weight': 1.0,\n", "    'focal_weight': 0.5,\n", "    'focal_gamma': 1.0,\n", "    'size_adaptive': True,\n", "    'density_aware': True,\n", "    'confidence_weighted': True\n", "}\n", "\n", "# Training configuration\n", "TRAIN_CONFIG = {\n", "    'learning_rate': 0.001,\n", "    'num_epochs': 450,\n", "    'early_stopping_patience': 20,\n", "    'save_best_model': True,\n", "    'model_save_path': 'best_spot_detection_model.pth',\n", "    'gradient_clipping': 1.0,  # Set to None to disable gradient clipping\n", "    'use_mixed_precision': True  # Use mixed precision training if available\n", "}\n", "\n", "# Prediction configuration\n", "PRED_CONFIG = {\n", "    'threshold': 0.5,\n", "    'min_spot_size': 1,\n", "    'use_test_time_augmentation': False,  # Use test-time augmentation for better results\n", "    'tta_flips': True,  # Use flips for test-time augmentation\n", "    'tta_rotations': True  # Use rotations for test-time augmentation\n", "}\n", "\n", "# Semi-supervised learning configuration\n", "SSL_CONFIG = {\n", "    'enabled': True,\n", "    'num_iterations': 3,\n", "    'confidence_threshold': 0.9,\n", "    'epochs_per_iteration': 10\n", "}\n", "\n", "# Synthetic data configuration\n", "SYNTHETIC_CONFIG = {\n", "    'min_spots': 20,\n", "    'max_spots': 100,\n", "    'min_radius': 2,\n", "    'max_radius': 10,\n", "    'allow_overlapping': True,\n", "    'shape_variation': 0.3,\n", "    'add_gradients': True,\n", "    'realistic_noise': True\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"data\"></a>\n", "## 2. Data Loading and Preparation\n", "\n", "Now, let's implement the dataset classes and data loading functions."]}], "metadata": {"kernelspec": {"display_name": "Model_spot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}