{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Optimized Image Alignment\n", "\n", "This notebook uses optimized registration functions to speed up the alignment process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')\n", "\n", "# Import required libraries\n", "import os\n", "import re\n", "from pathlib import Path\n", "import time\n", "\n", "import cupy as cp\n", "import numpy as np\n", "import tifffile\n", "import ants\n", "import matplotlib.pyplot as plt\n", "from skimage.registration import phase_cross_correlation\n", "from tqdm import tqdm\n", "\n", "# Import our optimized functions\n", "from optimized_registration import (\n", "    normalize_image, \n", "    optimized_ants_registration, \n", "    optimized_non_rigid_registration,\n", "    optimized_apply_transforms,\n", "    cupy_fourier_shift\n", ")\n", "from optimized_batch_align import optimized_batch_align_to_reference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check GPU Memory\n", "\n", "Let's check available GPU memory to optimize our processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_gpu_memory():\n", "    \"\"\"Check and print GPU memory usage.\"\"\"\n", "    mem_info = cp.cuda.runtime.memGetInfo()\n", "    free_memory = mem_info[0] / (1024**3)  # Convert to GB\n", "    total_memory = mem_info[1] / (1024**3)  # Convert to GB\n", "    used_memory = total_memory - free_memory\n", "    print(f\"GPU Memory: {used_memory:.2f} GB used / {total_memory:.2f} GB total ({free_memory:.2f} GB free)\")\n", "    return free_memory, total_memory\n", "\n", "# Check initial GPU memory\n", "free_memory, total_memory = check_gpu_memory()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configure Alignment Parameters\n", "\n", "Set up the paths and parameters for the alignment process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define paths\n", "PADDED_DIR = \"/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/\"\n", "REFERENCE_IMG = (\n", "    \"/mnt/d/Users/<USER>/antho 4i alignment/\"\n", "    \"lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif\"\n", ")\n", "OUTPUT_DIR = os.path.join(PADDED_DIR, \"aligned_images_optimized\")\n", "\n", "# Create output directory if it doesn't exist\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optimized Alignment\n", "\n", "Run the optimized alignment process with the following improvements:\n", "\n", "1. Downsampling for non-rigid registration (50% resolution)\n", "2. Optimized registration parameters\n", "3. Memory management to prevent GPU out-of-memory errors\n", "4. Reduced iterations for faster convergence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the optimized batch alignment\n", "optimized_batch_align_to_reference(\n", "    REFERENCE_IMG,\n", "    PADDED_DIR,\n", "    OUTPUT_DIR,\n", "    affine_transform_type=\"Affine\",\n", "    use_non_rigid=True,\n", "    use_micro_registration=True,\n", "    save_intermediate=False,\n", "    show_overlay=False,  # Set to True if you want to see overlays\n", "    reg_interpolator=\"linear\",  # Faster interpolation\n", "    apply_interpolator=\"bSpline\",  # Better quality for final output\n", "    downsample_factor=0.5  # Use 50% resolution for non-rigid registration\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Results\n", "\n", "Let's compare the results with the original alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_images(original_dir, optimized_dir, pattern=\"*_ch00.tif\"):\n", "    \"\"\"Compare images from original and optimized directories.\"\"\"\n", "    import glob\n", "    from skimage.metrics import structural_similarity as ssim\n", "    \n", "    original_files = sorted(glob.glob(os.path.join(original_dir, pattern)))\n", "    optimized_files = sorted(glob.glob(os.path.join(optimized_dir, pattern)))\n", "    \n", "    if not original_files:\n", "        print(f\"No files matching {pattern} found in {original_dir}\")\n", "        return\n", "    \n", "    if not optimized_files:\n", "        print(f\"No files matching {pattern} found in {optimized_dir}\")\n", "        return\n", "    \n", "    print(f\"Found {len(original_files)} original files and {len(optimized_files)} optimized files\")\n", "    \n", "    # Compare files with matching names\n", "    for orig_file in original_files:\n", "        base_name = os.path.basename(orig_file)\n", "        opt_file = os.path.join(optimized_dir, base_name)\n", "        \n", "        if os.path.exists(opt_file):\n", "            # Load images\n", "            orig_img = tifffile.imread(orig_file)\n", "            opt_img = tifffile.imread(opt_file)\n", "            \n", "            # Calculate SSIM\n", "            similarity = ssim(orig_img, opt_img, data_range=orig_img.max() - orig_img.min())\n", "            \n", "            print(f\"{base_name}: SSIM = {similarity:.4f}\")\n", "            \n", "            # Plot comparison\n", "            fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "            axes[0].imshow(orig_img, cmap='gray')\n", "            axes[0].set_title('Original')\n", "            axes[0].axis('off')\n", "            \n", "            axes[1].imshow(opt_img, cmap='gray')\n", "            axes[1].set_title('Optimized')\n", "            axes[1].axis('off')\n", "            \n", "            # Difference image\n", "            diff = np.abs(orig_img.astype(float) - opt_img.astype(float))\n", "            axes[2].imshow(diff, cmap='hot')\n", "            axes[2].set_title('Difference')\n", "            axes[2].axis('off')\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "\n", "# Run comparison after both original and optimized alignments are complete\n", "# compare_images(os.path.join(PADDED_DIR, \"aligned_images_pyant\"), OUTPUT_DIR)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}