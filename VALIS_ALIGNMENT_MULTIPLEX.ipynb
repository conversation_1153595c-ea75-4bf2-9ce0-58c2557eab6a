{"cells": [{"cell_type": "markdown", "id": "title-section", "metadata": {}, "source": ["# VALIS Image Alignment for Multiplex Imaging\n", "\n", "This notebook demonstrates how to use the VALIS (Versatile Alignment of Imaging Systems) library to align multiplex images. VALIS is designed for registering and aligning images from different imaging modalities or sequential imaging rounds.\n", "\n", "## Overview\n", "- Load and preprocess multiplex images\n", "- Align images using VALIS registration algorithms\n", "- Save and visualize aligned images\n", "- Export results for further analysis"]}, {"cell_type": "markdown", "id": "setup-section", "metadata": {}, "source": ["## Setup and Dependencies\n", "\n", "First, let's import the necessary libraries and set up our environment."]}, {"cell_type": "code", "execution_count": null, "id": "import-cell", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import VALIS for image alignment\n", "from valis import registration as reg\n", "from valis import slide_io\n", "\n", "# Set up display options\n", "%matplotlib inline\n", "plt.rcParams['figure.figsize'] = (12, 10)"]}, {"cell_type": "markdown", "id": "config-section", "metadata": {}, "source": ["## Configuration Parameters\n", "\n", "Define the parameters for image alignment and processing."]}, {"cell_type": "code", "execution_count": null, "id": "config-cell", "metadata": {}, "outputs": [], "source": ["# Define input and output directories\n", "input_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/\"\n", "output_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/LB03/region R2/aligned/\"\n", "\n", "# Create output directory if it doesn't exist\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Set alignment parameters\n", "alignment_params = {\n", "    'series_name': 'LB03_R2_alignment',\n", "    'reference_img_index': 0,  # Index of the reference image\n", "    'img_size': 1024,  # Size to resize images for alignment\n", "    'align_to_reference': True,  # Align all images to the reference\n", "    'use_channel': 0,  # Channel to use for alignment (0 for grayscale)\n", "    'pyramid_level': 0  # Pyramid level for processing\n", "}"]}, {"cell_type": "markdown", "id": "alignment-section", "metadata": {}, "source": ["## Image Alignment Process\n", "\n", "Now we'll perform the image alignment using VALIS. This process includes:\n", "1. Loading images from the input directory\n", "2. Preprocessing images (resizing, normalization)\n", "3. Registering images to align them\n", "4. Saving the aligned images"]}, {"cell_type": "code", "execution_count": null, "id": "alignment-cell", "metadata": {}, "outputs": [], "source": ["# Initialize the VALIS aligner\n", "aligner = reg.Val<PERSON>(\n", "    img_dir=input_dir,\n", "    output_dir=output_dir,\n", "    series_name=alignment_params['series_name'],\n", "    reference_img_index=alignment_params['reference_img_index'],\n", "    img_size=alignment_params['img_size'],\n", "    align_to_reference=alignment_params['align_to_reference'],\n", "    use_channel=alignment_params['use_channel'],\n", "    pyramid_level=alignment_params['pyramid_level']\n", ")\n", "\n", "# Run the alignment process\n", "aligner.run()"]}, {"cell_type": "markdown", "id": "results-section", "metadata": {}, "source": ["## Visualize Alignment Results\n", "\n", "Let's visualize the results of our alignment to verify the quality."]}, {"cell_type": "code", "execution_count": null, "id": "visualization-cell", "metadata": {}, "outputs": [], "source": ["# Function to display aligned images\n", "def display_aligned_images(aligner, num_images=3):\n", "    \"\"\"Display a sample of aligned images to verify alignment quality.\"\"\"\n", "    fig, axes = plt.subplots(1, num_images, figsize=(15, 5))\n", "    \n", "    # Get a sample of aligned images\n", "    sample_indices = [0]  # Always include reference image\n", "    if len(aligner.img_list) > 1:\n", "        sample_indices.extend([1, min(2, len(aligner.img_list)-1)])\n", "    \n", "    for i, idx in enumerate(sample_indices[:num_images]):\n", "        if i < len(axes):\n", "            img_name = os.path.basename(aligner.img_list[idx])\n", "            aligned_img = aligner.get_aligned_image(idx)\n", "            axes[i].imshow(aligned_img, cmap='gray')\n", "            axes[i].set_title(f\"Image {idx}: {img_name[:20]}...\")\n", "            axes[i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Display sample aligned images\n", "try:\n", "    display_aligned_images(aligner)\n", "except Exception as e:\n", "    print(f\"Error displaying aligned images: {e}\")"]}, {"cell_type": "markdown", "id": "evaluation-section", "metadata": {}, "source": ["## Evaluate Alignment Quality\n", "\n", "Let's evaluate the quality of our alignment by examining the transformation metrics."]}, {"cell_type": "code", "execution_count": null, "id": "evaluation-cell", "metadata": {}, "outputs": [], "source": ["# Display alignment metrics\n", "def display_alignment_metrics(aligner):\n", "    \"\"\"Display metrics about the alignment transformations.\"\"\"\n", "    if hasattr(aligner, 'transforms') and aligner.transforms:\n", "        print(\"Alignment Transformation Summary:\")\n", "        print(\"-\" * 50)\n", "        \n", "        for i, transform in enumerate(aligner.transforms):\n", "            if i == aligner.reference_img_index:\n", "                print(f\"Image {i} (Reference): Identity transform\")\n", "                continue\n", "                \n", "            if hasattr(transform, 'get_matrix'):\n", "                matrix = transform.get_matrix()\n", "                translation = (matrix[0, 2], matrix[1, 2])\n", "                print(f\"Image {i}: Translation (x,y): {translation}\")\n", "            else:\n", "                print(f\"Image {i}: Complex transform (non-linear)\")\n", "        \n", "        print(\"-\" * 50)\n", "    else:\n", "        print(\"No transformation data available.\")\n", "\n", "# Display alignment metrics\n", "try:\n", "    display_alignment_metrics(aligner)\n", "except Exception as e:\n", "    print(f\"Error displaying metrics: {e}\")"]}, {"cell_type": "markdown", "id": "export-section", "metadata": {}, "source": ["## Export Aligned Images\n", "\n", "Save the aligned images for further analysis."]}, {"cell_type": "code", "execution_count": null, "id": "export-cell", "metadata": {}, "outputs": [], "source": ["# Export aligned images to files\n", "def export_aligned_images(aligner, output_format='tif'):\n", "    \"\"\"Export all aligned images to the output directory.\"\"\"\n", "    export_dir = os.path.join(aligner.output_dir, 'exported_aligned')\n", "    os.makedirs(export_dir, exist_ok=True)\n", "    \n", "    print(f\"Exporting aligned images to {export_dir}...\")\n", "    \n", "    for i, img_path in enumerate(aligner.img_list):\n", "        try:\n", "            img_name = os.path.splitext(os.path.basename(img_path))[0]\n", "            output_path = os.path.join(export_dir, f\"{img_name}_aligned.{output_format}\")\n", "            \n", "            # Get aligned image\n", "            aligned_img = aligner.get_aligned_image(i)\n", "            \n", "            # Save image\n", "            plt.imsave(output_path, aligned_img, cmap='gray')\n", "            print(f\"Saved: {output_path}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"Error exporting image {i}: {e}\")\n", "    \n", "    print(\"Export complete!\")\n", "\n", "# Export aligned images\n", "# Uncomment the line below to export images\n", "# export_aligned_images(aligner)"]}, {"cell_type": "markdown", "id": "conclusion-section", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to use VALIS for aligning multiplex images. The aligned images can now be used for further analysis, such as:\n", "\n", "- Quantitative analysis of marker co-localization\n", "- Cell segmentation across multiple imaging rounds\n", "- Feature extraction from aligned images\n", "- Spatial analysis of tissue architecture\n", "\n", "### Next Steps\n", "\n", "- Perform quality control on the aligned images\n", "- Extract features from the aligned images\n", "- Analyze co-localization of markers\n", "- Integrate with downstream analysis pipelines"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}