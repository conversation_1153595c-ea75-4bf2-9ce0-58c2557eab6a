{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["31"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import gc\n", "gc.collect()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import time\n", "import os\n", "import numpy as np\n", "\n", "from tqdm import tqdm\n", "import sys\n", "import pathlib\n", "from valis import registration, valtils, preprocessing, slide_io\n", "from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration\n", "\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/2d to TAF/\"\n", "dst_dir = \"/mnt/d/Users/<USER>/antho 4i alignment/2d to TAF/results 2d to TAF/\"\n", "reference_slide = \"/mnt/d/Users/<USER>/antho 4i alignment/2d to TAF/TAF snaps subj 1 .lif - Series001-1_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "     img_name = valtils.get_name(src_f)\n", "     round_name = img_name.lower().split(\"_merged\")[0]\n", "     return round_name\n", "\n", "# def get_round_name(src_f):\n", "#     # Extract the round name from the filename\n", "#     # Example filename: r02c02f01_round1_ch00.tiff\n", "#     img_name = os.path.basename(src_f)\n", "#     round_part = img_name.split('_round')[1]  # Split by '_round'\n", "#     round_name = round_part.split('_ch')[0]  # Get the part before '_ch'\n", "#     return round_name\n", "    \n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Failed to bootstrap the artifact.\n", "\n", "Possible solutions:\n", "* Double check the endpoint for correctness (https://search.maven.org/).\n", "* Add needed repositories to ~/.jgorc [repositories] block (see README).\n", "* Try with an explicit version number (release metadata might be wrong).\n", "\n", "Full Maven error output:\n", "\t[ERROR] [ERROR] Some problems were encountered while processing the POMs:\n", "\t[ERROR] Non-resolvable import POM: Failed to resolve version for ome:formats-gpl:pom:RELEASE: Could not find metadata ome:formats-gpl/maven-metadata.xml in local (/home/<USER>/.m2/repository) @ line 8, column 29\n", "\t[ERROR] The build could not read 1 project -> [Help 1]\n", "\t[ERROR]   \n", "\t[ERROR]   The project ome-BOOTSTRAPPER:formats-gpl-BOOTSTRAPPER:0 (/home/<USER>/.jgo/ome/formats-gpl/RELEASE/c3a5af5a8ce399ee15a44f14f7d62eb469aeadfbefc79ad45692dd77988d98bf/pom.xml) has 1 error\n", "\t[ERROR]     Non-resolvable import POM: Failed to resolve version for ome:formats-gpl:pom:RELEASE: Could not find metadata ome:formats-gpl/maven-metadata.xml in local (/home/<USER>/.m2/repository) @ line 8, column 29 -> [Help 2]\n", "\t[ERROR] \n", "\t[ERROR] To see the full stack trace of the errors, re-run <PERSON><PERSON> with the -e switch.\n", "\t[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n", "\t[ERROR] \n", "\t[ERROR] For more information about the errors and possible solutions, please read the following articles:\n", "\t[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/ProjectBuildingException\n", "\t[ERROR] [Help 2] http://cwiki.apache.org/confluence/display/MAVEN/UnresolvableModelException\n", "\n", "/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: Attempting to read /mnt/d/Users/<USER>/antho 4i alignment/2d to TAF/TAF snaps subj 1 .lif - Series001-1_Merged_ch00.tif created the following error:\n", "Command '('/usr/bin/mvn', '-B', '-f', '/home/<USER>/.jgo/ome/formats-gpl/RELEASE/c3a5af5a8ce399ee15a44f14f7d62eb469aeadfbefc79ad45692dd77988d98bf/pom.xml', 'dependency:resolve')' returned non-zero exit status 1.\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 2424, in create_img_reader_dict\n", "    slide_reader = slide_reader_cls(src_f=slide_f, **slide_reader_kwargs)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/slide_io.py\", line 1625, in __init__\n", "    self.metadata = self.create_metadata()\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/slide_io.py\", line 1635, in create_metadata\n", "    slide_meta = self._get_metadata_bf()\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/slide_io.py\", line 1707, in _get_metadata_bf\n", "    bf_reader = BioFormatsSlideReader(self.src_f)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/slide_io.py\", line 1176, in __init__\n", "    init_jvm()\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/slide_io.py\", line 250, in init_jvm\n", "    scyjava.start_jvm([f\"-Xmx{mem_gb}G\"])\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/scyjava/_jvm.py\", line 138, in start_jvm\n", "    _, workspace = jgo.resolve_dependencies(\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/jgo/jgo.py\", line 667, in resolve_dependencies\n", "    raise e\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/jgo/jgo.py\", line 638, in resolve_dependencies\n", "    mvn_out = run_and_combine_outputs(mvn, *mvn_args)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/jgo/jgo.py\", line 269, in run_and_combine_outputs\n", "    return subprocess.check_output(command_string, stderr=subprocess.STDOUT)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/subprocess.py\", line 421, in check_output\n", "    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/subprocess.py\", line 526, in run\n", "    raise CalledProcessError(retcode, process.args,\n", "subprocess.CalledProcessError: Command '('/usr/bin/mvn', '-B', '-f', '/home/<USER>/.jgo/ome/formats-gpl/RELEASE/c3a5af5a8ce399ee15a44f14f7d62eb469aeadfbefc79ad45692dd77988d98bf/pom.xml', 'dependency:resolve')' returned non-zero exit status 1.\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/valtils.py:25: UserWarning: local variable 'slide_reader' referenced before assignment\n", "  warnings.warn(warning_msg, warning_type)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 4594, in register\n", "    self.convert_imgs(series=self.series, reader_cls=reader_cls, reader_dict=reader_dict)\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 2452, in convert_imgs\n", "    named_reader_dict = self.create_img_reader_dict(reader_dict=reader_dict,\n", "  File \"/home/<USER>/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py\", line 2430, in create_img_reader_dict\n", "    named_reader_dict[slide_name] = slide_reader\n", "UnboundLocalError: local variable 'slide_reader' referenced before assignment\n", "\n", "JV<PERSON> has been killed. If this was due to an error, then a new Python session will need to be started\n"]}, {"ename": "UnboundLocalError", "evalue": "local variable 'slide_obj' referenced before assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[26], line 12\u001b[0m\n\u001b[1;32m      9\u001b[0m registrar \u001b[38;5;241m=\u001b[39m registration\u001b[38;5;241m.\u001b[39mValis(src_dir, dst_dir, img_list\u001b[38;5;241m=\u001b[39mdapi_imgs, reference_img_f\u001b[38;5;241m=\u001b[39mreference_slide, micro_rigid_registrar_cls\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,align_to_reference\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m     10\u001b[0m rigid_registrar, non_rigid_registrar, error_df \u001b[38;5;241m=\u001b[39m registrar\u001b[38;5;241m.\u001b[39mregister()\n\u001b[0;32m---> 12\u001b[0m reference_slide \u001b[38;5;241m=\u001b[39m \u001b[43mregistrar\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_ref_slide\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     13\u001b[0m micro_reg_size \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mfloor(np\u001b[38;5;241m.\u001b[39mmax(reference_slide\u001b[38;5;241m.\u001b[39mslide_dimensions_wh[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;241m*\u001b[39mmicro_reg_fraction)\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mint\u001b[39m)\n\u001b[1;32m     14\u001b[0m micro_reg, micro_error \u001b[38;5;241m=\u001b[39m registrar\u001b[38;5;241m.\u001b[39mregister_micro(max_non_rigid_registration_dim_px\u001b[38;5;241m=\u001b[39mmicro_reg_size)\n", "File \u001b[0;32m~/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py:2326\u001b[0m, in \u001b[0;36mValis.get_ref_slide\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   2325\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_ref_slide\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m-> 2326\u001b[0m     ref_slide \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_slide\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreference_img_f\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2328\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ref_slide\n", "File \u001b[0;32m~/anaconda3/envs/VALIS2/lib/python3.10/site-packages/valis/registration.py:2323\u001b[0m, in \u001b[0;36mValis.get_slide\u001b[0;34m(self, src_f)\u001b[0m\n\u001b[1;32m   2320\u001b[0m     valtils\u001b[38;5;241m.\u001b[39mprint_warning(msg, rgb\u001b[38;5;241m=\u001b[39mFore\u001b[38;5;241m.\u001b[39mRED)\n\u001b[1;32m   2321\u001b[0m     slide_obj \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 2323\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mslide_obj\u001b[49m\n", "\u001b[0;31mUnboundLocalError\u001b[0m: local variable 'slide_obj' referenced before assignment"]}], "source": ["merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size)\n", "\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import logging\n", "from tqdm import tqdm\n", "from multiprocessing import Pool, cpu_count\n", "from concurrent.futures import ThreadPoolExecutor\n", "\n", "# Configure logging\n", "#logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/d/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Helper functions\n", "def get_available_workers(max_workers=None):\n", "    # Get the total number of CPU cores\n", "    cpu_count = os.cpu_count()\n", "    \n", "    # You can adjust this number based on system load or reserve some cores\n", "    if max_workers:\n", "        # Use the user-defined max workers if set\n", "        num_workers = min(cpu_count, max_workers)\n", "    else:\n", "        # Default behavior: use 75% of the available CPUs to leave room for other processes\n", "        num_workers = max(1, int(cpu_count * 0.75))  # Ensure at least 1 worker is available\n", "    \n", "    logging.info(f\"Using {num_workers} workers out of {cpu_count} available CPUs.\")\n", "    return num_workers\n", "    \n", "# Function to extract the reference slide dynamically for each subfolder\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "# Function to generate OME-XML metadata for the warped image\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "def get_round_name(src_f):\n", "    # Extract the round name from the filename\n", "    # Example filename: r02c02f01_round1_ch00.tiff\n", "    img_name = os.path.basename(src_f)\n", "    round_part = img_name.split('_round')[1]  # Split by '_round'\n", "    round_name = round_part.split('_ch')[0]  # Get the part before '_ch'\n", "    return round_name\n", "\n", "# Function to process a single subfolder\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all .tiff files in the subfolder\n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path) if f.endswith(\".tiff\")]\n", "\n", "    # Perform registration using DAPI channels (assumed to be \"ch00.tiff\")\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "\n", "    # Warp images based on registration results\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] for slide_obj in registrar.slide_dict.values()}\n", "    \n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {slide_warper.name}\")\n", "        valtils.sort_nicely(round_slides)\n", "        \n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "            \n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "    # Create OME-XML metadata\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"sorted_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "    \n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving {ome_tiff_path}: {e}\")\n", "\n", "    # Clear memory\n", "    del warped_slide\n", "    gc.collect()\n", "\n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.debug(f\"Subfolders found: {len(subfolders)}\")\n", "\n", "    num_threads = min(1, len(subfolders))\n", "\n", "    batch_size = 1\n", "    for i in range(0, len(subfolders), batch_size):\n", "        batch = subfolders[i:i + batch_size]\n", "\n", "        with ThreadPoolExecutor(max_workers=num_threads) as executor:\n", "            executor.map(process_subfolder, batch)\n", "\n", "        logging.info(f\"Processed batch {i // batch_size + 1}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n", "\n", "# # Main function to process all subfolders with multiprocessing\n", "# def process_all_folders():\n", "#     logging.debug(f\"Main directory: {main_dir}\")\n", "    \n", "#     # Get the list of all valid subfolders\n", "#     subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "#                   if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "#     if len(subfolders) == 0:\n", "#         logging.info(\"No valid subfolders to process.\")\n", "#         return\n", "\n", "#     logging.debug(f\"Subfolders found: {len(subfolders)}\")\n", "\n", "#     # Limit the number of workers based on available CPU cores and the number of subfolders\n", "#     num_workers = min(cpu_count(), len(subfolders))\n", "\n", "#     # Divide subfolders into batches of 5\n", "#     batch_size = 1\n", "#     for i in range(0, len(subfolders), batch_size):\n", "#         batch = subfolders[i:i + batch_size]\n", "        \n", "#         # Restart the pool for each batch to clear memory\n", "#         with Pool(processes=num_workers) as pool:\n", "#             pool.map(process_subfolder, batch)\n", "\n", "#         logging.info(f\"Memory cleaned after processing batch {i // batch_size + 1}\")\n", "        \n", "#     logging.info(\"All folders processed.\")\n", "\n", "# Run the processing\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#no multithreading : \n", "import os\n", "import numpy as np\n", "import logging\n", "from tqdm import tqdm\n", "import gc\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "# Define main paths\n", "main_dir = \"/mnt/d/sorted FOV/\"\n", "output_base_dir = \"/mnt/c/Users/<USER>/4icellsneretti/\"\n", "micro_reg_fraction = 0.25  # Fraction for non-rigid registration\n", "\n", "# Create the output directory if it doesn't exist\n", "os.makedirs(output_base_dir, exist_ok=True)\n", "\n", "# Function to extract the reference slide dynamically for each subfolder\n", "def get_reference_slide(subfolder_name):\n", "    ref_slide_path = os.path.join(main_dir, subfolder_name, f\"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff\")\n", "    logging.debug(f\"Reference slide path for {subfolder_name}: {ref_slide_path}\")\n", "    return ref_slide_path\n", "\n", "# Function to generate OME-XML metadata for the warped image\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "    ref_meta = reference_slide.reader.metadata\n", "    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "    ome_xml_obj = slide_io.create_ome_xml(\n", "        shape_xyzct=out_xyczt,\n", "        bf_dtype=bf_dtype,\n", "        is_rgb=False,\n", "        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "        channel_names=channel_names,\n", "        colormap=None\n", "    )\n", "    return ome_xml_obj.to_xml()\n", "\n", "# Extract the round name from the filename\n", "def get_round_name(src_f):\n", "    # Example filename: r02c02f01_round1_ch00.tiff\n", "    img_name = os.path.basename(src_f)\n", "    round_part = img_name.split('_round')[1]\n", "    round_name = round_part.split('_ch')[0]\n", "    return round_name\n", "\n", "# Function to process a single subfolder\n", "def process_subfolder(subfolder_path):\n", "    subfolder_name = os.path.basename(subfolder_path)\n", "    logging.debug(f\"Processing subfolder: {subfolder_name}\")\n", "\n", "    reference_slide = get_reference_slide(subfolder_name)\n", "\n", "    if not os.path.exists(reference_slide):\n", "        logging.error(f\"Reference slide not found: {reference_slide}\")\n", "        return\n", "\n", "    logging.info(f\"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}\")\n", "\n", "    # List all .tiff files in the subfolder\n", "    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path) if f.endswith(\".tiff\")]\n", "\n", "    # Perform registration using DAPI channels (assumed to be \"ch00.tiff\")\n", "    dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tiff\")]\n", "    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)\n", "    rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "    reference_slide_obj = registrar.get_ref_slide()\n", "    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)\n", "    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)\n", "\n", "    # Warp images based on registration results\n", "    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] for slide_obj in registrar.slide_dict.values()}\n", "    \n", "    channel_names = []\n", "    warped_slide = None\n", "\n", "    for slide_warper, round_slides in tqdm(round_dict.items()):\n", "        logging.info(f\"Warping images associated with {slide_warper.name}\")\n", "        valtils.sort_nicely(round_slides)\n", "        \n", "        for src_f in round_slides:\n", "            img_name = valtils.get_name(src_f)\n", "            channel_names.append(img_name)\n", "            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "            \n", "            if warped_slide is None:\n", "                warped_slide = warped_channel\n", "            else:\n", "                warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "    # Create OME-XML metadata\n", "    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)\n", "\n", "    ome_tiff_name = f\"sorted_{subfolder_name}.ome.tiff\"\n", "    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)\n", "    \n", "    try:\n", "        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)\n", "        logging.info(f\"Saved: {ome_tiff_path}\")\n", "    except Exception as e:\n", "        logging.error(f\"Error saving {ome_tiff_path}: {e}\")\n", "\n", "    # Clear memory\n", "    del warped_slide\n", "    gc.collect()\n", "\n", "# Function to process all subfolders sequentially\n", "def process_all_folders():\n", "    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))\n", "                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']\n", "\n", "    if len(subfolders) == 0:\n", "        logging.info(\"No valid subfolders to process.\")\n", "        return\n", "\n", "    logging.info(f\"Found {len(subfolders)} subfolders to process.\")\n", "\n", "    # Process each subfolder sequentially\n", "    for subfolder in subfolders:\n", "        process_subfolder(subfolder)\n", "        logging.info(f\"Completed processing subfolder: {os.path.basename(subfolder)}\")\n", "\n", "    logging.info(\"All folders processed.\")\n", "\n", "# Main entry point\n", "if __name__ == \"__main__\":\n", "    process_all_folders()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["src_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua\"\n", "dst_dir = \"/mnt/c/Users/<USER>/antho 4i alignment/results young207ua FILLA/\"\n", "reference_slide = \"/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua/young ctrl skin 4i p16 red p21 green 25sep23_Region 4_Merged_ch00.tif\"\n", "micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_round_name(src_f):\n", "    img_name = valtils.get_name(src_f)\n", "    round_name = img_name.lower().split(\"_merged\")[0]\n", "    return round_name\n", "\n", "def get_ome_xml(warped_slide, reference_slide, channel_names=None):\n", "  \"\"\"Generate ome-xml for warped slide\n", "\n", "  Parameters\n", "  ----------\n", "  warped_slide : pyvips.Image\n", "    Registered slide that will be saved as an ome.tiff\n", "\n", "  reference_slide : registration.Slide\n", "    Slide object that the others were aligned to/towards\n", "\n", "  channel_names : str, list, optional\n", "    channel names for warped slide. If `None`, then the\n", "    channel names from `src_f` will be used\n", "\n", "\n", "  Returns\n", "  -------\n", "  ome_xml : str\n", "    String of the ome-xml metadata\n", "\n", "  \"\"\"\n", "\n", "  ref_meta = reference_slide.reader.metadata\n", "\n", "  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)\n", "  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)\n", "  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,\n", "                                        bf_dtype=bf_dtype,\n", "                                        is_rgb=False,\n", "                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,\n", "                                        channel_names=channel_names,\n", "                                        colormap=None)\n", "\n", "  ome_xml = ome_xml_obj.to_xml()\n", "\n", "  return ome_xml"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], \"slides\")\n", "pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)\n", "\n", "full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "\n", "# Register rounds using DAPI channels , which all end in \"ch00.tif\"\n", "dapi_imgs = [f for f in full_img_list if f.endswith(\"ch00.tif\")]\n", "\n", "registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)\n", "micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)\n", "\n", "\n", "# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features\n", "\n", "# Use registration parameters to warp DAPI channel and the other images from the same round\n", "round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]\n", "              for slide_obj in registrar.slide_dict.values()}\n", "\n", "all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]\n", "channel_names = []\n", "warped_slide = None\n", "\n", "for slide_warper, round_slides in tqdm(round_dict.items()):\n", "    print(f\"warping images associated with {slide_warper.name}\")\n", "    valtils.sort_nicely(round_slides)\n", "    for src_f in round_slides:\n", "        img_name = valtils.get_name(src_f)\n", "        channel_names.append(img_name)\n", "        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)\n", "        if warped_slide is None:\n", "            warped_slide = warped_channel\n", "        else:\n", "            warped_slide = warped_slide.bandjoin(warped_channel)\n", "\n", "reference_slide = registrar.get_ref_slide()\n", "merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)\n", "merged_slide_f = os.path.join(merged_slide_dir, \"merged.ome.tiff\")\n", "\n", "slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)"]}], "metadata": {"kernelspec": {"display_name": "VALIS2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}