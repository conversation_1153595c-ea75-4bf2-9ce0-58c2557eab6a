import sys
sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')










"Description of the Code
This code performs image alignment for multi-channel TIFF images using both affine and non-rigid registration techniques. The process involves the following steps:

Loading and Normalizing Images: The code reads the images from the specified source directory, normalizes their intensity values to the range [0,1], and extracts pixel size information from the TIFF metadata.
Resizing Images: Each image is resized to match the pixel size of a reference image to ensure consistent scaling.
Affine Registration: An affine transformation is applied to align the images to the reference image. This step corrects for translation, rotation, scaling, and shearing.
Non-Rigid Registration: A non-rigid transformation using the SyN (Symmetric Normalization) method is applied to further refine the alignment by accounting for local deformations.
Applying Transformations: The computed transformations are applied to all channels of each image set to ensure consistent alignment across channels.
Saving Aligned Images: The aligned images are saved in a multi-channel TIFF stack in the specified output directory."

import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import time
from tqdm import tqdm
import gc

import cupy as cp
import numpy as np
import tifffile
import ants
import matplotlib.pyplot as plt
from skimage.registration import phase_cross_correlation
from skimage.exposure import equalize_adapthist

def normalize_image(img: cp.ndarray) -> cp.ndarray:
    img_min = img.min()
    img_max = img.max()
    return (img - img_min) / (img_max - img_min + 1e-12)

def apply_clahe(img: cp.ndarray, kernel_size: Tuple[int, int] = (32, 32)) -> cp.ndarray:
    img_np = cp.asnumpy(img)
    img_eq = equalize_adapthist(img_np, kernel_size=kernel_size)
    return cp.asarray(img_eq)

def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:
    max_h = max(img.shape[0] for img in images)
    max_w = max(img.shape[1] for img in images)
    padded = []
    for img in images:
        pad_h = max_h - img.shape[0]
        pad_w = max_w - img.shape[1]
        pad_top = pad_h // 2
        pad_bottom = pad_h - pad_top
        pad_left = pad_w // 2
        pad_right = pad_w - pad_left
        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),
                            mode='constant', constant_values=0)
        padded.append(img_padded)
    return padded

def downsample_image(img: cp.ndarray, factor: int = 2) -> cp.ndarray:
    return img[::factor, ::factor]

def phase_correlation_shift(fixed: cp.ndarray, moving: cp.ndarray) -> tuple[int, int]:
    F1 = cp.fft.fft2(fixed)
    F2 = cp.fft.fft2(moving)
    R = F1 * cp.conj(F2)
    R /= cp.abs(R) + 1e-8
    r = cp.fft.ifft2(R)
    max_loc = cp.unravel_index(cp.argmax(cp.abs(r)), r.shape)
    shift_y, shift_x = max_loc
    if shift_y > r.shape[0] // 2:
        shift_y -= r.shape[0]
    if shift_x > r.shape[1] // 2:
        shift_x -= r.shape[1]
    return int(shift_y), int(shift_x)

def affine_initializer_fixed(fixed, moving):
    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed
    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving

    try:
        fixed_ants = ants.from_numpy(fixed_np)
        moving_ants = ants.from_numpy(moving_np)
        tx = ants.initial_transform(fixed=fixed_ants, moving=moving_ants, type_of_transform='Similarity')
        return tx
    except Exception as e:
        print(f"ANTs initializer failed: {e}")

    try:
        shift_y, shift_x, _ = phase_cross_correlation(fixed_np, moving_np, upsample_factor=10)
    except Exception as e:
        print(f"Phase correlation failed: {e}")
        shift_y, shift_x = phase_correlation_shift(fixed, moving)

    tx = ants.new_ants_transform(dimension=2)
    tx.set_parameters([0, 0, 0, 0, -shift_x, -shift_y])
    return tx

def compute_correlation_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:
    fixed_flat = fixed.flatten()
    moving_flat = moving.flatten()

    fixed_norm = (fixed_flat - fixed_flat.mean()) / (fixed_flat.std() + 1e-8)
    moving_norm = (moving_flat - moving_flat.mean()) / (moving_flat.std() + 1e-8)

    correlation = cp.corrcoef(fixed_norm, moving_norm)[0, 1]
    corr_val = float(cp.asnumpy(correlation))
    if cp.isnan(correlation) or cp.isinf(correlation):
        return 0.0
    return max(0.0, (corr_val + 1.0) / 2.0)

def ants_registration_optimized(fixed: cp.ndarray, moving: cp.ndarray,
                               transform_type: str,
                               initializer: Optional[ants.ANTsTransform] = None,
                               interpolator: str = 'linear',
                               downsample_factor: int = 1) -> (cp.ndarray, List[str], float):

    if downsample_factor > 1:
        fixed_ds = downsample_image(fixed, downsample_factor)
        moving_ds = downsample_image(moving, downsample_factor)
    else:
        fixed_ds = fixed
        moving_ds = moving

    fixed_ants = ants_from_cp(normalize_image(fixed_ds))
    moving_ants = ants_from_cp(apply_clahe(normalize_image(moving_ds)))

    if transform_type == "SyN":
        reg = ants.registration(
            fixed=fixed_ants,
            moving=moving_ants,
            type_of_transform=transform_type,
            grad_step=0.2,
            flow_sigma=3,
            total_sigma=0,
            syn_metric='CC',
            syn_sampling=16,
            reg_iterations=(40,30,20,10),
            verbose=False
        )
    else:
        reg = ants.registration(
            fixed=fixed_ants,
            moving=moving_ants,
            type_of_transform=transform_type,
            initial_transform=initializer,
            verbose=False
        )

    fwd_transforms = reg.get('fwdtransforms', [])
    if isinstance(fwd_transforms, str):
        fwd_transforms = [fwd_transforms]

    warped = ants.apply_transforms(
        fixed=ants_from_cp(normalize_image(fixed)),
        moving=ants_from_cp(normalize_image(moving)),
        transformlist=fwd_transforms,
        interpolator=interpolator
    )

    similarity = compute_correlation_similarity(ants_to_cp(ants_from_cp(normalize_image(fixed))), ants_to_cp(warped))

    return ants_to_cp(warped), fwd_transforms, similarity

def ants_registration_micro(fixed: cp.ndarray, moving: cp.ndarray,
                           interpolator: str = 'linear',
                           downsample_factor: int = 2,
                           timeout: int = 60) -> (cp.ndarray, List[str], float):

    if downsample_factor > 1:
        fixed_ds = downsample_image(fixed, downsample_factor)
        moving_ds = downsample_image(moving, downsample_factor)
    else:
        fixed_ds = fixed
        moving_ds = moving

    fixed_ants = ants_from_cp(normalize_image(fixed_ds))
    moving_ants = ants_from_cp(apply_clahe(normalize_image(moving_ds)))

    start_time = time.time()

    reg = ants.registration(
        fixed=fixed_ants,
        moving=moving_ants,
        type_of_transform="SyN",
        grad_step=0.5,
        flow_sigma=1,
        total_sigma=0,
        syn_metric='CC',
        syn_sampling=8,
        reg_iterations=(10,5,3),
        verbose=False
    )

    elapsed = time.time() - start_time
    if elapsed > timeout:
        raise TimeoutError(f"Micro-registration exceeded {timeout}s timeout")

    fwd_transforms = reg.get('fwdtransforms', [])
    if isinstance(fwd_transforms, str):
        fwd_transforms = [fwd_transforms]

    warped = ants.apply_transforms(
        fixed=ants_from_cp(normalize_image(fixed)),
        moving=ants_from_cp(normalize_image(moving)),
        transformlist=fwd_transforms,
        interpolator=interpolator
    )

    similarity = compute_correlation_similarity(ants_to_cp(ants_from_cp(normalize_image(fixed))), ants_to_cp(warped))

    return ants_to_cp(warped), fwd_transforms, similarity

def apply_shift(img: cp.ndarray, shift: tuple[int, int]) -> cp.ndarray:
    return cp.roll(cp.roll(img, shift[0], axis=0), shift[1], axis=1)

def ants_from_cp(img: cp.ndarray) -> ants.ANTsImage:
    return ants.from_numpy(cp.asnumpy(img))

def ants_to_cp(img: ants.ANTsImage) -> cp.ndarray:
    return cp.asarray(img.numpy())

def compute_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:
    from skimage.metrics import normalized_mutual_information
    return float(normalized_mutual_information(cp.asnumpy(fixed), cp.asnumpy(moving)))

def apply_transforms_to_channel(channel: cp.ndarray, fixed: cp.ndarray,
                              transformlist: List[str],
                              interpolator: str = 'bSpline') -> cp.ndarray:
    fixed_ants = ants_from_cp(fixed)
    moving_ants = ants_from_cp(channel)
    warped = ants.apply_transforms(
        fixed=fixed_ants,
        moving=moving_ants,
        transformlist=transformlist,
        interpolator=interpolator
    )
    return ants_to_cp(warped)

def save_image(img: cp.ndarray, output_dir: str, filename: str, original_img: cp.ndarray):
    out_path = os.path.join(output_dir, filename)

    # Ensure original_img is a CuPy array if it's not already
    if isinstance(original_img, np.ndarray):
        original_img = cp.asarray(original_img)

    # Perform operations using CuPy methods
    original_min = cp.asnumpy(original_img.min())
    original_max = cp.asnumpy(original_img.max())
    img_np = cp.asnumpy(img) * (original_max - original_min) + original_min

    tifffile.imwrite(out_path, img_np.astype(np.float32))
    print(f"Saved image: {out_path}")

def plot_overlay(fixed: cp.ndarray, moving: cp.ndarray, title: str, alpha=0.5):
    fixed_np = cp.asnumpy(normalize_image(fixed))
    moving_np = cp.asnumpy(normalize_image(moving))
    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)
    overlay[..., 0] = fixed_np
    overlay[..., 1] = moving_np

    plt.figure(figsize=(10, 10))
    plt.imshow(overlay)
    plt.title(title)
    plt.axis('off')
    plt.show()

def get_basename_key(filename: str) -> str:
    match = re.match(r"(.+)_ch\d{2}\.tif$", filename)
    if match:
        return match.group(1)
    raise ValueError(f"Filename {filename} does not match expected pattern *_chXX.tif")

def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:
    files = [f for f in os.listdir(folder) if f.endswith('.tif')]
    groups = {}
    for f in files:
        base = get_basename_key(f)
        ch_match = re.search(r"_ch(\d{2})\.tif$", f)
        if not ch_match:
            continue
        ch = f"ch{ch_match.group(1)}"
        groups.setdefault(base, {})[ch] = os.path.join(folder, f)
    return groups

def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:
    channels = {}
    for ch, p in paths.items():
        img = tifffile.imread(p)
        if img.ndim > 2:
            img = img[0]
        channels[ch] = cp.asarray(img)
    return channels

def cupy_fourier_shift(img: cp.ndarray, shift: tuple) -> cp.ndarray:
    cp.get_default_memory_pool().free_all_blocks()
    shift_y, shift_x = shift
    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)

def cleanup_memory():
    cp.get_default_memory_pool().free_all_blocks()
    gc.collect()

def batch_align_to_reference_optimized(
    reference_img_path,
    input_folder,
    output_folder,
    affine_transform_type="Affine",
    use_non_rigid=True,
    use_micro_registration=False,
    save_intermediate: bool = False,
    show_overlay: bool = False,
    reg_interpolator="linear",
    apply_interpolator="linear",
    downsample_factor: int = 2,
    similarity_threshold: float = 0.8
):
    print(f"[INFO] Loading reference ch00 image from {reference_img_path}...")
    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))

    groups = group_images_by_basename(input_folder)
    print(f"[INFO] Found {len(groups)} groups to align.")

    Path(output_folder).mkdir(parents=True, exist_ok=True)

    for base, channel_paths in tqdm(groups.items(), desc="Aligning groups"):
        if 'ch00' not in channel_paths:
            print(f"[WARNING] Skipping '{base}': no ch00 channel found.")
            continue

        print(f"\n[INFO] Processing group: {base}")
        channels = load_channels(channel_paths)
        moving_ch00 = cp.asarray(channels.get('ch00'))
        if moving_ch00 is None:
            print(f"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.")
            continue

        all_imgs = [fixed_ch00] + list(channels.values())
        all_padded = pad_to_largest(all_imgs)
        fixed_ch00_padded = all_padded[0]
        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))

        print(" - Estimating initial shift using phase correlation...")
        shift, _, _ = phase_cross_correlation(
            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=5
        )

        for ch_name in channels_padded:
            current_img = channels_padded[ch_name]
            shifted = cupy_fourier_shift(current_img, shift)
            channels_padded[ch_name] = shifted
            cleanup_memory()

        best_similarity = -1
        best_img = None
        best_transforms = []

        print(" - Running affine registration...")
        start_time = time.time()
        affine_img, affine_transforms, aff_sim = ants_registration_optimized(
            fixed_ch00_padded,
            channels_padded['ch00'],
            transform_type=affine_transform_type,
            interpolator=reg_interpolator,
            downsample_factor=1
        )
        print(f"   >> Affine similarity: {aff_sim:.6f} (took {time.time() - start_time:.2f}s)")
        best_similarity = aff_sim
        best_img = affine_img
        best_transforms = affine_transforms
        cleanup_memory()

        if use_non_rigid:
            print(" - Running non-rigid registration...")
            start_time = time.time()
            nonrigid_img, nonrigid_transforms, nonrigid_sim = ants_registration_optimized(
                fixed_ch00_padded,
                best_img,
                transform_type="SyN",
                interpolator=reg_interpolator,
                downsample_factor=downsample_factor
            )
            print(f"   >> Non-rigid similarity: {nonrigid_sim:.6f} (took {time.time() - start_time:.2f}s)")
            if nonrigid_sim > best_similarity:
                best_similarity = nonrigid_sim
                best_img = nonrigid_img
                best_transforms += nonrigid_transforms
            cleanup_memory()

        if use_micro_registration and best_similarity < similarity_threshold:
            print(" - Running micro-registration...")
            start_time = time.time()
            try:
                micro_img, micro_transforms, micro_sim = ants_registration_micro(
                    fixed_ch00_padded,
                    best_img,
                    interpolator=reg_interpolator,
                    downsample_factor=downsample_factor,
                    timeout=60
                )
                elapsed = time.time() - start_time
                print(f"   >> Micro-registration similarity: {micro_sim:.6f} (took {elapsed:.2f}s)")
                if micro_sim > best_similarity:
                    best_similarity = micro_sim
                    best_img = micro_img
                    best_transforms += micro_transforms
            except Exception as e:
                print(f"   >> Micro-registration failed or timed out: {e}")
            cleanup_memory()
        elif use_micro_registration:
            print(f" - Skipping micro-registration (similarity {best_similarity:.3f} > threshold {similarity_threshold})")

        print(f"   >> Final similarity: {best_similarity:.6f}")

        if show_overlay:
            plot_overlay(fixed_ch00_padded, best_img, title=f"Overlay: {base}")

        for ch_name, img in channels_padded.items():
            if ch_name == 'ch00':
                warped = best_img
            else:
                warped = apply_transforms_to_channel(
                    img, fixed_ch00_padded, best_transforms, interpolator=apply_interpolator
                )
            save_image(warped, output_folder, f"{base}_{ch_name}.tif", channels[ch_name])
            cleanup_memory()

        if save_intermediate:
            np.save(os.path.join(output_folder, f"{base}_transform_list.npy"), best_transforms)

        print(f"[INFO] Completed processing group: {base}")
        cleanup_memory()


if __name__ == "__main__":
    PADDED_DIR = "/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/"
    REFERENCE_IMG = (
        "/mnt/d/Users/<USER>/antho 4i alignment/"
        "lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif"
    )
    OUTPUT_DIR = os.path.join(PADDED_DIR, "aligned_images_pyant")

    batch_align_to_reference_optimized(
        REFERENCE_IMG,
        PADDED_DIR,
        OUTPUT_DIR,
        affine_transform_type="Affine",
        use_non_rigid=True,
        use_micro_registration=True,
        similarity_threshold=0.90,     # Very high threshold
        downsample_factor=2,
        save_intermediate=False,
    )


if __name__ == "__main__":
    PADDED_DIR = "/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/"
    REFERENCE_IMG = (
        "/mnt/d/Users/<USER>/antho 4i alignment/"
        "lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif"
    )
    OUTPUT_DIR = os.path.join(PADDED_DIR, "aligned_images_pyant")

    batch_align_to_reference_optimized(
        REFERENCE_IMG,
        PADDED_DIR,
        OUTPUT_DIR,
        affine_transform_type="Affine",
        use_non_rigid=False,
        use_micro_registration=True,
        similarity_threshold=0.90,     # Very high threshold
        downsample_factor=2,
        save_intermediate=False,
    )


################ ANTpy###########
import os
import re
from pathlib import Path
from typing import List, Dict, Optional

import cupy as cp
import numpy as np
import tifffile
import ants
import matplotlib.pyplot as plt
import time
from tqdm import tqdm
from skimage.registration import phase_cross_correlation
from scipy.ndimage import fourier_shift

def normalize_image(img: cp.ndarray) -> cp.ndarray:
    img_min = img.min()
    img_max = img.max()
    return (img - img_min) / (img_max - img_min + 1e-12)


def pad_to_largest(images: List[cp.ndarray]) -> List[cp.ndarray]:
    max_h = max(img.shape[0] for img in images)
    max_w = max(img.shape[1] for img in images)
    padded = []
    for img in images:
        pad_h = max_h - img.shape[0]
        pad_w = max_w - img.shape[1]
        pad_top = pad_h // 2
        pad_bottom = pad_h - pad_top
        pad_left = pad_w // 2
        pad_right = pad_w - pad_left
        img_padded = cp.pad(img, ((pad_top, pad_bottom), (pad_left, pad_right)),
                            mode='constant', constant_values=0)
        padded.append(img_padded)
    return padded

def phase_correlation_shift(fixed: cp.ndarray, moving: cp.ndarray) -> tuple[int, int]:
    F1 = cp.fft.fft2(fixed)
    F2 = cp.fft.fft2(moving)
    R = F1 * cp.conj(F2)
    R /= cp.abs(R) + 1e-8
    r = cp.fft.ifft2(R)
    max_loc = cp.unravel_index(cp.argmax(cp.abs(r)), r.shape)
    shift_y, shift_x = max_loc
    # Correct for wrap-around
    if shift_y > r.shape[0] // 2:
        shift_y -= r.shape[0]
    if shift_x > r.shape[1] // 2:
        shift_x -= r.shape[1]
    return int(shift_y), int(shift_x)

def affine_initializer_fixed(fixed, moving):
    """
    Create an affine initializer for ANTs registration using multiple fallback methods
    to ensure success regardless of ANTs version or input types.
    
    Args:
        fixed: Fixed image (reference)
        moving: Moving image to align
        
    Returns:
        An ANTs transform object
    """
    # Convert to numpy arrays if they're cupy arrays
    fixed_np = cp.asnumpy(fixed) if isinstance(fixed, cp.ndarray) else fixed
    moving_np = cp.asnumpy(moving) if isinstance(moving, cp.ndarray) else moving
    
    # Method 1: Try using ANTs' built-in initializer
    try:
        print("Trying ANTs built-in initializer...")
        fixed_ants = ants.from_numpy(fixed_np)
        moving_ants = ants.from_numpy(moving_np)
        tx = ants.initial_transform(fixed=fixed_ants, moving=moving_ants, type_of_transform='Similarity')
        return tx
    except Exception as e:
        print(f"ANTs initializer failed: {e}")
    
    # Method 2: Try creating a simple translation transform
    try:
        print("Trying simple translation transform...")
        # Get the shift using phase correlation
        try:
            shift_y, shift_x, _ = phase_cross_correlation(fixed_np, moving_np, upsample_factor=10)
        except Exception as e:
            print(f"Phase correlation failed: {e}")
            shift_y, shift_x = phase_correlation_shift(fixed, moving)
        
        print(f"Computed shift: y={shift_y}, x={shift_x}")
        
        # Try using identity transform with translation
        tx = ants.new_ants_transform(dimension=2)
        tx.set_parameters([0, 0, 0, 0, -shift_x, -shift_y])
        return tx
    except Exception as e:
        print(f"Translation transform failed: {e}")
    
    # Method 3: Last resort - return identity transform
    print("Using identity transform as fallback...")
    return ants.new_ants_transform(dimension=2)


def ants_registration(fixed: cp.ndarray, moving: cp.ndarray,
                      transform_type: str,
                      initializer: Optional[ants.ANTsTransform] = None,
                      interpolator: str = 'linear') -> (cp.ndarray, List[str], float):
    fixed_ants = ants_from_cp(normalize_image(fixed))
    moving_ants = ants_from_cp(normalize_image(moving))
    
    if transform_type == "SyN":
        reg = ants.registration(
            fixed=fixed_ants, 
            moving=moving_ants, 
            type_of_transform=transform_type,
            grad_step=0.1,
            flow_sigma=3,
            total_sigma=0,
            syn_metric='CC',
            syn_sampling=32,
            reg_iterations=(100,70,50,20)
        )
    else:
        reg = ants.registration(
            fixed=fixed_ants,
            moving=moving_ants,
            type_of_transform=transform_type,
            initial_transform=initializer
        )
    fwd_transforms = reg.get('fwdtransforms', [])
    if isinstance(fwd_transforms, str):
        fwd_transforms = [fwd_transforms]

    warped = ants.apply_transforms(
        fixed=fixed_ants,
        moving=moving_ants,
        transformlist=fwd_transforms,
        interpolator=interpolator
    )
    
    similarity = ants.image_similarity(fixed_ants, ants_from_cp(ants_to_cp(warped)), 'MattesMutualInformation')
    return ants_to_cp(warped), fwd_transforms, similarity


def apply_shift(img: cp.ndarray, shift: tuple[int, int]) -> cp.ndarray:
    return cp.roll(cp.roll(img, shift[0], axis=0), shift[1], axis=1)

def ants_from_cp(img: cp.ndarray) -> ants.ANTsImage:
    return ants.from_numpy(cp.asnumpy(img))


def ants_to_cp(img: ants.ANTsImage) -> cp.ndarray:
    return cp.asarray(img.numpy())



def compute_similarity(fixed: cp.ndarray, moving: cp.ndarray) -> float:
    from skimage.metrics import normalized_mutual_information
    return float(normalized_mutual_information(cp.asnumpy(fixed), cp.asnumpy(moving)))





def apply_transforms_to_channel(channel: cp.ndarray, fixed: cp.ndarray,
                              transformlist: List[str], 
                              interpolator: str = 'bSpline') -> cp.ndarray:
    """Apply transforms with advanced interpolation"""
    fixed_ants = ants_from_cp(fixed)
    moving_ants = ants_from_cp(channel)
    warped = ants.apply_transforms(
        fixed=fixed_ants,
        moving=moving_ants,
        transformlist=transformlist,
        interpolator=interpolator  # Use bspline for better quality
    )
    return ants_to_cp(warped)


def save_image(img: cp.ndarray, output_dir: str, filename: str):
    out_path = os.path.join(output_dir, filename)
    np_img = cp.asnumpy(img)
    tifffile.imwrite(out_path, np_img.astype(np.float32))
    print(f"Saved image: {out_path}")


def plot_overlay(fixed: cp.ndarray, moving: cp.ndarray, title: str, alpha=0.5):
    fixed_np = cp.asnumpy(normalize_image(fixed))
    moving_np = cp.asnumpy(normalize_image(moving))
    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)
    overlay[..., 0] = fixed_np
    overlay[..., 1] = moving_np
    plt.figure(figsize=(6, 6))
    plt.imshow(overlay)
    plt.title(title)
    plt.axis('off')
    plt.show()


def get_basename_key(filename: str) -> str:
    match = re.match(r"(.+)_ch\d{2}\.tif$", filename)
    if match:
        return match.group(1)
    raise ValueError(f"Filename {filename} does not match expected pattern *_chXX.tif")


def group_images_by_basename(folder: str) -> Dict[str, Dict[str, str]]:
    files = [f for f in os.listdir(folder) if f.endswith('.tif')]
    groups = {}
    for f in files:
        base = get_basename_key(f)
        ch_match = re.search(r"_ch(\d{2})\.tif$", f)
        if not ch_match:
            continue
        ch = f"ch{ch_match.group(1)}"
        groups.setdefault(base, {})[ch] = os.path.join(folder, f)
    return groups


def load_channels(paths: Dict[str, str]) -> Dict[str, cp.ndarray]:
    channels = {}
    for ch, p in paths.items():
        img = tifffile.imread(p)
        if img.ndim > 2:
            img = img[0]
        channels[ch] = cp.asarray(img)
    return channels


def plot_overlay(fixed: cp.ndarray, moving: cp.ndarray, title: str, alpha=0.5):
    fixed_np = cp.asnumpy(normalize_image(fixed))
    moving_np = cp.asnumpy(normalize_image(moving))
    overlay = np.zeros((*fixed_np.shape, 3), dtype=np.float32)
    overlay[..., 0] = fixed_np
    overlay[..., 1] = moving_np
    
    plt.figure(figsize=(10, 10))
    plt.imshow(overlay)
    plt.title(title)
    plt.axis('off')
    plt.show()

def cupy_fourier_shift(img: cp.ndarray, shift: tuple) -> cp.ndarray:
    # Free memory explicitly
    cp.get_default_memory_pool().free_all_blocks()
    
    # Use spatial domain shift instead of Fourier for large images
    shift_y, shift_x = shift
    return cp.roll(cp.roll(img, int(shift_y), axis=0), int(shift_x), axis=1)

def batch_align_to_reference(
    reference_img_path,
    input_folder,
    output_folder,
    affine_transform_type="Affine",
    use_non_rigid=True,
    use_micro_registration=True,
    save_intermediate: bool = False,
    show_overlay: bool = True,
    reg_interpolator="bSpline",
    apply_interpolator="bSpline"
):
    print(f"[INFO] Loading reference ch00 image from {reference_img_path}...")
    fixed_ch00 = cp.asarray(tifffile.imread(reference_img_path))

    groups = group_images_by_basename(input_folder)
    print(f"[INFO] Found {len(groups)} groups to align.")

    Path(output_folder).mkdir(parents=True, exist_ok=True)

    for base, channel_paths in tqdm(groups.items(), desc="Aligning groups"):
        if 'ch00' not in channel_paths:
            print(f"[WARNING] Skipping '{base}': no ch00 channel found.")
            continue

        print(f"\n[INFO] Processing group: {base}")
        channels = load_channels(channel_paths)
        moving_ch00 = cp.asarray(channels.get('ch00'))
        if moving_ch00 is None:
            print(f"[WARNING] Could not load 'ch00' for group '{base}'. Skipping.")
            continue

        # Pad all images to same size first
        print(" - Padding images...")
        all_imgs = [fixed_ch00] + list(channels.values())
        all_padded = pad_to_largest(all_imgs)
        fixed_ch00_padded = all_padded[0]
        channels_padded = dict(zip(channel_paths.keys(), all_padded[1:]))

        # Phase correlation shift estimation
        print(" - Estimating initial shift using phase correlation...")
        shift, _, _ = phase_cross_correlation(
            cp.asnumpy(fixed_ch00_padded), cp.asnumpy(channels_padded['ch00']), upsample_factor=10
        )

        # Apply shift to all channels
        for ch_name in channels_padded:
            current_img = channels_padded[ch_name]
            # Use direct spatial shift instead of Fourier transform
            shifted = cupy_fourier_shift(current_img, shift)
            channels_padded[ch_name] = shifted
            # Free memory after each iteration
            cp.get_default_memory_pool().free_all_blocks()

        best_similarity = -1
        best_img = None
        best_transforms = []

        # Affine registration
        print(" - Running affine registration...")
        affine_img, affine_transforms, aff_sim = ants_registration(
            fixed_ch00_padded,
            channels_padded['ch00'],
            transform_type=affine_transform_type,
            interpolator=reg_interpolator
        )
        print(f"   >> Affine similarity: {aff_sim:.6f}")
        best_similarity = aff_sim
        best_img = affine_img
        best_transforms = affine_transforms

        # Non-rigid registration
        if use_non_rigid:
            print(" - Running non-rigid registration...")
            nonrigid_img, nonrigid_transforms, nonrigid_sim = ants_registration(
                fixed_ch00_padded,
                best_img,
                transform_type="SyN",
                interpolator=reg_interpolator
            )
            print(f"   >> Non-rigid similarity: {nonrigid_sim:.6f}")
            if nonrigid_sim > best_similarity:
                best_similarity = nonrigid_sim
                best_img = nonrigid_img
                best_transforms += nonrigid_transforms

        # Micro-registration
        if use_micro_registration:
            print(" - Running micro-registration...")
            micro_img, micro_transforms, micro_sim = ants_registration(
                fixed_ch00_padded,
                best_img,
                transform_type="SyN",
                interpolator=reg_interpolator
            )
            print(f"   >> Micro-registration similarity: {micro_sim:.6f}")
            if micro_sim > best_similarity:
                best_similarity = micro_sim
                best_img = micro_img
                best_transforms += micro_transforms

        print(f"   >> Best similarity: {best_similarity:.6f}")

        if show_overlay:
            plot_overlay(fixed_ch00_padded, best_img, title=f"Overlay: {base}")

        # Apply final transforms to all padded channels
        for ch_name, img in channels_padded.items():
            if ch_name == 'ch00':
                warped = best_img
            else:
                warped = apply_transforms_to_channel(
                    img, fixed_ch00_padded, best_transforms, interpolator=apply_interpolator
                )
            save_image(warped, output_folder, f"{base}_{ch_name}.tif")

        if save_intermediate:
            np.save(os.path.join(output_folder, f"{base}_transform_list.npy"), best_transforms)






import os
import tifffile
import numpy as np
import cupy as cp
import ants
import matplotlib.pyplot as plt
from skimage.transform import resize, rescale
from skimage.color import rgb2hed, hed2rgb
from tqdm import tqdm

def check_gpu():
    """Check if GPU is available and print the number of GPUs detected."""
    num_gpus = cp.cuda.runtime.getDeviceCount()
    if num_gpus > 0:
        print(f"GPUs detected: {num_gpus}")
    else:
        print("No GPUs detected. Using CPU.")
    return num_gpus > 0

def get_pixel_size(tif_path):
    """Extract pixel size from TIFF metadata."""
    with tifffile.TiffFile(tif_path) as tif:
        metadata = tif.pages[0].tags
        if "XResolution" in metadata and "YResolution" in metadata:
            x_res = metadata["XResolution"].value
            y_res = metadata["YResolution"].value
            return (x_res[1] / x_res[0], y_res[1] / y_res[0])
    return None

def normalize_image(image, min_val=0, max_val=1):
    """Normalize image to [min_val, max_val] range for consistent processing."""
    image_cp = cp.asarray(image)
    norm_image = (image_cp - cp.min(image_cp)) / (cp.max(image_cp) - cp.min(image_cp) + 1e-8)
    norm_image = norm_image * (max_val - min_val) + min_val
    return norm_image

def resize_image(image, scale_y, scale_x, target_shape):
    """Resize image while preserving aspect ratio."""
    print(f"Resizing image from {image.shape} with scales (y: {scale_y}, x: {scale_x}) to target shape {target_shape}")
    image_cp = cp.asarray(image)
    scaled_image = rescale(cp.asnumpy(image_cp), (scale_y, scale_x), anti_aliasing=True, preserve_range=True, channel_axis=-1)
    resized_image = resize(scaled_image, target_shape, anti_aliasing=True, preserve_range=True, channel_axis=-1)
    print(f"Resized image shape: {resized_image.shape}")
    return cp.asarray(resized_image)

def affine_registration(fixed, moving, transform_type="Affine"):
    """Perform affine registration and return transformed image and transformation paths."""
    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))
    moving_ants = ants.from_numpy(cp.asnumpy(moving))
    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=transform_type)
    affine_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Affine registration completed. Transform paths: {affine_transform_paths}")
    return cp.asarray(reg['warpedmovout'].numpy()), affine_transform_paths  # Return image + paths

def non_rigid_registration(fixed, moving):
    """Perform non-rigid registration using SyN and return transformed image and transformation paths."""
    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))
    moving_ants = ants.from_numpy(cp.asnumpy(moving))
    reg = ants.registration(fixed_ants, moving_ants, type_of_transform="SyN")
    non_rigid_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Non-rigid registration completed. Transform paths: {non_rigid_transform_paths}")
    return cp.asarray(reg['warpedmovout'].numpy()), non_rigid_transform_paths  # Return image + paths

def micro_registration(fixed, moving, patch_size=64, overlap=32):
    """Perform micro-registration using small patches."""
    fixed_ants = ants.from_numpy(cp.asnumpy(fixed))
    moving_ants = ants.from_numpy(cp.asnumpy(moving))
    reg = ants.registration(
        fixed_ants, 
        moving_ants, 
        type_of_transform="SyN", 
        reg_iterations=(20, 10, 0), 
        syn_metric='CC', 
        syn_sampling=2, 
        syn_radius=4, 
        syn_patch_size=patch_size, 
        syn_overlap=overlap
    )
    micro_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Micro-registration completed. Transform paths: {micro_transform_paths}")
    return cp.asarray(reg['warpedmovout'].numpy()), micro_transform_paths  # Return image + paths

def apply_transform(image, transform_paths):
    """Apply stored transformations to another channel."""
    ants_image = ants.from_numpy(cp.asnumpy(image))
    transformed_image = ants.apply_transforms(fixed=ants_image, moving=ants_image, transformlist=transform_paths)
    image_transformed = cp.asarray(transformed_image.numpy())
    print(f"Applied transform. Final image shape: {image_transformed.shape}")
    return image_transformed

def plot_images(reference, aligned, title):
    """Plot reference, aligned, and difference images."""
    difference = cp.abs(reference - aligned)
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(cp.asnumpy(reference), cmap="gray")
    axes[0].set_title("Reference Image")
    axes[1].imshow(cp.asnumpy(aligned), cmap="gray")
    axes[1].set_title("Aligned Image")
    axes[2].imshow(cp.asnumpy(difference), cmap="hot")
    axes[2].set_title("Difference")
    plt.suptitle(title)
    plt.show()

def save_image(image, output_directory, filename):
    """Save image to disk."""
    output_path = os.path.join(output_directory, filename)
    tifffile.imwrite(output_path, cp.asnumpy(image).astype(np.float32))
    print(f"Saved image to: {output_path}")

def align_images(image_sets, reference_image, reference_pixel_size, output_directory, use_non_rigid=True, use_micro_registration=False, affine_transform_type="Affine", normalize=True, save_intermediate=False):
    """Align all channels to the reference image."""
    os.makedirs(output_directory, exist_ok=True)
    aligned_images = {"reference": reference_image}
    transforms = {}

    if normalize:
        reference_image = normalize_image(reference_image)  # Ensure consistent intensity
    
    for base_name, channels in tqdm(image_sets.items(), desc="Processing images"):
        print(f"Processing base name: {base_name}")
        ch00_path = channels['ch00']
        print(f"Processing {ch00_path}")
        image = tifffile.imread(ch00_path)
        image_cp = cp.asarray(image)
        
        # Perform color deconvolution to separate channels
        hed_image = rgb2hed(cp.asnumpy(image_cp))
        blue_channel = cp.asarray(hed_image[..., 2])  # Use the blue channel for alignment

        if normalize:
            blue_channel = normalize_image(blue_channel)  # Normalize intensity before processing

        image_pixel_size = get_pixel_size(ch00_path)
        if not image_pixel_size:
            print(f"Skipping {ch00_path}: Pixel size not found.")
            continue

        print(f"Image pixel size: {image_pixel_size}, Reference pixel size: {reference_pixel_size}")

        # Rescale image to match reference pixel size
        scale_x, scale_y = image_pixel_size[0] / reference_pixel_size[0], image_pixel_size[1] / reference_pixel_size[1]
        blue_resized = resize_image(blue_channel, scale_y, scale_x, reference_image.shape)

        if save_intermediate:
            # Save resized image
            save_image(blue_resized, output_directory, f"{os.path.basename(ch00_path)}_resized.tif")

        # Plot resized image
        plt.figure()
        plt.imshow(cp.asnumpy(blue_resized), cmap="gray")
        plt.title(f"Resized Image: {os.path.basename(ch00_path)}")
        plt.show()

        # Apply affine registration
        blue_affine, affine_transform = affine_registration(reference_image, blue_resized, transform_type=affine_transform_type)

        if save_intermediate:
            # Save affine registered image
            save_image(blue_affine, output_directory, f"{os.path.basename(ch00_path)}_affine.tif")

        # Plot affine registered image
        plt.figure()
        plt.imshow(cp.asnumpy(blue_affine), cmap="gray")
        plt.title(f"Affine Registered Image: {os.path.basename(ch00_path)}")
        plt.show()

        if use_non_rigid:
            # Apply non-rigid registration
            blue_non_rigid, non_rigid_transform = non_rigid_registration(reference_image, blue_affine)

            if save_intermediate:
                # Save non-rigid registered image
                save_image(blue_non_rigid, output_directory, f"{os.path.basename(ch00_path)}_non_rigid.tif")

            # Plot non-rigid registered image
            plt.figure()
            plt.imshow(cp.asnumpy(blue_non_rigid), cmap="gray")
            plt.title(f"Non-Rigid Registered Image: {os.path.basename(ch00_path)}")
            plt.show()

            # Save transformations
            transforms[ch00_path] = affine_transform + non_rigid_transform

            plot_images(reference_image, blue_non_rigid, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = blue_non_rigid
        else:
            # Save transformations
            transforms[ch00_path] = affine_transform

            plot_images(reference_image, blue_affine, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = blue_affine

        if use_micro_registration:
            # Apply micro-registration
            blue_micro, micro_transform = micro_registration(reference_image, aligned_images[ch00_path])

            if save_intermediate:
                # Save micro-registered image
                save_image(blue_micro, output_directory, f"{os.path.basename(ch00_path)}_micro.tif")

            # Plot micro-registered image
            plt.figure()
            plt.imshow(cp.asnumpy(blue_micro), cmap="gray")
            plt.title(f"Micro-Registered Image: {os.path.basename(ch00_path)}")
            plt.show()

            # Save transformations
            transforms[ch00_path] += micro_transform

            plot_images(reference_image, blue_micro, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = blue_micro

        # Apply the same transformations to the red and green channels
        red_channel = cp.asarray(hed_image[..., 0])
        green_channel = cp.asarray(hed_image[..., 1])

        red_aligned = apply_transform(red_channel, transforms[ch00_path])
        green_aligned = apply_transform(green_channel, transforms[ch00_path])

        # Combine the aligned channels back into an RGB image
        aligned_rgb = hed2rgb(np.stack([cp.asnumpy(red_aligned), cp.asnumpy(green_aligned), cp.asnumpy(aligned_images[ch00_path])], axis=-1))

        # Save the aligned RGB image
        save_image(cp.asarray(aligned_rgb), output_directory, f"{os.path.basename(ch00_path)}_aligned_rgb.tif")

    try:
        # Ensure the reference image is included as the first channel in the stack
        aligned_images = {"reference": reference_image, **aligned_images}

        # Save all aligned images in a multi-channel stack
        output_filename = os.path.join(output_directory, "aligned_stack.tif")
        tifffile.imwrite(output_filename, cp.asnumpy(cp.stack(list(aligned_images.values()), axis=0)).astype(np.float32))
        print(f"Saved aligned images to: {output_filename}")
    except ValueError as e:
        print(f"Error stacking images: {e}. Saving images individually.")
        # Save each aligned image individually
        for channel_id, aligned_image in aligned_images.items():
            save_image(aligned_image, output_directory, f"{channel_id}_aligned.tif")

# Main Execution
if __name__ == "__main__":
    if check_gpu():
        src_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/"
        dst_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/aligned_images_cupy"
        reference_slide = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tif"

        reference_image = cp.asarray(tifffile.imread(reference_slide))
        reference_pixel_size = get_pixel_size(reference_slide)

        # Get all image files and group them by base name
        image_sets = {}
        for f in sorted(os.listdir(src_dir)):
            if f.endswith(".tif"):
                base_name, channel = f.rsplit('_ch', 1)
                channel = 'ch' + channel.split('.')[0]
                if base_name not in image_sets:
                    image_sets[base_name] = {}
                image_sets[base_name][channel] = os.path.join(src_dir, f)

        # Ensure the reference image is excluded from the image sets
        image_sets.pop(os.path.basename(reference_slide).rsplit('_ch', 1)[0], None)

        # Parameters for alignment
        use_non_rigid = True
        use_micro_registration = True
        affine_transform_type = "Affine"
        normalize = False
        save_intermediate = True

        align_images(image_sets, reference_image, reference_pixel_size, dst_dir, use_non_rigid, use_micro_registration, affine_transform_type, normalize, save_intermediate)
    else:
        print("No GPU available. Exiting.")





import os
import tifffile
import numpy as np
import ants
import matplotlib.pyplot as plt
from skimage.transform import resize, rescale

def get_pixel_size(tif_path):
    """Extract pixel size from TIFF metadata."""
    with tifffile.TiffFile(tif_path) as tif:
        metadata = tif.pages[0].tags
        if "XResolution" in metadata and "YResolution" in metadata:
            x_res = metadata["XResolution"].value
            y_res = metadata["YResolution"].value
            return (x_res[1] / x_res[0], y_res[1] / y_res[0])
    return None

def normalize_image(image, min_val=0, max_val=1):
    """Normalize image to [min_val, max_val] range for consistent processing."""
    norm_image = (image - np.min(image)) / (np.max(image) - np.min(image) + 1e-8)
    return norm_image * (max_val - min_val) + min_val

def rgb_to_grayscale(image):
    """Convert an RGB image to grayscale."""
    if image.ndim == 3 and image.shape[2] == 3:
        grayscale_image = 0.2989 * image[..., 0] + 0.5870 * image[..., 1] + 0.1140 * image[..., 2]
        return grayscale_image
    return image

def resize_image(image, scale_y, scale_x, target_shape):
    """Resize image while preserving aspect ratio."""
    print(f"Resizing image from {image.shape} with scales (y: {scale_y}, x: {scale_x}) to target shape {target_shape}")
    scaled_image = rescale(image, (scale_y, scale_x), anti_aliasing=True, preserve_range=True)
    resized_image = resize(scaled_image, target_shape, anti_aliasing=True, preserve_range=True)
    print(f"Resized image shape: {resized_image.shape}")
    return resized_image

def affine_registration(fixed, moving, transform_type="Affine"):
    """Perform affine registration and return transformed image and transformation paths."""
    fixed_ants = ants.from_numpy(fixed)
    moving_ants = ants.from_numpy(moving)
    reg = ants.registration(fixed_ants, moving_ants, type_of_transform=transform_type)
    affine_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Affine registration completed. Transform paths: {affine_transform_paths}")
    return reg['warpedmovout'].numpy(), affine_transform_paths  # Return image + paths

def non_rigid_registration(fixed, moving):
    """Perform non-rigid registration using SyN and return transformed image and transformation paths."""
    fixed_ants = ants.from_numpy(fixed)
    moving_ants = ants.from_numpy(moving)
    reg = ants.registration(fixed_ants, moving_ants, type_of_transform="SyN")
    non_rigid_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Non-rigid registration completed. Transform paths: {non_rigid_transform_paths}")
    return reg['warpedmovout'].numpy(), non_rigid_transform_paths  # Return image + paths

def micro_registration(fixed, moving, patch_size=64, overlap=32):
    """Perform micro-registration using small patches."""
    fixed_ants = ants.from_numpy(fixed)
    moving_ants = ants.from_numpy(moving)
    reg = ants.registration(
        fixed_ants, 
        moving_ants, 
        type_of_transform="SyN", 
        reg_iterations=(20, 10, 0), 
        syn_metric='CC', 
        syn_sampling=2, 
        syn_radius=4, 
        syn_patch_size=patch_size, 
        syn_overlap=overlap
    )
    micro_transform_paths = reg['fwdtransforms']  # This is a list of file paths
    print(f"Micro-registration completed. Transform paths: {micro_transform_paths}")
    return reg['warpedmovout'].numpy(), micro_transform_paths  # Return image + paths

def apply_transform(image, transform_paths):
    """Apply stored transformations to another channel."""
    ants_image = ants.from_numpy(image)
    transformed_image = ants.apply_transforms(fixed=ants_image, moving=ants_image, transformlist=transform_paths)
    image_transformed = transformed_image.numpy()
    print(f"Applied transform. Final image shape: {image_transformed.shape}")
    return image_transformed

def plot_images(reference, aligned, title):
    """Plot reference, aligned, and difference images."""
    difference = np.abs(reference - aligned)
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    axes[0].imshow(reference, cmap="gray")
    axes[0].set_title("Reference Image")
    axes[1].imshow(aligned, cmap="gray")
    axes[1].set_title("Aligned Image")
    axes[2].imshow(difference, cmap="hot")
    axes[2].set_title("Difference")
    plt.suptitle(title)
    plt.show()

def save_image(image, output_directory, filename):
    """Save image to disk."""
    output_path = os.path.join(output_directory, filename)
    tifffile.imwrite(output_path, image.astype(np.float32))
    print(f"Saved image to: {output_path}")

def align_images(image_sets, reference_image, reference_pixel_size, output_directory, use_non_rigid=True, use_micro_registration=False, affine_transform_type="Affine", normalize=True, save_intermediate=False):
    """Align all channels to the reference image."""
    os.makedirs(output_directory, exist_ok=True)
    aligned_images = {"reference": reference_image}
    transforms = {}

    if normalize:
        reference_image = normalize_image(reference_image)  # Ensure consistent intensity
    
    for base_name, channels in image_sets.items():
        print(f"Processing base name: {base_name}")
        ch00_path = channels['ch00']
        print(f"Processing {ch00_path}")
        image = tifffile.imread(ch00_path)
        grayscale_image = rgb_to_grayscale(image)  # Convert RGB to grayscale for alignment
        if normalize:
            grayscale_image = normalize_image(grayscale_image)  # Normalize intensity before processing

        image_pixel_size = get_pixel_size(ch00_path)
        if not image_pixel_size:
            print(f"Skipping {ch00_path}: Pixel size not found.")
            continue

        print(f"Image pixel size: {image_pixel_size}, Reference pixel size: {reference_pixel_size}")

        # Rescale image to match reference pixel size
        scale_x, scale_y = image_pixel_size[0] / reference_pixel_size[0], image_pixel_size[1] / reference_pixel_size[1]
        image_resized = resize_image(grayscale_image, scale_y, scale_x, reference_image.shape)

        if save_intermediate:
            # Save resized image
            save_image(image_resized, output_directory, f"{os.path.basename(ch00_path)}_resized.tif")

        # Plot resized image
        plt.figure()
        plt.imshow(image_resized, cmap="gray")
        plt.title(f"Resized Image: {os.path.basename(ch00_path)}")
        plt.show()

        # Apply affine registration
        image_affine, affine_transform = affine_registration(reference_image, image_resized, transform_type=affine_transform_type)

        if save_intermediate:
            # Save affine registered image
            save_image(image_affine, output_directory, f"{os.path.basename(ch00_path)}_affine.tif")

        # Plot affine registered image
        plt.figure()
        plt.imshow(image_affine, cmap="gray")
        plt.title(f"Affine Registered Image: {os.path.basename(ch00_path)}")
        plt.show()

        if use_non_rigid:
            # Apply non-rigid registration
            image_non_rigid, non_rigid_transform = non_rigid_registration(reference_image, image_affine)

            if save_intermediate:
                # Save non-rigid registered image
                save_image(image_non_rigid, output_directory, f"{os.path.basename(ch00_path)}_non_rigid.tif")

            # Plot non-rigid registered image
            plt.figure()
            plt.imshow(image_non_rigid, cmap="gray")
            plt.title(f"Non-Rigid Registered Image: {os.path.basename(ch00_path)}")
            plt.show()

            # Save transformations
            transforms[ch00_path] = affine_transform + non_rigid_transform

            plot_images(reference_image, image_non_rigid, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = image_non_rigid
        else:
            # Save transformations
            transforms[ch00_path] = affine_transform

            plot_images(reference_image, image_affine, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = image_affine

        if use_micro_registration:
            # Apply micro-registration
            image_micro, micro_transform = micro_registration(reference_image, aligned_images[ch00_path])

            if save_intermediate:
                # Save micro-registered image
                save_image(image_micro, output_directory, f"{os.path.basename(ch00_path)}_micro.tif")

            # Plot micro-registered image
            plt.figure()
            plt.imshow(image_micro, cmap="gray")
            plt.title(f"Micro-Registered Image: {os.path.basename(ch00_path)}")
            plt.show()

            # Save transformations
            transforms[ch00_path] += micro_transform

            plot_images(reference_image, image_micro, f"Aligned: {os.path.basename(ch00_path)}")
            aligned_images[ch00_path] = image_micro

        # Align other channels using the same transformations as ch00
        for channel_id, ch_path in channels.items():
            if channel_id == 'ch00':
                continue
            print(f"Processing {ch_path}")
            ch_image = tifffile.imread(ch_path)
            grayscale_ch_image = rgb_to_grayscale(ch_image)  # Convert RGB to grayscale for alignment
            if normalize:
                grayscale_ch_image = normalize_image(grayscale_ch_image)

            # Rescale ch image to match reference pixel size
            ch_resized = resize_image(grayscale_ch_image, scale_y, scale_x, reference_image.shape)

            transform_paths = transforms[ch00_path]
            ch_aligned = apply_transform(ch_resized, transform_paths)

            plot_images(reference_image, ch_aligned, f"Aligned {channel_id}: {os.path.basename(ch_path)}")
            aligned_images[ch_path] = ch_aligned

    try:
        # Ensure the reference image is included as the first channel in the stack
        aligned_images = {"reference": reference_image, **aligned_images}

        # Save all aligned images in a multi-channel stack
        output_filename = os.path.join(output_directory, "aligned_stack.tif")
        tifffile.imwrite(output_filename, np.stack(list(aligned_images.values()), axis=0).astype(np.float32))
        print(f"Saved aligned images to: {output_filename}")
    except ValueError as e:
        print(f"Error stacking images: {e}. Saving images individually.")
        # Save each aligned image individually
        for channel_id, aligned_image in aligned_images.items():
            save_image(aligned_image, output_directory, f"{channel_id}_aligned.tif")

# Main Execution
src_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/"
dst_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/aligned_images"
reference_slide = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tif"

reference_image = tifffile.imread(reference_slide)
reference_pixel_size = get_pixel_size(reference_slide)

# Get all image files and group them by base name
image_sets = {}
for f in sorted(os.listdir(src_dir)):
    if f.endswith(".tif"):
        base_name, channel = f.rsplit('_ch', 1)
        channel = 'ch' + channel.split('.')[0]
        if base_name not in image_sets:
            image_sets[base_name] = {}
        image_sets[base_name][channel] = os.path.join(src_dir, f)

# Ensure the reference image is excluded from the image sets
image_sets.pop(os.path.basename(reference_slide).rsplit('_ch', 1)[0], None)

# Parameters for alignment
use_non_rigid = True
use_micro_registration = True
affine_transform_type = "Affine"
normalize = False
save_intermediate = True

align_images(image_sets, reference_image, reference_pixel_size, dst_dir, use_non_rigid, use_micro_registration, affine_transform_type, normalize, save_intermediate)





VALIS

import time
import os
import numpy as np

from tqdm import tqdm
import sys
import pathlib
from valis import registration, valtils, preprocessing, slide_io
from valis.micro_rigid_registrar import MicroRigidRegistrar # For high resolution rigid registration

from PIL import Image
import matplotlib.pyplot as plt
import pyvips

src_dir = "/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/"
dst_dir = "/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/aligned_images/"
reference_slide = "/mnt/d/Users/<USER>/antho 4i alignment/LB06_Tiffs/test/LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00"
micro_reg_fraction = 0.3 # Fraction full resolution used for non-rigid registration



#single images , no rounds : 
from valis import registration
slide_src_dir = "/mnt/d/Users/<USER>/antho 4i alignment/LB06"
results_dst_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/slide_registration_example"
registered_slide_dst_dir = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/slide_registration_example/registered_slides"
reference_slide = "/mnt/d/Users/<USER>/antho 4i alignment/skin dapi hne/dr3216481 82 male strip 4i_R 1_Merged_ch00.tiff"

# Create a Valis object and use it to register the slides in slide_src_dir, aligning *towards* the reference slide.
registrar = registration.Valis(slide_src_dir, results_dst_dir, reference_img_f=reference_slide)
rigid_registrar, non_rigid_registrar, error_df = registrar.register()


# Save all registered slides as ome.tiff
registrar.warp_and_save_slides(registered_slide_dst_dir, crop="overlap")

# Kill the JVM
registration.kill_jvm()

def get_round_name(src_f):
    img_name = valtils.get_name(src_f)
    round_name = img_name.lower().split("_merged")[0]
    return round_name

# def get_round_name(src_f):
#     # Extract the round name from the filename
#     # Example filename: r02c02f01_round1_ch00.tiff
#     img_name = os.path.basename(src_f)
#     round_part = img_name.split('_round')[1]  # Split by '_round'
#     round_name = round_part.split('_ch')[0]  # Get the part before '_ch'
#     return round_name

def get_channel_number(src_f):
    """Extract the channel number from the filename.
    For filenames like 'LB06 BP 4i aSMA green cxcr3 red 28mar25_R 3_Merged_ch00.tif',
    this returns 0
    """
    img_name = os.path.basename(src_f).lower()
    if "_ch00" in img_name:
        return 0
    elif "_ch01" in img_name:
        return 1
    elif "_ch02" in img_name:
        return 2
    else:
        return None
       
def get_ome_xml(warped_slide, reference_slide, channel_names=None):
  """Generate ome-xml for warped slide

  Parameters
  ----------
  warped_slide : pyvips.Image
    Registered slide that will be saved as an ome.tiff

  reference_slide : registration.Slide
    Slide object that the others were aligned to/towards

  channel_names : str, list, optional
    channel names for warped slide. If `None`, then the
    channel names from `src_f` will be used


  Returns
  -------
  ome_xml : str
    String of the ome-xml metadata

  """

  ref_meta = reference_slide.reader.metadata
  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)
  ome_xml_obj = slide_io.create_ome_xml(
      shape_xyzct=out_xyczt,
      bf_dtype=bf_dtype,
      is_rgb=False,
      pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
      channel_names=channel_names,
      colormap=None
  )
  return ome_xml_obj.to_xml()

"""Process images with direct channel handling to ensure proper ordering"""
print("Starting image processing with DIRECT channel handling...")

# Create output directory
merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], "slides")
pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)

# Directory for individual warped channels
individual_channel_dir = os.path.join(merged_slide_dir, "warped_channels")
os.makedirs(individual_channel_dir, exist_ok=True)

# Get all images
full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]

# Register rounds using DAPI channels , which all end in "ch00.tif"
dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tif")]
print(f"Found {len(dapi_imgs)} DAPI images for registration")

# Initialize VALIS registrar
registrar = registration.Valis(
    src_dir, dst_dir, img_list=dapi_imgs,
    reference_img_f=reference_slide,
    micro_rigid_registrar_cls=None,
    align_to_reference=True
)
print("Performing registration...")
rigid_registrar, non_rigid_registrar, error_df = registrar.register()

reference_slide = registrar.get_ref_slide()
micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)
print(f"Performing micro-registration with size: {micro_reg_size}...")
micro_reg, micro_error = registrar.register_micro(
    max_non_rigid_registration_dim_px=micro_reg_size,
    align_to_reference=True
)

# Use registration parameters to warp DAPI channel and the other images from the same round
round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]
              for slide_obj in registrar.slide_dict.values()}

channel_names = []
warped_slide = None

reference_shape = (reference_slide.slide_dimensions_wh[0], reference_slide.slide_dimensions_wh[1])

for slide_warper, round_slides in tqdm(round_dict.items()):
    print(f"\n[DEBUG] Warping images for round: {slide_warper.name}")
    valtils.sort_nicely(round_slides)
    print(f"[DEBUG] Files in this round: {round_slides}")

    for src_f in round_slides:
        img_name = valtils.get_name(src_f)
        print(f"[DEBUG] Warping channel: {img_name}")
        channel_names.append(img_name)
        channel_img = pyvips.Image.new_from_file(src_f)
        print(f"[DEBUG] Channel image shape: {channel_img.width}x{channel_img.height}, bands: {channel_img.bands}")

        # Apply the DAPI transformation to this channel
        warped_channel = slide_warper.warp_img(img=channel_img, crop=False)
        print(f"[DEBUG] Warped channel shape: {warped_channel.width}x{warped_channel.height}, bands: {warped_channel.bands}")

        # Resize warped channel to reference image shape if needed
        if (warped_channel.width, warped_channel.height) != reference_shape:
            scale_x = reference_shape[0] / warped_channel.width
            scale_y = reference_shape[1] / warped_channel.height
            warped_channel = warped_channel.resize(scale_x, vscale=scale_y)
            print(f"[DEBUG] Resized warped channel to reference shape: {warped_channel.width}x{warped_channel.height}")

        # Save each warped channel individually
        channel_basename = os.path.basename(src_f)
        individual_channel_path = os.path.join(individual_channel_dir, f"warped_{channel_basename}")
        warped_channel.write_to_file(individual_channel_path)
        print(f"[DEBUG] Saved individual warped channel: {individual_channel_path}")

        if warped_slide is None:
            warped_slide = warped_channel
            print(f"[DEBUG] Created initial warped_slide with {warped_slide.bands} bands")
        else:
            warped_slide = warped_slide.bandjoin(warped_channel)
            print(f"[DEBUG] After bandjoin: {warped_slide.bands} bands in warped_slide")

merged_slide_f = os.path.join(merged_slide_dir, "merged.ome.tiff")
merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)
slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)


 """Process images with direct channel handling to ensure proper ordering"""
print("Starting image processing with DIRECT channel handling...")
# Create output directory

merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], "slides")
pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)

# Get all images

full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]

# Register rounds using DAPI channels , which all end in "ch00.tif"
dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tif")]
print(f"Found {len(dapi_imgs)} DAPI images for registration")

# Initialize VALIS registrar

registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=MicroRigidRegistrar,align_to_reference=True)
# Perform registration
print("Performing registration...")
rigid_registrar, non_rigid_registrar, error_df = registrar.register()

reference_slide = registrar.get_ref_slide()
micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)
print(f"Performing micro-registration with size: {micro_reg_size}...")
micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)


# Use registration parameters to warp DAPI channel and the other images from the same round
round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]
              for slide_obj in registrar.slide_dict.values()}

all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]
channel_names = []
warped_slide = None

for slide_warper, round_slides in tqdm(round_dict.items()):
    print(f"\n[DEBUG] Warping images for round: {slide_warper.name}")
    valtils.sort_nicely(round_slides)
    print(f"[DEBUG] Files in this round: {round_slides}")
    # Extract transformation parameters from the DAPI slide_warper
    M = slide_warper.M
    bk_dxdy = slide_warper.bk_dxdy
    transformation_src_shape_rc = slide_warper.processed_img_shape_rc
    transformation_dst_shape_rc = slide_warper.reg_img_shape_rc
    out_shape_rc = slide_warper.aligned_slide_shape_rc
    print(f"[DEBUG] Transformation M: {M}")
    print(f"[DEBUG] Non-rigid bk_dxdy: {bk_dxdy}")
    print(f"[DEBUG] Source shape: {transformation_src_shape_rc}, Dest shape: {transformation_dst_shape_rc}, Out shape: {out_shape_rc}")

    for src_f in round_slides:
        img_name = valtils.get_name(src_f)
        print(f"[DEBUG] Warping channel: {img_name}")
        channel_names.append(img_name)
        # Read the channel image
        channel_img = pyvips.Image.new_from_file(src_f)
        print(f"[DEBUG] Channel image shape: {channel_img.width}x{channel_img.height}, bands: {channel_img.bands}")
        # Apply the DAPI transformation to this channel
        warped_channel = slide_warper.warp_img(
            img=channel_img, crop=False
            
        )
        print(f"[DEBUG] Warped channel shape: {warped_channel.width}x{warped_channel.height}, bands: {warped_channel.bands}")
        if warped_slide is None:
            warped_slide = warped_channel
            print(f"[DEBUG] Created initial warped_slide with {warped_slide.bands} bands")
        else:
            warped_slide = warped_slide.bandjoin(warped_channel)
            print(f"[DEBUG] After bandjoin: {warped_slide.bands} bands in warped_slide")


merged_slide_f = os.path.join(merged_slide_dir, "merged.ome.tiff")
merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)
slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)

# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features

# # Use registration parameters to warp DAPI channel and the other images from the same round
# # Group images by round
# print("Grouping images by round...")
# round_dict = {}
# for slide_obj in registrar.slide_dict.values():
#     round_name = get_round_name(slide_obj.src_f)
#     round_files = [f for f in full_img_list if get_round_name(f) == round_name]
#     round_dict[round_name] = {
#         'warper': slide_obj,
#         'files': round_files
#     }

# print(f"Found {len(round_dict)} rounds to process")
# all_warped_channels = []
# channel_names = []
# original_filenames = []  # Track original filenames for debugging output

# # Process each round
# print("Processing rounds and channels...")
# for round_name, round_info in tqdm(round_dict.items()):
#     print(f"\nProcessing round: {round_name}")
#     slide_warper = round_info['warper']
#     round_files = round_info['files']
    
#     # Sort files by channel number to ensure correct order (ch00, ch01, ch02...)
#     round_files.sort(key=get_channel_number)
    
#     # Process each channel file in this round
#     for channel_file in round_files:
#         channel_num = get_channel_number(channel_file)
#         filename = os.path.basename(channel_file)
#         print(f"  Processing channel {channel_num} from {filename}")
        
#         # Read the channel image
#         channel_img = pyvips.Image.new_from_file(channel_file)
        
#         # Apply warping transformation from the round's warper
#         warped_channel = slide_warper.warp_slide(
#             channel_img, 
#             level,
#             rigid_registrar, 
#             non_rigid_registrar, 
#             micro_reg)
        
#         # Add to our collection of warped channels
#         all_warped_channels.append(warped_channel)
#         original_filenames.append(filename)  # Store the original filename
        
#         # Create a meaningful channel name
#         channel_name = f"{round_name}_ch{channel_num:02d}"
#         channel_names.append(channel_name)
#         print(f"  Added {channel_name} to channel list")

# # Combine all channels into a single multi-band image
# print("\nCombining all channels into a single multi-band image...")
# if not all_warped_channels:
#     raise ValueError("No warped channels were created!")

# # Start with the first channel
# warped_slide = all_warped_channels[0]
# print(f"Starting with first channel: {warped_slide.width}x{warped_slide.height}, bands: {warped_slide.bands}, file: {original_filenames[0]}")

# # Add each additional channel
# for i, channel in enumerate(all_warped_channels[1:], 1):
#     print(f"Adding channel {i}: {channel.width}x{channel.height}, bands: {channel.bands}, file: {original_filenames[i]}")
#     warped_slide = warped_slide.bandjoin(channel)
#     print(f" Combined image now has {warped_slide.bands} bands")

# # Generate OME-XML and save the merged slide
# print("\nGenerating OME-XML and saving merged slide...")
# reference_slide = registrar.get_ref_slide()
# merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)
# merged_slide_f = os.path.join(merged_slide_dir, "merged_direct.ome.tiff")

# print(f"Saving merged slide with {len(channel_names)} channels to: {merged_slide_f}")
# print(f"Final image dimensions: {warped_slide.width}x{warped_slide.height}, bands: {warped_slide.bands}")
# print(f"Channel names: {channel_names}")

# # Save the merged slide
# slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)
# print(f"Successfully saved merged slide to: {merged_slide_f}")

# # Also save individual channels for verification
# print("\nSaving individual channels for verification...")
# for i, channel in enumerate(all_warped_channels):
#     channel_name = channel_names[i]
#     original_name = original_filenames[i]
#     channel_file = os.path.join(merged_slide_dir, f"channel_{i}_{channel_name}.tiff")
#     print(f"Saving channel {i}: {channel_file} (from {original_name})")
#     channel.tiffsave(channel_file, compression="lzw")

# return merged_slide_f


print(slide_warper.warp_img)  # See what methods/attributes it has

slide_warper.aligned_slide_shape_rc

import os
import numpy as np
import logging
import gc
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO)

# Define main paths
main_dir = "/mnt/c/Users/<USER>/4icellsneretti/sorted FOV/"
output_base_dir = "/mnt/c/Users/<USER>/4icellsneretti/ome-tiff"
micro_reg_fraction = 0.25  # Fraction for non-rigid registration

# Create the output directory if it doesn't exist
os.makedirs(output_base_dir, exist_ok=True)

# Helper functions
def get_available_workers(max_workers=None):
    # Get the total number of CPU cores
    cpu_count = os.cpu_count()
    
    # Adjust this number based on system load or reserve some cores
    if max_workers:
        num_workers = min(cpu_count, max_workers)
    else:
        num_workers = max(1, int(cpu_count * 0.75))  # Ensure at least 1 worker is available
    
    logging.info(f"Using {num_workers} workers out of {cpu_count} available CPUs.")
    return num_workers

def get_reference_slide(subfolder_name):
    ref_slide_path = os.path.join(main_dir, subfolder_name, f"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff")
    logging.debug(f"Reference slide path for {subfolder_name}: {ref_slide_path}")
    return ref_slide_path

def get_ome_xml(warped_slide, reference_slide, channel_names=None):
    ref_meta = reference_slide.reader.metadata
    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)
    ome_xml_obj = slide_io.create_ome_xml(
        shape_xyzct=out_xyczt,
        bf_dtype=bf_dtype,
        is_rgb=False,
        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
        channel_names=channel_names,
        colormap=None
    )
    return ome_xml_obj.to_xml()

def get_round_name(src_f):
    img_name = os.path.basename(src_f).lower()
    try:
        # Extract the part that starts with 'round'
        round_part = next(part for part in img_name.split('_') if part.startswith('round'))
        
        # Get the correct round name (rXXcXXfXX_roundX)
        round_name = '_'.join(img_name.split('_')[:2])  # Get the first 2 parts (rXXcXXfXX, roundX)
        
        logging.debug(f"Extracted round name: {round_name}")
        
    except (IndexError, StopIteration) as e:
        logging.error(f"Error extracting round name from filename {img_name}: {e}")
        round_name = 'unknown'

    return round_name

def process_subfolder(subfolder_path):
    subfolder_name = os.path.basename(subfolder_path)
    logging.debug(f"Processing subfolder: {subfolder_name}")

    reference_slide = get_reference_slide(subfolder_name)

    if not os.path.exists(reference_slide):
        logging.error(f"Reference slide not found: {reference_slide}")
        return

    logging.info(f"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}")

    # List all TIFF files in the subfolder
    
    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]
    logging.info(f"Full image list: {full_img_list}")

    # Register rounds using DAPI channels , which all end in "ch00.tiff"
    dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tiff")]
    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)
    rigid_registrar, non_rigid_registrar, error_df = registrar.register()

    reference_slide_obj = registrar.get_ref_slide()
    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)
    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)
    
    
    # Create a dictionary with the slides grouped by their round (like round1, round2, etc.)
    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] 
                  for slide_obj in registrar.slide_dict.values()}

    all_imgs = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path)]
    channel_names = []
    warped_slide = None

    for slide_warper, round_slides in tqdm(round_dict.items()):
        logging.info(f"Warping images associated with {get_round_name(slide_warper.src_f)}")
        
        # Sort the images based on both round and channel to ensure proper order
        try:
            valtils.sort_nicely(round_slides)
        except Exception as e:
            logging.error(f"Error sorting slides: {e}")
            continue
        
        logging.debug(f"Sorted round slides: {round_slides}")

        for src_f in round_slides:
            img_name = valtils.get_name(src_f)
            channel_names.append(img_name)
            
            logging.info(f"Processing channel: {img_name}")

            try:
                warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)
                logging.debug(f"Warped channel shape: {warped_channel.shape}")
            except Exception as e:
                logging.error(f"Error warping slide {src_f}: {e}")
                continue

            if warped_slide is None:
                warped_slide = warped_channel
            else:
                warped_slide = warped_slide.bandjoin(warped_channel)
            logging.info(f"Processed channel: {img_name}")

    if warped_slide is None:
        logging.error("No warped slide created.")
        return

    reference_slide_obj = registrar.get_ref_slide()
    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)

    ome_tiff_name = f"merged_{subfolder_name}.ome.tiff"
    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)

    try:
        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)
        logging.info(f"Saved: {ome_tiff_path}")
    except Exception as e:
        logging.error(f"Error saving OME-TIFF file: {e}")

    del warped_slide
    gc.collect()
    
def process_all_folders():
    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))
                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']

    if len(subfolders) == 0:
        logging.info("No valid subfolders to process.")
        return

    logging.debug(f"Subfolders found: {len(subfolders)}")

    num_threads = get_available_workers(max_workers=len(subfolders))

    batch_size = 1
    for i in range(0, len(subfolders), batch_size):
        batch = subfolders[i:i + batch_size]

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            executor.map(process_subfolder, batch)

        logging.info(f"Processed batch {i // batch_size + 1}")

    logging.info("All folders processed.")

if __name__ == "__main__":
    process_all_folders()


#no multithreading : 
import os
import numpy as np
import logging
from tqdm import tqdm
import gc

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Define main paths
main_dir = "/mnt/d/sorted FOV/"
output_base_dir = "/mnt/c/Users/<USER>/4icellsneretti/"
micro_reg_fraction = 0.25  # Fraction for non-rigid registration

# Create the output directory if it doesn't exist
os.makedirs(output_base_dir, exist_ok=True)

# Function to extract the reference slide dynamically for each subfolder
def get_reference_slide(subfolder_name):
    ref_slide_path = os.path.join(main_dir, subfolder_name, f"{subfolder_name.split('sorted')[-1]}_round1_ch00.tiff")
    logging.debug(f"Reference slide path for {subfolder_name}: {ref_slide_path}")
    return ref_slide_path

# Function to generate OME-XML metadata for the warped image
def get_ome_xml(warped_slide, reference_slide, channel_names=None):
    ref_meta = reference_slide.reader.metadata
    bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
    out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)
    ome_xml_obj = slide_io.create_ome_xml(
        shape_xyzct=out_xyczt,
        bf_dtype=bf_dtype,
        is_rgb=False,
        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
        channel_names=channel_names,
        colormap=None
    )
    return ome_xml_obj.to_xml()

# Extract the round name from the filename
def get_round_name(src_f):
    # Example filename: r02c02f01_round1_ch00.tiff
    img_name = os.path.basename(src_f)
    round_part = img_name.split('_round')[1]
    round_name = round_part.split('_ch')[0]
    return round_name

# Function to process a single subfolder
def process_subfolder(subfolder_path):
    subfolder_name = os.path.basename(subfolder_path)
    logging.debug(f"Processing subfolder: {subfolder_name}")

    reference_slide = get_reference_slide(subfolder_name)

    if not os.path.exists(reference_slide):
        logging.error(f"Reference slide not found: {reference_slide}")
        return

    logging.info(f"Processing subfolder: {subfolder_name} using reference slide: {reference_slide}")

    # List all .tiff files in the subfolder
    full_img_list = [os.path.join(subfolder_path, f) for f in os.listdir(subfolder_path) if f.endswith(".tiff")]

    # Perform registration using DAPI channels (assumed to be "ch00.tiff")
    dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tiff")]
    registrar = registration.Valis(subfolder_path, output_base_dir, img_list=dapi_imgs, reference_img_f=reference_slide, align_to_reference=True)
    rigid_registrar, non_rigid_registrar, error_df = registrar.register()

    reference_slide_obj = registrar.get_ref_slide()
    micro_reg_size = np.floor(np.max(reference_slide_obj.slide_dimensions_wh[0]) * micro_reg_fraction).astype(int)
    micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size, align_to_reference=True)

    # Warp images based on registration results
    round_dict = {slide_obj: [f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)] for slide_obj in registrar.slide_dict.values()}
    
    channel_names = []
    warped_slide = None

    for slide_warper, round_slides in tqdm(round_dict.items()):
        logging.info(f"Warping images associated with {slide_warper.name}")
        valtils.sort_nicely(round_slides)
        
        for src_f in round_slides:
            img_name = valtils.get_name(src_f)
            channel_names.append(img_name)
            warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)
            
            if warped_slide is None:
                warped_slide = warped_channel
            else:
                warped_slide = warped_slide.bandjoin(warped_channel)

    # Create OME-XML metadata
    merged_ome_xml = get_ome_xml(warped_slide, reference_slide_obj, channel_names)

    ome_tiff_name = f"sorted_{subfolder_name}.ome.tiff"
    ome_tiff_path = os.path.join(output_base_dir, ome_tiff_name)
    
    try:
        slide_io.save_ome_tiff(img=warped_slide, dst_f=ome_tiff_path, ome_xml=merged_ome_xml)
        logging.info(f"Saved: {ome_tiff_path}")
    except Exception as e:
        logging.error(f"Error saving {ome_tiff_path}: {e}")

    # Clear memory
    del warped_slide
    gc.collect()

# Function to process all subfolders sequentially
def process_all_folders():
    subfolders = [os.path.join(main_dir, subfolder) for subfolder in sorted(os.listdir(main_dir))
                  if os.path.isdir(os.path.join(main_dir, subfolder)) and subfolder != 'ome-tiff']

    if len(subfolders) == 0:
        logging.info("No valid subfolders to process.")
        return

    logging.info(f"Found {len(subfolders)} subfolders to process.")

    # Process each subfolder sequentially
    for subfolder in subfolders:
        process_subfolder(subfolder)
        logging.info(f"Completed processing subfolder: {os.path.basename(subfolder)}")

    logging.info("All folders processed.")

# Main entry point
if __name__ == "__main__":
    process_all_folders()


src_dir = "/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua"
dst_dir = "/mnt/c/Users/<USER>/antho 4i alignment/results young207ua FILLA/"
reference_slide = "/mnt/c/Users/<USER>/antho 4i alignment/imageyoung207ua/young ctrl skin 4i p16 red p21 green 25sep23_Region 4_Merged_ch00.tif"
micro_reg_fraction = 0.25 # Fraction full resolution used for non-rigid registration

def get_round_name(src_f):
    img_name = valtils.get_name(src_f)
    round_name = img_name.lower().split("_merged")[0]
    return round_name

def get_ome_xml(warped_slide, reference_slide, channel_names=None):
  """Generate ome-xml for warped slide

  Parameters
  ----------
  warped_slide : pyvips.Image
    Registered slide that will be saved as an ome.tiff

  reference_slide : registration.Slide
    Slide object that the others were aligned to/towards

  channel_names : str, list, optional
    channel names for warped slide. If `None`, then the
    channel names from `src_f` will be used


  Returns
  -------
  ome_xml : str
    String of the ome-xml metadata

  """

  ref_meta = reference_slide.reader.metadata

  bf_dtype = slide_io.vips2bf_dtype(warped_slide.format)
  out_xyczt = slide_io.get_shape_xyzct((warped_slide.width, warped_slide.height), warped_slide.bands)
  ome_xml_obj = slide_io.create_ome_xml(shape_xyzct=out_xyczt,
                                        bf_dtype=bf_dtype,
                                        is_rgb=False,
                                        pixel_physical_size_xyu=ref_meta.pixel_physical_size_xyu,
                                        channel_names=channel_names,
                                        colormap=None)

  ome_xml = ome_xml_obj.to_xml()

  return ome_xml

merged_slide_dir = os.path.join(os.path.split(dst_dir)[0], "slides")
pathlib.Path(merged_slide_dir).mkdir(exist_ok=True, parents=True)

full_img_list = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]

# Register rounds using DAPI channels , which all end in "ch00.tif"
dapi_imgs = [f for f in full_img_list if f.endswith("ch00.tif")]

registrar = registration.Valis(src_dir, dst_dir, img_list=dapi_imgs, reference_img_f=reference_slide, micro_rigid_registrar_cls=None,align_to_reference=True)
rigid_registrar, non_rigid_registrar, error_df = registrar.register()

reference_slide = registrar.get_ref_slide()
micro_reg_size = np.floor(np.max(reference_slide.slide_dimensions_wh[0])*micro_reg_fraction).astype(int)
micro_reg, micro_error = registrar.register_micro(max_non_rigid_registration_dim_px=micro_reg_size,align_to_reference=True)


# registrar.draw_matches(registrar.dst_dir) # Uncomment to save images showing matched features

# Use registration parameters to warp DAPI channel and the other images from the same round
round_dict = {slide_obj:[f for f in full_img_list if get_round_name(f) == get_round_name(slide_obj.src_f)]
              for slide_obj in registrar.slide_dict.values()}

all_imgs = [os.path.join(src_dir, f) for f in os.listdir(src_dir)]
channel_names = []
warped_slide = None

for slide_warper, round_slides in tqdm(round_dict.items()):
    print(f"warping images associated with {slide_warper.name}")
    valtils.sort_nicely(round_slides)
    for src_f in round_slides:
        img_name = valtils.get_name(src_f)
        channel_names.append(img_name)
        warped_channel = slide_warper.warp_slide(src_f=src_f, level=0)
        if warped_slide is None:
            warped_slide = warped_channel
        else:
            warped_slide = warped_slide.bandjoin(warped_channel)

reference_slide = registrar.get_ref_slide()
merged_ome_xml = get_ome_xml(warped_slide, reference_slide, channel_names)
merged_slide_f = os.path.join(merged_slide_dir, "merged.ome.tiff")

slide_io.save_ome_tiff(img=warped_slide, dst_f=merged_slide_f, ome_xml=merged_ome_xml)