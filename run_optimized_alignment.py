#!/usr/bin/env python3
"""
Run optimized image alignment.
This script uses optimized registration functions to speed up the alignment process.
"""

import sys
import os
import time
from pathlib import Path

# Add local site-packages to path
sys.path.append('/home/<USER>/.local/lib/python3.10/site-packages')

# Import required libraries
try:
    import cupy as cp
    import numpy as np
    import tifffile
    import ants
    from tqdm import tqdm
    from skimage.registration import phase_cross_correlation
    
    # Import our optimized functions
    from optimized_batch_align import optimized_batch_align_to_reference
    
    print("All required libraries imported successfully")
except ImportError as e:
    print(f"Error importing libraries: {e}")
    sys.exit(1)

def main():
    """Main function to run the optimized alignment."""
    # Define paths
    PADDED_DIR = "/mnt/d/Users/<USER>/antho 4i alignment/lb06/R2/"
    REFERENCE_IMG = (
        "/mnt/d/Users/<USER>/antho 4i alignment/"
        "lb06/R2/LB06 BP lung 4i p21 green p16 red 9apr25.lif - R 2_Merged_ch00.tif"
    )
    OUTPUT_DIR = os.path.join(PADDED_DIR, "aligned_images_optimized")
    
    # Create output directory if it doesn't exist
    Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
    
    print(f"Reference image: {REFERENCE_IMG}")
    print(f"Input directory: {PADDED_DIR}")
    print(f"Output directory: {OUTPUT_DIR}")
    
    # Check if reference image exists
    if not os.path.exists(REFERENCE_IMG):
        print(f"Error: Reference image not found at {REFERENCE_IMG}")
        sys.exit(1)
    
    # Check if input directory exists
    if not os.path.exists(PADDED_DIR):
        print(f"Error: Input directory not found at {PADDED_DIR}")
        sys.exit(1)
    
    # Run the optimized batch alignment
    try:
        start_time = time.time()
        
        optimized_batch_align_to_reference(
            REFERENCE_IMG,
            PADDED_DIR,
            OUTPUT_DIR,
            affine_transform_type="Affine",
            use_non_rigid=True,
            use_micro_registration=True,
            save_intermediate=False,
            show_overlay=False,  # Set to True if you want to see overlays
            reg_interpolator="linear",  # Faster interpolation
            apply_interpolator="bSpline",  # Better quality for final output
            downsample_factor=0.5  # Use 50% resolution for non-rigid registration
        )
        
        end_time = time.time()
        print(f"Total execution time: {end_time - start_time:.2f} seconds")
        
    except Exception as e:
        print(f"Error during alignment: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
