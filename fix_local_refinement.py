# Fix for the local refinement error
# Replace the problematic section in hierarchical_registration_and_flow function

# Find this section in your code:
"""
print(f"[LOCAL REFINEMENT] Completed. Combined similarity: {pre_refinement_combined:.4f} → {post_refinement_combined:.4f}")
print(f"[LOCAL REFINEMENT] Correlation: {pre_refinement_similarity:.4f} → {post_refinement_similarity:.4f}")
print(f"[LOCAL REFINEMENT] SSIM: {pre_refinement_ssim:.4f} → {post_ssim:.4f}")
"""

# And replace it with:
"""
print(f"[LOCAL REFINEMENT] Completed. Combined similarity: {pre_refinement_combined:.4f} → {post_refinement_combined:.4f}")
print(f"[LOCAL REFINEMENT] Correlation: {pre_refinement_similarity:.4f} → {post_refinement_similarity:.4f}")
"""

# The error occurs because pre_refinement_ssim and post_ssim variables don't exist
# Simply removing the line that tries to print them will fix the issue