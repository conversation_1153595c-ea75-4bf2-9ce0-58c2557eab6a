{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'valis'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m registration\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mPIL\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Image\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmat<PERSON>lotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>lot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'valis'"]}], "source": ["from valis import registration\n", "from PIL import Image\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["slide_src_dir = \"/mnt/c/Users/<USER>/SynologyDrive/Mayo/Research/Passos Lab/Projects/TAF_Alignment/Data/4i_mice_skin/Images\"\n", "results_dst_dir = \"/mnt/c/Users/<USER>/SynologyDrive/Mayo/Research/Passos Lab/Projects/TAF_Alignment/Data/4i_mice_skin/Results\"\n", "registered_slide_dst_dir = \"/mnt/c/Users/<USER>/SynologyDrive/Mayo/Research/Passos Lab/Projects/TAF_Alignment/Data/4i_mice_skin/Results/Registered_slides.ome.tiff\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS/lib/python3.9/site-packages/PIL/Image.py:3176: DecompressionBombWarning: Image size (170912115 pixels) exceeds limit of 89478485 pixels, could be decompression bomb DOS attack.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Replace with your TIFF file path\n", "file_path = '/mnt/c/Users/<USER>/SynologyDrive/Mayo/Research/Passos Lab/Projects/TAF_Alignment/Data/4i_mice_skin/Images/4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00.tif'\n", "\n", "# Load the image\n", "image = Image.open(file_path)\n", "\n", "# Display the image\n", "plt.imshow(image)\n", "plt.axis('off')  # Turn off axis numbers\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["SLF4J: Failed to load class \"org.slf4j.impl.StaticLoggerBinder\".\n", "SLF4J: Defaulting to no-operation (NOP) logger implementation\n", "SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["JVM has been initialized. Be sure to call registration.kill_jvm() or slide_io.kill_jvm() at the end of your script.\n", "\n", "==== Converting images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Converting images: 100%|██████████| 11/11 [00:55<00:00,  5.07s/image]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Processing images\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing images : 100%|██████████| 11/11 [00:36<00:00,  3.29s/image]\n", "Normalizing images: 100%|██████████| 11/11 [00:01<00:00,  9.33image/s]\n", "Denoising images  : 100%|██████████| 11/11 [00:00<00:00, 21.99image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==== Rigid registration\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/VALIS/lib/python3.9/site-packages/valis/valtils.py:22: UserWarning: ('The reference was specified as 4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00 ', 'but `align_to_reference` is `False`, and so images will be aligned serially. ', 'If you would like all images to be directly aligned to 4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00, then set `align_to_reference` to `True`')\n", "  warnings.warn(warning_msg, warning_type)\n", "Detecting features   : 100%|██████████| 11/11 [00:00<00:00, 13.47image/s]\n", "Matching images      : 100%|██████████| 55/55 [00:01<00:00, 40.82image/s]\n", "Finding transforms   : 100%|██████████| 10/10 [00:00<00:00, 3096.34image/s]\n", "Finalizing           : 100%|██████████| 11/11 [00:00<00:00, 8320.53image/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======== Rigid registration complete in 16.205 seconds\n", "\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mT<PERSON> crashed while executing code in the the current cell or a previous cell. Please review the code in the cell(s) to identify a possible cause of the failure. Click <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. View Jupyter <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["registrar = registration.Valis(slide_src_dir, results_dst_dir, reference_img_f=\"/mnt/c/Users/<USER>/SynologyDrive/Mayo/Research/Passos Lab/Projects/TAF_Alignment/Data/4i_mice_skin/4i 1st round skin lamin B1 tile _TileScan 1_Merged_ch00.tif\")\n", "rigid_registrar, non_rigid_registrar, error_df = registrar.register()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}